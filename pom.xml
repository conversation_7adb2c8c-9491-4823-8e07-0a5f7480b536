<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
        <relativePath/>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>erp-adaptor</artifactId>
    <packaging>pom</packaging>
    <name>erp-adaptor</name>
    <groupId>com.xiaoju.corebanking</groupId>
    <version>0.0.1-SNAPSHOT</version>
    <description>erp-adaptor</description>


    <modules>
        <module>erp-adaptor-common</module>
        <module>erp-adaptor-bootstrap</module>
        <module>erp-adaptor-service</module>
        <module>erp-adaptor-integration</module>
        <module>erp-adaptor-repository</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <spring.boot.version>2.7.18</spring.boot.version>
        <spring.framework.version>5.3.35</spring.framework.version>
        <jacoco.version>0.8.6</jacoco.version>
        <godson.version>********-sofi</godson.version>
        <degrade911.version>1.0.14</degrade911.version>
        <elvish.version>0.0.49</elvish.version>
        <ddmq.version>2.2.29-SNAPSHOT</ddmq.version>
        <log4j2.version>2.17.2</log4j2.version>
        <compliance.version>0.2.2</compliance.version>
        <digitalbank.common.version>0.0.16-sofi.5</digitalbank.common.version>
        <account.version>0.0.1-SNAPSHOT</account.version>
        <digitalbank.component.version>0.0.4</digitalbank.component.version>
        <digitalbank.naming.version>0.0.2</digitalbank.naming.version>
        <fastjson.version>1.2.83</fastjson.version>
        <fin-base.version>0.0.4-SNAPSHOT</fin-base.version>
        <swagger.version>1.7.0</swagger.version>
        <erp.version>0.0.1-SNAPSHOT</erp.version>
        <guava.version>32.1.3-jre</guava.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <jacoco.version>0.8.6</jacoco.version>
        <godson.version>0.0.59-sofi</godson.version>
        <disf.version>0.3.0</disf.version>
        <degrade911.version>1.0.14</degrade911.version>
        <elvish.version>0.0.44</elvish.version>
        <ddmq.version>2.2.29-SNAPSHOT</ddmq.version>
        <log4j2.version>2.17.2</log4j2.version>
        <digitalbank.common.version>0.0.16-sofi.20</digitalbank.common.version>
        <exchange.version>0.0.1-SNAPSHOT</exchange.version>
        <compliance.version>0.3.2</compliance.version>
        <globalpay.version>1.2.18</globalpay.version>
        <gson.version>2.8.6</gson.version>
        <retry.version>1.3.1</retry.version>
        <aws.sdk.version>2.25.31</aws.sdk.version>
        <javax.mail.version>1.6.2</javax.mail.version>
        <fastjson.version>1.2.83</fastjson.version>
        <lombok.version>1.18.20</lombok.version>
        <didi.kms.version>1.0.15</didi.kms.version>
        <commons.csv.version>1.8</commons.csv.version>
        <java.jwt.version>4.4.0</java.jwt.version>
        <mockito.version>4.5.1</mockito.version>
        <mockito.all.version>1.10.19</mockito.all.version>
        <powermock.version>2.0.0</powermock.version>
        <junit.jupiter.version>5.7.1</junit.jupiter.version>
        <junit.platform.version>1.7.1</junit.platform.version>
        <testable.version>0.6.7</testable.version>
        <surfire.version>2.22.2</surfire.version>
        <disruptor.version>3.4.2</disruptor.version>
        <pagehelper.version>5.3.0</pagehelper.version>
        <minio.version>8.5.4</minio.version>
        <hibernate.validator.version>6.2.0.Final</hibernate.validator.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate.validator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <optional>true</optional>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xiaoju.corebanking</groupId>
                <artifactId>erp-adaptor-common</artifactId>
                <version>${erp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xiaoju.corebanking</groupId>
                <artifactId>erp-adaptor-integration</artifactId>
                <version>${erp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>kms</groupId>
                <artifactId>didi-java-sdk-kms</artifactId>
                <version>${didi.kms.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xiaoju.corebanking</groupId>
                <artifactId>erp-adaptor-service</artifactId>
                <version>${erp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xiaoju.corebanking</groupId>
                <artifactId>erp-adaptor-repository</artifactId>
                <version>${erp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${java.jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring.framework.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${spring.framework.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring.framework.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-expression</artifactId>
                <version>${spring.framework.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${spring.framework.version}</version>
            </dependency>

            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>


            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-all</artifactId>
                <version>${mockito.all.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.jacoco</groupId>
                <artifactId>org.jacoco.agent</artifactId>
                <version>${jacoco.version}</version>
                <classifier>runtime</classifier>
            </dependency>

            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-api</artifactId>
                <version>${junit.jupiter.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-engine</artifactId>
                <version>${junit.jupiter.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.platform</groupId>
                <artifactId>junit-platform-launcher</artifactId>
                <version>${junit.platform.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.platform</groupId>
                <artifactId>junit-platform-commons</artifactId>
                <version>${junit.platform.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.testable</groupId>
                <artifactId>testable-all</artifactId>
                <version>${testable.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>${disruptor.version}</version>
            </dependency>

            <!-- godson 框架 https://git.xiaojukeji.com/hanjiancheng/godson-framework -->
            <dependency>
                <groupId>com.xiaoju.godson</groupId>
                <artifactId>godson-log-api</artifactId>
                <version>${godson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xiaoju.godson</groupId>
                <artifactId>godson-log-log4j2</artifactId>
                <version>${godson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xiaoju.godson</groupId>
                <artifactId>godson-trace</artifactId>
                <version>${godson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xiaoju.godson</groupId>
                <artifactId>godson-degrade</artifactId>
                <version>${godson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xiaoju.godson</groupId>
                <artifactId>godson-dirpc</artifactId>
                <version>${godson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xiaoju.godson</groupId>
                <artifactId>godson-crypto</artifactId>
                <version>${godson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xiaoju.godson</groupId>
                <artifactId>godson-redis</artifactId>
                <version>${godson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xiaoju.godson</groupId>
                <artifactId>godson-mybatis-datasource</artifactId>
                <version>${godson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xiaoju.godson</groupId>
                <artifactId>godson-mybatis-interceptor</artifactId>
                <version>${godson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xiaoju.godson</groupId>
                <artifactId>godson-metric</artifactId>
                <version>${godson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xiaoju</groupId>
                <artifactId>compliance-java-sdk</artifactId>
                <version>${compliance.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>metric</artifactId>
                        <groupId>com.xiaoju.metric</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 公共组件，如json -->
            <dependency>
                <groupId>com.xiaoju.godson</groupId>
                <artifactId>godson-common</artifactId>
                <version>${godson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xiaoju.godson</groupId>
                <artifactId>godson-web</artifactId>
                <version>${godson.version}</version>
            </dependency>

            <!--  屏蔽json序列化的thrift非属性化字段   -->
            <dependency>
                <groupId>com.xiaoju.godson</groupId>
                <artifactId>godson-jackson-thrift</artifactId>
                <version>${godson.version}</version>
            </dependency>
            <!-- 公共组件，如json -->

            <!-- httpClient -->
            <dependency>
                <groupId>com.xiaoju.godson</groupId>
                <artifactId>godson-http</artifactId>
                <version>${godson.version}</version>
            </dependency>
            <!-- httpClient -->

            <!-- 911降级 -->
            <dependency>
                <groupId>com.xiaoju.onekey</groupId>
                <artifactId>degrade_sdk_java</artifactId>
                <version>${degrade911.version}</version>
            </dependency>
            <!-- 911降级 -->

            <!--统一货币sdk-->
            <dependency>
                <groupId>com.xiaoju.elvish</groupId>
                <artifactId>JavaSdk</artifactId>
                <version>${elvish.version}</version>
            </dependency>
            <!--统一货币sdk-->

            <dependency>
                <groupId>com.xiaojukeji.copywriter-java-sdk</groupId>
                <artifactId>copywriter-java-sdk</artifactId>
                <version>0.2.1</version>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>

            <!-- KMS 加密组件 -->
            <dependency>
                <groupId>kms</groupId>
                <artifactId>didi-kms-spring-boot-starter</artifactId>
                <version>0.0.14</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-csv</artifactId>
                <version>${commons.csv.version}</version>
            </dependency>
            <dependency>
                <artifactId>message-client</artifactId>
                <groupId>com.xiaoju.digitalbank</groupId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

        </dependencies>
    </dependencyManagement>


<dependencies>
    <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <optional>true</optional>
    </dependency>
</dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestEntries>
                            <!--suppress UnresolvedMavenProperty -->
                            <OE_GIT_REPO_NAME>${env.OE_GIT_REPO_NAME}</OE_GIT_REPO_NAME>
                            <!--suppress UnresolvedMavenProperty -->
                            <OE_BRANCH_NAME>${env.OE_BRANCH_NAME}</OE_BRANCH_NAME>
                            <!--suppress UnresolvedMavenProperty -->
                            <OE_COMMIT_SHA>${env.OE_COMMIT_SHA}</OE_COMMIT_SHA>
                            <!--suppress UnresolvedMavenProperty -->
                            <OE_BASE_COMMIT_SHA>${env.OE_BASE_COMMIT_SHA}</OE_BASE_COMMIT_SHA>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
            <!--  指定compile时的jdk版本信息  -->
            <!--  https://blog.csdn.net/liupeifeng3514/article/details/80236077 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>

            <!-- 资源文件相关配置，可以指定配置文件的位置及属性值  -->
            <!-- https://blog.csdn.net/u014515854/article/details/79166061 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
                <!--使用默认的变量分割符即${}-->
                <configuration>
                    <useDefaultDelimiters>true</useDefaultDelimiters>
                </configuration>
            </plugin>

            <!--  自动生成单测模板文件 -->
            <plugin>
                <groupId>com.github.houbb</groupId>
                <artifactId>gen-test-plugin</artifactId>
                <version>0.0.1</version>
                <configuration>
                    <junitVersion>5</junitVersion>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-report-plugin</artifactId>
                <version>2.19</version>
                <executions>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <!-- 要绑定到的生命周期的阶段 -->
                        <goals>
                            <goal>report</goal>
                            <!-- 要绑定的插件的目标 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- 添加jacoco插件 -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.6</version>
                <executions>
                    <execution>
                        <id>default-prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>central</id>
            <name>artifactory-main-releases</name>
            <url>
                http://artifactory.intra.xiaojukeji.com:80/artifactory/libs-release
            </url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>artifactory-main-snapshots</name>
            <url>
                http://artifactory.intra.xiaojukeji.com:80/artifactory/libs-snapshot
            </url>
        </snapshotRepository>
    </distributionManagement>

</project>
