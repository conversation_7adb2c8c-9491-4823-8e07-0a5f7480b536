###
sofi_journal_check.csv
sofi_journal_001.csv
checksum


### 多文件上传
POST http://dsim366-api.intra.xiaojukeji.com/erp/uploadWithDate
Content-Type: multipart/form-data; boundary=WebAppBoundary

--WebAppBoundary
Content-Disposition: form-data; name="files"; filename="sofi_journal_202507.csv"

< /Users/<USER>/liuxingliang_v/code/code20241015/erp-adaptor/erp-adaptor-contract/20250731/sofi_journal_202507.csv
--WebAppBoundary
Content-Disposition: form-data; name="files"; filename="sofi_journal_check.checksum"

< /Users/<USER>/liuxingliang_v/code/code20241015/erp-adaptor/erp-adaptor-contract/20250731/sofi_journal_check.checksum
--WebAppBoundary
Content-Disposition: form-data; name="date"

20250731
--WebAppBoundary--

###




### SAFI文件处理
POST http://localhost:9109/erp/test/preprocess-safi?
    param=20250731
Content-Type: application/x-www-form-urlencoded


### 【safi】轮训状态
POST http://localhost:9109/erp/test/pollLoadRequestStatus?
    param=20250815
Content-Type: application/x-www-form-urlencoded



### 【safi】funcion返回结果处理
POST http://localhost:9109/erp/test/handleFusionResultJob?
    param=20250815
Content-Type: application/x-www-form-urlencoded




# @Value("${fusion.path:/Users/<USER>/liuxingliang_v/code/code20241015/erp-adaptor/erp-adaptor-contract}")
### 神马文件处理  --  sofi_gl_shenma
POST http://localhost:9109/erp/test/preprocess?
    param=20250815
Content-Type: application/x-www-form-urlencoded


### 【shenma】轮训状态
POST http://localhost:9109/erp/test/pollShenmaLoadRequestStatus?
    param=20250816
Content-Type: application/x-www-form-urlencoded



### 【shenma】funcion返回结果处理
POST http://localhost:9109/erp/test/handleShenmaFusionResultJob?
    param=20250816
Content-Type: application/x-www-form-urlencoded



### 会计科目表文件处理 -- sofi_gl_subject
POST http://localhost:9109/erp/test/sofi-gl-subject?
    param=20250819
Content-Type: application/x-www-form-urlencoded



### 二维表值表 -- cux_two_tab_values
POST http://localhost:9109/erp/test/voucher-group?
    param=20250819
Content-Type: application/x-www-form-urlencoded



### 总账账户历史表文件处理 --- sofi_gl_acct_hist
POST http://localhost:9109/erp/test/sofi-gl-acct-hist?
    param=20250819
Content-Type: application/x-www-form-urlencoded
