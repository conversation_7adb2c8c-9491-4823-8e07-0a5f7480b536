#!/bin/bash

# Description:
# 1. Splits a CSV file into smaller chunks if a line count is provided.
# 2. Calculates the MD5 checksum for each generated data file.
# 3. Creates a check file with the filenames and line counts of the data file(s).
# 4. Calculates the MD5 checksum for the check file.
#
# Usage: ./genmd5.sh [input_csv_file] [lines_per_file]
#
# [input_csv_file]: Optional. Path to the input CSV file.
#                   Defaults to 'sofi_journal_generated.csv'.
# [lines_per_file]: Optional. Number of data rows to include in each split file.
#                   If not provided, the file is not split.

set -euo pipefail

# --- Configuration ---
INPUT_CSV="${1:-sofi_journal_generated.csv}"
SPLIT_LINES="${2:-}"
CHECK_CSV="sofi_journal_check.csv"
CHECK_CHECKSUM="sofi_journal_check.checksum"
OUTPUT_BASENAME="sofi_journal"

# --- Input Validation ---
if [ ! -f "$INPUT_CSV" ]; then
    echo "Error: Input file not found: '$INPUT_CSV'"
    exit 1
fi

if [ -n "$SPLIT_LINES" ] && ! [[ "$SPLIT_LINES" =~ ^[0-9]+$ ]] && [ "$SPLIT_LINES" -gt 0 ]; then
    echo "Error: lines_per_file must be a positive integer."
    exit 1
fi

# --- MD5 Command Detection ---
MD5_CMD=""
if command -v md5 >/dev/null 2>&1; then
    MD5_CMD="md5 -q" # macOS
elif command -v md5sum >/dev/null 2>&1; then
    MD5_CMD="md5sum" # Linux
else
    echo "Error: Could not find 'md5' or 'md5sum' command."
    exit 1
fi

# --- Main Processing ---

# Prepare the check file with a header, overwriting any old one
current_dir=$(basename "$PWD")
printf "filename,count\n" > "$CHECK_CSV"
echo "Created/Reset '$CHECK_CSV'"

# Get the header from the input file
header=$(head -n 1 "$INPUT_CSV")

# Function to process a single file (either the original or a split)
process_file() {
    local file_to_process="$1"
    local output_filename="$2"
    local file_checksum_path="${output_filename%.csv}.checksum"

    # If the file to process is not the same as the target output, move it
    if [ "$file_to_process" != "$output_filename" ]; then
         mv "$file_to_process" "$output_filename"
    fi

    echo "Processing '$output_filename'..."

    # 1. Calculate MD5 for the data file
    echo " -> Generating checksum..."
    if [[ "$MD5_CMD" == "md5sum" ]]; then
        md5sum "$output_filename" | awk '{print $1}' > "$file_checksum_path"
    else
        $MD5_CMD "$output_filename" > "$file_checksum_path"
    fi
    echo "    -> Saved to '$file_checksum_path'"

    # 2. Append line count information to the check file
    line_count=$(( $(wc -l < "$output_filename" | xargs) - 1 ))
    printf "%s/%s,%s\n" "$current_dir" "$output_filename" "$line_count" >> "$CHECK_CSV"
    echo " -> Appended line count ($line_count) to '$CHECK_CSV'"
}


if [ -n "$SPLIT_LINES" ] && [ "$SPLIT_LINES" -gt 0 ]; then
    # --- Splitting Logic ---
    echo "Splitting '$INPUT_CSV' into files of $SPLIT_LINES lines each."
    # Create a temporary file without the header to split
    tail -n +2 "$INPUT_CSV" > body.tmp
    
    # Split the body
    split -l "$SPLIT_LINES" body.tmp "split_part_"
    rm body.tmp # Clean up temp body

    file_index=1
    for part in split_part_*; do
        output_file=$(printf "%s_%03d.csv" "$OUTPUT_BASENAME" "$file_index")
        
        # Add header to the split part
        echo "$header" > "$output_file"
        cat "$part" >> "$output_file"
        rm "$part" # Clean up split part

        process_file "$output_file" "$output_file"
        file_index=$((file_index + 1))
    done
else
    # --- Single File Logic ---
    echo "Processing single file: '$INPUT_CSV'"
    # For consistency, we can treat the single file as one part.
    # The output name will be sofi_journal_001.csv
    output_file=$(printf "%s_001.csv" "$OUTPUT_BASENAME")
    cp "$INPUT_CSV" "$output_file"
    process_file "$output_file" "$output_file"
fi


# 3. Calculate MD5 for the final check file
echo "Generating checksum for '$CHECK_CSV'..."
if [[ "$MD5_CMD" == "md5sum" ]]; then
    $MD5_CMD "$CHECK_CSV" | awk '{print $1}' > "$CHECK_CHECKSUM"
else
    $MD5_CMD "$CHECK_CSV" > "$CHECK_CHECKSUM"
fi
echo " -> Saved to '$CHECK_CHECKSUM'"

echo -e "\nProcessing complete."