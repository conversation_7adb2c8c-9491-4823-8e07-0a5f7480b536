#!/bin/bash

# Description: Generates test data by duplicating entries from a source CSV file.
# Usage: ./generate_data.sh <gaven_times> [output_file] [seed_file]
#
# <gaven_times>: The total number of 2-line groups to generate.
# [output_file]: Optional. The name of the output CSV file. Defaults to sofi_journal_generated.csv.
# [seed_file]:   Optional. The source CSV file. Defaults to seed.csv.

set -euo pipefail

if [[ "$#" -lt 1 || "$#" -gt 3 ]]; then
    echo "Usage: $0 <gaven_times> [output_file] [seed_file]"
    exit 1
fi

gaven_times=$1
output_file="${2:-sofi_journal_generated.csv}"
input_file="${3:-seed.csv}"

if ! [[ "$gaven_times" =~ ^[0-9]+$ ]] || [[ "$gaven_times" -le 0 ]]; then
    echo "Error: <gaven_times> must be a positive integer."
    exit 1
fi

if [ ! -f "$input_file" ]; then
    echo "Error: Input file not found: $input_file"
    exit 1
fi

# Read the header and the two data lines to be used as templates
header=$(head -n 1 "$input_file")
line1=$(sed -n '2p' "$input_file")
line2=$(sed -n '3p' "$input_file")

# Extract the starting PolizaID from the first data line
initial_poliza_id=$(echo "$line1" | awk -F, '{print $5}')

# Open the output file for writing
exec 3> "$output_file"

# Print the header to the output file
echo "$header" >&3

# Generate the data
for (( i=0; i<gaven_times; i++ )); do
    current_poliza_id=$((initial_poliza_id + i))

    # Substitute the PolizaID in both template lines and print to the output file
    echo "$line1" | awk -v poliza_id="$current_poliza_id" -F, 'BEGIN{OFS=","} {$5=poliza_id; print}' >&3
    echo "$line2" | awk -v poliza_id="$current_poliza_id" -F, 'BEGIN{OFS=","} {$5=poliza_id; print}' >&3
done

# Close the file descriptor
exec 3>&-

echo "Successfully generated $gaven_times groups of data in '$output_file'."
