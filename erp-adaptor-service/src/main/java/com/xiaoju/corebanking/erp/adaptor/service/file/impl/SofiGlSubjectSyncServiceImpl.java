package com.xiaoju.corebanking.erp.adaptor.service.file.impl;

import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlSubjectRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlSubjectDO;
import com.xiaoju.corebanking.erp.adaptor.service.common.AbstractDataSyncService;
import com.xiaoju.corebanking.erp.adaptor.service.file.SofiGlSubjectSyncService;
import com.xiaoju.corebanking.erp.adaptor.service.file.enums.SofiGlSubjectEnum;
import com.xiaoju.digitalbank.ddd.po.ExecResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/7 17:09
 */
@Service
@Slf4j
public class SofiGlSubjectSyncServiceImpl extends AbstractDataSyncService<SofiGlSubjectDO> implements SofiGlSubjectSyncService {

    @Resource
    private SofiGlSubjectRepository sofiGlSubjectRepository;

    @Override
    public ExecResult processSofiGlSubjectFile(String filePath, String processDay) {
        return processDataFile(filePath, processDay);
    }

    @Override
    protected String getBusinessName() {
        return "SofiGlSubject";
    }

    @Override
    protected String getDataFileName() {
        return CommonConstant.SOFI_GL_SUBJECT_CSV;
    }

    @Override
    protected String getCheckFileName() {
        return CommonConstant.SOFI_GL_SUBJECT_CHECK_CSV;
    }

    @Override
    protected SofiGlSubjectDO parseRecord(CSVRecord record, int lineNumber, String processDay) {
        try {
            SofiGlSubjectDO sofiGlSubjectDO = new SofiGlSubjectDO();

            for (String headerName : record.getParser().getHeaderNames()) {
                String value = record.get(headerName);
                SofiGlSubjectEnum.setField(sofiGlSubjectDO, headerName, value);
            }

            if (StringUtils.isBlank(sofiGlSubjectDO.getSubjectCode()) ||
                    StringUtils.isBlank(sofiGlSubjectDO.getSubjectSetNo())) {
                log.info("第{}行SubjectCode或SubjectSetNo为空，跳过", lineNumber);
                return null;
            }

            log.debug("解析第{}行数据成功: SubjectCode={}, SubjectSetNo={}, SubjectDesc={}",
                    lineNumber, sofiGlSubjectDO.getSubjectCode(),
                    sofiGlSubjectDO.getSubjectSetNo(), sofiGlSubjectDO.getSubjectDesc());

            return sofiGlSubjectDO;

        } catch (Exception e) {
            log.error("解析第{}行数据失败", lineNumber, e);
            return null;
        }
    }

    @Override
    protected int syncData(List<SofiGlSubjectDO> dataList) {
        return sofiGlSubjectRepository.syncSofiGlSubjectData(dataList);
    }
}
