package com.xiaoju.corebanking.erp.adaptor.service.transaction;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;

public interface SofiShenMaTransactionService {
    void updateShenMaInterfaceAndHeader(SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO, SofiGlShenmaDO sofiGlShenmaDO,ProcessStatusEnum newStatus);
}
