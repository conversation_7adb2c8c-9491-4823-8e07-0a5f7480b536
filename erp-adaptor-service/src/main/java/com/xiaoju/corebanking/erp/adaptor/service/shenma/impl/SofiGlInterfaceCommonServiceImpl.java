package com.xiaoju.corebanking.erp.adaptor.service.shenma.impl;

import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceCommonRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceCommonDO;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlInterfaceCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 类描述
 * @author: didi
 **/
@Slf4j
@Service
public class SofiGlInterfaceCommonServiceImpl implements SofiGlInterfaceCommonService {
    @Autowired
    private SofiGlInterfaceCommonRepository sofiGlInterfaceCommonRepository;
    @Override
    public void insertSelective(SofiGlInterfaceCommonDO sofiGlInterfaceCommonDO) {
        sofiGlInterfaceCommonRepository.insertSelective(sofiGlInterfaceCommonDO);
    }

    @Override
    public void batchInsert(List<SofiGlInterfaceCommonDO> list) {
        sofiGlInterfaceCommonRepository.batchInsert(list);
    }

    @Override
    public List<Long> queryGroupIds(Integer pageNum,Integer pageSize,String processDay) {
        return sofiGlInterfaceCommonRepository.queryGroupIds(pageNum,pageSize,processDay);
    }
    
    @Override
    public List<SofiGlInterfaceCommonDO> queryByReference5AndProcessDay(List<String> externalReferenceList, String processDay) {
        return sofiGlInterfaceCommonRepository.queryByReference5AndProcessDay(externalReferenceList, processDay);
    }
    
    @Override
    public void updateInterfaceCommon(SofiGlInterfaceCommonDO original, SofiGlInterfaceCommonDO update) {
        sofiGlInterfaceCommonRepository.updateInterfaceCommon(original, update);
    }
}
