package com.xiaoju.corebanking.erp.adaptor.service.shenma.impl;

import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.common.errorno.ErpAdaptorErrorNo;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceCommonDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValueExtraPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePO;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlShenmaValidatedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description: 类描述
 * @author: didi
 **/
@Slf4j
@Service
public class SofiGlShenmaValidatedServiceImpl implements SofiGlShenmaValidatedService {

    @Override
    public String validated(SofiGlShenmaDO sofiGlShenmaDO) {
        StringBuilder errMessage = new StringBuilder();
        errMessage.append(ErpAdaptorErrorNo.IMP_ERR_ERROR.getErrorMsg());

        if (StringUtils.isEmpty(sofiGlShenmaDO.getLinkReference())) {
            errMessage.append(" LinkReference is null,");
        }

        if (StringUtils.isEmpty(sofiGlShenmaDO.getSourceBranch())) {
            errMessage.append(" SourceBranch is null,");
        }
        if (StringUtils.isEmpty(sofiGlShenmaDO.getCcy())) {
            errMessage.append(" Ccy is null,");
        }
        if (StringUtils.isEmpty(sofiGlShenmaDO.getGlCode())) {
            errMessage.append(" GlCode is null,");
        }
        if (Objects.isNull(sofiGlShenmaDO.getEnteredDebitAmount())) {
            errMessage.append(" EnteredDebitAmount is null,");
        }
        if (Objects.isNull(sofiGlShenmaDO.getEnteredCreditAmount())) {
            errMessage.append(" EnteredCreditAmount is null,");
        }
        if (StringUtils.isEmpty(sofiGlShenmaDO.getProfitCenter())) {
            errMessage.append(" ProfitCenter is null,");
        }
        if (StringUtils.isEmpty(sofiGlShenmaDO.getClientType())) {
            errMessage.append(" ClientType is null,");
        }
        if (StringUtils.isEmpty(sofiGlShenmaDO.getAmtType())) {
            errMessage.append(" AmtType is null,");
        }
        if (StringUtils.isEmpty(sofiGlShenmaDO.getTranType())) {
            errMessage.append(" TranType is null,");
        }
        if (StringUtils.isEmpty(sofiGlShenmaDO.getEventType())) {
            errMessage.append(" EventType is null,");
        }
        if (StringUtils.isEmpty(sofiGlShenmaDO.getProdType())) {
            errMessage.append(" ProdType is null,");
        }
        if (StringUtils.isEmpty(sofiGlShenmaDO.getPostDate())) {
            errMessage.append(" PostDate is null,");
        }
        if (StringUtils.isEmpty(sofiGlShenmaDO.getIntercompany())) {
            errMessage.append(" Intercompany is null,");
        }
        if (Objects.isNull(sofiGlShenmaDO.getFlatRate())) {
            errMessage.append(" FlatRate is null,");
        }
        if (Objects.isNull(sofiGlShenmaDO.getCustRate())) {
            errMessage.append(" CustRate is null,");
        }
        //todo
//        if (StringUtils.isEmpty(sofiGlShenmaDO.getInlandOffshore())) {
//            errMessage.append(" InlandOffshore is null,");
//        }
        if (StringUtils.isEmpty(sofiGlShenmaDO.getClientNo())) {
            errMessage.append(" ClientNo is null,");
        }
        if (StringUtils.isEmpty(sofiGlShenmaDO.getSeqNo())) {
            errMessage.append(" SeqNo is null,");
        }
        if (StringUtils.isEmpty(sofiGlShenmaDO.getSystemId())) {
            errMessage.append(" SystemId is null,");
        }
        if (StringUtils.isEmpty(sofiGlShenmaDO.getCompany())) {
            errMessage.append(" Company is null,");
        }
        if (StringUtils.isEmpty(sofiGlShenmaDO.getGroupClient())) {
            errMessage.append(" GroupClient is null,");
        }
        if (StringUtils.isEmpty(sofiGlShenmaDO.getVoucherGroup())) {
            errMessage.append(" VoucherGroup is null,");
        }

        return errMessage.toString();
    }

    @Override
    public String mappedResult(SofiGlShenmaDO sofiGlShenmaDO,SofiGlInterfaceCommonDO sofiGlInterfaceCommonDO, Map<String, Map<String, CuxTwoTabValueExtraPO>> cache) {
        StringBuilder errMessage = new StringBuilder();
        errMessage.append(ErpAdaptorErrorNo.IMP_ERR_ERROR.getErrorMsg());
        // mapping 转换
        commonDataMapping(sofiGlShenmaDO,sofiGlInterfaceCommonDO,cache);
        return errMessage.toString();
    }
    private void companyMapping(SofiGlShenmaDO sofiGlShenmaDO, SofiGlInterfaceCommonDO sofiGlInterfaceCommonDO,Map<String, Map<String, CuxTwoTabValueExtraPO>> cache) {
        Map<String, CuxTwoTabValueExtraPO> companyMap = cache.get(CommonConstant.COMPANY_LEDGER_MAPPINGS);
        CuxTwoTabValueExtraPO mapping = companyMap.get(String.valueOf(sofiGlShenmaDO.getSourceBranch()));
        if (StringUtils.isNotEmpty(mapping.getValue3()) && StringUtils.isNotEmpty(mapping.getValue5())) {
            sofiGlInterfaceCommonDO.setLedgerId(Long.valueOf(mapping.getValue4()));
            sofiGlInterfaceCommonDO.setLedgerName(mapping.getValue3());
            sofiGlInterfaceCommonDO.setSegment1(mapping.getValue5());
        }
    }


    private void currencyMapping(SofiGlShenmaDO sofiGlShenmaDO, SofiGlInterfaceCommonDO sofiGlInterfaceCommonDO,Map<String, Map<String, CuxTwoTabValueExtraPO>> cache) {
        Map<String, CuxTwoTabValueExtraPO> currencyMappings = cache.get(CommonConstant.CURRENCY_MAPPINGS);
        CuxTwoTabValueExtraPO mapping = currencyMappings.get(String.valueOf(sofiGlShenmaDO.getCcy()));
        if (StringUtils.isNotEmpty(mapping.getValue3())) {
            sofiGlInterfaceCommonDO.setCurrencyCode(mapping.getValue3());
        }
    }

    private void productMapping(SofiGlShenmaDO sofiGlShenmaDO, SofiGlInterfaceCommonDO sofiGlInterfaceCommonDO,Map<String, Map<String, CuxTwoTabValueExtraPO>> cache) {
        Map<String, CuxTwoTabValueExtraPO> productMappings = cache.get(CommonConstant.PRODUCT_MAPPINGS);
        CuxTwoTabValueExtraPO mapping = productMappings.get(String.valueOf(sofiGlShenmaDO.getProdType()));
        if (StringUtils.isNotEmpty(mapping.getValue3())) {
            sofiGlInterfaceCommonDO.setSegment6(mapping.getValue3());
        }
    }
    private void profitCenterMappings(SofiGlShenmaDO sofiGlShenmaDO, SofiGlInterfaceCommonDO sofiGlInterfaceCommonDO,Map<String, Map<String, CuxTwoTabValueExtraPO>> cache) {
        Map<String, CuxTwoTabValueExtraPO> productMappings = cache.get(CommonConstant.PROFIT_CENTER_MAPPINGS);
        CuxTwoTabValueExtraPO mapping = productMappings.get(String.valueOf(sofiGlShenmaDO.getProfitCenter()));
        if (StringUtils.isNotEmpty(mapping.getValue3())) {
            sofiGlInterfaceCommonDO.setSegment5(mapping.getValue3());
        }
    }
    private void glCodeMappings(SofiGlShenmaDO sofiGlShenmaDO, SofiGlInterfaceCommonDO sofiGlInterfaceCommonDO,Map<String, Map<String, CuxTwoTabValueExtraPO>> cache) {
        Map<String, CuxTwoTabValueExtraPO>  glCodeMappings= cache.get(CommonConstant.GL_CODE_MAPPINGS);
        CuxTwoTabValueExtraPO mapping = glCodeMappings.get(sofiGlShenmaDO.getGlCode());
        if (StringUtils.isNotEmpty(mapping.getValue3())) {
            sofiGlInterfaceCommonDO.setSegment3(mapping.getValue3());
        }
    }


    private void commonDataMapping(SofiGlShenmaDO sofiGlShenmaDO,SofiGlInterfaceCommonDO sofiGlInterfaceCommonDO, Map<String, Map<String, CuxTwoTabValueExtraPO>> cache) {
        companyMapping(sofiGlShenmaDO,sofiGlInterfaceCommonDO,cache);
        currencyMapping(sofiGlShenmaDO,sofiGlInterfaceCommonDO,cache);
        productMapping(sofiGlShenmaDO,sofiGlInterfaceCommonDO,cache);
        profitCenterMappings(sofiGlShenmaDO,sofiGlInterfaceCommonDO,cache);
        glCodeMappings(sofiGlShenmaDO,sofiGlInterfaceCommonDO,cache);
    }

}
