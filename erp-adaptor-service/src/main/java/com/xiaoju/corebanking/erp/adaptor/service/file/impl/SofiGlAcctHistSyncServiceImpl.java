package com.xiaoju.corebanking.erp.adaptor.service.file.impl;

import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlAcctHistRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlAcctHistDO;
import com.xiaoju.corebanking.erp.adaptor.service.common.AbstractDataSyncService;
import com.xiaoju.corebanking.erp.adaptor.service.file.enums.SofiGlAcctEnum;
import com.xiaoju.corebanking.erp.adaptor.service.file.SofiGlAcctHistSyncService;
import com.xiaoju.digitalbank.ddd.po.ExecResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * SofiGlAcctHist文件数据入库
 * <AUTHOR>
 * @date 2025/7/16
 */
@Service
@Slf4j
public class SofiGlAcctHistSyncServiceImpl extends AbstractDataSyncService<SofiGlAcctHistDO> implements SofiGlAcctHistSyncService {

    @Resource
    private SofiGlAcctHistRepository sofiGlAcctHistRepository;

    @Override
    public ExecResult processSofiGlAcctHistFile(String filePath, String processDay) {
        return processDataFile(filePath, processDay);
    }

    @Override
    protected String getBusinessName() {
        return "SofiGlAcctHist";
    }

    @Override
    protected String getDataFileName() {
        return CommonConstant.SOFI_GL_ACCT_HIST_CSV;
    }

    @Override
    protected String getCheckFileName() {
        return CommonConstant.SOFI_GL_ACCT_HIST_CHECK_CSV;
    }

    @Override
    protected SofiGlAcctHistDO parseRecord(CSVRecord record, int lineNumber, String processDay) {
        try {
            SofiGlAcctHistDO sofiGlAcctHistDO = new SofiGlAcctHistDO();
            sofiGlAcctHistDO.setProcessDay(processDay);

            for (String headerName : record.getParser().getHeaderNames()) {
                String value = record.get(headerName);
                SofiGlAcctEnum.setField(sofiGlAcctHistDO, headerName, value);
            }

            log.debug("解析第{}行数据成功: InternalKey={}, Branch={}, GlCode={}",
                    lineNumber, sofiGlAcctHistDO.getInternalKey(),
                    sofiGlAcctHistDO.getBranch(), sofiGlAcctHistDO.getGlCode());

            return sofiGlAcctHistDO;

        } catch (Exception e) {
            log.error("解析第{}行数据失败", lineNumber, e);
            return null;
        }
    }

    @Override
    protected int syncData(List<SofiGlAcctHistDO> dataList) {
        return sofiGlAcctHistRepository.syncSofiGlAcctHistData(dataList);
    }
} 