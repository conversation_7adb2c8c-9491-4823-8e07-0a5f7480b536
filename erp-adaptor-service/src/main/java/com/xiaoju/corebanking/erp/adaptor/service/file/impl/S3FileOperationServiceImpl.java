package com.xiaoju.corebanking.erp.adaptor.service.file.impl;

import com.xiaoju.corebanking.erp.adaptor.service.common.MinioFileOperator;
import com.xiaoju.corebanking.erp.adaptor.service.file.S3FileOperationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 文件操作
 * @date 2025/5/16 16:41
 */
@Service
@Slf4j
public class S3FileOperationServiceImpl implements S3FileOperationService {

    @Resource
    private MinioFileOperator minioFileOperator;

    @Override
    public boolean checkFileExists(String filePath) {
        try {
            return minioFileOperator.checkObjectExists(filePath);
        } catch (Throwable e) {
            log.warn("MinioFileOperator检查文件存在性失败，尝试本地文件: {}", filePath, e);
            return checkLocalFileExists(filePath);
        }
    }

    @Override
    public byte[] downloadFileAsBytes(String filePath) {
        if (filePath == null) {
            log.error("下载文件失败: 文件路径为空");
            return null;
        }

        try (InputStream inputStream = minioFileOperator.download(filePath)) {
            if (inputStream == null) {
                log.error("无法从MinioFileOperator下载文件: {}", filePath);
                return downloadLocalFileAsBytes(filePath);
            }
            return inputStreamToByteArray(inputStream);
        } catch (Throwable e) {
            log.warn("MinioFileOperator下载文件失败，尝试本地文件: {}", filePath, e);
            return downloadLocalFileAsBytes(filePath);
        }
    }

    private byte[] inputStreamToByteArray(InputStream inputStream) throws IOException {
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, bytesRead);
            }
            return byteArrayOutputStream.toByteArray();
        }
    }

    @Override
    public List<String> readFileLines(String filePath) {
        return readFileLines(filePath, null);
    }

    @Override
    public List<String> readFileLines(String filePath, Integer maxLines) {
        try (InputStream inputStream = minioFileOperator.download(filePath);
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {

            List<String> lines = new ArrayList<>();
            String line;
            int count = 0;
            while ((line = reader.readLine()) != null) {
                lines.add(line);
                count++;
                if (maxLines != null && maxLines > 0 && count >= maxLines) {
                    break;
                }
            }
            return lines;
        } catch (Throwable e) {
            log.warn("MinioFileOperator读取文件行失败，尝试本地文件: {}", filePath, e);
            return readLocalFileLines(filePath, maxLines);
        }
    }

    @Override
    public String readFileFirstLine(String filePath) {
        List<String> lines = readFileLines(filePath, 1);
        if (CollectionUtils.isEmpty(lines)) {
            return null;
        }
        String line = lines.get(0);
        return line != null ? line.trim() : null;
    }

    @Override
    public boolean verifyMd5(String filePath, String expectedMd5) {
        if (filePath == null || expectedMd5 == null) {
            log.error("MD5校验失败: 文件不存在或期望值为空");
            return false;
        }

        try (InputStream is = minioFileOperator.download(filePath)) {
            String actualMd5 = DigestUtils.md5Hex(is);
            return expectedMd5.trim().equalsIgnoreCase(actualMd5.trim());
        } catch (Throwable e) {
            log.warn("MinioFileOperator计算MD5失败，尝试本地文件: {}", filePath, e);
            return verifyLocalMd5(filePath, expectedMd5);
        }
    }

    @Override
    public Map<String, Integer> readSubfilesWithCount(String checkCsvKey) {
        Map<String, Integer> subFilesWithCount = new HashMap<>();

        try {
            List<String> lines = readFileLines(checkCsvKey);
            if (CollectionUtils.isEmpty(lines)) {
                return subFilesWithCount;
            }

            // 读取所有子文件
            for (int i = 1; i < lines.size(); i++) {
                String line = lines.get(i);
                String[] parts = line.split(",");
                if (parts.length >= 2) {
                    String filename = parts[0].trim();
                    String countStr = parts[1].trim();

                    if (!StringUtils.isEmpty(filename)) {
                        try {
                            int count = Integer.parseInt(countStr.trim());
                            subFilesWithCount.put(filename, count);
                        } catch (NumberFormatException e) {
                            log.error("无法解析文件{}的记录数: {}", filename, countStr, e);
                        }
                    }
                } else {
                    log.error("CSV行格式不正确: {}", line);
                }
            }
        } catch (Throwable e) {
            log.error("读取check.csv文件内容失败: {}", checkCsvKey, e);
        }

        return subFilesWithCount;
    }

    @Override
    public Long getFileSize(String filePath) {
        if (filePath == null) {
            return 0L;
        }

        byte[] fileBytes = downloadFileAsBytes(filePath);
        return fileBytes != null ? (long) fileBytes.length : 0L;
    }

    @Override
    public InputStream download(String filePath) {
        try {
            InputStream inputStream = minioFileOperator.download(filePath);
            if (inputStream != null) {
                return inputStream;
            }
        } catch (Throwable e) {
            log.warn("MinioFileOperator下载失败，尝试本地文件: {}", filePath, e);
        }
        return downloadLocalFile(filePath);
    }

    public boolean checkLocalFileExists(String filePath) {
        try {
            String localPath = buildLocalPath(filePath);
            return Files.exists(Paths.get(localPath));
        } catch (Throwable e) {
            log.error("检查本地文件存在性失败: {}", filePath, e);
            return false;
        }
    }

    public byte[] downloadLocalFileAsBytes(String filePath) {
        try {
            String localPath = buildLocalPath(filePath);
            if (!Files.exists(Paths.get(localPath))) {
                log.error("本地文件不存在: {}", localPath);
                return null;
            }

            return Files.readAllBytes(Paths.get(localPath));
        } catch (Throwable e) {
            log.error("从本地文件读取字节数组失败: {}", filePath, e);
            return null;
        }
    }

    private List<String> readLocalFileLines(String filePath, Integer maxLines) {
        List<String> lines = new ArrayList<>();
        try {
            String localPath = buildLocalPath(filePath);
            if (!Files.exists(Paths.get(localPath))) {
                log.error("本地文件不存在: {}", localPath);
                return lines;
            }

            try (BufferedReader reader = Files.newBufferedReader(Paths.get(localPath), StandardCharsets.UTF_8)) {
                String line;
                int count = 0;
                while ((line = reader.readLine()) != null) {
                    lines.add(line);
                    count++;
                    if (maxLines != null && maxLines > 0 && count >= maxLines) {
                        break;
                    }
                }
            }
        } catch (Throwable e) {
            log.error("读取本地文件行失败: {}", filePath, e);
        }
        return lines;
    }

    private boolean verifyLocalMd5(String filePath, String expectedMd5) {
        try {
            String localPath = buildLocalPath(filePath);
            if (!Files.exists(Paths.get(localPath))) {
                log.error("本地文件不存在: {}", localPath);
                return false;
            }

            try (InputStream is = Files.newInputStream(Paths.get(localPath))) {
                String actualMd5 = DigestUtils.md5Hex(is);
                return expectedMd5.trim().equalsIgnoreCase(actualMd5.trim());
            }
        } catch (Throwable e) {
            log.error("计算本地文件MD5失败: {}", filePath, e);
            return false;
        }
    }

    private InputStream downloadLocalFile(String filePath) {
        try {
            String localPath = buildLocalPath(filePath);
            if (!Files.exists(Paths.get(localPath))) {
                log.error("本地文件不存在: {}", localPath);
                return null;
            }

            return Files.newInputStream(Paths.get(localPath));
        } catch (Throwable e) {
            log.error("从本地文件创建输入流失败: {}", filePath, e);
            return null;
        }
    }

    public String buildLocalPath(String filePath) {
        return Paths.get(filePath).toString();
    }
}
