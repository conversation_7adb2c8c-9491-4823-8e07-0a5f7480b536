package com.xiaoju.corebanking.erp.adaptor.service.common;

import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.enums.SourceSysEnum;
import com.xiaoju.corebanking.erp.adaptor.common.utils.ErpUtil;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlFileControlRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.ParseStatistics;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlFileControlDO;
import com.xiaoju.corebanking.erp.adaptor.service.file.S3FileOperationService;
import com.xiaoju.digitalbank.ddd.po.ExecResult;
import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/16
 */
@Slf4j
public abstract class AbstractDataSyncService<T> {

    @Resource
    protected S3FileOperationService s3FileOperationService;

    @Resource
    protected ErpUtil erpUtil;

    @Resource
    protected SofiGlFileControlRepository sofiGlFileControlRepository;

    public ExecResult processDataFile(String filePath, String processDay) {
        try {
            String finalProcessDay = parseProcessDay(processDay);
            String fullPath = erpUtil.buildFullPath(filePath, finalProcessDay);
            log.info("开始处理{}文件，路径: {}, 处理日期: {}", getBusinessName(), fullPath, finalProcessDay);

            // 1. 文件校验
            ExecResult validationResult = validateFiles(fullPath);
            if (!validationResult.isSuccess()) {
                createControlStatus(0, finalProcessDay, false, "文件校验失败: " + validationResult.getDebugMessage());
                return validationResult;
            }

            // 2. 处理数据文件
            ExecResult processResult = processBusinessDataFile(fullPath, finalProcessDay);
            if (!processResult.isSuccess()) {
                createControlStatus(0, finalProcessDay, false, "文件处理失败: " + processResult.getDebugMessage());
                return processResult;
            }

            log.info("{}文件处理成功: {}", getBusinessName(), fullPath);
            return ExecResult.success();

        } catch (Exception e) {
            log.error("处理{}文件时发生异常，文件路径: {}, 处理日期: {}", getBusinessName(), filePath, processDay, e);
            String finalProcessDay = parseProcessDay(processDay);
            createControlStatus(0, finalProcessDay, false, "处理" + getBusinessName() + "文件时发生异常: " + e.getMessage());
            return ExecResult.error(CommonErrorNo.FILE_ERROR, "处理" + getBusinessName() + "文件时发生异常: " + e.getMessage());
        }
    }

    /**
     * 处理业务数据文件
     */
    private ExecResult processBusinessDataFile(String filePath, String processDay) {
        try {
            String dataFilePath = filePath + getDataFileName();
            log.info("开始处理{}文件: {}", getBusinessName(), dataFilePath);

            // 解析文件数据
            List<T> dataList = parseDataFile(dataFilePath, processDay);

            if (dataList.isEmpty()) {
                log.warn("解析到的{}数据为空，无需同步", getBusinessName());
                return ExecResult.success();
            }

            // 数据同步
            int syncResult = syncData(dataList);
            log.info("{}数据同步完成，共处理{}条记录，同步结果: {}", getBusinessName(), dataList.size(), syncResult);

            createControlStatus(dataList.size(), processDay, true, getBusinessName() + "文件处理成功，共处理" + dataList.size() + "条记录");

            return ExecResult.success();

        } catch (IOException e) {
            log.error("处理{}文件时发生IO异常", getBusinessName(), e);
            return ExecResult.error(CommonErrorNo.FILE_ERROR, "文件读取异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("处理{}文件时发生未知异常", getBusinessName(), e);
            return ExecResult.error(CommonErrorNo.FILE_ERROR, "数据处理异常: " + e.getMessage());
        }
    }

    /**
     * 解析数据文件
     */
    private List<T> parseDataFile(String filePath, String processDay) throws IOException {
        log.info("开始解析{}文件: {}", getBusinessName(), filePath);

        try (InputStream inputStream = s3FileOperationService.download(filePath)) {
            List<T> result = new ArrayList<>();

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
                CSVFormat csvFormat = CSVFormat.DEFAULT
                        .withFirstRecordAsHeader()
                        .withIgnoreHeaderCase()
                        .withTrim();

                try (CSVParser csvParser = new CSVParser(reader, csvFormat)) {
                    ParseStatistics statistics = new ParseStatistics();

                    for (CSVRecord record : csvParser) {
                        statistics.incrementTotalLines();

                        T dataObject = parseRecord(record, statistics.getTotalLines(), processDay);
                        if (dataObject != null) {
                            result.add(dataObject);
                            statistics.incrementValidLines();
                        } else {
                            statistics.incrementSkippedLines();
                        }
                    }

                    statistics.logSummary();
                }
            }

            log.info("解析完成，共获得{}条{}记录", result.size(), getBusinessName());
            return result;
        }
    }

    /**
     * 验证文件有效性
     */
    private ExecResult validateFiles(String filePath) {
        try {
            String checkFileKey = filePath + getCheckFileName();
            String sourceFileKey = filePath + getDataFileName();

            log.info("开始检查{}文件有效性，check文件: {}", getBusinessName(), checkFileKey);

            // 检查文件存在性
            if (!checkFileExists(checkFileKey)) {
                return ExecResult.error(CommonErrorNo.FILE_ERROR, getBusinessName() + " check文件不存在");
            }

            if (!checkFileExists(sourceFileKey)) {
                return ExecResult.error(CommonErrorNo.FILE_ERROR, getBusinessName() + "源文件不存在");
            }

            // MD5校验
            ExecResult md5Result = validateFileMd5(checkFileKey, sourceFileKey);
            if (!md5Result.isSuccess()) {
                return md5Result;
            }

            log.info("{}文件校验通过: 源文件={}", getBusinessName(), sourceFileKey);
            return ExecResult.success();

        } catch (IOException e) {
            log.error("检查{}文件有效性时发生IO异常，文件路径: {}", getBusinessName(), filePath, e);
            return ExecResult.error(CommonErrorNo.FILE_ERROR, "文件访问异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("检查{}文件有效性时发生未知异常，文件路径: {}", getBusinessName(), filePath, e);
            return ExecResult.error(CommonErrorNo.FILE_ERROR, "文件校验异常: " + e.getMessage());
        }
    }

    private String parseProcessDay(String processDay) {
        if (StringUtils.isEmpty(processDay)) {
            return erpUtil.getDateDirFromInput("");
        }
        return processDay.trim();
    }

    private boolean checkFileExists(String fileKey) throws IOException {
        boolean exists = s3FileOperationService.checkFileExists(fileKey);
        if (!exists) {
            log.error("{} 不存在: {}", getBusinessName(),fileKey);
        }
        return exists;
    }

    private ExecResult validateFileMd5(String checkFileKey, String sourceFileKey) throws IOException {
        String expectedMd5 = s3FileOperationService.readFileFirstLine(checkFileKey);
        if (StringUtils.isBlank(expectedMd5)) {
            log.error("无法读取{} check文件中的MD5值: {}", getBusinessName(), checkFileKey);
            return ExecResult.error(CommonErrorNo.FILE_ERROR, "check文件MD5值为空");
        }

        String trimmedMd5 = expectedMd5.trim();
        boolean isValid = s3FileOperationService.verifyMd5(sourceFileKey, trimmedMd5);
        if (!isValid) {
            log.error("{}源文件MD5校验失败: 源文件={}, 预期MD5={}", getBusinessName(), sourceFileKey, trimmedMd5);
            return ExecResult.error(CommonErrorNo.FILE_ERROR, "文件MD5校验失败");
        }

        return ExecResult.success();
    }


    /**
     * 获取业务名称
     */
    protected abstract String getBusinessName();
    
    /**
     * 获取数据文件名
     */
    protected abstract String getDataFileName();
    
    /**
     * 获取校验文件名
     */
    protected abstract String getCheckFileName();
    
    /**
     * 解析单条记录
     */
    protected abstract T parseRecord(CSVRecord record, int lineNumber, String processDay);
    
    /**
     * 同步数据到数据库
     */
    protected abstract int syncData(List<T> dataList);


    protected void createControlStatus(int actualCount, String processDay, boolean success, String message) {
        try {
            SofiGlFileControlDO controlDO = new SofiGlFileControlDO();
            controlDO.setFileName(getDataFileName());
            controlDO.setProcessDay(processDay);
            controlDO.setSystemCode(SourceSysEnum.SHEN_MA.getCode());
            controlDO.setFileCount((long) actualCount);
            controlDO.setObjectVersionNumber(1);
            controlDO.setCreatedBy(CommonConstant.SYSTEM_USER);
            controlDO.setLastModifiedBy(CommonConstant.SYSTEM_USER);
            controlDO.setCreationDate(new Date());
            controlDO.setLastModifyDate(new Date());
            
            if (success) {
                controlDO.setProcessStatus(ProcessStatusEnum.SUCCESS.getCode());
            } else {
                controlDO.setProcessStatus(ProcessStatusEnum.FAILED.getCode());
            }
            controlDO.setProcessMessage(message);

            sofiGlFileControlRepository.insertSelective(controlDO);
            log.info("{}文件状态记录{}: {}", getDataFileName(), success ? "成功" : "失败", message);
        } catch (Exception e) {
            log.error("记录{}文件控制状态失败", getDataFileName(), e);
        }
    }
} 