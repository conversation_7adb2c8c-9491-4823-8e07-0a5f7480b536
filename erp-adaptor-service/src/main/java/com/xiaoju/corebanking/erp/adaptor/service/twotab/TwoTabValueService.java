package com.xiaoju.corebanking.erp.adaptor.service.twotab;

import com.xiaoju.corebanking.erp.adaptor.repository.TwoTabValueRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.CuxTwoTabValueDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValueExtraPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class TwoTabValueService {

    @Resource
    TwoTabValueRepository twoTabValueRepository;

    public CuxTwoTabValuePO insertValue(CuxTwoTabValueDO value){
        return twoTabValueRepository.insertValue(value);
    }

    public int syncValues(List<CuxTwoTabValueDO> list){
        return twoTabValueRepository.insertOrUpdate(list);
    }

    public List<CuxTwoTabValuePO> selectByFormCode(String fromCode){
        return twoTabValueRepository.selectByFormCode(fromCode);
    }
    public List<CuxTwoTabValuePO> selectSimpleByFormCode(String fromCode){
        return twoTabValueRepository.selectSimpleByFormCode(fromCode);
    }
    public List<CuxTwoTabValueExtraPO> selectAllByFormCode(){
        return twoTabValueRepository.selectAllByFormCode();
    }

}
