package com.xiaoju.corebanking.erp.adaptor.service.transaction;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;

public interface SofiGlInterfaceTransactionService {
    void updateInterfaceAndInterfaceHeaderByPolizaId(SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO, SofiGlInterfaceDO sofiGlInterfaceDO,ProcessStatusEnum newStatus);
    void updateInterfaceAndInterfaceHeader(SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO, SofiGlInterfaceDO sofiGlInterfaceDO,ProcessStatusEnum newStatus);


}
