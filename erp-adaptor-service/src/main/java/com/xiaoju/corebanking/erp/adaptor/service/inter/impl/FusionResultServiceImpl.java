package com.xiaoju.corebanking.erp.adaptor.service.inter.impl;

import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.common.domain.ErpAdaptorMessageRequest;
import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.enums.SourceSysEnum;
import com.xiaoju.corebanking.erp.adaptor.common.utils.ErpUtil;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceCommonDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;
import com.xiaoju.corebanking.erp.adaptor.service.header.SofiGlInterfaceHeaderService;
import com.xiaoju.corebanking.erp.adaptor.service.inter.FusionResultService;
import com.xiaoju.corebanking.erp.adaptor.service.message.MessageGateway;
import com.xiaoju.corebanking.erp.adaptor.service.polling.StatusPollingService;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlInterfaceCommonService;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlShenMaService;
import com.xiaoju.godson.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.w3c.dom.Document;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.annotation.Resource;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/23 19:28
 */
@Slf4j
@Service
public class FusionResultServiceImpl implements FusionResultService {
    @Resource
    private SofiGlInterfaceHeaderService sofiGlInterfaceHeaderService;

    @Resource
    private SofiGlInterfaceRepository sofiGlInterfaceRepository;

    @Resource
    private SofiGlInterfaceCommonService sofiGlInterfaceCommonService;

    @Resource
    private SofiGlShenMaService sofiGlShenMaService;

    @Resource
    private RestTemplate restTemplate;

    @Value("${fusion.soap.url}")
    private String fusionSoapUrl;

    @Value("${fusion.jwt.username}")
    private String fusionUsername;

    @Value("${fusion.jwt.password}")
    private String fusionPassword;

    @Value("${fusion.soap.report-path}")
    private String reportPath;

    @Resource
    private StatusPollingService statusPollingService;

    @Resource
    private ErpUtil erpUtil;

    @Resource
    private MessageGateway messageGateway;

    public void processSingleFusionResult(List<SofiGlInterfaceDO> interfaceDataList, List<SofiGlInterfaceHeaderDO> headerList) {
        try {
            // 按GroupId分组
            Map<Long, List<SofiGlInterfaceHeaderDO>> groupedHeaders = headerList.stream()
                    .collect(Collectors.groupingBy(SofiGlInterfaceHeaderDO::getGroupId));

            // 调用状态查询接口
            log.info("FusionResultJob: 调用状态查询接口, LoadRequestId={}", interfaceDataList.get(0).getLoadRequestId());
            boolean statusCheckSuccess = statusPollingService.checkRequestStatus(interfaceDataList.get(0).getLoadRequestId());

            if (!statusCheckSuccess) {
                log.info("FusionResultJob: 状态查询失败，不执行后续处理");
                return;
            }

            for (Map.Entry<Long, List<SofiGlInterfaceHeaderDO>> entry : groupedHeaders.entrySet()) {
                Long groupId = entry.getKey();
                List<SofiGlInterfaceHeaderDO> headersByGroup = entry.getValue();

                if (headersByGroup.isEmpty()) {
                    continue;
                }

                SofiGlInterfaceHeaderDO headerDO = headersByGroup.get(0);
                String externalReference = headerDO.getExternalReference();
                String processDay = headerDO.getProcessDay();

                log.info("处理GroupId={}, PolizaId={}, ProcessDay={}", groupId, externalReference, processDay);

                // 1. 调用SOAP接口获取reportBytes
                String soapRequest = buildSoapRequest(groupId);
                HttpHeaders httpHeaders = new HttpHeaders();
                httpHeaders.setContentType(MediaType.TEXT_XML);
                httpHeaders.add("SOAPAction", "#POST");
                HttpEntity<String> request = new HttpEntity<>(soapRequest, httpHeaders);

                log.info("FusionResultService: 调用SOAP接口, URL={}", fusionSoapUrl);
                String response = restTemplate.postForObject(fusionSoapUrl, request, String.class);
                log.debug("FusionResultService: SOAP响应={}", response);

                // 2. 解析reportBytes
                String reportBytes = extractReportBytesFromXml(response);
                if (reportBytes == null) {
                    throw new RuntimeException("获取reportBytes失败");
                }

                String decodedContent = new String(Base64.getDecoder().decode(reportBytes), StandardCharsets.UTF_8);
                log.debug("FusionResultService: 解码后内容={}", decodedContent);

                // 3. 解析XML内容，提取字段
                String fusionId = parseJeHeaderId(decodedContent);
                String status = parseStatus(decodedContent);
                log.info("FusionResultService: 解析结果 fusionId={}, status={}", fusionId, status);

                // 4. 根据status处理数据
                boolean isSuccess = "S".equals(status);
                boolean isFailed = "E".equals(status);

                if (isSuccess || isFailed) {
                    if (isFailed) {
                        log.error("FusionResultService: 处理失败，需要发送报警邮件, groupId={}, polizaId={}", groupId, externalReference);
                        messageGateway.sendEmail(new ErpAdaptorMessageRequest() {{
                            setAttachments(Collections.singletonList(CommonConstant.AMAZON_FILE));
                        }});
                    }

                    for (SofiGlInterfaceHeaderDO header : headersByGroup) {
                        SofiGlInterfaceHeaderDO updateHeader = new SofiGlInterfaceHeaderDO();
                        updateHeader.setProcessStatus(isSuccess ? ProcessStatusEnum.IMPORTED : ProcessStatusEnum.IMPORT_FAILED);
                        updateHeader.setProcessMessage(decodedContent.substring(0, Math.min(decodedContent.length(), 199)));
                        sofiGlInterfaceHeaderService.updateInterfaceHeader(header, updateHeader, header.getSystemCode());
                    }

                    // 更新interface表
                    List<SofiGlInterfaceDO> groupInterfaceList = interfaceDataList.stream()
                            .filter(item -> groupId.equals(item.getGroupId()))
                            .collect(Collectors.toList());

                    for (SofiGlInterfaceDO interfaceDO : groupInterfaceList) {
                        SofiGlInterfaceDO updateInterface = new SofiGlInterfaceDO();
                        updateInterface.setProcessStatus(isSuccess ? ProcessStatusEnum.IMPORTED : ProcessStatusEnum.IMPORT_FAILED);
                        updateInterface.setProcessMessage(decodedContent.substring(0, Math.min(decodedContent.length(), 199)));
                        if (isSuccess && fusionId != null) {
                            updateInterface.setJeHeaderId(Long.valueOf(fusionId));
                        }
                        sofiGlInterfaceRepository.updateInterface(interfaceDO, updateInterface);
                    }

                    log.info("FusionResultService: 处理groupId={}完成，fusionId={}, status={}", groupId, fusionId, status);
                } else {
                    log.info("FusionResultService: 状态{}不需要处理，跳过更新", status);
                }
            }
        } catch (Exception e) {
            log.error("FusionResultService: 处理结果异常", e);
            throw e;
        }
    }

    public void processSingleFusionResultForShenma(List<SofiGlInterfaceCommonDO> interfaceDataList, List<SofiGlInterfaceHeaderDO> headerList) {
        try {
            // 按GroupId分组
            Map<Long, List<SofiGlInterfaceHeaderDO>> groupedHeaders = headerList.stream()
                    .collect(Collectors.groupingBy(SofiGlInterfaceHeaderDO::getGroupId));

            // 调用状态查询接口
            log.info("FusionResultJob(Shenma): 调用状态查询接口, LoadRequestId={}", interfaceDataList.get(0).getLoadRequestId());
            boolean statusCheckSuccess = statusPollingService.checkRequestStatus(interfaceDataList.get(0).getLoadRequestId());

            if (!statusCheckSuccess) {
                log.info("FusionResultJob(Shenma): 状态查询失败，不执行后续处理");
                return;
            }

            for (Map.Entry<Long, List<SofiGlInterfaceHeaderDO>> entry : groupedHeaders.entrySet()) {
                Long groupId = entry.getKey();
                List<SofiGlInterfaceHeaderDO> headersByGroup = entry.getValue();

                if (headersByGroup.isEmpty()) {
                    continue;
                }

                SofiGlInterfaceHeaderDO headerDO = headersByGroup.get(0);
                String externalReference = headerDO.getExternalReference();
                String processDay = headerDO.getProcessDay();

                log.info("处理Shenma GroupId={}, PolizaId={}, ProcessDay={}", groupId, externalReference, processDay);

                // 1. 调用SOAP接口获取reportBytes
                String soapRequest = buildSoapRequest(groupId);
                HttpHeaders httpHeaders = new HttpHeaders();
                httpHeaders.setContentType(MediaType.TEXT_XML);
                httpHeaders.add("SOAPAction", "#POST");
                HttpEntity<String> request = new HttpEntity<>(soapRequest, httpHeaders);

                log.info("FusionResultService(Shenma): 调用SOAP接口, URL={}", fusionSoapUrl);
                String response = restTemplate.postForObject(fusionSoapUrl, request, String.class);
                log.debug("FusionResultService(Shenma): SOAP响应={}", response);

                // 2. 解析reportBytes
                String reportBytes = extractReportBytesFromXml(response);
                if (reportBytes == null) {
                    throw new RuntimeException("获取reportBytes失败");
                }

                String decodedContent = new String(Base64.getDecoder().decode(reportBytes), StandardCharsets.UTF_8);
                log.debug("FusionResultService(Shenma): 解码后内容={}", decodedContent);

                // 3. 解析XML内容，提取字段
                String fusionId = parseJeHeaderId(decodedContent);
                String status = parseStatus(decodedContent);
                log.info("FusionResultService(Shenma): 解析结果 fusionId={}, status={}", fusionId, status);

                // 4. 根据status处理数据
                boolean isSuccess = "S".equals(status);
                boolean isFailed = "E".equals(status);

                if (isSuccess || isFailed) {
                    if (isFailed) {
                        log.error("FusionResultService(Shenma): 处理失败，需要发送报警邮件, groupId={}, polizaId={}", groupId, externalReference);
                        messageGateway.sendEmail(new ErpAdaptorMessageRequest() {{
                            setAttachments(Collections.singletonList(CommonConstant.AMAZON_FILE));
                        }});
                    }

                    for (SofiGlInterfaceHeaderDO header : headersByGroup) {
                        SofiGlInterfaceHeaderDO updateHeader = new SofiGlInterfaceHeaderDO();
                        updateHeader.setProcessStatus(isSuccess ? ProcessStatusEnum.IMPORTED : ProcessStatusEnum.IMPORT_FAILED);
                        updateHeader.setProcessMessage(decodedContent.substring(0, Math.min(decodedContent.length(), 199)));
                        sofiGlInterfaceHeaderService.updateInterfaceHeader(header, updateHeader, header.getSystemCode());
                    }

                    // 更新Common表
                    List<SofiGlInterfaceCommonDO> groupInterfaceList = interfaceDataList.stream()
                            .filter(item -> groupId.equals(item.getGroupId()))
                            .collect(Collectors.toList());

                    for (SofiGlInterfaceCommonDO interfaceDO : groupInterfaceList) {
                        SofiGlInterfaceCommonDO updateInterface = new SofiGlInterfaceCommonDO();
                        updateInterface.setProcessStatus(isSuccess ? ProcessStatusEnum.IMPORTED : ProcessStatusEnum.IMPORT_FAILED);
                        updateInterface.setProcessMessage(decodedContent.substring(0, Math.min(decodedContent.length(), 199)));
                        if (isSuccess && fusionId != null) {
                            updateInterface.setJeHeaderId(Long.valueOf(fusionId));
                        }
                        sofiGlInterfaceCommonService.updateInterfaceCommon(interfaceDO, updateInterface);
                    }

                    // 同时更新sofi_gl_shenma表
                    List<String> groupExternalReferences = headersByGroup.stream()
                            .map(SofiGlInterfaceHeaderDO::getExternalReference)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());

                    List<SofiGlShenmaDO> shenmaList = sofiGlShenMaService.queryByPolizaIdsAndProcessDay(groupExternalReferences, processDay);
                    if (shenmaList != null && !shenmaList.isEmpty()) {
                        for (SofiGlShenmaDO shenmaItem : shenmaList) {
                            if (groupId.equals(shenmaItem.getGroupId())) {
                                SofiGlShenmaDO shenmaUpdate = new SofiGlShenmaDO();
                                shenmaUpdate.setProcessStatus(isSuccess ? ProcessStatusEnum.IMPORTED : ProcessStatusEnum.IMPORT_FAILED);
                                shenmaUpdate.setProcessMessage(decodedContent.substring(0, Math.min(decodedContent.length(), 199)));
                                sofiGlShenMaService.updateShenmaInterfaceByReference(shenmaItem, shenmaUpdate);
                            }
                        }
                    }

                    log.info("FusionResultService(Shenma): 处理groupId={}完成，fusionId={}, status={}", groupId, fusionId, status);
                } else {
                    log.info("FusionResultService(Shenma): 状态{}不需要处理，跳过更新", status);
                }
            }
        } catch (Exception e) {
            log.error("FusionResultService(Shenma): 处理结果异常", e);
            throw e;
        }
    }

    /**
     * 构建SOAP请求
     */
    private String buildSoapRequest(Long groupId) {
        return "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" +
                "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:v2=\"http://xmlns.oracle.com/oxp/service/v2\">\n" +
                "    <soapenv:Header/>\n" +
                "    <soapenv:Body>\n" +
                "        <v2:runDataModel>\n" +
                "            <v2:reportRequest>\n" +
                "                <v2:parameterNameValues>\n" +
                "                    <v2:listOfParamNameValues>\n" +
                "                        <v2:item>\n" +
                "                            <v2:name>P_GROUP_ID</v2:name>\n" +
                "                            <v2:values>\n" +
                "                                <v2:item>" + groupId + "</v2:item>\n" +
                "                            </v2:values>\n" +
                "                        </v2:item>\n" +
                "                        <v2:item>\n" +
                "                            <v2:name>P_SOURCE_SYSTEM</v2:name>\n" +
                "                            <v2:values>\n" +
                "                                <v2:item></v2:item>\n" +
                "                            </v2:values>\n" +
                "                        </v2:item>\n" +
                "                        <v2:item>\n" +
                "                            <v2:name>P_SOURCE_ID</v2:name>\n" +
                "                            <v2:values>\n" +
                "                                <v2:item></v2:item>\n" +
                "                            </v2:values>\n" +
                "                        </v2:item>\n" +
                "                    </v2:listOfParamNameValues>\n" +
                "                </v2:parameterNameValues>\n" +
                "                <v2:reportAbsolutePath>" + reportPath + "</v2:reportAbsolutePath>\n" +
                "            </v2:reportRequest>\n" +
                "            <v2:userID>" + fusionUsername + "</v2:userID>\n" +
                "            <v2:password>" + fusionPassword + "</v2:password>\n" +
                "        </v2:runDataModel>\n" +
                "    </soapenv:Body>\n" +
                "</soapenv:Envelope>";
    }

    private String extractReportBytesFromXml(String xml) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            factory.setFeature("http://xml.org/sax/features/external-general-entities", false);
            factory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new InputSource(new StringReader(xml)));
            NodeList nodeList = doc.getElementsByTagName("reportBytes");
            if (nodeList.getLength() > 0) {
                return nodeList.item(0).getTextContent();
            }
        } catch (Exception e) {
            log.error("解析reportBytes异常", e);
        }
        return null;
    }

    private String parseJeHeaderId(String xml) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            factory.setFeature("http://xml.org/sax/features/external-general-entities", false);
            factory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new InputSource(new StringReader(xml)));
            NodeList nodeList = doc.getElementsByTagName("FUSION_ID");
            if (nodeList.getLength() > 0) {
                return nodeList.item(0).getTextContent();
            }
        } catch (Exception e) {
            log.error("解析FUSION_ID异常", e);
        }
        return null;
    }

    private String parseStatus(String xml) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            factory.setFeature("http://xml.org/sax/features/external-general-entities", false);
            factory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new InputSource(new StringReader(xml)));
            NodeList nodeList = doc.getElementsByTagName("STATUS");
            if (nodeList.getLength() > 0) {
                return nodeList.item(0).getTextContent();
            }
        } catch (Exception e) {
            log.error("解析STATUS异常", e);
        }
        return null;
    }

    @Override
    public void handleFusionResultJob(String systemCode, String param) {
        String processDate;
        if (StringUtils.isEmpty(param)) {
            processDate = erpUtil.getDateDirFromInput("");
        } else {
            processDate = JsonUtil.toObject(param, String.class);
        }

        if (SourceSysEnum.SAFI.getCode().equalsIgnoreCase(systemCode)) {
            handleSafiFusionResult(systemCode, processDate);
        } else if (SourceSysEnum.SHEN_MA.getCode().equalsIgnoreCase(systemCode)) {
            handleShenmaBotFusionResult(systemCode, processDate);
        } else {
            log.warn("FusionResultJob: 未知的系统代码 {}", systemCode);
        }
    }

    private void handleSafiFusionResult(String systemCode, String processDate) {
        // 查询需要处理的header
        SofiGlInterfaceHeaderDO queryDO = new SofiGlInterfaceHeaderDO();
        queryDO.setProcessStatus(ProcessStatusEnum.IMPORTED);
        queryDO.setProcessDay(processDate);

        List<SofiGlInterfaceHeaderDO> headerList = sofiGlInterfaceHeaderService.queryHeaderData(queryDO, systemCode);
        if (headerList == null || headerList.isEmpty()) {
            log.info("FusionResultJob(SAFI): 没有需要处理的数据");
            return;
        }

        List<String> externalReferenceList = headerList.stream()
                .map(SofiGlInterfaceHeaderDO::getExternalReference)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 查询对应的明细
        List<SofiGlInterfaceDO> interfaceList = sofiGlInterfaceRepository.selectSofiGlInterfaceByPolizaIdsAndProcessDay(externalReferenceList, headerList.get(0).getProcessDay());

        if (interfaceList == null || interfaceList.isEmpty()) {
            log.info("FusionResultJob(SAFI): 未找到对应的Interface数据, PolizaIds={}, ProcessDay={}", externalReferenceList, headerList.get(0).getProcessDay());
            return;
        }

        // 处理通过状态检查的数据
        processSingleFusionResult(interfaceList, headerList);
    }

    private void handleShenmaBotFusionResult(String systemCode, String processDate) {
        // 查询需要处理的header
        SofiGlInterfaceHeaderDO queryDO = new SofiGlInterfaceHeaderDO();
        queryDO.setProcessStatus(ProcessStatusEnum.IMPORTED);
        queryDO.setProcessDay(processDate);

        List<SofiGlInterfaceHeaderDO> headerList = sofiGlInterfaceHeaderService.queryHeaderData(queryDO, systemCode);
        if (headerList == null || headerList.isEmpty()) {
            log.info("FusionResultJob(SHENMA): 没有需要处理的数据");
            return;
        }

        List<String> externalReferenceList = headerList.stream()
                .map(SofiGlInterfaceHeaderDO::getExternalReference)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 查询对应的神马Common表
        List<SofiGlInterfaceCommonDO> interfaceList = sofiGlInterfaceCommonService.queryByReference5AndProcessDay(externalReferenceList, processDate);

        if (interfaceList == null || interfaceList.isEmpty()) {
            log.info("FusionResultJob(SHENMA): 未找到对应的Common Interface数据, PolizaIds={}, ProcessDay={}", externalReferenceList, processDate);
            return;
        }

        // 处理通过状态检查的数据
        processSingleFusionResultForShenma(interfaceList, headerList);
    }
}
