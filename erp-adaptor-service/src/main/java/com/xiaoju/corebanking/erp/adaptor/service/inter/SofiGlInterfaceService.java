package com.xiaoju.corebanking.erp.adaptor.service.inter;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.CargosAndAbonosDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiEmailDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderQueryDO;

import java.util.List;

public interface SofiGlInterfaceService {
    /**
     * 更新接口表数据
     */
    void updateInterface(SofiGlInterfaceDO sofiGlInterfaceDO, SofiGlInterfaceDO update);
    /**
     * 更新接口表数据
     */
    void updateInterfaceByPolizaId(SofiGlInterfaceDO sofiGlInterfaceDO, SofiGlInterfaceDO update);
    
    /**
     * 根据polizaId查询接口表数据
     */
    List<SofiGlInterfaceDO> queryByPolizaId(List<Long> externalReference);

    List<SofiGlInterfaceDO> queryByPolizaIdsAndProcessDay(List<String> externalReferenceList, String processDay);

    SofiGlInterfaceDO selectByPolizaIdAndDetalle(Long polizaId , Long detallePolizaId);
    
    /**
     * 更新接口表数据,备份his表
     * 金额和ID字段不允许修改
     */
    SofiGlInterfaceDO updateInterfaceWithBackup(Long polizaId,Long detallePolizaId, SofiGlInterfaceDO update);

    List<Long> queryGroupId(SofiGlInterfaceHeaderQueryDO sofiGlInterfaceHeaderQueryDO);

    List<SofiGlInterfaceDO> querySofiGlInterface(SofiGlInterfaceDO sofiGlInterfaceDO);
    CargosAndAbonosDO queryCargosAndAbonosDO(Long polizaId);

    List<SofiEmailDO> queryCountInterfaceEmailData(String processDay);

    List<SofiGlInterfaceDO> querySofiGlInterfaceByGroupIdAndProcessDay(Long groupId, String processDay);

    void validateCoa(String processDay);

}
