package com.xiaoju.corebanking.erp.adaptor.service.file.enums;

import cn.hutool.core.date.DatePattern;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlAcctHistDO;
import com.xiaoju.digitalbank.util.tools.ElvishDateUtils;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 * @date 2025/7/3
 */
@Slf4j
public enum SofiGlAcctEnum {
    INTERNAL_KEY("internalkey", (entity, value) -> entity.setInternalKey(Long.valueOf(value.trim()))),
    BRANCH("branch", (entity, value) -> entity.setBranch(value.trim())),
    CCY("ccy", (entity, value) -> entity.setCcy(value.trim())),
    GL_CODE("glcode", (entity, value) -> entity.setGlCode(value.trim())),
    CLIENT_NO("clientno", (entity, value) -> entity.setClientNo(value.trim())),
    PROFIT_CENTER("profitcenter", (entity, value) -> entity.setProfitCenter(value.trim())),
    SEQ_NO("seqno", (entity, value) -> entity.setSeqNo(value.trim())),
    ACCT_OPEN_DATE("acctopendate", (entity, value) -> {
        if (!value.trim().isEmpty()) {
            entity.setAcctOpenDate(ElvishDateUtils.parseDateTime(value.trim(), DatePattern.NORM_DATETIME_PATTERN));
        }
    }),
    OPEN_TRAN_DATE("opentrandate", (entity, value) -> {
        if (!value.trim().isEmpty()) {
            entity.setOpenTranDate(ElvishDateUtils.parseDateTime(value.trim(), DatePattern.NORM_DATETIME_PATTERN));
        }
    }),
    ACCT_CLOSE_DATE("acctclosedate", (entity, value) -> {
        if (!value.trim().isEmpty()) {
            entity.setAcctCloseDate(ElvishDateUtils.parseDateTime(value.trim(), DatePattern.NORM_DATETIME_PATTERN));
        }
    }),
    ACCT_CLOSE_REASON("acctclosereason", (entity, value) -> entity.setAcctCloseReason(value.trim())),
    GL_ACCT_NO("glacctno", (entity, value) -> entity.setGlAcctNo(value.trim())),
    ACCT_STATUS("acctstatus", (entity, value) -> entity.setAcctStatus(value.trim())),
    ACTUAL_BAL("actualbal", (entity, value) -> {
        if (!value.trim().isEmpty()) {
            entity.setActualBal(new BigDecimal(value.trim()));
        }
    }),
    DR_ACTUAL_BAL("dractualbal", (entity, value) -> {
        if (!value.trim().isEmpty()) {
            entity.setDrActualBal(new BigDecimal(value.trim()));
        }
    }),
    CR_ACTUAL_BAL("cractualbal", (entity, value) -> {
        if (!value.trim().isEmpty()) {
            entity.setCrActualBal(new BigDecimal(value.trim()));
        }
    }),
    CTD_DAYS("ctddays", (entity, value) -> entity.setCtdDays(Integer.valueOf(value.trim()))),
    MTD_DAYS("mtddays", (entity, value) -> entity.setMtdDays(Integer.valueOf(value.trim()))),
    YTD_DAYS("ytddays", (entity, value) -> entity.setYtdDays(Integer.valueOf(value.trim()))),
    MTD_BAL("mtdbal", (entity, value) -> {
        if (!value.trim().isEmpty()) {
            entity.setMtdBal(new BigDecimal(value.trim()));
        }
    }),
    YTD_BAL("ytdbal", (entity, value) -> {
        if (!value.trim().isEmpty()) {
            entity.setYtdBal(new BigDecimal(value.trim()));
        }
    }),
    AGG_BAL_CTD("aggbalctd", (entity, value) -> {
        if (!value.trim().isEmpty()) {
            entity.setAggBalCtd(new BigDecimal(value.trim()));
        }
    }),
    AGG_BAL_MTD("aggbalmtd", (entity, value) -> {
        if (!value.trim().isEmpty()) {
            entity.setAggBalMtd(new BigDecimal(value.trim()));
        }
    }),
    AGG_BAL_YTD("aggbalytd", (entity, value) -> {
        if (!value.trim().isEmpty()) {
            entity.setAggBalYtd(new BigDecimal(value.trim()));
        }
    }),
    PERIOD_NO("periodno", (entity, value) -> entity.setPeriodNo(value.trim())),
    USER_ID("userid", (entity, value) -> entity.setUserId(value.trim())),
    MANUAL_ACCOUNT("manualaccount", (entity, value) -> entity.setManualAccount(value.trim())),
    OD_FACILITY("odfacility", (entity, value) -> entity.setOdFacility(value.trim())),
    LAST_CHANGE_DATE("lastchangedate", (entity, value) -> {
        if (!value.trim().isEmpty()) {
            entity.setLastChangeDate(ElvishDateUtils.parseDateTime(value.trim(), DatePattern.NORM_DATETIME_PATTERN));
        }
    }),
    ACCT_NAME("acctname", (entity, value) -> entity.setAcctName(value.trim())),
    COMPANY("company", (entity, value) -> entity.setCompany(value.trim())),
    BACKUP_DATE("backupdate", (entity, value) -> {
        if (!value.trim().isEmpty()) {
            entity.setBackupDate(ElvishDateUtils.parseDateTime(value.trim(), DatePattern.NORM_DATETIME_PATTERN));
        }
    }),
    TRAN_TIMESTAMP("trantimestamp", (entity, value) -> entity.setTranTimestamp(value.trim())),
    SYSTEM_ID("systemid", (entity, value) -> entity.setSystemId(value.trim())),
    GROUP_CLIENT("groupclient", (entity, value) -> entity.setGroupClient(value.trim())),
    DR_TRAN_AMT("drtranamt", (entity, value) -> {
        if (!value.trim().isEmpty()) {
            entity.setDrTranAmt(new BigDecimal(value.trim()));
        }
    }),
    CR_TRAN_AMT("crtranamt", (entity, value) -> {
        if (!value.trim().isEmpty()) {
            entity.setCrTranAmt(new BigDecimal(value.trim()));
        }
    }),
    LAST_ACTUAL_BAL("lastactualbal", (entity, value) -> {
        if (!value.trim().isEmpty()) {
            entity.setLastActualBal(new BigDecimal(value.trim()));
        }
    }),
    LAST_DR_ACTUAL_BAL("lastdractualbal", (entity, value) -> {
        if (!value.trim().isEmpty()) {
            entity.setLastDrActualBal(new BigDecimal(value.trim()));
        }
    }),
    LAST_CR_ACTUAL_BAL("lastcractualbal", (entity, value) -> {
        if (!value.trim().isEmpty()) {
            entity.setLastCrActualBal(new BigDecimal(value.trim()));
        }
    });

    private final String fieldName;
    private final BiConsumer<SofiGlAcctHistDO, String> setter;
    private static final Map<String, SofiGlAcctEnum> NAME_MAP = new HashMap<>();

    static {
        for (SofiGlAcctEnum field : values()) {
            NAME_MAP.put(field.fieldName, field);
        }
    }

    SofiGlAcctEnum(String fieldName, BiConsumer<SofiGlAcctHistDO, String> setter) {
        this.fieldName = fieldName;
        this.setter = setter;
    }

    public static void setField(SofiGlAcctHistDO entity, String fieldName, String value) {
        if (entity == null || fieldName == null || value == null) {
            return;
        }

        String normalizedField = fieldName.replaceAll("\\p{C}", "").toLowerCase().replace("_", "");
        SofiGlAcctEnum field = NAME_MAP.get(normalizedField);

        if (field != null) {
            try {
                if (!value.trim().isEmpty()) {
                    field.setter.accept(entity, value);
                }
            } catch (NumberFormatException e) {
                log.error("SofiGlAcctEnum转换字段{}的值{}失败: {}", fieldName, value, e.getMessage());
            } catch (Exception e) {
                log.error("SofiGlAcctEnum设置字段{}的值{}失败: {}", fieldName, value, e.getMessage());
            }
        } else {
            log.warn("SofiGlAcctEnum未找到字段映射: {}", fieldName);
        }
    }
}