package com.xiaoju.corebanking.erp.adaptor.service.fusion;

import com.alibaba.fastjson.JSONObject;
import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.common.domain.SubmitESSJobDTO;
import com.xiaoju.corebanking.erp.adaptor.common.domain.SubmitESSJobResponse;
import com.xiaoju.corebanking.erp.adaptor.common.errorno.ErpAdaptorErrorNo;
import com.xiaoju.godson.common.utils.JsonUtil;
import com.xiaoju.godson.http.entity.HttpClientConnection;
import com.xiaoju.godson.http.entity.HttpClientResponse;
import com.xiaoju.godson.http.util.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;

@Slf4j
@Service
public class FunsionFileImportService {

    @Value("${fusion.jwt.username}")
    private String username;
    @Value("${fusion.url.common-url}")
    private String commonUrl;
    @Autowired
    RestTemplate restTemplate;

    public SubmitESSJobResponse importFile(SubmitESSJobDTO submitESSJobDTO, String token) {
        StringBuilder stringBuilder = new StringBuilder();
        submitESSJobDTO.setOperationName(CommonConstant.OPERATIONNAME_SUBMITESSJOBREQUEST);
        submitESSJobDTO.setJobPackageName(CommonConstant.JOBPACKAGENAME);
        submitESSJobDTO.setJobDefName(CommonConstant.JOBDEFNAME);
        StringBuilder eSSParameters = new StringBuilder("15,");
        eSSParameters.append(submitESSJobDTO.getESSParameters()).append(",").append(CommonConstant.N).append(",").append(CommonConstant.N);
        submitESSJobDTO.setESSParameters(eSSParameters.toString());
        stringBuilder.append(commonUrl).append(CommonConstant.UPLOADFILETOUCMAPI);

        HttpClientConnection httpClientConnection = HttpClientUtil.post(stringBuilder.toString());
        httpClientConnection.addHeader("Authorization", "Bearer " + token);
        httpClientConnection.addHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        httpClientConnection.bodyJson(JsonUtil.toString(submitESSJobDTO));
        HttpClientResponse response = null;
        try {
            response = httpClientConnection.execute();
        } catch (IOException e) {
            log.error("importFile errorNo:{}", ErpAdaptorErrorNo.SYSTEM_ERROR_DEFAULT_FAIL, e);
        }
        if (response.isSuccess()) {
            JSONObject jsonObject = JSONObject.parseObject(response.getString());
            log.info("importFile result:{}", jsonObject);
            SubmitESSJobResponse submitESSJobResponse = JSONObject.toJavaObject(jsonObject, SubmitESSJobResponse.class);
            return submitESSJobResponse;
        }
        return null;
    }


}
