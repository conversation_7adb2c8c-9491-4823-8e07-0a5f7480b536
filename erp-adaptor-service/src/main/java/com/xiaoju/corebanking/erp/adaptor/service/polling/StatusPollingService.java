package com.xiaoju.corebanking.erp.adaptor.service.polling;

import com.xiaoju.digitalbank.ddd.po.ExecResult;

/**
 * <AUTHOR>
 * @date 2025/5/21 16:29
 */
public interface StatusPollingService {
    ExecResult pollLoadRequestStatus(String systemCode,String param);

    ExecResult pollLoadRequestStatusAsync();

    /**
     * 检查请求状态
     * @param loadRequestId 加载请求ID
     * @return 检查结果是否成功
     */
    boolean checkRequestStatus(Long loadRequestId);
}
