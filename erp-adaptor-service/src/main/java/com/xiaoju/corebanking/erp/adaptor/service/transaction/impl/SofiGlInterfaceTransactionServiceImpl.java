package com.xiaoju.corebanking.erp.adaptor.service.transaction.impl;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.enums.SourceSysEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;
import com.xiaoju.corebanking.erp.adaptor.service.header.SofiGlInterfaceHeaderService;
import com.xiaoju.corebanking.erp.adaptor.service.inter.SofiGlInterfaceService;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlShenMaService;
import com.xiaoju.corebanking.erp.adaptor.service.transaction.SofiGlInterfaceTransactionService;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

@Slf4j
@Service
public class SofiGlInterfaceTransactionServiceImpl implements SofiGlInterfaceTransactionService {
    @Autowired
    private SofiGlInterfaceHeaderService sofiGlInterfaceHeaderService;

    @Autowired
    private SofiGlInterfaceService sofiGlInterfaceService;


    @Transactional(value = "erpTransactionManager", rollbackFor = Exception.class)
    @Override
    public void updateInterfaceAndInterfaceHeaderByPolizaId(SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO, SofiGlInterfaceDO sofiGlInterfaceDO, ProcessStatusEnum newStatus) {
        SofiGlInterfaceHeaderDO headerUpdate = new SofiGlInterfaceHeaderDO();
        headerUpdate.setProcessStatus(newStatus);
        if(StringUtils.isNotEmpty(sofiGlInterfaceHeaderDO.getProcessMessage())){
            headerUpdate.setProcessMessage(sofiGlInterfaceDO.getProcessMessage());
        }
        sofiGlInterfaceHeaderService.updateInterfaceHeader(sofiGlInterfaceHeaderDO, headerUpdate, SourceSysEnum.SAFI.getCode());

        SofiGlInterfaceDO interfaceUpdate = new SofiGlInterfaceDO();
        interfaceUpdate.setProcessStatus(newStatus);
        if(ProcessStatusEnum.MAP_FAILED.equals(newStatus) || ProcessStatusEnum.VALIDATE_FAILED.equals(newStatus)){
            interfaceUpdate.setLedgerId(sofiGlInterfaceDO.getLedgerId());
            interfaceUpdate.setLedgerName(sofiGlInterfaceDO.getLedgerName());
            interfaceUpdate.setSegment1(sofiGlInterfaceDO.getSegment1());
            interfaceUpdate.setJournalSource(sofiGlInterfaceDO.getJournalSource());
            interfaceUpdate.setJournalSourceName(sofiGlInterfaceDO.getJournalSourceName());
            interfaceUpdate.setJournalCategory(sofiGlInterfaceDO.getJournalCategory());
            interfaceUpdate.setCurrencyCode(sofiGlInterfaceDO.getCurrencyCode());
            interfaceUpdate.setSegment2(sofiGlInterfaceDO.getSegment2());
            interfaceUpdate.setSegment3(sofiGlInterfaceDO.getSegment3());
            interfaceUpdate.setSegment4(sofiGlInterfaceDO.getSegment4());
            interfaceUpdate.setSegment5(sofiGlInterfaceDO.getSegment5());
            interfaceUpdate.setSegment6(sofiGlInterfaceDO.getSegment6());
            interfaceUpdate.setSegment7(sofiGlInterfaceDO.getSegment7());
            interfaceUpdate.setSegment8(sofiGlInterfaceDO.getSegment8());
            interfaceUpdate.setSegment9(sofiGlInterfaceDO.getSegment9());
            interfaceUpdate.setSegment10(sofiGlInterfaceDO.getSegment10());
        }

        if(StringUtils.isNotEmpty(sofiGlInterfaceDO.getProcessMessage())){
            interfaceUpdate.setProcessMessage(sofiGlInterfaceDO.getProcessMessage());
        }
        if(Objects.nonNull(sofiGlInterfaceDO.getGroupId()) && 0 != sofiGlInterfaceDO.getGroupId()){
            interfaceUpdate.setGroupId(sofiGlInterfaceDO.getGroupId());
        }
        sofiGlInterfaceService.updateInterfaceByPolizaId(sofiGlInterfaceDO, interfaceUpdate);
    }
    @Override
    public void updateInterfaceAndInterfaceHeader(SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO, SofiGlInterfaceDO sofiGlInterfaceDO, ProcessStatusEnum newStatus) {
        SofiGlInterfaceHeaderDO headerUpdate = new SofiGlInterfaceHeaderDO();
        headerUpdate.setProcessStatus(newStatus);
        sofiGlInterfaceHeaderService.updateInterfaceHeader(sofiGlInterfaceHeaderDO, headerUpdate,SourceSysEnum.SAFI.getCode());

        SofiGlInterfaceDO interfaceUpdate = new SofiGlInterfaceDO();
        interfaceUpdate.setProcessStatus(newStatus);
        if(ProcessStatusEnum.VALIDATED.equals(newStatus) || ProcessStatusEnum.MAP_FAILED.equals(newStatus)){
            interfaceUpdate.setLedgerId(sofiGlInterfaceDO.getLedgerId());
            interfaceUpdate.setLedgerName(sofiGlInterfaceDO.getLedgerName());
            interfaceUpdate.setSegment1(sofiGlInterfaceDO.getSegment1());
            interfaceUpdate.setJournalSource(sofiGlInterfaceDO.getJournalSource());
            interfaceUpdate.setJournalSourceName(sofiGlInterfaceDO.getJournalSourceName());
            interfaceUpdate.setJournalCategory(sofiGlInterfaceDO.getJournalCategory());
            interfaceUpdate.setCurrencyCode(sofiGlInterfaceDO.getCurrencyCode());
            interfaceUpdate.setSegment2(sofiGlInterfaceDO.getSegment2());
            interfaceUpdate.setSegment3(sofiGlInterfaceDO.getSegment3());
            interfaceUpdate.setSegment4(sofiGlInterfaceDO.getSegment4());
            interfaceUpdate.setSegment5(sofiGlInterfaceDO.getSegment5());
            interfaceUpdate.setSegment6(sofiGlInterfaceDO.getSegment6());
            interfaceUpdate.setSegment7(sofiGlInterfaceDO.getSegment7());
            interfaceUpdate.setSegment8(sofiGlInterfaceDO.getSegment8());
            interfaceUpdate.setSegment9(sofiGlInterfaceDO.getSegment9());
            interfaceUpdate.setSegment10(sofiGlInterfaceDO.getSegment10());
        }

        if(StringUtils.isNotEmpty(sofiGlInterfaceDO.getProcessMessage())){
            interfaceUpdate.setProcessMessage(sofiGlInterfaceDO.getProcessMessage());
        }
        if(Objects.nonNull(sofiGlInterfaceDO.getGroupId()) && 0 != sofiGlInterfaceDO.getGroupId()){
            interfaceUpdate.setGroupId(sofiGlInterfaceDO.getGroupId());
        }
        sofiGlInterfaceService.updateInterface(sofiGlInterfaceDO, interfaceUpdate);
    }

}
