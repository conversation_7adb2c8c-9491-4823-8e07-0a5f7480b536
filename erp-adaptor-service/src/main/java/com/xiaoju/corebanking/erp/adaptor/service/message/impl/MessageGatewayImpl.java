package com.xiaoju.corebanking.erp.adaptor.service.message.impl;

import com.xiaoju.corebanking.erp.adaptor.common.apollo.ErpCommonApolloConfig;
import com.xiaoju.corebanking.erp.adaptor.common.domain.ErpAdaptorMessageRequest;
import com.xiaoju.corebanking.erp.adaptor.common.errorno.ErpAdaptorErrorNo;
import com.xiaoju.corebanking.erp.adaptor.integration.MessageRpc;
import com.xiaoju.corebanking.erp.adaptor.service.message.MessageGateway;
import com.xiaoju.digitalbank.enums.CountryEnum;
import com.xiaoju.digitalbank.exception.BaseException;
import com.xiaoju.digitalbank.message.platform.req.PlatformSingleMessageRequest;
import com.xiaoju.digitalbank.message.platform.res.PlatformSingleMessageResponse;
import com.xiaoju.digitalbank.util.tools.ElvishDateUtils;
import com.xiaoju.godson.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MessageGatewayImpl implements MessageGateway{
    private static final String EMPTY_JSON_STRING = "{}";
    @Autowired
    MessageRpc messageRpc;

    @Autowired
    private ErpCommonApolloConfig erpCommonApolloConfig;

    @Override
    public void sendEmail(ErpAdaptorMessageRequest erpAdaptorMessageRequest) {
        log.info("sendEmail erpAdaptorMessageRequest:{}", erpAdaptorMessageRequest);
        PlatformSingleMessageResponse response = messageRpc.sendMessagePush(buildPlatformMessageRequest(erpAdaptorMessageRequest));
        log.info("sendEmail response:{}", response);
        boolean messageStatusOk = messageRpc.checkMessageReceived(response);
        if (!messageStatusOk) {
            throw new BaseException(ErpAdaptorErrorNo.MESSAGE_NOTIFY_ERROR, "Failed to send email");
        }
    }
    private PlatformSingleMessageRequest buildPlatformMessageRequest(ErpAdaptorMessageRequest erpAdaptorMessageRequest){
        PlatformSingleMessageRequest platformSingleMessageRequest = new PlatformSingleMessageRequest();
        //nolint
        String requestId = "ERP" + ElvishDateUtils.formatToYYYYMMDDHHMMSS_Compact(System.currentTimeMillis());
        platformSingleMessageRequest.setTemplateId(erpCommonApolloConfig.getTemplateId());
        platformSingleMessageRequest.setRequestId(requestId);
        platformSingleMessageRequest.setReceiver(erpCommonApolloConfig.getReceiver());
        platformSingleMessageRequest.setExtraInfo(JsonUtil.toString(erpAdaptorMessageRequest));
        platformSingleMessageRequest.setLang(CountryEnum.MEX.getI18n());
        platformSingleMessageRequest.setTemplateParam(EMPTY_JSON_STRING);
        return platformSingleMessageRequest;
    }
}