package com.xiaoju.corebanking.erp.adaptor.service.shenma;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.*;

import java.util.List;

public interface SofiGlShenMaService {
    /**
     * 更新接口表数据
     */
    void updateShenmaInterfaceByReference(SofiGlShenmaDO sofiGlShenmaDO, SofiGlShenmaDO update);

    List<SofiGlShenmaDO> querySofiGlShenmaByGroupIdAndProcessDay(Long groupId, String processDay);
    void validateCoa(String processDay);
    void updateByProcessDayAndGroupIdAndStatus(String processDay, Long groupId, String processStatus, SofiGlShenmaDO update);
    List<SummaryResultDO> queryShenMaSummary(String processDay);
    List<SummaryResultDO> queryShenMaSumByVoucherGroup(String processDay);

    List<SofiGlShenmaDO> queryByPolizaIdsAndProcessDay(List<String> externalReferenceList, String processDay);

}
