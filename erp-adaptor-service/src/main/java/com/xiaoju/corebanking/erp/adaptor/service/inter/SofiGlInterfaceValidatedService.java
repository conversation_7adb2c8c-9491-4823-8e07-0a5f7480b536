package com.xiaoju.corebanking.erp.adaptor.service.inter;

import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValueExtraPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;

import java.util.List;
import java.util.Map;

public interface SofiGlInterfaceValidatedService {
    String validated(SofiGlInterfaceDO sofiGlInterfaceDO, Map<Long, List<SofiGlInterfaceDO>> map);

    String validatedCargosAndAbonos(SofiGlInterfaceDO sofiGlInterfaceDO);

    String mappedResult(SofiGlInterfaceDO sofiGlInterfaceDO, Map<String, Map<String, CuxTwoTabValueExtraPO>> cache);


}
