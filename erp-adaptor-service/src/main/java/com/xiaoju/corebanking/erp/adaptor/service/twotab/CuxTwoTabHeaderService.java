package com.xiaoju.corebanking.erp.adaptor.service.twotab;

import com.xiaoju.corebanking.erp.adaptor.repository.TwoTabHeaderRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.CuxTwoTabHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabHeadersPO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class CuxTwoTabHeaderService {

    @Resource
    TwoTabHeaderRepository twoTabHeaderRepository;

    public CuxTwoTabHeadersPO findByFormCode(String fromCode){
       return twoTabHeaderRepository.selectByFormCode(fromCode);
    }

    public CuxTwoTabHeadersPO insertHeader(CuxTwoTabHeaderDO header){
        return twoTabHeaderRepository.insertByExample(header);
    }

    public List<CuxTwoTabHeadersPO> selectAllHeaders(){
        return twoTabHeaderRepository.selectAll();
    }

    public void saveHeader(CuxTwoTabHeaderDO header){
        twoTabHeaderRepository.saveHeader(header);
    }
}
