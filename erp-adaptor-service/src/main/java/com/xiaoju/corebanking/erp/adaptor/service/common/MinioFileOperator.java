package com.xiaoju.corebanking.erp.adaptor.service.common;


import com.xiaoju.corebanking.erp.adaptor.common.apollo.AwsS3Config;
import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import com.xiaoju.digitalbank.exception.BaseException;
import io.minio.*;
import io.minio.errors.*;
import io.minio.http.Method;
import io.minio.messages.Item;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * Minio的文件服务
 */
@Slf4j
@Component
public class MinioFileOperator {

    @Resource
    private AwsS3Config awsS3Config;

    private static final int FILE_URL_EXPIRE_TIME = 7;


    public void upload(String fileKey, InputStream input) {
        try {
            getClient().putObject(
                    PutObjectArgs.builder()
                            .bucket(awsS3Config.getBucketName()).object(fileKey)
                            .stream(input, input.available(), -1)
                            .build());
        } catch (Exception e) {
            log.error("upload error,fileKey:{}", fileKey, e);
        }
    }

    public InputStream download(String fileKey) {
        try {
            return getClient().getObject(
                    GetObjectArgs.builder()
                            .bucket(awsS3Config.getBucketName()).object(fileKey)
                            .object(fileKey)
                            .build());
        } catch (Exception e) {
            log.error("download error,fileKey:{}", fileKey, e);
        }
        return null;
    }

    public MinioClient getClient() {
        return MinioClient.builder()
                .endpoint(awsS3Config.getEndpoint())
                .credentials(awsS3Config.getAccessKey(), awsS3Config.getSecretKey())
                .build();
    }

    public String getUrl(String fileKey) {
        try {
            return getClient().getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket(awsS3Config.getBucketName())
                    .object(fileKey)
                    .expiry(FILE_URL_EXPIRE_TIME, TimeUnit.DAYS)
                    .build());
        } catch (Exception e) {
            log.error("getUrl error,fileKey:{}", fileKey, e);
        }
        return null;
    }

    public String upload(MultipartFile file) {
        String fileKey = UUID.randomUUID() + file.getName();
        try {
            log.info("upload fileKey {}", fileKey);
            upload(fileKey, file.getInputStream());
        } catch (IOException e) {
            log.error("upload error,fileKey:{}", fileKey, e);
        }
        return fileKey;
    }

    public void remove(String fileKey) {
        try {
            getClient().removeObject(RemoveObjectArgs.builder()
                    .bucket(awsS3Config.getBucketName()).object(fileKey)
                    .object(fileKey)
                    .build());
        } catch (Exception e) {
            log.error("remove error,fileKey:{}", fileKey, e);
        }
    }

    // 获取所有文件
    public List<String> listObjects(String prefix) throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException {
        List<String> list = new ArrayList<>();
        Iterable<Result<Item>> results = getClient().listObjects(ListObjectsArgs
                .builder()
                .bucket(awsS3Config.getBucketName())
                .prefix(prefix)
                .build());
        for (Result<Item> result : results) {
            Item item = result.get();
            if(!item.isDir()){
                System.out.println(item.lastModified());
                list.add(item.objectName());
            }
        }
        return list;
    }

    public StatObjectResponse getFileInfo(String objectName) {
        try {
            return getClient().statObject(
                    StatObjectArgs.builder()
                            .bucket(awsS3Config.getBucketName())
                            .object(objectName)
                            .build());
        } catch (Exception e) {
            throw new RuntimeException("获取文件信息失败: " + e.getMessage(), e);
        }
    }

    public boolean checkObjectExists(String objectName) throws BaseException {
        try {
            getClient().statObject(
                    StatObjectArgs.builder().bucket(awsS3Config.getBucketName()).object(objectName).build()
            );
            log.info("checkObjectExists success exist, bucketName={}, objectName={}", awsS3Config.getBucketName(), objectName);
            return true;
        } catch (MinioException e) {
            log.error("checkObjectExists failed error={}, bucketName={}, objectName={}", CommonErrorNo.DI_RPC_ERROR, awsS3Config.getBucketName(),
                    objectName, e);
        } catch (Exception e) {
            log.error("checkObjectExists failed error={}, bucketName={}, objectName={}", CommonErrorNo.DI_RPC_ERROR, awsS3Config.getBucketName(),
                    objectName, e);
            throw new BaseException(CommonErrorNo.DI_RPC_ERROR);
        }
        return false;
    }
}