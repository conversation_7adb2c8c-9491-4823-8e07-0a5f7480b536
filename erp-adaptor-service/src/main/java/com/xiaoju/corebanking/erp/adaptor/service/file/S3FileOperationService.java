package com.xiaoju.corebanking.erp.adaptor.service.file;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/16 16:40
 */
public interface S3FileOperationService {
    /**
     * 检查文件是否存在
     */
    boolean checkFileExists(String filePath);

    /**
     * 下载文件为字节数组
     */
    byte[] downloadFileAsBytes(String filePath);

    /**
     * 读取文件所有行
     */
    List<String> readFileLines(String filePath);

    /**
     * 读取文件指定行数
     */
    List<String> readFileLines(String filePath, Integer maxLines);

    /**
     * 读取文件第一行
     */
    String readFileFirstLine(String filePath);

    /**
     * 验证文件MD5
     */
    boolean verifyMd5(String filePath, String expectedMd5);

    /**
     * 从check文件中读取子文件列表及其记录数
     */
    Map<String, Integer> readSubfilesWithCount(String checkCsvKey);

    /**
     * 获取文件大小
     */
    Long getFileSize(String filePath);

    /**
     * 下载文件
     */
    InputStream download(String filePath);
}
