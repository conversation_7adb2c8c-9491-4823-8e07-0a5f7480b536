package com.xiaoju.corebanking.erp.adaptor.service.header.impl;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceHeaderRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderQueryDO;
import com.xiaoju.corebanking.erp.adaptor.service.header.SofiGlInterfaceHeaderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 类描述
 * @author: zhangcc
 * @date: 2025/5/16$
 **/
@Slf4j
@Service
public class SofiGlInterfaceHeaderServiceImpl implements SofiGlInterfaceHeaderService {
    @Autowired
    private SofiGlInterfaceHeaderRepository sofiGlInterfaceHeaderRepository;

    @Override
    public List<SofiGlInterfaceHeaderDO> queryInterfaceHeaderList(SofiGlInterfaceHeaderQueryDO sofiGlInterfaceHeaderQueryDO) {
        return sofiGlInterfaceHeaderRepository.queryList(sofiGlInterfaceHeaderQueryDO);
    }

    @Override
    public void updateInterfaceHeader(SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO, SofiGlInterfaceHeaderDO update,String systemCode) {
        sofiGlInterfaceHeaderRepository.updateInterfaceHeader(sofiGlInterfaceHeaderDO, update,systemCode);
    }

    @Override
    public List<SofiGlInterfaceHeaderDO> queryHeaderData(SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO,String systemCode) {
        return sofiGlInterfaceHeaderRepository.queryData(sofiGlInterfaceHeaderDO,systemCode);
    }

    @Override
    public List<Long> findGroupIdByProcessDay(String processDay, String systemCode) {
        return sofiGlInterfaceHeaderRepository.selectGroupByProcessDay(processDay, systemCode);
    }

    @Override
    public void updateInterfaceHeaderByGroupId(String systemCode, String processDay, Long groupId, ProcessStatusEnum oldStatus, ProcessStatusEnum newStatus) {
        sofiGlInterfaceHeaderRepository.updateInterfaceHeaderByGroupId(systemCode,processDay,groupId,oldStatus,newStatus);
    }
}
