package com.xiaoju.corebanking.erp.adaptor.service.shenma.impl;

import com.github.pagehelper.PageInfo;
import com.xiaoju.corebanking.erp.adaptor.repository.CoaRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlShenmaRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SummaryResultDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.ValidationRequestDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.ValidationResultDO;
import com.xiaoju.corebanking.erp.adaptor.service.fusion.CoaValidationService;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlShenMaService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 类描述
 * @author: didi
 **/
@Slf4j
@Service
public class SofiGlShenMaServiceImpl implements SofiGlShenMaService {
    @Autowired
    private SofiGlShenmaRepository sofiGlShenmaRepository;
    @Resource
    private CoaRepository coaRepository;
    @Resource
    private CoaValidationService coaValidationService;

    @Override
    public void updateShenmaInterfaceByReference(SofiGlShenmaDO sofiGlShenmaDO, SofiGlShenmaDO update) {
        sofiGlShenmaRepository.updateShenmaInterfaceByReference(sofiGlShenmaDO, update);
    }

    @Override
    public List<SofiGlShenmaDO> querySofiGlShenmaByGroupIdAndProcessDay(Long groupId, String processDay) {
        return sofiGlShenmaRepository.querySofiGlShenmaByGroupIdAndProcessDay(groupId, processDay);
    }

    @Override
    public void validateCoa(String processDay) {
        log.info("shen ma validateCoa start");
        int pageNum = 1;
        int pageSize = 5000; //分页分大点
        int totalUpdated = 0;
        int totalShenMaUpdated = 0;
        boolean hasData = false;
        PageInfo<ValidationRequestDO> pageInfo;
        do {
            pageInfo = coaRepository.pagingSelectShenMaCoaList(processDay, pageNum, pageSize);
            List<ValidationRequestDO> coaList = pageInfo.getList();

            if (CollectionUtils.isEmpty(coaList)) {
                break;
            }

            hasData = true;

            List<ValidationResultDO> validationResultDOS = coaValidationService.validateCoa(coaList);

            for (ValidationResultDO validationResult : validationResultDOS) {
                String processStatus = "Invalid".equals(validationResult.getStatus())
                        ? "MAP_FAILED" : "MAPPED";
                //回写sofi_gl_interface
                //虽然是按照COA维度更新，但里面是按照ID更新
                int linesUpdated = coaRepository.updateCommonCoaStatusByIdBatch(
                        processDay,
                        validationResult,
                        processStatus,
                        validationResult.getError()
                );

                int shenmaUpdates = coaRepository.updateShenmaCoaStatusByIdBatch( processDay,
                        validationResult,
                        processStatus,validationResult.getError());
                log.info("shen ma 1.validate Coa，分页更新sofi_gl_interface条数 {} @{}", linesUpdated, processDay);

                totalUpdated += linesUpdated;
                totalShenMaUpdated += shenmaUpdates;
            }
            pageNum++;
        } while (pageInfo.isHasNextPage());

        log.info("common validate Coa，总更新sofi_gl_interface_common条数 {} @{}", totalUpdated, processDay);
        log.info("shen ma validate Coa，总更新sofi_gl_shenma条数 {} @{}", totalShenMaUpdated, processDay);

        int headersUpdated = 0;
        if (hasData) {
            headersUpdated = coaRepository.updateShenmaHeaderStatus(processDay);
        }

        log.info("shen ma 2.validate Coa，更新sofi_gl_interface_header条数 {} @{}", headersUpdated, processDay);
    }

    @Override
    public void updateByProcessDayAndGroupIdAndStatus(String processDay, Long groupId, String processStatus, SofiGlShenmaDO update) {
        sofiGlShenmaRepository.updateByProcessDayAndGroupIdAndStatus(processDay,groupId,processStatus,update);
    }

    @Override
    public List<SummaryResultDO> queryShenMaSummary(String processDay) {
        return sofiGlShenmaRepository.selectShenMaSummary(processDay);
    }

    @Override
    public List<SummaryResultDO> queryShenMaSumByVoucherGroup(String processDay) {
        return sofiGlShenmaRepository.selectShenMaSumByVoucherGroup(processDay);
    }

    @Override
    public List<SofiGlShenmaDO> queryByPolizaIdsAndProcessDay(List<String> externalReferenceList, String processDay) {
        return sofiGlShenmaRepository.queryByPolizaIdsAndProcessDay(externalReferenceList, processDay);
    }
}
