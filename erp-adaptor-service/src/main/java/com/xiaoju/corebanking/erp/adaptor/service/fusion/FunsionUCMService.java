package com.xiaoju.corebanking.erp.adaptor.service.fusion;

import com.alibaba.fastjson.JSONObject;
import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.common.domain.FusionFileToUCMDTO;
import com.xiaoju.corebanking.erp.adaptor.common.domain.FusionUcmResponse;
import com.xiaoju.corebanking.erp.adaptor.common.errorno.ErpAdaptorErrorNo;
import com.xiaoju.corebanking.erp.adaptor.service.inter.SofiGlInterfaceService;
import com.xiaoju.godson.common.utils.JsonUtil;
import com.xiaoju.godson.http.entity.HttpClientConnection;
import com.xiaoju.godson.http.entity.HttpClientResponse;
import com.xiaoju.godson.http.util.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.Objects;
@Slf4j
@Service
public class FunsionUCMService {

    @Value("${fusion.jwt.username}")
    private String username;
    @Value("${fusion.url.common-url}")
    private String commonUrl;
    @Autowired
    RestTemplate restTemplate;

    public FusionUcmResponse uploadFileToUCM(String documentContent, String fileName,String token) {
        StringBuilder stringBuilder = new StringBuilder();
        FusionFileToUCMDTO fusionFileToUCMDTO = new FusionFileToUCMDTO();
        fusionFileToUCMDTO.setOperationName(CommonConstant.UPLOADFILETOUCM);
        fusionFileToUCMDTO.setDocumentContent(documentContent);
        fusionFileToUCMDTO.setDocumentAccount(CommonConstant.DOCUMENTACCOUNT);
        fusionFileToUCMDTO.setContentType(CommonConstant.CONTENTTYPE);
        fusionFileToUCMDTO.setFileName(fileName);
        stringBuilder.append(commonUrl).append(CommonConstant.UPLOADFILETOUCMAPI);

        HttpClientConnection httpClientConnection = HttpClientUtil.post(stringBuilder.toString());
        httpClientConnection.addHeader("Authorization", "Bearer " + token);
        httpClientConnection.addHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        httpClientConnection.bodyJson(JsonUtil.toString(fusionFileToUCMDTO));
        httpClientConnection.connectTimeout(30000);
        HttpClientResponse response = null;
        try {
            response = httpClientConnection.execute();
            if (response.isSuccess()) {
                JSONObject jsonObject = JSONObject.parseObject(response.getString());
                log.info("uploadFileToUCM result:{}", jsonObject);
                return JSONObject.toJavaObject(jsonObject, FusionUcmResponse.class);
            }
        } catch (IOException e) {
            log.error("uploadFileToUCM errorNo:{}", ErpAdaptorErrorNo.SYSTEM_ERROR_DEFAULT_FAIL, e);
        }
        return null;
    }


}
