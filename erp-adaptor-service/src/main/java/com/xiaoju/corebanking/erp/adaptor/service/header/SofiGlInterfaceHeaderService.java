package com.xiaoju.corebanking.erp.adaptor.service.header;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderQueryDO;

import java.util.List;


/**
 * @Description: 类描述
 * @author: zhangcc
 * @date: 2025/5/16$
 **/
public interface SofiGlInterfaceHeaderService {
    List<SofiGlInterfaceHeaderDO> queryInterfaceHeaderList(SofiGlInterfaceHeaderQueryDO sofiGlInterfaceHeaderQueryDO);
    void updateInterfaceHeader(SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO, SofiGlInterfaceHeaderDO update,String systemCode);
    List<SofiGlInterfaceHeaderDO> queryHeaderData(SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO,String filePath);
    List<Long> findGroupIdByProcessDay(String processDay, String systemCode);
    void updateInterfaceHeaderByGroupId(String systemCode, String processDay, Long groupId, ProcessStatusEnum oldStatus, ProcessStatusEnum newStatus);

}
