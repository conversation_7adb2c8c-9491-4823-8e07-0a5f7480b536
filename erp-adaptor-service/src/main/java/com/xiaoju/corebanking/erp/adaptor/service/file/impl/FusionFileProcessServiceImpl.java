package com.xiaoju.corebanking.erp.adaptor.service.file.impl;

import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.common.domain.*;
import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.enums.SourceSysEnum;
import com.xiaoju.corebanking.erp.adaptor.common.utils.CSVToZipUtil;
import com.xiaoju.corebanking.erp.adaptor.common.utils.FileUtil;
import com.xiaoju.corebanking.erp.adaptor.common.utils.ZipToBase64Util;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceCommonRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.*;
import com.xiaoju.corebanking.erp.adaptor.service.file.FusionFileProcessService;
import com.xiaoju.corebanking.erp.adaptor.service.fusion.FunsionFileImportService;
import com.xiaoju.corebanking.erp.adaptor.service.fusion.FunsionUCMService;
import com.xiaoju.corebanking.erp.adaptor.service.header.SofiGlInterfaceHeaderService;
import com.xiaoju.corebanking.erp.adaptor.service.inter.SofiGlInterfaceService;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceRepository;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlShenMaService;
import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import com.xiaoju.digitalbank.exception.BaseException;
import com.xiaoju.digitalbank.util.tools.ElvishDateUtils;
import com.xiaoju.elvish.sdk.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.*;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.xiaoju.digitalbank.util.tools.ElvishDateUtils.getTimeZoneAndLocale;

/**
 * @Description: 类描述
 * @author: zhangcc
 * @date: 2025/5/21$
 **/
@Slf4j
@Service
public class FusionFileProcessServiceImpl implements FusionFileProcessService {
    @Autowired
    private SofiGlInterfaceRepository sofiGlInterfaceRepository;
    @Autowired
    private ZipToBase64Util zipToBase64Util;
    @Autowired
    private FunsionUCMService funsionUCMService;

    @Autowired
    private SofiGlInterfaceHeaderService sofiGlInterfaceHeaderService;

    @Autowired
    private SofiGlInterfaceService sofiGlInterfaceService;

    @Autowired
    private FunsionFileImportService funsionFileImportService;

    @Autowired
    private SofiGlInterfaceCommonRepository sofiGlInterfaceCommonRepository;

    @Autowired
    private SofiGlShenMaService sofiGlShenMaService;

    @Override
    public void processCSVFile(List<Long> groupIds, String token) {
        log.info("processCSVFile groupIds:{}", groupIds);
        pushFusion(groupIds, token);
    }

    @Override
    public <T> void generateCsvToStream(OutputStream outputStream, List<T> dataList,boolean withHeaderFlag) throws IOException {
            if (dataList == null || dataList.isEmpty()) {
                throw new IllegalArgumentException("Data list cannot be empty");
            }
            log.info("generate {} lines csv to outputStream...", dataList.size());
            generateCsvToStreamFromAnnotations(outputStream, dataList,withHeaderFlag);
    }

    @Override
    public void processCSVFileToCommon(String systemCode, String processDay, List<Long> groupIds, String token) {
        log.info("processCSVFileToCommon groupIds:{}", groupIds);
        pushCommonDataToFusion(systemCode,processDay,groupIds, token);
    }

    private <T> void generateCsvToStreamFromAnnotations(OutputStream outputStream,
                                                        List<T> dataList,
                                                        boolean withHeaderFlag) throws IOException {
        Class<?> dataClass = dataList.get(0).getClass();

        List<Field> annotatedFields = Arrays.stream(dataClass.getDeclaredFields())
                .filter(field -> field.isAnnotationPresent(CsvColumn.class))
                .sorted(Comparator.comparingInt(field ->
                        field.getAnnotation(CsvColumn.class).order()))
                .collect(Collectors.toList());

        String[] headers = annotatedFields.stream()
                .map(field -> field.getAnnotation(CsvColumn.class).description())
                .toArray(String[]::new);

        CSVFormat csvFormat;

        if (withHeaderFlag) {
            csvFormat = CSVFormat.DEFAULT
                    .withHeader(headers)
                    .withNullString("")
                    .withIgnoreSurroundingSpaces(true);
        } else {
            csvFormat = CSVFormat.DEFAULT
                    .withNullString("")
                    .withIgnoreSurroundingSpaces(true);
        }

        try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(outputStream));
             CSVPrinter csvPrinter = new CSVPrinter(writer, csvFormat)) {

            for (T data : dataList) {
                List<Object> rowData = new ArrayList<>(annotatedFields.size());

                for (Field field : annotatedFields) {
                    try {
                        field.setAccessible(true);
                        rowData.add(field.get(data));
                    } catch (IllegalAccessException e) {
                        rowData.add(null);
                    }
                }

                csvPrinter.printRecord(rowData);
            }

            csvPrinter.flush();
        }
    }

    private void pushFusion(List<Long> groupIds, String token) {
        try {
            for (Long groupId : groupIds) {
                // 创建CSVPrinter对象
                String fileName = groupId + CommonConstant.CSV_EXTENSION;
                String zipName = groupId + CommonConstant.ZIP_EXTENSION;
                // 写入CSV记录
                SofiGlInterfaceDO sofiGlInterfaceDO = new SofiGlInterfaceDO();
                sofiGlInterfaceDO.setGroupId(groupId);
                List<SofiGlInterfaceDO> sofiGlInterfaceDOList = sofiGlInterfaceRepository.queryGLInterfaceData(sofiGlInterfaceDO);
                FusionUcmResponse fusionUcmResponse = uploadFileToUCM(fileName, sofiGlInterfaceDOList, zipName, token);
                if (Objects.nonNull(fusionUcmResponse) && StringUtils.isNotEmpty(fusionUcmResponse.getDocumentId())) {
                    Long documentId = Long.valueOf(fusionUcmResponse.getDocumentId());
                    //更新状态
                    updateUploadStatus(groupId, ProcessStatusEnum.MAPPED, ProcessStatusEnum.UPLOADED, documentId, null);
                    //file import
                    SubmitESSJobDTO submitESSJobDTO = new SubmitESSJobDTO();
                    submitESSJobDTO.setESSParameters(documentId + "");
                    SubmitESSJobResponse submitESSJobResponse = funsionFileImportService.importFile(submitESSJobDTO, token);
                    if (Objects.nonNull(submitESSJobResponse) && StringUtils.isNotEmpty(submitESSJobResponse.getReqstId())) {
                        Long loadRequestId = Long.valueOf(submitESSJobResponse.getReqstId());

                        //更新状态
                        updateUploadStatus(groupId, ProcessStatusEnum.UPLOADED, ProcessStatusEnum.LOADED, documentId, loadRequestId);
                        log.info("import file success,groupId = {}", groupId);
                    } else {
                        updateUploadStatus(groupId, ProcessStatusEnum.UPLOADED, ProcessStatusEnum.LOAD_FAILED, documentId, null);
                        log.info("import file fail,groupId = {}", groupId);
                    }
                } else {
                    log.info("upload fail,fileName={}", fileName);
                    //更新状态
                    updateUploadStatus(groupId, ProcessStatusEnum.MAPPED, ProcessStatusEnum.UPLOAD_FAILED, null, null);
                }

            }
        } catch (IOException e) {
            log.error("push fusion file excepiton,errorNo ={}", CommonErrorNo.FILE_ERROR, e);
        }
    }

    private FusionUcmResponse uploadFileToUCM(String fileName, List<SofiGlInterfaceDO> sofiGlInterfaceDOList, String zipName, String token) throws IOException {
        List<GlInterfaceDto> glnterfaceList = new ArrayList<>();
        for (SofiGlInterfaceDO interfaceDO : sofiGlInterfaceDOList) {
            StringBuilder reference2 = new StringBuilder();
            StringBuilder reference4 = new StringBuilder();
            String descripcion = "";
            if (interfaceDO.getDescripcion().length() > CommonConstant.MAX_LENGTH) {
                descripcion = interfaceDO.getDescripcion().substring(Integer.valueOf(CommonConstant.ZERO), CommonConstant.MAX_LENGTH);
            } else {
                descripcion = interfaceDO.getDescripcion();
            }
            //nolint 
            Date date = ElvishDateUtils.parseDateTime(interfaceDO.getFechaActual(), "yyyy-MM-dd");
            //nolint
            Date dateFecha = ElvishDateUtils.parseDateTime(interfaceDO.getFecha(), "yyyy-MM-dd");

            //nolint
            String fechaActual = formatDateTime(date.getTime(), ElvishDateUtils.defaultTimeZoneCountryCode, "yyyy/MM/dd");
            //nolint
            String fechaDate = formatDateTime(dateFecha.getTime(), ElvishDateUtils.defaultTimeZoneCountryCode, "yyyy/MM/dd");
            Date fDate = ElvishDateUtils.parseDateTime(interfaceDO.getFecha(), "yyyy-MM");
            String fecha = ElvishDateUtils.formatToYYYY_MM_Dash(fDate.getTime());
            reference2.append(interfaceDO.getSegment1()).append("_").append(fecha).append("_").append(interfaceDO.getSourceSys());
            reference4.append(reference2).append("_").append(interfaceDO.getPolizaId());
            GlInterfaceDto glInterfaceDto = new GlInterfaceDto();
            glInterfaceDto.setStatus(CommonConstant.NEW);
            glInterfaceDto.setLedgerId(interfaceDO.getLedgerId());
            glInterfaceDto.setTransactionDate(fechaDate);
            glInterfaceDto.setJournalSourceName(interfaceDO.getJournalSource());
            glInterfaceDto.setJournalCategoryName(interfaceDO.getJournalCategory());
            glInterfaceDto.setCurrencyCode(interfaceDO.getCurrencyCode());
            glInterfaceDto.setCreationDate(fechaActual);
            glInterfaceDto.setActualFlag(CommonConstant.ACTUAL_FLAG);
            glInterfaceDto.setSegment1(interfaceDO.getSegment1());
            glInterfaceDto.setSegment2(interfaceDO.getSegment2());
            glInterfaceDto.setSegment3(interfaceDO.getSegment3());
            glInterfaceDto.setSegment4(interfaceDO.getSegment4());
            glInterfaceDto.setSegment5(interfaceDO.getSegment5());
            glInterfaceDto.setSegment6(interfaceDO.getSegment6());
            glInterfaceDto.setSegment7(interfaceDO.getSegment7());
            glInterfaceDto.setSegment8(interfaceDO.getSegment8());
            glInterfaceDto.setSegment9(interfaceDO.getSegment9());
            glInterfaceDto.setSegment10(interfaceDO.getSegment10());
            glInterfaceDto.setEnteredDr(interfaceDO.getCargos());
            glInterfaceDto.setEnteredCr(interfaceDO.getAbonos());
            glInterfaceDto.setAccountedDr(interfaceDO.getCargos());
            glInterfaceDto.setAccountedCr(interfaceDO.getAbonos());
            glInterfaceDto.setReference1(interfaceDO.getSegment1() + "_" + interfaceDO.getGroupId());
            glInterfaceDto.setReference2(reference2.toString());
            glInterfaceDto.setReference4(reference4.toString());
            glInterfaceDto.setReference5(interfaceDO.getReferencia());
            glInterfaceDto.setReference6(interfaceDO.getPolizaId()+"");
            glInterfaceDto.setReference10(descripcion);
            glInterfaceDto.setReference21(interfaceDO.getJournalSource());
            glInterfaceDto.setReference22(interfaceDO.getPolizaId()+"");
            glInterfaceDto.setReference23(interfaceDO.getDetallePolizaId()+"");
            glInterfaceDto.setGroupId(interfaceDO.getGroupId());
            glInterfaceDto.setAttributeCategory(CommonConstant.CORE_BANKING);
            glInterfaceDto.setAttribute7(CommonConstant.ATTRIBUTE7);
            glInterfaceDto.setAttribute9(interfaceDO.getNumTransaccion()+"");
            glInterfaceDto.setAttribute19(interfaceDO.getFolioUuid());
            glInterfaceDto.setAttribute20(interfaceDO.getRfc());
            glInterfaceDto.setPeriodName(fecha);
            glnterfaceList.add(glInterfaceDto);
        }
        ByteArrayOutputStream csvOutputStream = new ByteArrayOutputStream();
        generateCsvToStream(csvOutputStream,glnterfaceList,false);
        byte[] csvData = csvOutputStream.toByteArray();
        ByteArrayOutputStream zipOutputStream = new ByteArrayOutputStream();
        try (ZipOutputStream zipOut = new ZipOutputStream(zipOutputStream)) {
            ZipEntry zipEntry = new ZipEntry(fileName);
            zipOut.putNextEntry(zipEntry);
            zipOut.write(csvData);
            zipOut.closeEntry();
        }
         byte[] byteArray = zipOutputStream.toByteArray();
        //将zip转base64
        String base64 = zipToBase64Util.zipToBase64(byteArray);
        FusionUcmResponse fusionUcmResponse = funsionUCMService.uploadFileToUCM(base64, zipName, token);
        return fusionUcmResponse;
    }

    private FusionUcmResponse uploadCommonFileToUCM(String fileName, List<SofiGlInterfaceCommonDO> sofiGlInterfaceCommonDOS, String zipName, String token) throws IOException {
        List<GlInterfaceDto> glnterfaceList = new ArrayList<>();
        for (SofiGlInterfaceCommonDO commonDO : sofiGlInterfaceCommonDOS) {
            GlInterfaceDto glInterfaceDto = new GlInterfaceDto();
            glInterfaceDto.setStatus(CommonConstant.NEW);
            glInterfaceDto.setLedgerId(commonDO.getLedgerId());
            //nolint
            String fechaDate = formatDateTime(commonDO.getAccountingDate().getTime(), ElvishDateUtils.defaultTimeZoneCountryCode, "yyyy/MM/dd");
            glInterfaceDto.setTransactionDate(fechaDate);
            glInterfaceDto.setJournalSourceName(commonDO.getJournalSource());
            glInterfaceDto.setJournalCategoryName(commonDO.getJournalCategory());
            glInterfaceDto.setCurrencyCode(commonDO.getCurrencyCode());
            //nolint
            glInterfaceDto.setCreationDate(formatDateTime(commonDO.getCreationDate() .getTime(), ElvishDateUtils.defaultTimeZoneCountryCode, "yyyy/MM/dd"));
            glInterfaceDto.setActualFlag(CommonConstant.ACTUAL_FLAG);
            glInterfaceDto.setSegment1(commonDO.getSegment1());
            glInterfaceDto.setSegment2(commonDO.getSegment2());
            glInterfaceDto.setSegment3(commonDO.getSegment3());
            glInterfaceDto.setSegment4(commonDO.getSegment4());
            glInterfaceDto.setSegment5(commonDO.getSegment5());
            glInterfaceDto.setSegment6(commonDO.getSegment6());
            glInterfaceDto.setSegment7(commonDO.getSegment7());
            glInterfaceDto.setSegment8(commonDO.getSegment8());
            glInterfaceDto.setSegment9(commonDO.getSegment9());
            glInterfaceDto.setSegment10(commonDO.getSegment10());
            glInterfaceDto.setEnteredDr(commonDO.getEnteredDr());
            glInterfaceDto.setEnteredCr(commonDO.getEnteredCr());
            glInterfaceDto.setAccountedDr(commonDO.getAccountedDr());
            glInterfaceDto.setAccountedCr(commonDO.getAccountedCr());
            glInterfaceDto.setReference1(commonDO.getSegment1() + "_" + commonDO.getGroupId());
            glInterfaceDto.setReference2(commonDO.getReference2());
            glInterfaceDto.setReference4(commonDO.getReference4());
            glInterfaceDto.setReference5(commonDO.getReference5());
            glInterfaceDto.setReference6(commonDO.getReference6());
            glInterfaceDto.setReference10(commonDO.getReference10());
            glInterfaceDto.setReference21(commonDO.getJournalSource());
            glInterfaceDto.setReference22(commonDO.getReference22());
            glInterfaceDto.setReference23(commonDO.getReference23());
            glInterfaceDto.setGroupId(commonDO.getGroupId());
            glInterfaceDto.setAttributeCategory(commonDO.getAttributeCategory());
            glInterfaceDto.setAttribute1(commonDO.getLineAttribute1());
            glInterfaceDto.setAttribute3(commonDO.getLineAttribute3());
            glInterfaceDto.setAttribute4(commonDO.getLineAttribute4());
            glInterfaceDto.setAttribute5(commonDO.getLineAttribute5());
            glInterfaceDto.setAttribute6(commonDO.getLineAttribute6());
            glInterfaceDto.setAttribute7(commonDO.getLineAttribute7());
            glInterfaceDto.setAttribute9(commonDO.getLineAttribute9());
            glInterfaceDto.setAttribute19(commonDO.getLineAttribute19());
            glInterfaceDto.setAttribute20(commonDO.getLineAttribute20());
            glInterfaceDto.setPeriodName(commonDO.getPeriodName());
            glnterfaceList.add(glInterfaceDto);
        }
        ByteArrayOutputStream csvOutputStream = new ByteArrayOutputStream();
        generateCsvToStream(csvOutputStream,glnterfaceList,false);
        byte[] csvData = csvOutputStream.toByteArray();
        ByteArrayOutputStream zipOutputStream = new ByteArrayOutputStream();
        try (ZipOutputStream zipOut = new ZipOutputStream(zipOutputStream)) {
            ZipEntry zipEntry = new ZipEntry(fileName);
            zipOut.putNextEntry(zipEntry);
            zipOut.write(csvData);
            zipOut.closeEntry();
        }
        byte[] byteArray = zipOutputStream.toByteArray();
        //将zip转base64
        String base64 = zipToBase64Util.zipToBase64(byteArray);
        FusionUcmResponse fusionUcmResponse = funsionUCMService.uploadFileToUCM(base64, zipName, token);
        return fusionUcmResponse;
    }

    public void updateUploadStatus(Long groupId, ProcessStatusEnum oldStatus, ProcessStatusEnum processStatusEnum, Long documentId, Long loadRequestId) {
        SofiGlInterfaceDO sofiGlInterfaceDO = new SofiGlInterfaceDO();
        SofiGlInterfaceHeaderQueryDO queryDO = new SofiGlInterfaceHeaderQueryDO();
        queryDO.setGroupId(groupId);
        sofiGlInterfaceDO.setGroupId(groupId);
        List<SofiGlInterfaceDO> sofiGlInterfaceDOList = sofiGlInterfaceService.querySofiGlInterface(sofiGlInterfaceDO);
        List<SofiGlInterfaceHeaderDO> sofiGlInterfaceHeaderDOS = sofiGlInterfaceHeaderService.queryInterfaceHeaderList(queryDO);
        for (SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO : sofiGlInterfaceHeaderDOS) {
            sofiGlInterfaceHeaderDO.setProcessStatus(oldStatus);
            SofiGlInterfaceHeaderDO headerUpdate = new SofiGlInterfaceHeaderDO();
            headerUpdate.setProcessStatus(processStatusEnum);
            sofiGlInterfaceHeaderService.updateInterfaceHeader(sofiGlInterfaceHeaderDO, headerUpdate, SourceSysEnum.SAFI.getCode());
        }
        for (SofiGlInterfaceDO sofiDO : sofiGlInterfaceDOList) {
            SofiGlInterfaceDO update = new SofiGlInterfaceDO();
            if (Objects.nonNull(documentId)) {
                update.setDocumentId(documentId);
            }
            update.setProcessStatus(processStatusEnum);
            if (Objects.nonNull(loadRequestId)) {
                update.setLoadRequestId(loadRequestId);
            }
            sofiDO.setProcessStatus(oldStatus);
            sofiGlInterfaceService.updateInterface(sofiDO, update);
        }

    }

    public void updateCommonUploadStatus(String systemCode, String processDay,Long groupId, ProcessStatusEnum oldStatus, ProcessStatusEnum newStatus, Long documentId, Long loadRequestId) {
        //1.header 表更新
        sofiGlInterfaceHeaderService.updateInterfaceHeaderByGroupId(systemCode,processDay,groupId,oldStatus,newStatus);
        //2.神码表更新
        SofiGlShenmaDO update = new SofiGlShenmaDO();
        update.setProcessStatus(newStatus);
        sofiGlShenMaService.updateByProcessDayAndGroupIdAndStatus(processDay,groupId,oldStatus.getCode(),update);
        //3.common 表更新
        SofiGlInterfaceCommonDO commonDO = new SofiGlInterfaceCommonDO();
        commonDO.setProcessStatus(newStatus);
        if(Objects.nonNull(documentId)) {
            commonDO.setDocumentId(documentId);
        }
        if(Objects.nonNull(loadRequestId)) {
            commonDO.setLoadRequestId(loadRequestId);
        }
        sofiGlInterfaceCommonRepository.updateSofiGlInterfaceCommon(systemCode,processDay,oldStatus.getCode(),groupId,commonDO);

    }

    private static String formatDateTime(Long msTimeStamp, String countryCode, String pattern) throws BaseException {
        try {
            String[] timeZoneAndLocale = getTimeZoneAndLocale(countryCode);
            return DateTimeUtil.format(timeZoneAndLocale[1], timeZoneAndLocale[0], msTimeStamp / 1000L, pattern);
        } catch (Exception var4) {
            log.error("use elvish to format date time to string error={},countryCode:{}", new Object[]{CommonErrorNo.ELVISH_ERROR, countryCode, var4});
            throw new BaseException(CommonErrorNo.ELVISH_ERROR);
        }
    }

    private void pushCommonDataToFusion(String systemCode, String processDay, List<Long> groupIds, String token) {
        try {
            for (Long groupId : groupIds) {
                // 创建CSVPrinter对象
                String fileName = groupId + CommonConstant.CSV_EXTENSION;
                String zipName = groupId + CommonConstant.ZIP_EXTENSION;
                // 写入CSV记录
                List<SofiGlInterfaceCommonDO> sofiGlInterfaceCommonDOS = sofiGlInterfaceCommonRepository.querySofiGlInterfaceCommonDOList(systemCode,processDay,groupId);
                FusionUcmResponse fusionUcmResponse = uploadCommonFileToUCM(fileName, sofiGlInterfaceCommonDOS, zipName, token);
                if (Objects.nonNull(fusionUcmResponse) && StringUtils.isNotEmpty(fusionUcmResponse.getDocumentId())) {
                    Long documentId = Long.valueOf(fusionUcmResponse.getDocumentId());
                    //更新状态
                    updateCommonUploadStatus(systemCode,processDay,groupId,ProcessStatusEnum.MAPPED,ProcessStatusEnum.UPLOADED, documentId, null);
                    //file import
                    SubmitESSJobDTO submitESSJobDTO = new SubmitESSJobDTO();
                    submitESSJobDTO.setESSParameters(documentId + "");
                    SubmitESSJobResponse submitESSJobResponse = funsionFileImportService.importFile(submitESSJobDTO, token);
                    if (Objects.nonNull(submitESSJobResponse) && StringUtils.isNotEmpty(submitESSJobResponse.getReqstId())) {
                        Long loadRequestId = Long.valueOf(submitESSJobResponse.getReqstId());

                        //更新状态
                        updateCommonUploadStatus(systemCode,processDay,groupId, ProcessStatusEnum.UPLOADED, ProcessStatusEnum.LOADED,documentId, loadRequestId);
                        log.info("import file success,groupId = {}", groupId);
                    } else {
                        updateCommonUploadStatus(systemCode,processDay,groupId, ProcessStatusEnum.UPLOADED, ProcessStatusEnum.LOAD_FAILED, documentId, null);
                        log.info("import file fail,groupId = {}", groupId);
                    }
                } else {
                    log.info("upload fail,fileName={}", fileName);
                    //更新状态
                    updateCommonUploadStatus(systemCode,processDay,groupId, ProcessStatusEnum.MAPPED, ProcessStatusEnum.UPLOAD_FAILED, null, null);
                }

            }
        } catch (IOException e) {
            log.error("push fusion file excepiton,errorNo ={}", CommonErrorNo.FILE_ERROR, e);
        }
    }
}
