package com.xiaoju.corebanking.erp.adaptor.service.file.impl;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.enums.SourceSysEnum;
import com.xiaoju.corebanking.erp.adaptor.common.utils.ErpUtil;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlFileControlRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlFileControlDO;
import com.xiaoju.corebanking.erp.adaptor.service.file.ShenmaDataProcessService;
import com.xiaoju.corebanking.erp.adaptor.service.file.ShenmaFilePreprocessService;
import com.xiaoju.digitalbank.ddd.po.ExecDataResult;
import com.xiaoju.digitalbank.ddd.po.ExecResult;
import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import com.xiaoju.godson.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/7/3
 */
@Service
@Slf4j
public class ShenmaFilePreprocessServiceImpl implements ShenmaFilePreprocessService {

    @Resource
    private ShenmaDataProcessService shenmaDataProcessService;

    @Resource
    private ErpUtil erpUtil;

    @Resource
    private SofiGlFileControlRepository sofiGlFileControlRepository;

    @Override
    public ExecResult processShenmaFile(String filePath, String param) {
        try {
            String processDate;
            if (StringUtils.isEmpty(param)) {
                processDate = erpUtil.getDateDirFromInput(filePath);
            } else {
                processDate = JsonUtil.toObject(param, String.class);
            }
            filePath = erpUtil.buildFullPath(filePath, processDate);

            log.info("开始处理Shenma文件，使用日期目录: {}, 完整路径: {}", processDate, filePath);

            // 1. 检查文件
            boolean isCheckFileValid = checkFileValidity(filePath);
            if (!isCheckFileValid) {
                createControlStatus(0, processDate, false, "检查文件无效或不存在");
                return ExecResult.error(CommonErrorNo.FILE_ERROR, "检查文件无效或不存在");
            }

            // 2. 获取并处理所有CSV文件
            ExecDataResult<String> result = processAllShenmaFiles(filePath, processDate);
            if (result == null || !result.isSuccess()) {
                createControlStatus(0, processDate, false, "处理Shenma文件失败");
                return ExecResult.error(CommonErrorNo.FILE_ERROR, "处理Shenma文件失败");
            }

            // 3. 创建header记录
            ExecResult headerResult = shenmaDataProcessService.createShenmaHeaderRecords(processDate);
            if (!headerResult.isSuccess()) {
                log.error("创建Shenma头记录失败: {}", headerResult.getDebugMessage());
                createControlStatus(0, processDate, false, "创建Shenma头记录失败");
                return ExecResult.error(CommonErrorNo.DB_DATA_ERROR, "创建Shenma头记录失败");
            }

            // 4. 记录文件处理成功状态
            int totalRecordCount = getTotalRecordCount(filePath);
            createControlStatus(totalRecordCount, processDate, true, "Shenma文件处理成功");

            log.info("Shenma文件处理成功完成: {}", filePath);
            return ExecResult.success();

        } catch (Exception e) {
            log.error("处理Shenma文件时发生异常，文件路径: {}", filePath, e);
            createControlStatus(0, param != null ? JsonUtil.toObject(param, String.class) : erpUtil.getDateDirFromInput(filePath),
                    false, "处理Shenma文件时发生异常: " + e.getMessage());
            return ExecResult.error(CommonErrorNo.FILE_ERROR, "处理Shenma文件时发生异常: " + e.getMessage());
        }
    }

    /**
     * 处理所有Shenma文件
     */
    public ExecDataResult<String> processAllShenmaFiles(String filePath, String processDate) {
        try {
            // 获取sofi_journal_shenma.csv文件
            List<File> csvFiles = getAllCsvFiles(filePath);
            if (csvFiles.isEmpty()) {
                log.error("未找到sofi_journal_shenma.csv文件，文件路径: {}", filePath);
                return ExecDataResult.error(CommonErrorNo.FILE_ERROR, "未找到sofi_journal_shenma.csv文件");
            }

            log.info("获取到sofi_journal_shenma.csv文件，共{}个文件", csvFiles.size());

            int availableProcessors = Runtime.getRuntime().availableProcessors();
            int threadPoolSize = Math.min(availableProcessors, csvFiles.size());

            ThreadPoolExecutor asyncTaskExecutor = new ThreadPoolExecutor(
                    threadPoolSize,
                    threadPoolSize,
                    5L,
                    TimeUnit.MINUTES,
                    new LinkedBlockingQueue<>(1024),
                    new ThreadFactoryBuilder().setNameFormat("shenma-process-executor-pool-%d").build(),
                    (r, executor) -> {
                        log.error("shenmaProcessExecutor is Rejected，PoolSize:{},activeCount:{},taskCounter:{}",
                                executor.getPoolSize(), executor.getActiveCount(), executor.getTaskCount());
                    });
            List<Future<Boolean>> futures = new ArrayList<>();

            // 提交处理任务
            for (File csvFile : csvFiles) {
                log.info("提交sofi_journal_shenma.csv文件处理任务: {}", csvFile.getName());
                Future<Boolean> future = asyncTaskExecutor.submit(() ->
                        processSingleCsvFile(csvFile, processDate));
                futures.add(future);
            }

            // 等待所有任务完成
            boolean allSuccessful = true;
            for (Future<Boolean> future : futures) {
                try {
                    if (!future.get()) {
                        allSuccessful = false;
                    }
                } catch (Exception e) {
                    log.error("CSV文件处理任务执行异常", e);
                    allSuccessful = false;
                }
            }

            // 关闭线程池
            asyncTaskExecutor.shutdown();

            if (!allSuccessful) {
                return ExecDataResult.error(CommonErrorNo.FILE_ERROR, "部分CSV文件处理失败");
            }

            log.info("sofi_journal_shenma.csv文件并行处理成功");
            return ExecDataResult.success("Shenma文件处理成功");
        } catch (Exception e) {
            log.error("处理Shenma文件时发生异常，文件路径: {}", filePath, e);
            return ExecDataResult.error(CommonErrorNo.FILE_ERROR, "处理Shenma文件时发生异常: " + e.getMessage());
        }
    }

    private boolean processSingleCsvFile(File csvFile, String processDate) {
        try {
            log.info("开始处理文件: {}", csvFile.getName());

            // 读取文件内容
            String csvContent = readFileContent(csvFile);
            if (!StringUtils.hasText(csvContent)) {
                log.warn("文件内容为空，跳过文件: {}", csvFile.getName());
                return true;
            }

            // 批量插入Shenma数据
            ExecResult insertResult = shenmaDataProcessService.batchInsertShenmaData(
                    processDate, csvFile.getName(), csvContent);

            if (!insertResult.isSuccess()) {
                log.error("处理文件{}失败: {}", csvFile.getName(), insertResult.getDebugMessage());
                return false;
            }

            log.info("成功处理文件: {}", csvFile.getName());
            return true;
        } catch (Exception e) {
            log.error("处理单个CSV文件异常: {}", csvFile.getName(), e);
            return false;
        }
    }

    /**
     * 检查文件有效性
     */
    public boolean checkFileValidity(String filePath) {
        try {
            File directory = new File(filePath);
            if (!directory.exists() || !directory.isDirectory()) {
                log.error("目录不存在或不是目录: {}", filePath);
                return false;
            }

            String csvFileName = CommonConstant.FILE_CHECK_SHENMA_CSV;
            String checksumFileName = CommonConstant.FILE_CHECK_SHENMA_MD5;

            File csvFile = new File(directory, csvFileName);
            if (!csvFile.exists() || !csvFile.isFile()) {
                log.error("CSV文件不存在: {}", csvFileName);
                return false;
            }

            File checksumFile = new File(directory, checksumFileName);
            if (!checksumFile.exists()) {
                log.error("CSV文件{}对应的checksum文件不存在: {}", csvFileName, checksumFileName);
                return false;
            }

            // MD5校验
            String expectedMd5 = readChecksumFile(checksumFile);
            if (expectedMd5 == null) {
                log.error("无法读取checksum文件内容: {}", checksumFileName);
                return false;
            }

            boolean isValid = verifyFileMd5(csvFile, expectedMd5);
            if (!isValid) {
                log.error("CSV文件{}的MD5校验失败，停止处理", csvFileName);
                return false;
            }

            log.info("找到CSV文件{}和对应的checksum文件{}，MD5校验通过", csvFileName, checksumFileName);
            log.info("文件路径有效，sofi_journal_shenma文件存在且MD5校验通过: {}", filePath);
            return true;

        } catch (Exception e) {
            log.error("检查文件有效性时发生错误，文件路径: {}", filePath, e);
            return false;
        }
    }


    private String readChecksumFile(File checksumFile) {
        try {
            List<String> lines = Files.readAllLines(checksumFile.toPath(), StandardCharsets.UTF_8);
            if (lines.isEmpty()) {
                log.error("checksum文件为空: {}", checksumFile.getName());
                return null;
            }
            return lines.get(0).trim();
        } catch (IOException e) {
            log.error("读取checksum文件失败: {}", checksumFile.getName(), e);
            return null;
        }
    }

    private boolean verifyFileMd5(File file, String expectedMd5) {
        try (FileInputStream fis = new FileInputStream(file)) {
            String actualMd5 = DigestUtils.md5Hex(fis);
            boolean isValid = expectedMd5.equalsIgnoreCase(actualMd5);

            if (!isValid) {
                log.error("MD5校验失败，文件: {}, 期望MD5: {}, 实际MD5: {}",
                        file.getName(), expectedMd5, actualMd5);
            } else {
                log.info("MD5校验成功，文件: {}, MD5: {}", file.getName(), actualMd5);
            }

            return isValid;
        } catch (Exception e) {
            log.error("计算文件MD5失败: {}", file.getName(), e);
            return false;
        }
    }

    /**
     * 获取sofi_journal_shenma.csv文件
     */
    private List<File> getAllCsvFiles(String dirPath) {
        try {
            File directory = new File(dirPath);
            if (!directory.exists() || !directory.isDirectory()) {
                log.warn("目录不存在或不是目录: {}", dirPath);
                return Collections.emptyList();
            }

            String csvFileName = CommonConstant.FILE_CHECK_SHENMA_CSV;
            File csvFile = new File(directory, csvFileName);

            if (csvFile.exists() && csvFile.isFile()) {
                log.info("找到sofi_journal_shenma.csv文件");
                return Collections.singletonList(csvFile);
            } else {
                log.warn("sofi_journal_shenma.csv文件不存在: {}", dirPath);
                return Collections.emptyList();
            }

        } catch (Exception e) {
            log.error("获取sofi_journal_shenma.csv文件失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 读取文件内容
     */
    private String readFileContent(File file) {
        try {
            Path path = file.toPath();
            byte[] bytes = Files.readAllBytes(path);
            return new String(bytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("读取文件内容失败: {}", file.getName(), e);
            return null;
        }
    }

    private void createControlStatus(int actualCount, String processDate, boolean success, String message) {
        try {
            SofiGlFileControlDO controlDO = new SofiGlFileControlDO();
            controlDO.setFileName(CommonConstant.FILE_CHECK_SHENMA_CSV);
            controlDO.setProcessDay(processDate);
            controlDO.setSystemCode(SourceSysEnum.SHEN_MA.getCode());
            controlDO.setFileCount((long) actualCount);
            controlDO.setObjectVersionNumber(1);
            controlDO.setCreatedBy(CommonConstant.SYSTEM_USER);
            controlDO.setLastModifiedBy(CommonConstant.SYSTEM_USER);
            controlDO.setCreationDate(new Date());
            controlDO.setLastModifyDate(new Date());

            if (success) {
                controlDO.setProcessStatus(ProcessStatusEnum.SUCCESS.getCode());
            } else {
                controlDO.setProcessStatus(ProcessStatusEnum.FAILED.getCode());
            }
            controlDO.setProcessMessage(message);

            sofiGlFileControlRepository.insertSelective(controlDO);
            log.info("文件{}状态记录{}: {}", CommonConstant.FILE_CHECK_SHENMA_CSV, success ? "成功" : "失败", message);
        } catch (Exception e) {
            log.error("记录文件{}控制状态失败", CommonConstant.FILE_CHECK_SHENMA_CSV, e);
        }
    }


    private int getTotalRecordCount(String filePath) {
        try {
            List<File> csvFiles = getAllCsvFiles(filePath);
            int totalCount = 0;

            for (File csvFile : csvFiles) {
                String csvContent = readFileContent(csvFile);
                if (StringUtils.hasText(csvContent)) {
                    String[] lines = csvContent.split("\\r?\\n");
                    int lineCount = lines.length;
                    totalCount += Math.max(0, lineCount - 1);
                }
            }
            return totalCount;
        } catch (Exception e) {
            log.warn("获取文件记录数失败: {}", filePath, e);
            return 0;
        }
    }
} 