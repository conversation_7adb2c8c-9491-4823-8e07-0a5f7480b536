package com.xiaoju.corebanking.erp.adaptor.service.fusion;

import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.common.errorno.ErpAdaptorErrorNo;
import com.xiaoju.corebanking.erp.adaptor.common.exception.ErpAdaptorBusinessException;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.ValidationRequestDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.ValidationResultDO;
import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.List;
@Slf4j
@Service
public class CoaValidationService {

    @Value("${fusion.jwt.username}")
    private String username;
    @Value("${fusion.url.coa-validation-url}")
    private String caoValidationUrl;

    @Autowired
    FusionAuthService fusionAuthService;

    @Autowired
    RestTemplate restTemplate;

    private static final String DEFAULT_DYNAMIC_INSERTION = "Y";
    private static final String DEFAULT_SEGMENT = "0";
    private static final String DEFAULT_ENABLED_FLAG = "1";

    /**
     * 验证COA组合
     * @param requests COA列表，包含 ledgerName, segments 1-10, fromDate 生效日期
     * @return 验证结果
     */
    public List<ValidationResultDO> validateCoa(List<ValidationRequestDO> requests) {
        return validateCombinations(requests);
    }

    private List<ValidationResultDO> validateCombinations(List<ValidationRequestDO> requests) {
        try{
            String soapRequest = buildSoapRequest(requests);
            String token = fusionAuthService.generateJwtToken(username);
            HttpHeaders headers = createHeaders(token);

            HttpEntity<String> request = new HttpEntity<>(soapRequest, headers);
            log.info("validateCombinations coa soapRequest:{}",soapRequest);
            ResponseEntity<String> response = restTemplate.postForEntity(caoValidationUrl, request, String.class);
            return parseResponse(response.getBody());
        }catch (ErpAdaptorBusinessException e){
            log.error("ErpAdaptorBusinessException throws ErpAdaptorBusinessException, {}", ErpAdaptorErrorNo.COA_ERROR, e);
            List<ValidationResultDO> validateCombinations = new ArrayList<>();
            ValidationResultDO validationResultDO = new ValidationResultDO();
            validationResultDO.setStatus(CommonConstant.INVALID);
            validateCombinations.add(validationResultDO);
            return validateCombinations;
        }

    }

    private HttpHeaders createHeaders(String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_XML);
        headers.set("Authorization", "Bearer " + token);
        return headers;
    }

    private String buildSoapRequest(List<ValidationRequestDO> requests) {
        StringBuilder soapRequest = new StringBuilder();
        soapRequest.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" ")
                .append("xmlns:typ=\"http://xmlns.oracle.com/apps/financials/generalLedger/accounts/codeCombinations/accountCombinationService/types/\" ")
                .append("xmlns:acc=\"http://xmlns.oracle.com/apps/financials/generalLedger/accounts/codeCombinations/accountCombinationService/\">")
                .append("<soapenv:Header/>")
                .append("<soapenv:Body>")
                .append("<typ:validateAndCreateAccounts>");

        for (ValidationRequestDO request : requests) {
            soapRequest.append("<typ:validationInputRowList>")
                    .append("<acc:DynamicInsertion>").append(DEFAULT_DYNAMIC_INSERTION).append("</acc:DynamicInsertion>")
                    .append("<acc:Segment1>").append(request.getSegment1() != null ? request.getSegment1() : DEFAULT_SEGMENT).append("</acc:Segment1>")
                    .append("<acc:Segment2>").append(request.getSegment2() != null ? request.getSegment2() : DEFAULT_SEGMENT).append("</acc:Segment2>")
                    .append("<acc:Segment3>").append(request.getSegment3() != null ? request.getSegment3() : DEFAULT_SEGMENT).append("</acc:Segment3>")
                    .append("<acc:Segment4>").append(request.getSegment4() != null ? request.getSegment4() : DEFAULT_SEGMENT).append("</acc:Segment4>")
                    .append("<acc:Segment5>").append(request.getSegment5() != null ? request.getSegment5() : DEFAULT_SEGMENT).append("</acc:Segment5>")
                    .append("<acc:Segment6>").append(request.getSegment6() != null ? request.getSegment6() : DEFAULT_SEGMENT).append("</acc:Segment6>")
                    .append("<acc:Segment7>").append(request.getSegment7() != null ? request.getSegment7() : DEFAULT_SEGMENT).append("</acc:Segment7>")
                    .append("<acc:Segment8>").append(request.getSegment8() != null ? request.getSegment8() : DEFAULT_SEGMENT).append("</acc:Segment8>")
                    .append("<acc:Segment9>").append(request.getSegment9() != null ? request.getSegment9() : DEFAULT_SEGMENT).append("</acc:Segment9>")
                    .append("<acc:Segment10>").append(request.getSegment10() != null ? request.getSegment10() : DEFAULT_SEGMENT).append("</acc:Segment10>")
                    .append("<acc:LedgerName>").append(request.getLedgerName()).append("</acc:LedgerName>")
                    .append("<acc:EnabledFlag>").append(DEFAULT_ENABLED_FLAG).append("</acc:EnabledFlag>");


            soapRequest.append("</typ:validationInputRowList>");
        }

        soapRequest.append("</typ:validateAndCreateAccounts>")
                .append("</soapenv:Body>")
                .append("</soapenv:Envelope>");

        return soapRequest.toString();
    }

    private List<ValidationResultDO> parseResponse(String responseXml) {
        List<ValidationResultDO> results = new ArrayList<>();

        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new InputSource(new StringReader(responseXml)));

            NodeList resultNodes = doc.getElementsByTagName("ns2:result");

            for (int i = 0; i < resultNodes.getLength(); i++) {
                Element resultElement = (Element) resultNodes.item(i);
                ValidationResultDO result = new ValidationResultDO();

                result.setSegment1(getElementValue(resultElement, "ns1:Segment1"));
                result.setSegment2(getElementValue(resultElement, "ns1:Segment2"));
                result.setSegment3(getElementValue(resultElement, "ns1:Segment3"));
                result.setSegment4(getElementValue(resultElement, "ns1:Segment4"));
                result.setSegment5(getElementValue(resultElement, "ns1:Segment5"));
                result.setSegment6(getElementValue(resultElement, "ns1:Segment6"));
                result.setSegment7(getElementValue(resultElement, "ns1:Segment7"));
                result.setSegment8(getElementValue(resultElement, "ns1:Segment8"));
                result.setSegment9(getElementValue(resultElement, "ns1:Segment9"));
                result.setSegment10(getElementValue(resultElement, "ns1:Segment10"));
                result.setLedgerName(getElementValue(resultElement, "ns1:LedgerName"));
                result.setStatus(getElementValue(resultElement, "ns1:Status"));
                result.setCcId(getElementValue(resultElement, "ns1:CcId"));
                result.setError(getElementValue(resultElement, "ns1:Error"));
                result.setErrorCode(getElementValue(resultElement, "ns1:ErrorCode"));
                result.setFromDate(getElementValue(resultElement, "ns1:FromDate"));

                results.add(result);
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to parse SOAP response", e);
        }

        return results;
    }

    private String getElementValue(Element parent, String tagName) {
        NodeList nodes = parent.getElementsByTagName(tagName);
        if (nodes.getLength() > 0) {
            return nodes.item(0).getTextContent();
        }
        return null;
    }

}
