package com.xiaoju.corebanking.erp.adaptor.service.fusion;

import com.alibaba.fastjson.JSONObject;
import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.common.domain.FusionStatusResponse;
import com.xiaoju.corebanking.erp.adaptor.common.enums.FusionStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.errorno.ErpAdaptorErrorNo;
import com.xiaoju.godson.http.entity.HttpClientConnection;
import com.xiaoju.godson.http.entity.HttpClientResponse;
import com.xiaoju.godson.http.util.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class FusionStatusService {


    @Value("${fusion.url.common-url}")
    private String statusUrl;

    ConcurrentHashMap<String, Boolean> map = new ConcurrentHashMap<>();
    public ConcurrentHashMap<String, Boolean> queryFusionOpenStatus(String periodName, String ledgerId, String token) {

        if(Objects.nonNull(map.get(periodName))){
            return map;
        }
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(statusUrl).append(CommonConstant.STATUSAPI).append(CommonConstant.Q).append(CommonConstant.APPLICATION).append(CommonConstant.LEDGERID).append(ledgerId).append(";");

        stringBuilder.append("PeriodNameId=").append(periodName).append("&fields=ClosingStatus&onlyData=true&links=canonical");
        log.info("queryFusionCloseStatus stringBuilder:{}", stringBuilder);
        HttpClientConnection httpClientConnection = HttpClientUtil.get(stringBuilder.toString());
        httpClientConnection.addHeader("Authorization", "Bearer "+token);
        HttpClientResponse response;
        try {
            response = httpClientConnection.execute();
            if (response.isSuccess()) {
                JSONObject jsonObject = JSONObject.parseObject(response.getString());
                log.info("queryFusionCloseStatus result:{}", jsonObject);
                FusionStatusResponse javaObject = JSONObject.toJavaObject(jsonObject, FusionStatusResponse.class);
                log.info("queryFusionCloseStatus javaObject result:{}", javaObject);
                if(FusionStatusEnum.getCloseStatus(javaObject.getItems().get(0).getClosingStatus())) {
                    map.put(periodName,false);
                }else {
                    map.put(periodName,true);
                }
                return map;
            }
        } catch (IOException e) {
            log.error("queryFusionCloseStatus errorNo:{}", ErpAdaptorErrorNo.SYSTEM_ERROR_DEFAULT_FAIL,e);
        }
        log.info("queryFusionCloseStatus result fail");
        return map;
    }

}
