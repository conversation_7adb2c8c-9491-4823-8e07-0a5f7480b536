package com.xiaoju.corebanking.erp.adaptor.service.file.enums;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 * @date 2025/5/22 15:04
 */
@Slf4j
public enum SafiFieldEnum {
    DETALLE_POLIZA_ID("detallepolizaid", (entity, value) -> entity.setDetallePolizaId(Long.parseLong(value.trim()))),
    SOURCE_SYS("sourcesys", (entity, value) -> entity.setSourceSys(value.trim())),
    EMPRESA_ID("empresaid", (entity, value) -> entity.setEmpresaId(Long.parseLong(value.trim()))),
    POLIZA_ID("polizaid", (entity, value) -> entity.setPolizaId(Long.parseLong(value.trim()))),
    FECHA("fecha", (entity, value) -> entity.setFecha(value.trim())),
    CENTRO_COSTO_ID("centrocostoid", (entity, value) -> entity.setCentroCostoId(Long.parseLong(value.trim()))),
    CUENTA_COMPLETA("cuentacompleta", (entity, value) -> entity.setCuentaCompleta(value.trim())),
    INSTRUMENTO("instrumento", (entity, value) -> entity.setInstrumento(Long.parseLong(value.trim()))),
    MONEDA_ID("monedaid", (entity, value) -> entity.setMonedaId(Long.parseLong(value.trim()))),
    CARGOS("cargos", (entity, value) -> entity.setCargos(new BigDecimal(value.trim()))),
    ABONOS("abonos", (entity, value) -> entity.setAbonos(new BigDecimal(value.trim()))),
    DESCRIPCION("descripcion", (entity, value) -> entity.setDescripcion(value.trim())),
    REFERENCIA("referencia", (entity, value) -> {
        String v = value.trim();
        entity.setReferencia(v.length() > 50 ? v.substring(0, 49) : v);
    }),
    PROCEDIMIENTO_CONT("procedimientocont", (entity, value) -> entity.setProcedimientoCont(value.trim())),
    TIPO_INSTRUMENTO_ID("tipoinstrumentoid", (entity, value) -> entity.setTipoInstrumentoId(value.trim())),
    RFC("rfc", (entity, value) -> entity.setRfc(value.trim())),
    TOTAL_FACTURA("totalfactura", (entity, value) -> entity.setTotalFactura(new BigDecimal(value.trim()))),
    FOLIO_UUID("foliouuid", (entity, value) -> entity.setFolioUuid(value.trim())),
    USUARIO("usuario", (entity, value) -> entity.setUsuario(Long.parseLong(value.trim()))),
    FECHA_ACTUAL("fechaactual", (entity, value) -> entity.setFechaActual(value.trim())),
    DIRECCION_IP("direccionip", (entity, value) -> entity.setDireccionIp(value.trim())),
    PROGRAMA_ID("programaid", (entity, value) -> entity.setProgramaId(value.trim())),
    SUCURSAL("sucursal", (entity, value) -> entity.setSucursal(Long.parseLong(value.trim()))),
    NUM_TRANSACCION("numtransaccion", (entity, value) -> entity.setNumTransaccion(Long.parseLong(value.trim()))),
    CURRENCY_RATE("currencyrate", (entity, value) -> entity.setCurrencyConversionRate(new BigDecimal(value.trim())))

    ;

    private final String fieldName;
    private final BiConsumer<SofiGlInterfaceDO, String> setter;
    private static final Map<String, SafiFieldEnum> NAME_MAP = new HashMap<>();

    static {
        for (SafiFieldEnum field : values()) {
            NAME_MAP.put(field.fieldName, field);
        }
    }

    SafiFieldEnum(String fieldName, BiConsumer<SofiGlInterfaceDO, String> setter) {
        this.fieldName = fieldName;
        this.setter = setter;
    }

    public static void setField(SofiGlInterfaceDO entity, String fieldName, String value) {
        if (entity == null || fieldName == null || value == null) {
            return;
        }

        String normalizedField = fieldName.replaceAll("\\p{C}", "").toLowerCase().replace("_", "");
        SafiFieldEnum field = NAME_MAP.get(normalizedField);

        if (field != null) {
            try {
                if (!value.trim().isEmpty()) {
                    field.setter.accept(entity, value);
                }
            } catch (NumberFormatException e) {
                log.error("SafiFieldEnum{}的值{}失败: {}", fieldName, value, e.getMessage());
            } catch (Exception e) {
                log.error("SafiFieldEnum{}的值{}失败: {}", fieldName, value, e.getMessage());
            }
        }
    }
}
