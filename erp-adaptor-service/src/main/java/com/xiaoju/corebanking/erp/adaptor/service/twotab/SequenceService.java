package com.xiaoju.corebanking.erp.adaptor.service.twotab;

import com.xiaoju.corebanking.erp.adaptor.common.exception.AccengException;
import com.xiaoju.corebanking.erp.adaptor.repository.impl.SequenceDao;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SequenceCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

@Service
@Slf4j
public class SequenceService {
    @Resource
    private SequenceDao sequenceDao;

    /**
     * 不用缓存，每次从数据库拿新的，多节点用缓存不准确
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW,rollbackFor = AccengException.class)
    public long nextval(String sequenceName) {
        try {
            // 每次直接从数据库获取一个值
            Long current = sequenceDao.getCurrentValueWithLock(sequenceName);
            if (current == null) {
                throw new AccengException("序列不存在: " + sequenceName);
            }

            // 递增1并更新
            sequenceDao.updateValue(sequenceName, current + 1);
            return current;
        } catch (Exception e) {
            log.error("获取序列失败", e);
            throw new AccengException(e);
        }
    }
    /**
     * 创建新序列
     */
    public void createSequence(String sequenceName, long startValue) {
        if (Objects.isNull(sequenceDao.getCurrentValue(sequenceName))) {
            sequenceDao.createSequence(sequenceName, startValue, 20);
        } else {
            throw new AccengException("序列已经存在");
        }
    }

    /**
     * 更新序列
     */
    public void updateBatchSize(String sequenceName, int batchSize) {
        if (Objects.nonNull(sequenceDao.getCurrentValue(sequenceName))) {
            sequenceDao.updateBatchSize(sequenceName, batchSize);
        } else {
            throw new AccengException("序列不存在");
        }
    }
}
