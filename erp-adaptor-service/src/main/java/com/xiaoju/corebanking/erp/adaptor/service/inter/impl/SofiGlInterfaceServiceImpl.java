package com.xiaoju.corebanking.erp.adaptor.service.inter.impl;

import com.github.pagehelper.PageInfo;
import com.xiaoju.corebanking.erp.adaptor.repository.CoaRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceHisRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.converter.SofiGlInterfaceModelConverter;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.CargosAndAbonosDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiEmailDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderQueryDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.ValidationRequestDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.ValidationResultDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfacePO;
import com.xiaoju.corebanking.erp.adaptor.service.fusion.CoaValidationService;
import com.xiaoju.corebanking.erp.adaptor.service.inter.SofiGlInterfaceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class SofiGlInterfaceServiceImpl implements SofiGlInterfaceService {
    @Resource
    private SofiGlInterfaceRepository sofiGlInterfaceRepository;

    @Resource
    private SofiGlInterfaceHisRepository sofiGlInterfaceHisRepository;

    @Resource
    private SofiGlInterfaceModelConverter sofiGlInterfaceModelConverter;

    @Resource
    private CoaValidationService coaValidationService;

    @Resource
    private CoaRepository coaRepository;

    @Override
    public void updateInterface(SofiGlInterfaceDO sofiGlInterfaceDO, SofiGlInterfaceDO update) {
        sofiGlInterfaceRepository.updateInterface(sofiGlInterfaceDO, update);
    }

    @Override
    public void updateInterfaceByPolizaId(SofiGlInterfaceDO sofiGlInterfaceDO, SofiGlInterfaceDO update) {
        sofiGlInterfaceRepository.updateInterfaceByPolizaId(sofiGlInterfaceDO, update);
    }

    @Override
    public List<SofiGlInterfaceDO> queryByPolizaId(List<Long> externalReference) {
        return sofiGlInterfaceRepository.selectSofiGlInterfaceByExternalReference(externalReference);
    }

    @Override
    public List<SofiGlInterfaceDO> queryByPolizaIdsAndProcessDay(List<String> externalReferenceList, String processDay) {
        return sofiGlInterfaceRepository.selectSofiGlInterfaceByPolizaIdsAndProcessDay(externalReferenceList, processDay);
    }

    @Override
    public SofiGlInterfaceDO selectByPolizaIdAndDetalle(Long polizaId, Long detallePolizaId) {
        List<SofiGlInterfacePO> sofiGlInterfacePOS = sofiGlInterfaceRepository.selectByPolizaIdAndDetalle(polizaId, detallePolizaId);
        if (sofiGlInterfacePOS == null || sofiGlInterfacePOS.isEmpty()) {
            return null;
        }
        return sofiGlInterfaceModelConverter.convert(sofiGlInterfacePOS.get(0));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SofiGlInterfaceDO updateInterfaceWithBackup(Long polizaId, Long detallePolizaId, SofiGlInterfaceDO update) {
        Assert.notNull(polizaId, "polizaId不能为空");
        Assert.notNull(detallePolizaId, "detallePolizaId不能为空");
        Assert.notNull(update, "更新数据不能为空");

        List<SofiGlInterfacePO> oldList = sofiGlInterfaceRepository.selectByPolizaIdAndDetalle(polizaId, detallePolizaId);
        if (oldList == null || oldList.isEmpty()) {
            throw new IllegalArgumentException("未找到原始数据，无法备份和更新");
        }
        SofiGlInterfacePO oldPO = oldList.get(0);

        // 备份his
        sofiGlInterfaceHisRepository.backupInterfaceHis(Collections.singletonList(oldPO));

        // 更新Interface
        SofiGlInterfaceDO oldDO = sofiGlInterfaceModelConverter.convert(oldPO);
        sofiGlInterfaceRepository.updateInterface(oldDO, update);

        List<SofiGlInterfacePO> newList = sofiGlInterfaceRepository.selectByPolizaIdAndDetalle(polizaId, detallePolizaId);
        if (newList == null || newList.isEmpty()) {
            return null;
        }
        return sofiGlInterfaceModelConverter.convert(newList.get(0));
    }

    @Override
    public List<Long> queryGroupId(SofiGlInterfaceHeaderQueryDO sofiGlInterfaceHeaderQueryDO) {
        return sofiGlInterfaceRepository.queryGroupId(sofiGlInterfaceHeaderQueryDO);
    }

    @Override
    public List<SofiGlInterfaceDO> querySofiGlInterface(SofiGlInterfaceDO sofiGlInterfaceDO) {
        return sofiGlInterfaceRepository.queryGLInterfaceData(sofiGlInterfaceDO);
    }

    @Override
    public CargosAndAbonosDO queryCargosAndAbonosDO(Long polizaId) {
        return sofiGlInterfaceRepository.selectCargosAndAbonosDO(polizaId);
    }

    @Override
    public List<SofiEmailDO> queryCountInterfaceEmailData(String processDay) {
        return sofiGlInterfaceRepository.selectCountInterfaceEmailData(processDay);
    }

    @Override
    public List<SofiGlInterfaceDO> querySofiGlInterfaceByGroupIdAndProcessDay(Long groupId, String processDay) {
        return sofiGlInterfaceRepository.selectSofiGlInterfaceByGroupIdAndProcessDay(groupId, processDay);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void validateCoa(String processDay) {
        log.info("validateCoa start");
        int pageNum = 1;
        int pageSize = 5000; //分页分大点
        int totalUpdated = 0;
        boolean hasData = false;
        PageInfo<ValidationRequestDO> pageInfo;
        do {
            pageInfo = coaRepository.pagingSelectCoaList(processDay, pageNum, pageSize);
            List<ValidationRequestDO> coaList = pageInfo.getList();

            if (CollectionUtils.isEmpty(coaList)) {
                break;
            }

            hasData = true;

            List<ValidationResultDO> validationResultDOS = coaValidationService.validateCoa(coaList);

            for (ValidationResultDO validationResult : validationResultDOS) {
                String processStatus = "Invalid".equals(validationResult.getStatus())
                        ? "MAP_FAILED" : "MAPPED";
                //回写sofi_gl_interface
                //虽然是按照COA维度更新，但里面是按照ID更新
                int linesUpdated = coaRepository.updateCoaStatusByIdBatch(
                        processDay,
                        validationResult,
                        processStatus,
                        validationResult.getError()
                );
                log.info("1.validate Coa，分页更新sofi_gl_interface条数 {} @{}", linesUpdated, processDay);

                totalUpdated += linesUpdated;

            }
            pageNum++;
        } while (pageInfo.isHasNextPage());

        log.info("1.validate Coa，总更新sofi_gl_interface_header条数 {} @{}", totalUpdated, processDay);

        int headersUpdated = 0;
        if (hasData) {
            headersUpdated = coaRepository.updateSafiHeaderStatus(processDay);
        }

        log.info("2.validate Coa，更新sofi_gl_interface_header条数 {} @{}", headersUpdated, processDay);

    }

}
