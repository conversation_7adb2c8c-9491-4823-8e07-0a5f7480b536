package com.xiaoju.corebanking.erp.adaptor.service.file.enums;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 * @date 2025/7/3
 */
@Slf4j
public enum ShenmaFieldEnum {
    LINK_REFERENCE("linkreference", (entity, value) -> entity.setLinkReference(value.trim())),
    SOURCE_BRANCH("sourcebranch", (entity, value) -> entity.setSourceBranch(value.trim())),
    CCY("ccy", (entity, value) -> entity.setCcy(value.trim())),
    GL_CODE("glcode", (entity, value) -> entity.setGlCode(value.trim())),
    CR_DR_IND("crdrind", (entity, value) -> entity.setCrDrInd(value.trim())),
    ENTERED_DEBIT_AMOUNT("entereddebitamount", (entity, value) -> {
        if (!value.trim().isEmpty()) {
            entity.setEnteredDebitAmount(new BigDecimal(value.trim()));
        }
    }),
    ENTERED_CREDIT_AMOUNT("enteredcreditamount", (entity, value) -> {
        if (!value.trim().isEmpty()) {
            entity.setEnteredCreditAmount(new BigDecimal(value.trim()));
        }
    }),
    PROFIT_CENTER("profitcenter", (entity, value) -> entity.setProfitCenter(value.trim())),
    CLIENT_TYPE("clienttype", (entity, value) -> entity.setClientType(value.trim())),
    AMT_TYPE("amttype", (entity, value) -> entity.setAmtType(value.trim())),
    TRAN_TYPE("trantype", (entity, value) -> entity.setTranType(value.trim())),
    EVENT_TYPE("eventtype", (entity, value) -> entity.setEventType(value.trim())),
    PROD_TYPE("prodtype", (entity, value) -> entity.setProdType(value.trim())),
    POST_DATE("postdate", (entity, value) -> entity.setPostDate(value.trim())),
    INTERCOMPANY("intercompany", (entity, value) -> entity.setIntercompany(value.trim())),
    FLAT_RATE("flatrate", (entity, value) -> {
        if (!value.trim().isEmpty()) {
            entity.setFlatRate(new BigDecimal(value.trim()));
        }
    }),
    CUST_RATE("custrate", (entity, value) -> {
        if (!value.trim().isEmpty()) {
            entity.setCustRate(new BigDecimal(value.trim()));
        }
    }),
    INLAND_OFFSHORE("inlandoffshore", (entity, value) -> entity.setInlandOffshore(value.trim())),
    CLIENT_NO("clientno", (entity, value) -> entity.setClientNo(value.trim())),
    SEQ_NO("seqno", (entity, value) -> entity.setSeqNo(value.trim())),
    SYSTEM_ID("systemid", (entity, value) -> entity.setSystemId(value.trim())),
    COMPANY("company", (entity, value) -> entity.setCompany(value.trim())),
    GROUP_CLIENT("groupclient", (entity, value) -> entity.setGroupClient(value.trim())),
    VOUCHER_GROUP("vouchergroup", (entity, value) -> entity.setVoucherGroup(value.trim()));

    private final String fieldName;
    private final BiConsumer<SofiGlShenmaDO, String> setter;
    private static final Map<String, ShenmaFieldEnum> NAME_MAP = new HashMap<>();

    static {
        for (ShenmaFieldEnum field : values()) {
            NAME_MAP.put(field.fieldName, field);
        }
    }

    ShenmaFieldEnum(String fieldName, BiConsumer<SofiGlShenmaDO, String> setter) {
        this.fieldName = fieldName;
        this.setter = setter;
    }

    public static void setField(SofiGlShenmaDO entity, String fieldName, String value) {
        if (entity == null || fieldName == null || value == null) {
            return;
        }

        String normalizedField = fieldName.replaceAll("\\p{C}", "").toLowerCase().replace("_", "");
        ShenmaFieldEnum field = NAME_MAP.get(normalizedField);

        if (field != null) {
            try {
                if (!value.trim().isEmpty()) {
                    field.setter.accept(entity, value);
                }
            } catch (NumberFormatException e) {
                log.error("ShenmaFieldEnum转换字段{}的值{}失败: {}", fieldName, value, e.getMessage());
            } catch (Exception e) {
                log.error("ShenmaFieldEnum设置字段{}的值{}失败: {}", fieldName, value, e.getMessage());
            }
        } else {
            log.warn("ShenmaFieldEnum未找到字段映射: {}", fieldName);
        }
    }
}