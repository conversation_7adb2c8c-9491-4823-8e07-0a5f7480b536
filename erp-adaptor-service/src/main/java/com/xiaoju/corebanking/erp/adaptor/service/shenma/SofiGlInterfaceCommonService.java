package com.xiaoju.corebanking.erp.adaptor.service.shenma;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceCommonDO;

import java.util.List;

public interface SofiGlInterfaceCommonService {
    void insertSelective(SofiGlInterfaceCommonDO sofiGlInterfaceCommonDO);
    void batchInsert(List<SofiGlInterfaceCommonDO> list);
    List<Long> queryGroupIds(Integer pageNum,Integer pageSize,String processDay);

    /**
     * 根据外部引用列表和处理日期查询通用接口数据
     */
    List<SofiGlInterfaceCommonDO> queryByReference5AndProcessDay(List<String> externalReferenceList, String processDay);

    /**
     * 更新通用接口数据
     */
    void updateInterfaceCommon(SofiGlInterfaceCommonDO original, SofiGlInterfaceCommonDO update);
}
