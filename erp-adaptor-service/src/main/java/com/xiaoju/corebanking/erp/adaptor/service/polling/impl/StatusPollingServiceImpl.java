package com.xiaoju.corebanking.erp.adaptor.service.polling.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.enums.RequestStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.enums.SourceSysEnum;
import com.xiaoju.corebanking.erp.adaptor.common.utils.ErpUtil;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceCommonDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePO;
import com.xiaoju.corebanking.erp.adaptor.service.fusion.FusionAuthService;
import com.xiaoju.corebanking.erp.adaptor.service.header.SofiGlInterfaceHeaderService;
import com.xiaoju.corebanking.erp.adaptor.service.inter.SofiGlInterfaceService;
import com.xiaoju.corebanking.erp.adaptor.service.polling.StatusPollingService;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlInterfaceCommonService;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlShenMaService;
import com.xiaoju.corebanking.erp.adaptor.service.twotab.TwoTabValueService;
import com.xiaoju.digitalbank.ddd.po.ExecResult;
import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import com.xiaoju.godson.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/21 16:34
 */
@Slf4j
@Service
public class StatusPollingServiceImpl implements StatusPollingService {

    @Value("${fusion.url.status-path}")
    private String statusApiPath;

    @Value("${fusion.url.journal-import-path}")
    private String journalImportApiPath;

    @Value("${fusion.jwt.username:www.didiglobal.com}")
    private String issuer;

    @Value("${fusion.polling.status-check.interval-ms:10000}")
    private long statusCheckIntervalMs;

    @Value("${fusion.polling.status-check.retry-interval-ms:5000}")
    private long statusCheckRetryIntervalMs;

    @Resource
    private SofiGlInterfaceHeaderService sofiGlInterfaceHeaderService;

    @Resource
    private SofiGlInterfaceService sofiGlInterfaceService;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private FusionAuthService fusionAuthService;

    @Resource
    private ErpUtil erpUtil;

    @Resource
    private TwoTabValueService twoTabValueService;

    @Resource
    private SofiGlShenMaService sofiGlShenMaService;

    @Resource
    private SofiGlInterfaceCommonService sofiGlInterfaceCommonService;

    @Resource(name = "statusPollingThreadPool")
    private ThreadPoolTaskExecutor executorService;

    @Override
    public ExecResult pollLoadRequestStatus(String systemCode, String param) {
        log.info("开始执行轮询load_request_id状态");

        String processDate;
        if (StringUtils.isEmpty(param)) {
            processDate = erpUtil.getDateDirFromInput("");
        } else {
            processDate = JsonUtil.toObject(param, String.class);
        }

        SofiGlInterfaceHeaderDO queryDO = new SofiGlInterfaceHeaderDO();
        queryDO.setProcessStatus(ProcessStatusEnum.LOADED);
        queryDO.setProcessDay(processDate);

        List<SofiGlInterfaceHeaderDO> headerList = sofiGlInterfaceHeaderService.queryHeaderData(queryDO, systemCode);
        if (CollectionUtils.isEmpty(headerList)) {
            log.info("没有需要轮询的load_request_id数据，任务结束");
            return ExecResult.error(CommonErrorNo.SERVER_ERROR, "没有需要轮询的load_request_id数据，任务结束");
        }

        // 处理数据
        ExecResult result = processHeaderList(headerList, systemCode);
        if (result != null) {
            return result;
        }
        return ExecResult.success();
    }

    @Override
    public ExecResult pollLoadRequestStatusAsync() {
        log.info("开始执行异步轮询load_request_id状态");

        SofiGlInterfaceHeaderDO queryDO = new SofiGlInterfaceHeaderDO();
        queryDO.setProcessStatus(ProcessStatusEnum.LOADED);
        queryDO.setProcessDay(erpUtil.getDateDirFromInput(""));

        List<SofiGlInterfaceHeaderDO> headerList = sofiGlInterfaceHeaderService.queryHeaderData(queryDO, SourceSysEnum.SAFI.getCode());
        if (CollectionUtils.isEmpty(headerList)) {
            log.info("没有需要轮询的load_request_id数据，任务结束");
            return ExecResult.error(CommonErrorNo.SERVER_ERROR, "没有需要轮询的load_request_id数据，任务结束");
        }

        // 异步处理数据
        ExecResult result = processHeaderListAsync(headerList);
        if (result != null) {
            return result;
        }
        return ExecResult.success();
    }

    private ExecResult processHeaderList(List<SofiGlInterfaceHeaderDO> headerList, String systemCode) {
        List<String> externalReferences = getExternalReferences(headerList);
        if (CollectionUtils.isEmpty(externalReferences)) {
            return null;
        }

        String processDay = headerList.get(0).getProcessDay();

        if (SourceSysEnum.SHEN_MA.getCode().equals(systemCode)) {
            return processHeaderListForShenma(headerList, externalReferences, processDay);
        } else {
            return processHeaderListForSafi(headerList, externalReferences, processDay, systemCode);
        }
    }

    /**
     * 处理SAFI系统的header列表
     */
    private ExecResult processHeaderListForSafi(List<SofiGlInterfaceHeaderDO> headerList, List<String> externalReferences, String processDay, String systemCode) {
        // 同时使用poliza_id和process_day查询
        List<SofiGlInterfaceDO> allInterfaces = sofiGlInterfaceService.queryByPolizaIdsAndProcessDay(externalReferences, processDay);
        if (CollectionUtils.isEmpty(allInterfaces)) {
            log.info("没有找到需要处理的Interface数据，polizaIds={}, processDay={}", externalReferences, processDay);
            return null;
        }

        Map<Long, List<SofiGlInterfaceDO>> groupByGroupId = allInterfaces.stream()
                .collect(Collectors.groupingBy(SofiGlInterfaceDO::getGroupId));

        // 按照groupId为纬度
        for (Map.Entry<Long, List<SofiGlInterfaceDO>> entry : groupByGroupId.entrySet()) {
            Long groupId = entry.getKey();
            List<SofiGlInterfaceDO> interfaceList = entry.getValue();

            if (CollectionUtils.isEmpty(interfaceList)) {
                continue;
            }

            SofiGlInterfaceDO representative = interfaceList.get(0);
            if (representative.getLoadRequestId() == null) {
                log.error("groupId={}对应的数据load_request_id为空，系统类型={}", groupId, systemCode);
                continue;
            }

            try {
                log.info("开始检查请求状态，groupId={}, loadRequestId={}", groupId, representative.getLoadRequestId());
                // 调用Fusion API获取状态
                String status = getRequestStatus(representative.getLoadRequestId());

                List<SofiGlInterfaceHeaderDO> affectedHeaders = headerList.stream()
                        .filter(header -> header.getGroupId().equals(groupId) &&
                                processDay.equals(header.getProcessDay()))
                        .collect(Collectors.toList());

                if (RequestStatusEnum.SUCCEEDED.getCode().equals(status)) {
                    // 调用Journal Import Process接口
                    Long importRequestId = callJournalImportProcess(representative);
                    if (importRequestId != null) {
                        for (SofiGlInterfaceHeaderDO headerDO : affectedHeaders) {
                            SofiGlInterfaceHeaderDO update = new SofiGlInterfaceHeaderDO();
                            update.setProcessStatus(ProcessStatusEnum.IMPORTED);
                            sofiGlInterfaceHeaderService.updateInterfaceHeader(headerDO, update, headerDO.getSystemCode());
                        }

                        for (SofiGlInterfaceDO item : interfaceList) {
                            SofiGlInterfaceDO update = new SofiGlInterfaceDO();
                            update.setImportRequestId(importRequestId);
                            update.setProcessStatus(ProcessStatusEnum.IMPORTED);
                            sofiGlInterfaceService.updateInterface(item, update);
                        }
                    }
                } else if (RequestStatusEnum.FAILED.getCode().equals(status)) {
                    for (SofiGlInterfaceHeaderDO headerDO : affectedHeaders) {
                        SofiGlInterfaceHeaderDO update = new SofiGlInterfaceHeaderDO();
                        update.setProcessStatus(ProcessStatusEnum.IMPORT_FAILED);
                        sofiGlInterfaceHeaderService.updateInterfaceHeader(headerDO, update, headerDO.getSystemCode());
                    }

                    for (SofiGlInterfaceDO item : interfaceList) {
                        SofiGlInterfaceDO update = new SofiGlInterfaceDO();
                        update.setProcessStatus(ProcessStatusEnum.IMPORT_FAILED);
                        sofiGlInterfaceService.updateInterface(item, update);
                    }
                } else {
                    log.info("请求状态为{}，不做处理，groupId={}, loadRequestId={}", status, groupId, representative.getLoadRequestId());
                }
            } catch (Exception e) {
                log.error("处理group_id={}状态异常，loadRequestId={}", groupId, representative.getLoadRequestId(), e);
            }
        }

        return null;
    }

    /**
     * 处理SHENMA系统的header列表
     */
    private ExecResult processHeaderListForShenma(List<SofiGlInterfaceHeaderDO> headerList, List<String> externalReferences, String processDay) {
        // 查询SHENMA-comm数据
        List<SofiGlInterfaceCommonDO> allCommonInterfaces = sofiGlInterfaceCommonService.queryByReference5AndProcessDay(externalReferences, processDay);
        if (CollectionUtils.isEmpty(allCommonInterfaces)) {
            log.info("没有找到需要处理的SHENMA Common数据，externalReferences={}, processDay={}", externalReferences, processDay);
            return null;
        }

        // 查询对应的SHENMA原始数据
        List<SofiGlShenmaDO> allShenmaInterfaces = sofiGlShenMaService.queryByPolizaIdsAndProcessDay(externalReferences, processDay);
        if (CollectionUtils.isEmpty(allShenmaInterfaces)) {
            log.info("没有找到需要处理的SHENMA原始数据，externalReferences={}, processDay={}", externalReferences, processDay);
            return null;
        }

        Map<Long, List<SofiGlInterfaceCommonDO>> groupByGroupId = allCommonInterfaces.stream()
                .collect(Collectors.groupingBy(SofiGlInterfaceCommonDO::getGroupId));

        Map<Long, List<SofiGlShenmaDO>> shenmaGroupByGroupId = allShenmaInterfaces.stream()
                .collect(Collectors.groupingBy(SofiGlShenmaDO::getGroupId));

        // 按照groupId为纬度处理
        for (Map.Entry<Long, List<SofiGlInterfaceCommonDO>> entry : groupByGroupId.entrySet()) {
            Long groupId = entry.getKey();
            List<SofiGlInterfaceCommonDO> commonList = entry.getValue();

            if (CollectionUtils.isEmpty(commonList)) {
                continue;
            }

            SofiGlInterfaceCommonDO representative = commonList.get(0);
            if (representative.getLoadRequestId() == null) {
                log.error("groupId={}对应的SHENMA Common数据load_request_id为空", groupId);
                continue;
            }

            try {
                log.info("开始检查SHENMA请求状态，groupId={}, loadRequestId={}", groupId, representative.getLoadRequestId());
                // 调用Fusion API获取状态
                String status = getRequestStatus(representative.getLoadRequestId());

                List<SofiGlInterfaceHeaderDO> affectedHeaders = headerList.stream()
                        .filter(header -> header.getGroupId().equals(groupId) &&
                                processDay.equals(header.getProcessDay()))
                        .collect(Collectors.toList());

                if (RequestStatusEnum.SUCCEEDED.getCode().equals(status)) {
                    // 调用Journal Import Process接口
                    Long importRequestId = callJournalImportProcessForCommon(representative);
                    if (importRequestId != null) {
                        // 更新Header数据
                        for (SofiGlInterfaceHeaderDO headerDO : affectedHeaders) {
                            SofiGlInterfaceHeaderDO update = new SofiGlInterfaceHeaderDO();
                            update.setProcessStatus(ProcessStatusEnum.IMPORTED);
                            sofiGlInterfaceHeaderService.updateInterfaceHeader(headerDO, update, headerDO.getSystemCode());
                        }

                        // 更新Common数据
                        for (SofiGlInterfaceCommonDO item : commonList) {
                            SofiGlInterfaceCommonDO update = new SofiGlInterfaceCommonDO();
                            update.setImportRequestId(importRequestId);
                            update.setProcessStatus(ProcessStatusEnum.IMPORTED);
                            sofiGlInterfaceCommonService.updateInterfaceCommon(item, update);
                        }

                        // 更新对应的ShenMa原始数据
                        List<SofiGlShenmaDO> shenmaList = shenmaGroupByGroupId.get(groupId);
                        if (!CollectionUtils.isEmpty(shenmaList)) {
                            for (SofiGlShenmaDO shenmaItem : shenmaList) {
                                SofiGlShenmaDO shenmaUpdate = new SofiGlShenmaDO();
                                shenmaUpdate.setProcessStatus(ProcessStatusEnum.IMPORTED);
                                sofiGlShenMaService.updateShenmaInterfaceByReference(shenmaItem, shenmaUpdate);
                            }
                        }
                    }
                } else if (RequestStatusEnum.FAILED.getCode().equals(status)) {
                    // 更新Header数据
                    for (SofiGlInterfaceHeaderDO headerDO : affectedHeaders) {
                        SofiGlInterfaceHeaderDO update = new SofiGlInterfaceHeaderDO();
                        update.setProcessStatus(ProcessStatusEnum.IMPORT_FAILED);
                        sofiGlInterfaceHeaderService.updateInterfaceHeader(headerDO, update, headerDO.getSystemCode());
                    }

                    // 更新Common数据
                    for (SofiGlInterfaceCommonDO item : commonList) {
                        SofiGlInterfaceCommonDO update = new SofiGlInterfaceCommonDO();
                        update.setProcessStatus(ProcessStatusEnum.IMPORT_FAILED);
                        sofiGlInterfaceCommonService.updateInterfaceCommon(item, update);
                    }

                    // 更新对应的ShenMa原始数据
                    List<SofiGlShenmaDO> shenmaList = shenmaGroupByGroupId.get(groupId);
                    if (!CollectionUtils.isEmpty(shenmaList)) {
                        for (SofiGlShenmaDO shenmaItem : shenmaList) {
                            SofiGlShenmaDO shenmaUpdate = new SofiGlShenmaDO();
                            shenmaUpdate.setProcessStatus(ProcessStatusEnum.IMPORT_FAILED);
                            sofiGlShenMaService.updateShenmaInterfaceByReference(shenmaItem, shenmaUpdate);
                        }
                    }
                } else {
                    log.info("SHENMA请求状态为{}，不做处理，groupId={}, loadRequestId={}", status, groupId, representative.getLoadRequestId());
                }
            } catch (Exception e) {
                log.error("处理SHENMA group_id={}状态异常，loadRequestId={}", groupId, representative.getLoadRequestId(), e);
            }
        }

        return null;
    }

    /**
     * 异步处理header列表
     * @param headerList header列表数据
     * @return 执行结果
     */
    private ExecResult processHeaderListAsync(List<SofiGlInterfaceHeaderDO> headerList) {
        List<String> externalReferences = getExternalReferences(headerList);
        if (CollectionUtils.isEmpty(externalReferences)) {
            return null;
        }

        String processDay = headerList.get(0).getProcessDay();

        // 同时使用poliza_id和process_day查询
        List<SofiGlInterfaceDO> allInterfaces = sofiGlInterfaceService.queryByPolizaIdsAndProcessDay(externalReferences, processDay);
        if (CollectionUtils.isEmpty(allInterfaces)) {
            log.info("没有找到需要处理的Interface数据，polizaIds={}, processDay={}", externalReferences, processDay);
            return null;
        }

        Map<Long, List<SofiGlInterfaceDO>> groupByGroupId = allInterfaces.stream()
                .collect(Collectors.groupingBy(SofiGlInterfaceDO::getGroupId));

        // 使用CompletableFuture异步处理每个组
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (Map.Entry<Long, List<SofiGlInterfaceDO>> entry : groupByGroupId.entrySet()) {
            Long groupId = entry.getKey();
            List<SofiGlInterfaceDO> interfaceList = entry.getValue();

            if (CollectionUtils.isEmpty(interfaceList)) {
                continue;
            }

            SofiGlInterfaceDO representative = interfaceList.get(0);
            if (representative.getLoadRequestId() == null) {
                log.error("groupId={}对应的数据load_request_id为空", groupId);
                continue;
            }

            // 创建异步任务处理每个组，使用自定义线程池
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    log.info("开始异步检查请求状态，groupId={}, loadRequestId={}", groupId, representative.getLoadRequestId());

                    // 异步轮询直到状态为SUCCEEDED或FAILED
                    String status = pollRequestStatusAsync(representative.getLoadRequestId()).join();

                    List<SofiGlInterfaceHeaderDO> affectedHeaders = headerList.stream()
                            .filter(header -> header.getGroupId().equals(groupId) &&
                                    processDay.equals(header.getProcessDay()))
                            .collect(Collectors.toList());

                    if (RequestStatusEnum.SUCCEEDED.getCode().equals(status)) {
                        // 调用Journal Import Process接口
                        Long importRequestId = callJournalImportProcess(representative);
                        if (importRequestId != null) {
                            for (SofiGlInterfaceHeaderDO headerDO : affectedHeaders) {
                                SofiGlInterfaceHeaderDO update = new SofiGlInterfaceHeaderDO();
                                update.setProcessStatus(ProcessStatusEnum.IMPORTED);
                                sofiGlInterfaceHeaderService.updateInterfaceHeader(headerDO, update, headerDO.getSystemCode());
                            }

                            for (SofiGlInterfaceDO item : interfaceList) {
                                SofiGlInterfaceDO update = new SofiGlInterfaceDO();
                                update.setImportRequestId(importRequestId);
                                update.setProcessStatus(ProcessStatusEnum.IMPORTED);
                                sofiGlInterfaceService.updateInterface(item, update);
                            }
                        }
                    } else if (RequestStatusEnum.FAILED.getCode().equals(status)) {
                        for (SofiGlInterfaceHeaderDO headerDO : affectedHeaders) {
                            SofiGlInterfaceHeaderDO update = new SofiGlInterfaceHeaderDO();
                            update.setProcessStatus(ProcessStatusEnum.IMPORT_FAILED);
                            sofiGlInterfaceHeaderService.updateInterfaceHeader(headerDO, update, headerDO.getSystemCode());
                        }

                        for (SofiGlInterfaceDO item : interfaceList) {
                            SofiGlInterfaceDO update = new SofiGlInterfaceDO();
                            update.setProcessStatus(ProcessStatusEnum.IMPORT_FAILED);
                            sofiGlInterfaceService.updateInterface(item, update);
                        }
                    } else {
                        log.info("请求状态为{}，不做处理，groupId={}, loadRequestId={}", status, groupId, representative.getLoadRequestId());
                    }
                } catch (Exception e) {
                    log.error("异步处理group_id={}状态异常，loadRequestId={}", groupId, representative.getLoadRequestId(), e);
                }
            }, executorService);

            futures.add(future);
        }

        // 等待所有异步任务完成
        try {
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allFutures.join();
        } catch (Exception e) {
            log.error("等待异步任务完成时发生异常", e);
            return ExecResult.error(CommonErrorNo.SERVER_ERROR, "处理数据时发生异常: " + e.getMessage());
        }

        return null;
    }

    /**
     * 异步轮询请求状态，直到状态为SUCCEEDED或FAILED
     *
     * @param loadRequestId 请求ID
     * @return 完成状态的CompletableFuture
     */
    private CompletableFuture<String> pollRequestStatusAsync(Long loadRequestId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                String status = null;
                // 轮询直到获取最终状态
                while (true) {
                    status = getRequestStatus(loadRequestId);
                    if (status == null) {
                        // 如果获取状态失败，等待一段时间后重试
                        log.warn("获取状态失败，loadRequestId={}，等待后重试", loadRequestId);
                        Thread.sleep(statusCheckRetryIntervalMs);
                        continue;
                    }

                    // 下面这个都是最终状态，则结束轮询
                    if (RequestStatusEnum.SUCCEEDED.getCode().equals(status)
                            || RequestStatusEnum.FAILED.getCode().equals(status)
                            || RequestStatusEnum.WARNING.getCode().equals(status)
                            || RequestStatusEnum.CANCELLED.getCode().equals(status)) {
                        log.info("获取到最终状态: {}, loadRequestId={}", status, loadRequestId);
                        break;
                    }

                    // 否则等待一段时间后继续轮询
                    log.debug("状态仍在处理中: {}, loadRequestId={}, 继续轮询", status, loadRequestId);
                    Thread.sleep(statusCheckIntervalMs);
                }
                return status;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("轮询状态被中断，loadRequestId={}", loadRequestId, e);
                throw new RuntimeException("轮询状态被中断", e);
            } catch (Exception e) {
                log.error("轮询状态发生异常，loadRequestId={}", loadRequestId, e);
                throw new RuntimeException("轮询状态发生异常", e);
            }
        }, executorService);
    }

    private List<String> getExternalReferences(List<SofiGlInterfaceHeaderDO> headerList) {
        return headerList.stream()
                .map(SofiGlInterfaceHeaderDO::getExternalReference)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 获取请求状态
     */
    private String getRequestStatus(Long loadRequestId) {
        try {
            HttpHeaders headers = createHeaders();

            // 发送请求
            HttpEntity<String> entity = new HttpEntity<>(null, headers);

            String url = UriComponentsBuilder.fromHttpUrl(statusApiPath)
                    .queryParam("finder", String.format("ESSJobStatusRF;requestId=%d", loadRequestId))
                    .queryParam("fields", "RequestStatus")
                    .queryParam("onlyData", "true")
                    .queryParam("links", "canonical")
                    .build(false)
                    .toUriString();
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                String responseBody = response.getBody();
                log.debug("状态查询接口返回: {}", responseBody);

                // 解析响应
                JSONObject jsonResponse = JSON.parseObject(responseBody);
                JSONArray items = jsonResponse.getJSONArray("items");
                if (items != null && !items.isEmpty()) {
                    JSONObject item = items.getJSONObject(0);
                    return item.getString("RequestStatus");
                }
            } else {
                log.error("调用状态查询接口失败，HTTP状态码: {}, loadRequestId={}", response.getStatusCode(), loadRequestId);
            }
            return null;
        } catch (Exception e) {
            log.error("调用状态查询接口异常，loadRequestId={}", loadRequestId, e);
            return null;
        }
    }

    /**
     * 调用Journal Import Process接口
     */
    public Long callJournalImportProcess(SofiGlInterfaceDO interfaceDO) {
        try {
            log.debug("调用Journal Import Process接口，polizaId={}, ledgerId={}, groupId={}",
                    interfaceDO.getPolizaId(), interfaceDO.getLedgerId(), interfaceDO.getGroupId());

            HttpHeaders headers = createHeaders();
            headers.add("Content-Type", "application/json");

            //取公司mapping
            List<CuxTwoTabValuePO> companyMappings
                    = twoTabValueService.selectSimpleByFormCode(CommonConstant.COMPANY_LEDGER_MAPPINGS);


            //公司+accessSet map
            Map<String, Long> companyDataAccessSetMap = companyMappings.stream()
                    .filter(po -> StringUtils.isNotEmpty(po.getValue5()) && StringUtils.isNotEmpty(po.getValue7()))
                    .collect(Collectors.toMap(
                            CuxTwoTabValuePO::getValue5,
                            po -> Long.parseLong(po.getValue7()),
                            (existing, replacement) -> existing
                    ));
            //根据fusion公司段取accessSetId
            Long accessSetId = companyDataAccessSetMap.get(interfaceDO.getSegment1());

            if (accessSetId == null) {
                log.error("二维表COMPANY_LEDGER_MAPPINGS没有维护数据访问权限集，公司段={}", interfaceDO.getSegment1());
                throw new RuntimeException("二维表COMPANY_LEDGER_MAPPINGS没有维护数据访问权限集，公司段=" + interfaceDO.getSegment1());
            }

            JSONObject requestParams = new JSONObject();
            requestParams.put("OperationName", "submitESSJobRequest");
            requestParams.put("JobPackageName", "oracle/apps/ess/financials/generalLedger/programs/common");
            requestParams.put("JobDefName", "JournalImportLauncher");
            requestParams.put("ESSParameters", String.format("%d,%s,%d,%d,%s,%s,%s",
                    accessSetId,
                    interfaceDO.getJournalSourceName(),
                    interfaceDO.getLedgerId(),
                    interfaceDO.getGroupId(),
                    "N",
                    "N",
                    "O"));

            // 发送请求
            HttpEntity<String> entity = new HttpEntity<>(requestParams.toJSONString(), headers);
            ResponseEntity<String> response = restTemplate.exchange(journalImportApiPath, HttpMethod.POST, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                String responseBody = response.getBody();
                log.debug("Journal Import接口返回: {}", responseBody);

                JSONObject jsonResponse = JSON.parseObject(responseBody);
                String reqstId = jsonResponse.getString("ReqstId");
                if (StringUtils.isNotEmpty(reqstId)) {
                    return Long.valueOf(reqstId);
                }
            } else {
                log.error("调用Journal Import接口失败，HTTP状态码: {}, polizaId={}", response.getStatusCode(), interfaceDO.getPolizaId());
            }

            return null;
        } catch (Exception e) {
            log.error("调用Journal Import接口异常，polizaId={}", interfaceDO.getPolizaId(), e);
            return null;
        }
    }

    /**
     * 调用Journal Import Process接口
     */
    public Long callJournalImportProcessForCommon(SofiGlInterfaceCommonDO commonDO) {
        try {
            log.debug("调用Journal Import Process接口 (Common)，reference5={}, ledgerId={}, groupId={}",
                    commonDO.getReference5(), commonDO.getLedgerId(), commonDO.getGroupId());

            HttpHeaders headers = createHeaders();
            headers.add("Content-Type", "application/json");

            //取公司mapping
            List<CuxTwoTabValuePO> companyMappings
                    = twoTabValueService.selectSimpleByFormCode(CommonConstant.COMPANY_LEDGER_MAPPINGS);


            //公司+accessSet map
            Map<String, Long> companyDataAccessSetMap = companyMappings.stream()
                    .filter(po -> StringUtils.isNotEmpty(po.getValue5()) && StringUtils.isNotEmpty(po.getValue7()))
                    .collect(Collectors.toMap(
                            CuxTwoTabValuePO::getValue5,
                            po -> Long.parseLong(po.getValue7()),
                            (existing, replacement) -> existing
                    ));
            //根据fusion公司段取accessSetId
            Long accessSetId = companyDataAccessSetMap.get(commonDO.getSegment1());

            if (accessSetId == null) {
                log.error("二维表COMPANY_LEDGER_MAPPINGS没有维护数据访问权限集，公司段={}", commonDO.getSegment1());
                throw new RuntimeException("二维表COMPANY_LEDGER_MAPPINGS没有维护数据访问权限集，公司段=" + commonDO.getSegment1());
            }

            JSONObject requestParams = new JSONObject();
            requestParams.put("OperationName", "submitESSJobRequest");
            requestParams.put("JobPackageName", "oracle/apps/ess/financials/generalLedger/programs/common");
            requestParams.put("JobDefName", "JournalImportLauncher");
            requestParams.put("ESSParameters", String.format("%d,%s,%d,%d,%s,%s,%s",
                    accessSetId,
                    commonDO.getJournalSourceName(),
                    commonDO.getLedgerId(),
                    commonDO.getGroupId(),
                    "N",
                    "N",
                    "O"));

            // 发送请求
            HttpEntity<String> entity = new HttpEntity<>(requestParams.toJSONString(), headers);
            ResponseEntity<String> response = restTemplate.exchange(journalImportApiPath, HttpMethod.POST, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                String responseBody = response.getBody();
                log.debug("Journal Import接口返回: {}", responseBody);

                JSONObject jsonResponse = JSON.parseObject(responseBody);
                String reqstId = jsonResponse.getString("ReqstId");
                if (StringUtils.isNotEmpty(reqstId)) {
                    return Long.valueOf(reqstId);
                }
            } else {
                log.error("调用Journal Import接口失败 (Common)，HTTP状态码: {}, reference5={}", response.getStatusCode(), commonDO.getReference5());
            }

            return null;
        } catch (Exception e) {
            log.error("调用Journal Import接口异常 (Common)，reference5={}", commonDO.getReference5(), e);
            return null;
        }
    }

    /**
     * 创建请求头
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        String token = fusionAuthService.generateJwtToken(issuer);
        headers.add("Authorization", "Bearer " + token);
        return headers;
    }

    /**
     * 检查请求状态
     */
    @Override
    public boolean checkRequestStatus(Long loadRequestId) {
        String status = getRequestStatus(loadRequestId);
        return RequestStatusEnum.SUCCEEDED.getCode().equals(status);
    }
}