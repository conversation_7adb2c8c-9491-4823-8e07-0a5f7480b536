package com.xiaoju.corebanking.erp.adaptor.service.file.impl;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.common.utils.ErpUtil;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlFileControlRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceHeaderRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceRepository;
import com.xiaoju.corebanking.erp.adaptor.service.file.DataProcessService;
import com.xiaoju.corebanking.erp.adaptor.service.file.FilePreprocessService;
import com.xiaoju.corebanking.erp.adaptor.service.file.S3FileOperationService;
import com.xiaoju.digitalbank.ddd.po.ExecDataResult;
import com.xiaoju.digitalbank.ddd.po.ExecResult;
import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import com.xiaoju.godson.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @description 拉取文件，预处理文件
 * @date 2025/5/14 14:05
 */
@Service
@Slf4j
public class FilePreprocessServiceImpl implements FilePreprocessService {

    @Resource
    private S3FileOperationService s3FileOperationService;

    @Resource
    private DataProcessService dataProcessService;

    @Resource
    private SofiGlFileControlRepository sofiGlFileControlRepository;

    @Resource
    private SofiGlInterfaceRepository sofiGlInterfaceRepository;

    @Resource
    private SofiGlInterfaceHeaderRepository sofiGlInterfaceHeaderRepository;

    @Resource
    private ErpUtil erpUtil;

    @Override
    public ExecResult processFile(String filePath,String param) {
        try {
            String processDate;
            if (StringUtils.isEmpty(param)){
                processDate = erpUtil.getDateDirFromInput(filePath);
            }else{
                processDate = JsonUtil.toObject(param, String.class);
            }
            filePath = erpUtil.buildFullPath(filePath, processDate);

            // 1. 检查文件
            boolean isCheckFileValid = checkFileValidity(filePath);
            if (!isCheckFileValid) {
                return ExecResult.error(CommonErrorNo.FILE_ERROR, "检查文件无效或不存在");
            }

            // 2. 获取文件 处理子文件
            ExecDataResult<String> result = getAllFile(filePath);
            if (result == null || !result.isSuccess()) {
                return ExecResult.error(CommonErrorNo.FILE_ERROR, "获取文件失败");
            }

            // 3. 创建header记录
            ExecResult execResult = dataProcessService.createInterfaceHeaderRecords(processDate,
                    sofiGlInterfaceRepository, sofiGlInterfaceHeaderRepository);
            if (!execResult.isSuccess()) {
                return ExecResult.error(CommonErrorNo.DB_DATA_ERROR, "创建header记录失败");
            }

            log.info("文件处理成功完成: {}", filePath);
            return ExecResult.success();

        } catch (Exception e) {
            log.error("处理文件时发生异常，文件路径: {}", filePath, e);
            return ExecResult.error(CommonErrorNo.FILE_ERROR, "处理文件时发生异常: " + e.getMessage());
        }
    }

    /**
     * 处理子文件
     */
    public ExecDataResult<String> getAllFile(String filePath) {
        try {
            String checkCsvKey = filePath + CommonConstant.FILE_CHECK_CSV;

            // 1. 获取子文件列表和预期记录数
            Map<String, Integer> subFilesWithCount = s3FileOperationService.readSubfilesWithCount(checkCsvKey);
            if (subFilesWithCount.isEmpty()) {
                log.error("未找到有效的子文件列表，文件路径: {}", checkCsvKey);
                return ExecDataResult.error(CommonErrorNo.FILE_ERROR, "未找到有效的子文件列表");
            }

            log.info("获取到子文件列表，共{}个文件", subFilesWithCount.size());

            String processDate = erpUtil.getDateDirFromInput(filePath);

            int availableProcessors = Runtime.getRuntime().availableProcessors();
            int threadPoolSize = Math.min(availableProcessors, subFilesWithCount.size());

            ThreadPoolExecutor asyncTaskExecutor = new ThreadPoolExecutor(
                    threadPoolSize,
                    threadPoolSize,
                    5L,
                    TimeUnit.MINUTES,
                    new LinkedBlockingQueue<>(1024),
                    new ThreadFactoryBuilder().setNameFormat("getAllFile-process-executor-pool-%d").build(), (r, executor) -> {
                log.error("getAllFileProcessExecutor is Rejected，PoolSize:{},activeCount:{},taskCounter:{}", executor.getPoolSize(), executor.getActiveCount(), executor.getTaskCount());
            });
            List<Future<Boolean>> futures = new ArrayList<>();

            // 提交处理任务
            for (Map.Entry<String, Integer> entry : subFilesWithCount.entrySet()) {
                String subFileName = entry.getKey();
                int expectedCount = entry.getValue();

                log.info("提交子文件处理任务: {}, expectedCount: {}", subFileName, expectedCount);
                String newPath = filePath.replaceAll("/\\d{8}/?$", "/");
                Future<Boolean> future = asyncTaskExecutor.submit(() ->
                        dataProcessService.processSubFile(newPath,subFileName, expectedCount, processDate,
                                sofiGlInterfaceRepository, sofiGlFileControlRepository));
                futures.add(future);
            }

            // 等待所有任务完成
            boolean allSuccessful = true;
            for (Future<Boolean> future : futures) {
                try {
                    if (!future.get()) {
                        allSuccessful = false;
                    }
                } catch (Exception e) {
                    log.error("子文件处理任务执行异常", e);
                    allSuccessful = false;
                }
            }

            // 关闭线程池
            asyncTaskExecutor.shutdown();

            if (!allSuccessful) {
                return ExecDataResult.error(CommonErrorNo.FILE_ERROR, "部分子文件处理失败");
            }

            log.info("所有子文件并行处理成功");
            return ExecDataResult.success("文件处理成功");
        } catch (Exception e) {
            log.error("处理文件时发生异常，文件路径: {}", filePath, e);
            return ExecDataResult.error(CommonErrorNo.FILE_ERROR, "处理文件时发生异常: " + e.getMessage());
        }
    }

    /**
     * 检查check文件
     */
    public boolean checkFileValidity(String filePath) {
        try {
            String checkCsvKey = filePath + CommonConstant.FILE_CHECK_CSV;
            String checkMd5Key = filePath + CommonConstant.FILE_CHECK_MD5;

            // 1. 检查check文件是否存在
            boolean csvExist = s3FileOperationService.checkFileExists(checkCsvKey);
            boolean md5Exist = s3FileOperationService.checkFileExists(checkMd5Key);

            if (!csvExist || !md5Exist) {
                log.error("文件不存在: CSV存在={}, MD5存在={}", csvExist, md5Exist);
                return false;
            }

            // 2. 根据_check.checksum中的MD5值进行校验
            String expectedMd5 = s3FileOperationService.readFileFirstLine(checkMd5Key);
            if (expectedMd5 == null) {
                log.error("无法读取MD5文件内容: {}", checkMd5Key);
                return false;
            }

            boolean isValid = s3FileOperationService.verifyMd5(checkCsvKey, expectedMd5);
            if (!isValid) {
                log.error("MD5校验失败，停止处理");
                return false;
            }

            // 3. 检查_check.csv中是否包含子文件列表
            Map<String, Integer> subFilesWithCount = s3FileOperationService.readSubfilesWithCount(checkCsvKey);
            if (subFilesWithCount.isEmpty()) {
                log.error("check.csv文件未包含有效的子文件列表，文件路径: {}", checkCsvKey);
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("检查文件有效性时发生错误，文件路径: {}", filePath, e);
            return false;
        }
    }
}
