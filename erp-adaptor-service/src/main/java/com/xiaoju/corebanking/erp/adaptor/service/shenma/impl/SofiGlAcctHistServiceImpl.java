package com.xiaoju.corebanking.erp.adaptor.service.shenma.impl;

import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlAcctHistRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SummaryResultDO;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlAcctHistService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 类描述
 * @author: didi
 **/
@Slf4j
@Service
public class SofiGlAcctHistServiceImpl implements SofiGlAcctHistService {
    @Autowired
    private SofiGlAcctHistRepository sofiGlAcctHistRepository;
    @Override
    public List<SummaryResultDO> queryAcctHisSummary(String processDay) {
        log.info("queryAcctHisSummary processDay:{}", processDay);
        return sofiGlAcctHistRepository.selectAcctHisSummary(processDay);
    }
}
