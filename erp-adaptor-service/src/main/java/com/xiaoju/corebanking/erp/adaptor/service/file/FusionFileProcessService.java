package com.xiaoju.corebanking.erp.adaptor.service.file;

import com.xiaoju.digitalbank.ddd.po.ExecResult;

import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 拉取文件，预处理文件
 * @date 2025/5/14 14:18
 */
public interface FusionFileProcessService {
    void processCSVFile(List<Long> groupIds,String token);
     <T> void generateCsvToStream(OutputStream outputStream, List<T> dataList, boolean withHeaderFlag) throws IOException;
    void processCSVFileToCommon(String systemCode, String processDay, List<Long> groupIds, String token);
}
