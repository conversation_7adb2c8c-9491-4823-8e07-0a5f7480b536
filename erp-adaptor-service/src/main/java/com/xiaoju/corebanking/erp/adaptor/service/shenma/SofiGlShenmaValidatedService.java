package com.xiaoju.corebanking.erp.adaptor.service.shenma;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceCommonDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValueExtraPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePO;

import java.util.List;
import java.util.Map;

public interface SofiGlShenmaValidatedService {
    String validated(SofiGlShenmaDO sofiGlShenmaDO);

    String mappedResult(SofiGlShenmaDO sofiGlShenmaDO, SofiGlInterfaceCommonDO sofiGlInterfaceCommonDO, Map<String, Map<String, CuxTwoTabValueExtraPO>> cache);

}
