package com.xiaoju.corebanking.erp.adaptor.service.file.impl;

import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.CuxTwoTabValueDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.VoucherGroupData;
import com.xiaoju.corebanking.erp.adaptor.service.common.AbstractDataSyncService;
import com.xiaoju.corebanking.erp.adaptor.service.twotab.TwoTabValueService;
import com.xiaoju.corebanking.erp.adaptor.service.file.VoucherGroupSyncService;
import com.xiaoju.digitalbank.ddd.po.ExecResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * VoucherGroup文件数据入库
 * <AUTHOR>
 * @date 2025/1/15
 */
@Service
@Slf4j
public class VoucherGroupSyncServiceImpl extends AbstractDataSyncService<CuxTwoTabValueDO> implements VoucherGroupSyncService {

    @Resource
    private TwoTabValueService twoTabValueService;

    @Override
    public ExecResult processVoucherGroupMapping(String filePath, String processDay) {
        return processDataFile(filePath, processDay);
    }

    @Override
    protected String getBusinessName() {
        return "VoucherGroup";
    }

    @Override
    protected String getDataFileName() {
        return CommonConstant.VOUCHER_GROUP_CSV;
    }

    @Override
    protected String getCheckFileName() {
        return CommonConstant.VOUCHER_GROUP_CHECK_CSV;
    }

    @Override
    protected CuxTwoTabValueDO parseRecord(CSVRecord record, int lineNumber, String processDay) {
        try {
            if (record.size() < CommonConstant.REQUIRED_COLUMN_COUNT) {
                log.warn("第{}行数据字段不足，跳过: 实际列数={}, 要求列数={}",
                        lineNumber, record.size(), CommonConstant.REQUIRED_COLUMN_COUNT);
                return null;
            }

            VoucherGroupData data = VoucherGroupData.builder()
                    .voucherGroup(trimField(record.get(CommonConstant.CSV0)))
                    .voucherGroupDesc(trimField(record.get(CommonConstant.CSV1)))
                    .voucherGroupDescEng(trimField(record.get(CommonConstant.CSV2)))
                    .eventType(trimField(record.get(CommonConstant.CSV3)))
                    .tranType(trimField(record.get(CommonConstant.CSV4)))
                    .build();

            if (StringUtils.isBlank(data.getVoucherGroup())) {
                log.warn("第{}行VOUCHER_GROUP为空，跳过", lineNumber);
                return null;
            }

            CuxTwoTabValueDO valueDO = buildCuxTwoTabValueDO(data);

            log.debug("解析第{}行数据成功: VOUCHER_GROUP={}, EVENT_TYPE={}, TRAN_TYPE={}",
                    lineNumber, data.getVoucherGroup(), data.getEventType(), data.getTranType());

            return valueDO;

        } catch (Exception e) {
            log.error("解析第{}行数据失败", lineNumber, e);
            return null;
        }
    }

    @Override
    protected int syncData(List<CuxTwoTabValueDO> dataList) {
        return twoTabValueService.syncValues(dataList);
    }

    private CuxTwoTabValueDO buildCuxTwoTabValueDO(VoucherGroupData data) {
        CuxTwoTabValueDO valueDO = new CuxTwoTabValueDO();
        valueDO.setFormCode(CommonConstant.FORM_CODE);
        valueDO.setValue1(data.getEventType());
        valueDO.setValue2(data.getTranType());
        valueDO.setValue3(data.getVoucherGroup());
        valueDO.setValue4(data.getVoucherGroupDesc());
        valueDO.setValue5(data.getVoucherGroupDescEng());
        valueDO.setEnabledFlag(CommonConstant.ENABLED_FLAG);
        valueDO.setLang(CommonConstant.LANG);
        return valueDO;
    }

    private String trimField(String field) {
        return StringUtils.isBlank(field) ? null : field.trim();
    }
} 