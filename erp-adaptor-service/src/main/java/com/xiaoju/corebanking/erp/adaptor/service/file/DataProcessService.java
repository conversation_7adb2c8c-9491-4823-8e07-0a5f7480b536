package com.xiaoju.corebanking.erp.adaptor.service.file;

import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlFileControlRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceHeaderRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;

import com.xiaoju.digitalbank.ddd.po.ExecResult;
import org.apache.commons.csv.CSVRecord;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 数据处理服务
 * @date 2025/5/16 16:54
 */
public interface DataProcessService {

    boolean processSubFile(String filePath, String subFile, int expectedCount, String processDate, SofiGlInterfaceRepository sofiGlInterfaceRepository, SofiGlFileControlRepository sofiGlFileControlRepository);

    boolean processFileContent(String filePath, String subFile, String processDate, int expectedCount, SofiGlInterfaceRepository sofiGlInterfaceRepository, SofiGlFileControlRepository sofiGlFileControlRepository);

    SofiGlInterfaceDO createInterfaceRecord(String[] headers, String[] values, String processDay, String subFile, String batchId);

    boolean processAndVerifyFile(String filePath, String subFile, String processDate, int expectedCount, SofiGlInterfaceRepository sofiGlInterfaceRepository, SofiGlFileControlRepository sofiGlFileControlRepository);

    void createControlStatus(int actualCount, String filePath, String processDate, boolean success, String message, SofiGlFileControlRepository sofiGlFileControlRepository);

    void setFieldValue(SofiGlInterfaceDO sofiGlInterfaceDO, String fieldName, String value);

    ExecResult createInterfaceHeaderRecords(String processDay, SofiGlInterfaceRepository sofiGlInterfaceRepository, SofiGlInterfaceHeaderRepository sofiGlInterfaceHeaderRepository);
}
