package com.xiaoju.corebanking.erp.adaptor.service.inter.impl;

import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.common.errorno.ErpAdaptorErrorNo;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.CargosAndAbonosDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValueExtraPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePO;
import com.xiaoju.corebanking.erp.adaptor.service.inter.SofiGlInterfaceService;
import com.xiaoju.corebanking.erp.adaptor.service.inter.SofiGlInterfaceValidatedService;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.godson.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 类描述
 * @author: zhangcc
 * @date: 2025/5/19$
 **/
@Slf4j
@Service
public class SofiGlInterfaceValidatedServiceImpl implements SofiGlInterfaceValidatedService {
    @Autowired
    private SofiGlInterfaceService sofiGlInterfaceService;
    @Override
    public String validated(SofiGlInterfaceDO sofiGlInterfaceDO, Map<Long, List<SofiGlInterfaceDO>> map) {
        StringBuilder errMessage = new StringBuilder();
        errMessage.append(ErpAdaptorErrorNo.IMP_ERR_ERROR.getErrorMsg());
        if (StringUtils.isEmpty(sofiGlInterfaceDO.getSourceSys())) {
            errMessage.append(" SourceSys is null,");
        }
        if (Objects.isNull(sofiGlInterfaceDO.getEmpresaId()) || CommonConstant.ZERO_NUM.equals(sofiGlInterfaceDO.getEmpresaId())) {
            errMessage.append(" EmpresaId is null,");
        }
        if (Objects.isNull(sofiGlInterfaceDO.getPolizaId()) || CommonConstant.ZERO_NUM.equals(sofiGlInterfaceDO.getPolizaId())) {
            errMessage.append(" PolizaId is null,");
        }
        if (StringUtils.isEmpty(sofiGlInterfaceDO.getFecha())) {
            errMessage.append(" Fecha is null,");
        }
        if (Objects.isNull(sofiGlInterfaceDO.getCentroCostoId()) || CommonConstant.ZERO_NUM.equals(sofiGlInterfaceDO.getCentroCostoId())) {
            errMessage.append(" CentroCostoID is null,");

        }
        if (String.valueOf(sofiGlInterfaceDO.getCentroCostoId()).length() > CommonConstant.C_LENGTH) {
            errMessage.append(" CentroCostoID length is max 25,");

        }
        if (StringUtils.isEmpty(sofiGlInterfaceDO.getCuentaCompleta())) {
            errMessage.append(" CuentaCompleta is null,");

        }
        if (sofiGlInterfaceDO.getCuentaCompleta().length() > CommonConstant.C_LENGTH) {
            errMessage.append(" CuentaCompleta length is max 25,");

        }
        if (Objects.isNull(sofiGlInterfaceDO.getInstrumento()) || CommonConstant.ZERO_NUM.equals(sofiGlInterfaceDO.getInstrumento())) {
            errMessage.append(" Instrumento is null,");

        }
        if (Objects.isNull(sofiGlInterfaceDO.getMonedaId()) || CommonConstant.ZERO_NUM.equals(sofiGlInterfaceDO.getMonedaId())) {
            errMessage.append(" MonedaID is null,");

        }
        if (Objects.isNull(sofiGlInterfaceDO.getCargos())) {
            errMessage.append(" Cargos is null,");
        }
        if (Objects.isNull(sofiGlInterfaceDO.getAbonos())) {
            errMessage.append(" Abonos is null,");

        }
        if (Objects.nonNull(map)) {
            List<SofiGlInterfaceDO> list = map.get(sofiGlInterfaceDO.getPolizaId());
            BigDecimal sumAbonos = BigDecimal.ZERO;
            BigDecimal sumCargos = BigDecimal.ZERO;
            for (SofiGlInterfaceDO sofiDo : list) {
                sumAbonos = sumAbonos.add(sofiDo.getAbonos());
                sumCargos = sumCargos.add(sofiDo.getCargos());
            }
            if (sumAbonos.compareTo(sumCargos) !=0) {
                errMessage.append(" Cargos is not equals Abonos,");
            }
        }
        if (StringUtils.isEmpty(sofiGlInterfaceDO.getReferencia())) {
            errMessage.append(" Referencia is null,");

        }
        if (StringUtils.isEmpty(sofiGlInterfaceDO.getFechaActual())) {
            errMessage.append(" FechaActual is null,");

        }
        if (Objects.isNull(sofiGlInterfaceDO.getSucursal()) || CommonConstant.ZERO_NUM.equals(sofiGlInterfaceDO.getSucursal())) {
            errMessage.append(" Sucursal is null,");

        }
        if (StringUtils.isEmpty(sofiGlInterfaceDO.getTipoInstrumentoId())) {
            errMessage.append(" TipoInstrumentoID is null");
        }
        return errMessage.toString();
    }

    @Override
    public String validatedCargosAndAbonos(SofiGlInterfaceDO sofiGlInterfaceDO) {
        Map<String, CargosAndAbonosDO> map = new HashMap<>();
        if (Objects.isNull(map.get(CommonConstant.CARGOS_NOT_ABONOS))) {
            CargosAndAbonosDO cargosAndAbonosDO = sofiGlInterfaceService.queryCargosAndAbonosDO(sofiGlInterfaceDO.getPolizaId());
            map.put(CommonConstant.CARGOS_NOT_ABONOS, cargosAndAbonosDO);
        }
        //借贷不相等
        CargosAndAbonosDO cargosAndAbonosDO = map.get(CommonConstant.CARGOS_NOT_ABONOS);
        log.info("validatedCargosAndAbonos map:{}", JsonUtil.toString(map));
        if (!cargosAndAbonosDO.getCargos().equals(cargosAndAbonosDO.getAbonos())) {
            return " Cargos is not equals Abonos,";
        }
        return null;
    }

    @Override
    public String mappedResult(SofiGlInterfaceDO sofiGlInterfaceDO, Map<String, Map<String, CuxTwoTabValueExtraPO>> cache) {
        StringBuilder errMessage = new StringBuilder();
        errMessage.append(ErpAdaptorErrorNo.IMP_ERR_ERROR.getErrorMsg());
        // mapping 转换
        commonDataMapping(sofiGlInterfaceDO, cache);
        Map<String, CuxTwoTabValueExtraPO> categoryMappings = cache.get(CommonConstant.CATEGORY_MAPPINGS);
        CuxTwoTabValueExtraPO mapping = categoryMappings.get(sofiGlInterfaceDO.getSourceSys());
        if (Objects.isNull(mapping)) {
            errMessage.append(" SourceSys is illegality,");
        }
        if (Objects.isNull(sofiGlInterfaceDO.getLedgerId()) || CommonConstant.ZERO_NUM.equals(sofiGlInterfaceDO.getLedgerId())) {
            errMessage.append(" not found companyMappings LedgerId is null,");
        }
        if (StringUtils.isEmpty(sofiGlInterfaceDO.getLedgerName())) {
            errMessage.append(" not found companyMappings LedgerName is null,");
        }
        if (StringUtils.isEmpty(sofiGlInterfaceDO.getSegment1())) {
            errMessage.append(" not found companyMappings Segment1 is null,");
        }
        if (StringUtils.isEmpty(sofiGlInterfaceDO.getJournalCategory())) {
            errMessage.append(" not found categoryMapping JournalCategory is null,");
        }
        if (StringUtils.isEmpty(sofiGlInterfaceDO.getCurrencyCode())) {
            errMessage.append(" not found currencyMapping CurrencyCode is null,");
        }
        if (StringUtils.isEmpty(sofiGlInterfaceDO.getSegment6())) {
            errMessage.append(" not found productMapping Segment6 is null,");
        }
        if (StringUtils.isEmpty(sofiGlInterfaceDO.getJournalSourceName())) {
            errMessage.append(" not found journalSourceName JournalSourceName is null,");
        }
        return errMessage.toString();
    }

    private static void companyMapping(SofiGlInterfaceDO sofiGlInterfaceDO, Map<String, Map<String, CuxTwoTabValueExtraPO>> cache) {
        Map<String, CuxTwoTabValueExtraPO> companyMap = cache.get(CommonConstant.COMPANY_LEDGER_MAPPINGS);
        CuxTwoTabValueExtraPO mapping = companyMap.get(String.valueOf(sofiGlInterfaceDO.getEmpresaId()));
        if (StringUtils.isNotEmpty(mapping.getValue3()) && StringUtils.isNotEmpty(mapping.getValue5())) {
            sofiGlInterfaceDO.setLedgerId(Long.valueOf(mapping.getValue4()));
            sofiGlInterfaceDO.setLedgerName(mapping.getValue3());
            sofiGlInterfaceDO.setSegment1(mapping.getValue5());
        }
    }


    private void categoryMapping(SofiGlInterfaceDO sofiGlInterfaceDO, Map<String, Map<String, CuxTwoTabValueExtraPO>> cache) {
        Map<String, CuxTwoTabValueExtraPO> categoryMappings = cache.get(CommonConstant.CATEGORY_MAPPINGS);
        CuxTwoTabValueExtraPO mapping = categoryMappings.get(sofiGlInterfaceDO.getSourceSys());
        if (StringUtils.isNotEmpty(mapping.getValue2())) {
            sofiGlInterfaceDO.setJournalCategory(mapping.getValue2());
        }

    }

    private void currencyMapping(SofiGlInterfaceDO sofiGlInterfaceDO, Map<String, Map<String, CuxTwoTabValueExtraPO>> cache) {
        Map<String, CuxTwoTabValueExtraPO> currencyMappings = cache.get(CommonConstant.CURRENCY_MAPPINGS);
        CuxTwoTabValueExtraPO mapping = currencyMappings.get(String.valueOf(sofiGlInterfaceDO.getMonedaId()));
        if (StringUtils.isNotEmpty(mapping.getValue3())) {
            sofiGlInterfaceDO.setCurrencyCode(mapping.getValue3());
        }
    }

    private void productMapping(SofiGlInterfaceDO sofiGlInterfaceDO, Map<String, Map<String, CuxTwoTabValueExtraPO>> cache) {
        Map<String, CuxTwoTabValueExtraPO> productMappings = cache.get(CommonConstant.PRODUCT_MAPPINGS);
        CuxTwoTabValueExtraPO mapping = productMappings.get(String.valueOf(sofiGlInterfaceDO.getTipoInstrumentoId()));
        if (StringUtils.isNotEmpty(mapping.getValue3())) {
            sofiGlInterfaceDO.setSegment6(mapping.getValue3());
        }

    }

    private void journalSourceNameMapping(SofiGlInterfaceDO sofiGlInterfaceDO, Map<String, Map<String, CuxTwoTabValueExtraPO>> cache) {
        Map<String, CuxTwoTabValueExtraPO> jorunalSourceMappings = cache.get(CommonConstant.GL_JOURNAL_SOURCE_MAPPINGS);
        CuxTwoTabValueExtraPO mapping = jorunalSourceMappings.get(String.valueOf(sofiGlInterfaceDO.getJournalSource()));
        if (StringUtils.isNotEmpty(mapping.getValue3())) {
            sofiGlInterfaceDO.setJournalSourceName((mapping.getValue3()));
        }
    }

    private void commonDataMapping(SofiGlInterfaceDO sofiGlInterfaceDO, Map<String, Map<String, CuxTwoTabValueExtraPO>> cache) {
        sofiGlInterfaceDO.setJournalSource(CommonConstant.SAFI_JOURNAL_SOURCE);
        companyMapping(sofiGlInterfaceDO, cache);
        categoryMapping(sofiGlInterfaceDO, cache);
        currencyMapping(sofiGlInterfaceDO, cache);
        productMapping(sofiGlInterfaceDO, cache);
        journalSourceNameMapping(sofiGlInterfaceDO, cache);
        sofiGlInterfaceDO.setSegment2(String.valueOf(sofiGlInterfaceDO.getCentroCostoId()));
        sofiGlInterfaceDO.setSegment3(sofiGlInterfaceDO.getCuentaCompleta());
        sofiGlInterfaceDO.setSegment4(CommonConstant.ZERO);
        sofiGlInterfaceDO.setSegment5(CommonConstant.SEGMENT_5);
        sofiGlInterfaceDO.setSegment7(CommonConstant.ZERO);
        sofiGlInterfaceDO.setSegment8(CommonConstant.ZERO);
        sofiGlInterfaceDO.setSegment9(CommonConstant.ZERO);
        sofiGlInterfaceDO.setSegment10(CommonConstant.ZERO);

    }
}
