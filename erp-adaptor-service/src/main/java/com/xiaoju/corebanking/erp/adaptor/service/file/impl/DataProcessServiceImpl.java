package com.xiaoju.corebanking.erp.adaptor.service.file.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.enums.SourceSysEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlFileControlRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceHeaderRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlFileControlDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.service.file.DataProcessService;
import com.xiaoju.corebanking.erp.adaptor.service.file.S3FileOperationService;
import com.xiaoju.corebanking.erp.adaptor.service.file.enums.SafiFieldEnum;
import com.xiaoju.corebanking.erp.adaptor.service.twotab.SequenceService;
import com.xiaoju.digitalbank.ddd.po.ExecResult;
import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 读取文件业务处理
 * @date 2025/5/16 16:54
 */
@Service
@Slf4j
public class DataProcessServiceImpl implements DataProcessService {

    @Resource
    private S3FileOperationService s3FileOperationService;

    @Resource
    private SequenceService sequenceService;

    @Override
    public boolean processSubFile(String filePath, String subFile, int expectedCount, String processDate, SofiGlInterfaceRepository sofiGlInterfaceRepository, SofiGlFileControlRepository sofiGlFileControlRepository) {
        try {
            String newPath = filePath.replaceAll("/\\d{8}/?$", "/");
            // 检查文件是否存在
            if (!s3FileOperationService.checkFileExists(newPath + subFile)) {
                log.error("子文件不存在: {}", subFile);
                createControlStatus(0, subFile, processDate, false, "文件处理失败：子文件不存在" + newPath + subFile, sofiGlFileControlRepository);
                return false;
            }

            // 验证MD5和处理文件内容
            if (!processAndVerifyFile(newPath, subFile, processDate, expectedCount, sofiGlInterfaceRepository, sofiGlFileControlRepository)) {
                log.error("处理子文件{}失败", subFile);
                createControlStatus(0, subFile, processDate, false, "文件处理失败：处理子文件" + subFile + "失败", sofiGlFileControlRepository);
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("处理子文件失败: {}", subFile, e);
            createControlStatus(0, subFile, processDate, false, "文件处理失败", sofiGlFileControlRepository);
            return false;
        }
    }

    @Override
    public boolean processFileContent(String filePath, String subFile, String processDate, int expectedCount, SofiGlInterfaceRepository sofiGlInterfaceRepository, SofiGlFileControlRepository sofiGlFileControlRepository) {
        try (InputStream inputStream = s3FileOperationService.download(filePath + subFile);
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {

            int actualCount = 0;
            boolean hasProcessedAnyLine = false;
            List<SofiGlInterfaceDO> interfaceList = new ArrayList<>();
            String batchId = processDate + "_" + 1;

            try (CSVParser csvParser = CSVFormat.DEFAULT
                    .withFirstRecordAsHeader()
                    .withIgnoreEmptyLines(true)
                    .withTrim()
                    .parse(reader)) {

                Map<String, Integer> headerMap = csvParser.getHeaderMap();
                if (headerMap == null || headerMap.isEmpty()) {
                    log.error("文件{}为空或标题行无效", subFile);
                    return false;
                }

                String[] headers = headerMap.keySet().toArray(new String[0]);

                // 处理每一行数据
                for (CSVRecord record : csvParser) {
                    actualCount++;

                    try {
                        String[] values = new String[record.size()];
                        for (int i = 0; i < record.size(); i++) {
                            values[i] = record.get(i);
                        }

                        SofiGlInterfaceDO sofiGlInterfaceDO = createInterfaceRecord(headers, values, processDate, subFile, batchId);
                        interfaceList.add(sofiGlInterfaceDO);
                        hasProcessedAnyLine = true;

                        // 当达到批处理大小时，执行批量插入并清空列表
                        if (interfaceList.size() >= CommonConstant.BATCH_SIZE) {
                            batchInsertInterface(interfaceList, sofiGlInterfaceRepository);
                            interfaceList.clear();
                        }
                    } catch (Exception e) {
                        log.error("处理文件{}第{}行时发生错误: {}", subFile, actualCount + 1, e.getMessage());
                        return false;
                    }
                }
            } catch (IOException e) {
                log.error("解析CSV文件{}时发生错误: {}", subFile, e.getMessage());
                return false;
            }

            // 检查记录数
            if (actualCount != expectedCount) {
                log.error("文件{}的记录数不匹配，期望值: {}，实际值: {}", subFile, expectedCount, actualCount);
                return false;
            }

            if (!hasProcessedAnyLine) {
                log.error("文件{}没有有效数据行", subFile);
                return false;
            }

            if (!interfaceList.isEmpty()) {
                batchInsertInterface(interfaceList, sofiGlInterfaceRepository);
            }

            // 入control表
            createControlStatus(actualCount, subFile, processDate, true, "文件处理成功", sofiGlFileControlRepository);
            return true;
        } catch (Exception e) {
            log.error("处理文件内容失败: {}", subFile, e);
            return false;
        }
    }

    @Override
    public SofiGlInterfaceDO createInterfaceRecord(String[] headers, String[] values, String processDay, String subFile, String batchId) {
        SofiGlInterfaceDO sofiGlInterfaceDO = new SofiGlInterfaceDO();
        sofiGlInterfaceDO.initializeDefaultValues();
        sofiGlInterfaceDO.setProcessDay(processDay);
        sofiGlInterfaceDO.setFileName(subFile);
        sofiGlInterfaceDO.setBatchId(batchId);
        sofiGlInterfaceDO.setProcessStatus(ProcessStatusEnum.NEW);

        for (int i = 0; i < Math.min(headers.length, values.length); i++) {
            setFieldValue(sofiGlInterfaceDO, headers[i], values[i]);
        }

        return sofiGlInterfaceDO;
    }

    @Override
    public boolean processAndVerifyFile(String filePath, String subFile, String processDate, int expectedCount, SofiGlInterfaceRepository sofiGlInterfaceRepository, SofiGlFileControlRepository sofiGlFileControlRepository) {
        try {
            // 1. 获取子文件的checksum文件
            String subFileChecksum = subFile.replace(CommonConstant.CSV_EXTENSION, CommonConstant.MD5_EXTENSION);
            if (!s3FileOperationService.checkFileExists(filePath + subFileChecksum)) {
                log.error("子文件校验文件不存在: {}", subFileChecksum);
                return false;
            }

            // 2. 读取预期的MD5值
            String expectedMd5 = s3FileOperationService.readFileFirstLine(filePath + subFileChecksum);
            if (expectedMd5 == null) {
                log.error("子文件checksum内容为空或读取失败: {}", subFileChecksum);
                return false;
            }

            // 3. 校验MD5
            boolean isValid = s3FileOperationService.verifyMd5(filePath + subFile, expectedMd5);
            if (!isValid) {
                log.error("子文件MD5校验失败，文件: {}, 期望值: {}", subFile, expectedMd5);
                return false;
            }

            // 4. 处理文件内容
            return processFileContent(filePath, subFile, processDate, expectedCount, sofiGlInterfaceRepository, sofiGlFileControlRepository);
        } catch (Exception e) {
            log.error("处理文件{}内容失败", subFile, e);
            return false;
        }
    }

    @Override
    public void createControlStatus(int actualCount, String filePath, String processDate, boolean success, String message, SofiGlFileControlRepository sofiGlFileControlRepository) {
        if (filePath == null) {
            log.error("更新文件控制表状态失败: 文件路径为空");
            return;
        }

        try {
            SofiGlFileControlDO controlDO = new SofiGlFileControlDO();
            controlDO.setFileName(filePath);
            controlDO.setProcessDay(processDate);
            controlDO.setSystemCode(SourceSysEnum.SAFI.getCode());
            controlDO.setObjectVersionNumber(1);
            controlDO.setCreatedBy(CommonConstant.SYSTEM_USER);
            controlDO.setLastModifiedBy(CommonConstant.SYSTEM_USER);
            controlDO.setCreationDate(new Date());
            controlDO.setLastModifyDate(new Date());

            if (success) {
                Long fileSize = s3FileOperationService.getFileSize(filePath);
                controlDO.setFileSize(fileSize);
                controlDO.setFileCount((long) actualCount);
                controlDO.setSystemCode(SourceSysEnum.SAFI.getCode());
                controlDO.setProcessStatus(ProcessStatusEnum.SUCCESS.getCode());
            } else {
                controlDO.setProcessStatus(ProcessStatusEnum.FAILED.getCode());
            }
            controlDO.setProcessMessage(message);

            sofiGlFileControlRepository.insertSelective(controlDO);

            log.info("文件{}状态更新{}", filePath, success ? "成功" : "失败");
        } catch (Exception e) {
            log.error("更新文件{}控制表状态失败", filePath, e);
            throw new RuntimeException("更新文件控制表状态失败", e);
        }
    }

    @Override
    public void setFieldValue(SofiGlInterfaceDO sofiGlInterfaceDO, String fieldName, String value) {
        if (sofiGlInterfaceDO == null || fieldName == null || value == null) {
            return;
        }
        SafiFieldEnum.setField(sofiGlInterfaceDO, fieldName, value);
    }

    public void batchInsertInterface(List<SofiGlInterfaceDO> interfaceList, SofiGlInterfaceRepository sofiGlInterfaceRepository) {
        if (CollectionUtils.isEmpty(interfaceList)) {
            return;
        }

        sofiGlInterfaceRepository.batchInsertInterface(interfaceList);
    }

    @Override
    public ExecResult createInterfaceHeaderRecords(String processDay, SofiGlInterfaceRepository sofiGlInterfaceRepository, SofiGlInterfaceHeaderRepository sofiGlInterfaceHeaderRepository) {
        if (processDay == null || processDay.trim().isEmpty()) {
            log.error("处理日期为空，无法创建interface header记录");
            return ExecResult.error(CommonErrorNo.FILE_ERROR, "处理日期不能为空");
        }

        try {
            List<SofiGlInterfaceHeaderDO> headerRecords = new ArrayList<>();
            int counter = 0;
            long currentGroupId = sequenceService.nextval("SAFI_GROUP_ID");

            int pageSize = 800;
            int pageNum = 1;
            boolean hasMore = true;

            while (hasMore) {
                try {
                    PageHelper.startPage(pageNum, pageSize);
                    List<Map<String, Object>> currentPageRecords = sofiGlInterfaceRepository.selectGroupByPolizaId(
                            processDay, ProcessStatusEnum.NEW.getCode());

                    PageInfo<Map<String, Object>> pageInfo = new PageInfo<>(currentPageRecords);

                    if (currentPageRecords.isEmpty()) {
                        break;
                    }

                    for (Map<String, Object> group : currentPageRecords) {
                        counter++;
                        if (counter > CommonConstant.GROUP_SIZE) {
                            currentGroupId = sequenceService.nextval("SAFI_GROUP_ID");
                            counter = 1;
                        }

                        SofiGlInterfaceHeaderDO headerDO = new SofiGlInterfaceHeaderDO();
                        headerDO.setSystemCode(SourceSysEnum.SAFI.getCode());
                        headerDO.setProcessMessage("");
                        headerDO.setCreationDate(new Date());
                        headerDO.setCreatedBy(CommonConstant.SYSTEM_USER);
                        headerDO.setLastModifyDate(new Date());
                        headerDO.setLastModifiedBy(CommonConstant.SYSTEM_USER);
                        headerDO.setProcessDay(processDay);
                        headerDO.setExternalReference(String.valueOf(group.get("poliza_id")));
                        headerDO.setBatchId((String) group.get("batch_id"));
                        headerDO.setJournalCount(((Number) group.get("record_count")).longValue());
                        headerDO.setProcessStatus(ProcessStatusEnum.NEW);
                        headerDO.setObjectVersionNumber(1L);
                        headerDO.setGroupId(currentGroupId);
                        headerRecords.add(headerDO);

                        // 批量插入
                        if (headerRecords.size() >= 800) {
                            batchInsertHeaderRecords(headerRecords, sofiGlInterfaceHeaderRepository);
                            headerRecords.clear();
                        }
                    }

                    hasMore = pageInfo.isHasNextPage();
                    pageNum++;

                } finally {
                    PageHelper.clearPage();
                }

                if (hasMore) {
                    try {
                        Thread.sleep(50);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }

            if (!headerRecords.isEmpty()) {
                batchInsertHeaderRecords(headerRecords, sofiGlInterfaceHeaderRepository);
            }
        } catch (Exception e) {
            log.error("处理sofi_gl_interface_header表数据时发生异常，处理日期: {}", processDay, e);
            return ExecResult.error(CommonErrorNo.FILE_ERROR, "处理sofi_gl_interface_header表数据时发生异常: " + e.getMessage());
        }
        return ExecResult.success();
    }

    public void batchInsertHeaderRecords(List<SofiGlInterfaceHeaderDO> headerRecords, SofiGlInterfaceHeaderRepository sofiGlInterfaceHeaderRepository) {
        if (CollectionUtils.isEmpty(headerRecords)) {
            log.error("没有header记录需要插入");
            return;
        }

        sofiGlInterfaceHeaderRepository.batchInsertInterfaceHeader(headerRecords);
    }
}
