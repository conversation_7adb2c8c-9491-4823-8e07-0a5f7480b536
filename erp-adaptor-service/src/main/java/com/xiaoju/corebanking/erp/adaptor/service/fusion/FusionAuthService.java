package com.xiaoju.corebanking.erp.adaptor.service.fusion;


import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.MessageDigest;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.io.IOException;
import java.security.GeneralSecurityException;
import java.io.ByteArrayOutputStream;

@Service
public class FusionAuthService {

    @Value("${fusion.jwt.issuer:www.didiglobal.com}")
    private String issuer;

    @Value("${fusion.jwt.exp:30}")
    private int expirationMinutes;

    @Value("${fusion.jwt.public-key:classpath:cert/pub.pem}")
    private String certificatePath;

    @Value("${fusion.jwt.private-key:classpath:cert/jwt.der}")
    private String privateKeyPath;

    @Autowired
    private ResourceLoader resourceLoader;

    private String base64UrlEncode(byte[] data) {
        return Base64.getUrlEncoder().withoutPadding().encodeToString(data);
    }

    /**
     * 生成JWT令牌
     @param username 要生成令牌的用户名（prn）
     @return 生成的JWT令牌
     @throws RuntimeException 如果令牌生成失败
     */
    public String generateJwtToken(String username) throws RuntimeException {
        try {
            String x5t = calculateX5t();
            String base64UrlHeader = createHeader(x5t);
            String base64UrlPayload = createPayload(username);
            String headerPayload = base64UrlHeader + "." + base64UrlPayload;
            String base64UrlSignature = signToken(headerPayload);
            return headerPayload + "." + base64UrlSignature;

        } catch (Exception e) {
            throw new RuntimeException("生成jwt token错误");
        }
    }

    private String calculateX5t() throws GeneralSecurityException, IOException {
        Resource certResource = resourceLoader.getResource(certificatePath);
        try (InputStream inStream = certResource.getInputStream()) {
            CertificateFactory cf = CertificateFactory.getInstance("X.509");
            X509Certificate publicKey = (X509Certificate) cf.generateCertificate(inStream);
            byte[] derCert = publicKey.getEncoded();
            MessageDigest sha1 = MessageDigest.getInstance("SHA-1");
            return base64UrlEncode(sha1.digest(derCert));
        }
    }

    private String createHeader(String x5t) {
        JSONObject header = new JSONObject();
        header.put("alg", "RS256");
        header.put("typ", "JWT");
        header.put("x5t", x5t);
        return base64UrlEncode(header.toJSONString().getBytes(StandardCharsets.UTF_8));
    }

    private String createPayload(String username) {
        long nowMillis = System.currentTimeMillis();
        JSONObject payload = new JSONObject();
        payload.put("iss", issuer);
        payload.put("prn", username);
        payload.put("iat", nowMillis / 1000);
        payload.put("exp", (nowMillis + (long) expirationMinutes * 60 * 1000) / 1000);
        return base64UrlEncode(payload.toJSONString().getBytes(StandardCharsets.UTF_8));
    }

    private String signToken(String headerPayload) throws GeneralSecurityException, IOException {
        Resource keyResource = resourceLoader.getResource(privateKeyPath);
        try (InputStream inputStream = keyResource.getInputStream()) {
            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            int nRead;
            byte[] data = new byte[4096];
            while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, nRead);
            }
            byte[] keyBytes = buffer.toByteArray();

            PKCS8EncodedKeySpec kspec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory kf = KeyFactory.getInstance("RSA");
            PrivateKey privateKey = kf.generatePrivate(kspec);

            Signature signature = Signature.getInstance("SHA256withRSA");
            signature.initSign(privateKey);
            signature.update(headerPayload.getBytes(StandardCharsets.UTF_8));
            return base64UrlEncode(signature.sign());
        }
    }

}
