package com.xiaoju.corebanking.erp.adaptor.service.file.impl;

import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.enums.SourceSysEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceHeaderRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlShenmaRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlShenmaHisRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;
import com.xiaoju.corebanking.erp.adaptor.repository.converter.SofiGlShenmaModelConverter;
import com.xiaoju.corebanking.erp.adaptor.service.file.ShenmaDataProcessService;
import com.xiaoju.corebanking.erp.adaptor.service.file.enums.ShenmaFieldEnum;
import com.xiaoju.corebanking.erp.adaptor.service.twotab.SequenceService;
import com.xiaoju.digitalbank.ddd.po.ExecResult;
import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/3
 */
@Service
@Slf4j
public class ShenmaDataProcessServiceImpl implements ShenmaDataProcessService {

    @Autowired
    private SofiGlShenmaRepository sofiGlShenmaRepository;

    @Autowired
    private SofiGlInterfaceHeaderRepository sofiGlInterfaceHeaderRepository;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private SofiGlShenmaHisRepository sofiGlShenmaHisRepository;

    @Autowired
    private SofiGlShenmaModelConverter sofiGlShenmaModelConverter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExecResult createShenmaHeaderRecords(String processDay) {
        try {
            if (!StringUtils.hasText(processDay)) {
                log.error("processDay不能为空");
                return ExecResult.error(CommonErrorNo.PARAM_ERROR, "processDay不能为空");
            }

            // 查询待处理的Shenma记录，按Reference分组
            List<SofiGlShenmaDO> shenmaRecords = sofiGlShenmaRepository.selectPendingShenmaRecords(ProcessStatusEnum.NEW, processDay);

            if (shenmaRecords.isEmpty()) {
                log.info("没有找到待处理的Shenma记录");
                return ExecResult.error(CommonErrorNo.PARAM_ERROR, "没有找到待处理的Shenma记录");
            }

            log.info("找到{}条Shenma记录", shenmaRecords.size());

            // 按Reference分组
            Map<String, List<SofiGlShenmaDO>> groupedRecords = shenmaRecords.stream()
                    .filter(record -> StringUtils.hasText(record.getLinkReference()))
                    .collect(Collectors.groupingBy(SofiGlShenmaDO::getLinkReference));

            log.info("按Reference分组后有{}个组", groupedRecords.size());

            List<SofiGlInterfaceHeaderDO> headerRecords = new ArrayList<>();
            int counter = 0;
            long currentGroupId = sequenceService.nextval("SHENMA_GROUP_ID");

            for (Map.Entry<String, List<SofiGlShenmaDO>> entry : groupedRecords.entrySet()) {
                counter++;
                if (counter > CommonConstant.GROUP_SIZE) {
                    currentGroupId = sequenceService.nextval("SHENMA_GROUP_ID");
                    counter = 1;
                }

                String reference = entry.getKey();
                List<SofiGlShenmaDO> records = entry.getValue();

                SofiGlInterfaceHeaderDO headerDO = new SofiGlInterfaceHeaderDO();
                headerDO.setSystemCode(SourceSysEnum.SHEN_MA.getCode());
                headerDO.setProcessDay(processDay);
                headerDO.setBatchId("0");
                headerDO.setExternalReference(reference);
                headerDO.setJournalCount((long) records.size());
                headerDO.setProcessStatus(ProcessStatusEnum.NEW);
                headerDO.setGroupId(currentGroupId);
                headerDO.setCreatedBy(CommonConstant.SYSTEM_USER);
                headerDO.setCreationDate(new Date());
                headerDO.setObjectVersionNumber(1L);
                headerDO.setProcessMessage("");
                headerDO.setLastModifyDate(new Date());
                headerDO.setLastModifiedBy(CommonConstant.SYSTEM_USER);
                headerRecords.add(headerDO);
            }

            // 批量插入头记录
            if (!headerRecords.isEmpty()) {
                sofiGlInterfaceHeaderRepository.batchInsert(headerRecords);
                log.info("成功创建{}条Shenma头记录", headerRecords.size());
            }

            return ExecResult.success();

        } catch (Exception e) {
            log.error("创建Shenma头记录失败", e);
            return ExecResult.error(CommonErrorNo.PARAM_ERROR, "创建Shenma头记录失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExecResult batchInsertShenmaData(String processDay, String fileName, String csvData) {
        log.info("开始批量插入Shenma数据，processDay: {}, fileName: {}", processDay, fileName);

        try {
            if (!StringUtils.hasText(processDay) || !StringUtils.hasText(fileName) || !StringUtils.hasText(csvData)) {
                log.error("参数不能为空");
                return ExecResult.error(CommonErrorNo.PARAM_ERROR, "参数不能为空");
            }

            List<SofiGlShenmaDO> shenmaList = parseCsvToShenmaList(csvData, processDay, fileName);

            if (shenmaList.isEmpty()) {
                log.info("没有解析到有效的Shenma数据");
                return ExecResult.error(CommonErrorNo.PARAM_ERROR, "没有解析到有效的Shenma数据");
            }

            List<SofiGlShenmaDO> toInsertList = new ArrayList<>();
            List<SofiGlShenmaDO> toHisList = new ArrayList<>();


            for (SofiGlShenmaDO newRecord : shenmaList) {
                List<SofiGlShenmaDO> existingRecords = sofiGlShenmaRepository.findByIndexFields(processDay, newRecord.getLinkReference());

                if (existingRecords.isEmpty()) {
                    toInsertList.add(newRecord);
                } else {
                    boolean hasImportedStatus = existingRecords.stream()
                        .anyMatch(record -> ProcessStatusEnum.IMPORTED.equals(record.getProcessStatus())
                                || ProcessStatusEnum.VALIDATED.equals(record.getProcessStatus())
                                || ProcessStatusEnum.LOADED.equals(record.getProcessStatus())
                                || ProcessStatusEnum.UPLOADED.equals(record.getProcessStatus())
                                || ProcessStatusEnum.NEW.equals(record.getProcessStatus())
                                || ProcessStatusEnum.MAPPED.equals(record.getProcessStatus()));
                    
                    boolean hasFailedStatus = existingRecords.stream()
                        .anyMatch(record -> ProcessStatusEnum.IMPORT_FAILED.equals(record.getProcessStatus()) 
                                           || ProcessStatusEnum.VALIDATE_FAILED.equals(record.getProcessStatus())
                                           || ProcessStatusEnum.UPLOAD_FAILED.equals(record.getProcessStatus())
                                           || ProcessStatusEnum.LOAD_FAILED.equals(record.getProcessStatus())
                                           || ProcessStatusEnum.MAP_FAILED.equals(record.getProcessStatus()));

                    if (hasImportedStatus) {
                        log.info("发现成功状态的重复数据，linkReference: {}, 文件数据将进入历史表", newRecord.getLinkReference());
                        newRecord.setProcessMessage("sofi_gl_shenma里面有相同的数据");
                        newRecord.setProcessStatus(ProcessStatusEnum.NEW);
                        toHisList.add(newRecord);
                    } else if (hasFailedStatus) {
                        log.info("发现失败状态的重复数据，linkReference: {}, 将替换现有数据", newRecord.getLinkReference());
                        
                        for (SofiGlShenmaDO existingRecord : existingRecords) {
                            existingRecord.setProcessMessage("被新文件数据替换");
                            toHisList.add(existingRecord);
                        }
                        
                        sofiGlShenmaRepository.deleteByIndexFields(processDay, newRecord.getLinkReference());
                        
                        toInsertList.add(newRecord);
                    } else {
                        log.info("发现其他状态的重复数据，linkReference: {}, 将替换现有数据", newRecord.getLinkReference());
                        
                        for (SofiGlShenmaDO existingRecord : existingRecords) {
                            existingRecord.setProcessMessage("被新文件数据替换");
                            toHisList.add(existingRecord);
                        }
                        
                        sofiGlShenmaRepository.deleteByIndexFields(processDay, newRecord.getLinkReference());
                        
                        toInsertList.add(newRecord);
                    }
                }
            }

            // 批量插入历史数据
            if (!toHisList.isEmpty()) {
                List<com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlShenmaPO> hisPOList = 
                    toHisList.stream()
                        .map(sofiGlShenmaModelConverter::convert)
                        .collect(Collectors.toList());
                sofiGlShenmaHisRepository.backupShenmaHis(hisPOList);
                log.info("成功备份{}条数据到历史表", hisPOList.size());
            }

            // 批量插入新数据
            if (!toInsertList.isEmpty()) {
                sofiGlShenmaRepository.batchInsert(toInsertList);
                log.info("成功插入{}条新Shenma数据", toInsertList.size());
            }

            return ExecResult.success();

        } catch (Exception e) {
            log.error("批量插入Shenma数据失败", e);
            return ExecResult.error(CommonErrorNo.PARAM_ERROR, "批量插入Shenma数据失败: " + e.getMessage());
        }
    }

    /**
     * 解析CSV数据为Shenma对象列表
     */
    private List<SofiGlShenmaDO> parseCsvToShenmaList(String csvData, String processDay, String fileName) {
        List<SofiGlShenmaDO> shenmaList = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new StringReader(csvData));
             CSVParser csvParser = CSVFormat.DEFAULT
                     .withFirstRecordAsHeader()
                     .withIgnoreEmptyLines(true)
                     .withTrim()
                     .parse(reader)) {

            Map<String, Integer> headerMap = csvParser.getHeaderMap();
            if (headerMap == null || headerMap.isEmpty()) {
                log.error("CSV文件{}为空或标题行无效", fileName);
                return shenmaList;
            }

            String[] headers = headerMap.keySet().toArray(new String[0]);
            int actualCount = 0;

            // 处理每一行数据
            for (CSVRecord record : csvParser) {
                actualCount++;

                try {
                    String[] values = new String[record.size()];
                    for (int i = 0; i < record.size(); i++) {
                        values[i] = record.get(i);
                    }

                    SofiGlShenmaDO shenmaDO = createShenmaRecord(headers, values, processDay, fileName);
                    shenmaList.add(shenmaDO);

                } catch (Exception e) {
                    log.error("处理文件{}第{}行时发生错误: {}", fileName, actualCount + 1, e.getMessage());
                }
            }

            log.info("成功解析CSV文件{}，共{}行数据", fileName, shenmaList.size());

        } catch (IOException e) {
            log.error("解析CSV文件{}时发生错误: {}", fileName, e.getMessage());
            throw new RuntimeException("解析CSV数据失败", e);
        } catch (Exception e) {
            log.error("解析CSV数据失败", e);
            throw new RuntimeException("解析CSV数据失败", e);
        }

        return shenmaList;
    }


    private SofiGlShenmaDO createShenmaRecord(String[] headers, String[] values, String processDay, String fileName) {
        SofiGlShenmaDO shenmaDO = new SofiGlShenmaDO();
        shenmaDO.initializeDefaultValues();
        shenmaDO.setProcessDay(processDay);
        shenmaDO.setFileName(fileName);
        shenmaDO.setProcessStatus(ProcessStatusEnum.NEW);
        shenmaDO.setCreatedBy(CommonConstant.SYSTEM_USER);

        for (int i = 0; i < Math.min(headers.length, values.length); i++) {
            setShenmaFieldValue(shenmaDO, headers[i], values[i]);
        }

        return shenmaDO;
    }

    private void setShenmaFieldValue(SofiGlShenmaDO shenmaDO, String fieldName, String value) {
        if (shenmaDO == null || fieldName == null || value == null) {
            return;
        }
        ShenmaFieldEnum.setField(shenmaDO, fieldName, value);
    }
} 