package com.xiaoju.corebanking.erp.adaptor.service.file.enums;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlSubjectDO;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 * @date 2025/7/3
 */
@Slf4j
public enum SofiGlSubjectEnum {
    SUBJECT_CODE("subjectcode", (entity, value) -> entity.setSubjectCode(value.trim())),
    SUBJECT_DESC("subjectdesc", (entity, value) -> entity.setSubjectDesc(value.trim())),
    SUBJECT_DESC_EN("subjectdescen", (entity, value) -> entity.setSubjectDescEn(value.trim())),
    CONTROL_SUBJECT("controlsubject", (entity, value) -> entity.setControlSubject(value.trim())),
    BSPL_TYPE("bspltype", (entity, value) -> entity.setBsplType(value.trim())),
    GL_TYPE("gltype", (entity, value) -> entity.setGlType(value.trim())),
    SUBJECT_TYPE("subjecttype", (entity, value) -> entity.setSubjectType(value.trim())),
    BALANCE_WAY("balanceway", (entity, value) -> entity.setBalanceWay(value.trim())),
    SUBJECT_STATUS("subjectstatus", (entity, value) -> entity.setSubjectStatus(value.trim())),
    SUBJECT_LEVEL("subjectlevel", (entity, value) -> entity.setSubjectLevel(value.trim())),
    MANUAL_ACCOUNT("manualaccount", (entity, value) -> entity.setManualAccount(value.trim())),
    SPECIAL_BOOKKEEPING("specialbookkeeping", (entity, value) -> entity.setSpecialBookkeeping(value.trim())),
    OD_FACILITY("odfacility", (entity, value) -> entity.setOdFacility(value.trim())),
    RANGE_NO("rangeno", (entity, value) -> entity.setRangeNo(value.trim())),
    SUBJECT_SET_NO("subjectsetno", (entity, value) -> entity.setSubjectSetNo(value.trim())),
    REVALUE_RATE_TYPE("revalueratetype", (entity, value) -> entity.setRevalueRateType(value.trim())),
    SYSTEM_ID("systemid", (entity, value) -> entity.setSystemId(value.trim())),
    MEASUREMENT_ATTR("measurementattr", (entity, value) -> entity.setMeasurementAttr(value.trim())),
    ITEM_SEGREGATION("itemsegregation", (entity, value) -> entity.setItemSegregation(value.trim())),
    COMPANY("company", (entity, value) -> entity.setCompany(value.trim())),
    TRAN_TIMESTAMP("trantimestamp", (entity, value) -> entity.setTranTimestamp(value.trim())),
    PAY_REC("payrec", (entity, value) -> entity.setPayRec(value.trim())),
    SUBJECT_REMARK("subjectremark", (entity, value) -> entity.setSubjectRemark(value.trim()));

    private final String fieldName;
    private final BiConsumer<SofiGlSubjectDO, String> setter;
    private static final Map<String, SofiGlSubjectEnum> NAME_MAP = new HashMap<>();

    static {
        for (SofiGlSubjectEnum field : values()) {
            NAME_MAP.put(field.fieldName, field);
        }
    }

    SofiGlSubjectEnum(String fieldName, BiConsumer<SofiGlSubjectDO, String> setter) {
        this.fieldName = fieldName;
        this.setter = setter;
    }

    public static void setField(SofiGlSubjectDO entity, String fieldName, String value) {
        if (entity == null || fieldName == null || value == null) {
            return;
        }

        String normalizedField = fieldName.replaceAll("\\p{C}", "").toLowerCase().replace("_", "");
        SofiGlSubjectEnum field = NAME_MAP.get(normalizedField);

        if (field != null) {
            try {
                if (!value.trim().isEmpty()) {
                    field.setter.accept(entity, value);
                }
            } catch (NumberFormatException e) {
                log.error("SofiGlSubjectEnum转换字段{}的值{}失败: {}", fieldName, value, e.getMessage());
            } catch (Exception e) {
                log.error("SofiGlSubjectEnum设置字段{}的值{}失败: {}", fieldName, value, e.getMessage());
            }
        } else {
            log.warn("SofiGlSubjectEnum未找到字段映射: {}", fieldName);
        }
    }
}