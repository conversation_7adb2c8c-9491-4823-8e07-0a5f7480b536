package com.xiaoju.corebanking.erp.adaptor.service.transaction.impl;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.enums.SourceSysEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;
import com.xiaoju.corebanking.erp.adaptor.service.header.SofiGlInterfaceHeaderService;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlShenMaService;
import com.xiaoju.corebanking.erp.adaptor.service.transaction.SofiShenMaTransactionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * @Description: 类描述
 * @author: didi
 **/
@Slf4j
@Service
public class SofiShenMaTransactionServiceImpl implements SofiShenMaTransactionService {

    @Autowired
    private SofiGlShenMaService sofiGlShenMaService;
    @Autowired
    private SofiGlInterfaceHeaderService sofiGlInterfaceHeaderService;

    @Transactional(value = "erpTransactionManager", rollbackFor = Exception.class)
    @Override
    public void updateShenMaInterfaceAndHeader(SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO, SofiGlShenmaDO sofiGlShenmaDO, ProcessStatusEnum newStatus) {
        SofiGlInterfaceHeaderDO headerUpdate = new SofiGlInterfaceHeaderDO();
        headerUpdate.setProcessStatus(newStatus);
        sofiGlInterfaceHeaderService.updateInterfaceHeader(sofiGlInterfaceHeaderDO, headerUpdate, SourceSysEnum.SHEN_MA.getCode());
        SofiGlShenmaDO update = new SofiGlShenmaDO();
        update.setProcessStatus(newStatus);
        if(ProcessStatusEnum.PROCESSING.equals(newStatus)) {
            update.setGroupId(sofiGlShenmaDO.getGroupId());
        }
        update.setProcessMessage(sofiGlShenmaDO.getProcessMessage());
        sofiGlShenMaService.updateShenmaInterfaceByReference(sofiGlShenmaDO, update);
    }
}
