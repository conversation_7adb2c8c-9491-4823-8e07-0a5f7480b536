#!/bin/bash

#java path
export JAVA_HOME="/usr/local/jdk1.8.0_411"
export JRE_HOME="${JAVA_HOME}"/jre
export SW_AGENT_NAME=erp-adaptor-bootstrap
export SW_AGENT_INSTANCE_NAME=$(hostname)
export SW_LOGGING_MAX_HISTORY_FILES=1
export SW_JDBC_TRACE_SQL_PARAMETERS=true
#收集http调用参数信息
export SW_PLUGIN_SPRINGMVC_COLLECT_HTTP_PARAMS=true
export SW_PLUGIN_HTTPCLIENT_COLLECT_HTTP_PARAMS=true
export SW_PLUGIN_HTTP_INCLUDE_HTTP_HEADERS=didi-header-rid

#https://www.huaweicloud.com/articles/b86de23d6c3d5a161b25b1013a388d8d.html
#java options
export XMS="-Xms2048m" #初始堆大小
export XMX="-Xmx4096m" #最大堆大小
export XMN="-Xmn512m" #年轻代大小
export X_PERM_SIZE="-XX:MetaspaceSize=128M" #持久代大小
export X_MAX_PERM_SIZE="-XX:MaxMetaspaceSize=256M" #持久代最大值
export X_SURVIVOR_RATIO="-XX:SurvivorRatio=4" #Eden区与Survivor区的大小比值

GC_LOG="$SERVICE_HOME/logs/gc.$(date "+%Y%m%d%H%M%S.%N").log"

JVM_OPTS="$XMS $XMX $XMN $X_PERM_SIZE $X_MAX_PERM_SIZE $X_SERVIVOR_RATIO $JAVA_AGENT -XX:+UseConcMarkSweepGC -XX:+ParallelRefProcEnabled -XX:+UseParNewGC -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xloggc:$GC_LOG -XX:CMSInitiatingOccupancyFraction=40 -XX:+UseCMSInitiatingOccupancyOnly -XX:+CMSClassUnloadingEnabled -XX:+DisableExplicitGC -XX:+PrintPromotionFailure -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$SERVICE_HOME/logs/oom.dump -Dlog4j2.formatMsgNoLookups=true"
