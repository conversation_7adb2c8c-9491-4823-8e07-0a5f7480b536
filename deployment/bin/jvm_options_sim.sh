#!/bin/bash
# default service home
export SERVICE_HOME="$(cd "${SCRIPT_DIR}/.."; pwd)"

#java path
export JAVA_HOME="/usr/local/jdk1.8.0_202"
export JRE_HOME="${JAVA_HOME}"/jre

#https://www.huaweicloud.com/articles/b86de23d6c3d5a161b25b1013a388d8d.html
#java options
export XMS="-Xms1024M" #初始堆大小
export XMX="-Xmx2048M" #最大堆大小
export XMN="-Xmn512m" #年轻代大小
export X_PERM_SIZE="-XX:MetaspaceSize=128M" #持久代大小
export X_MAX_PERM_SIZE="-XX:MaxMetaspaceSize=256M" #持久代最大值
export X_SURVIVOR_RATIO="-XX:SurvivorRatio=4" #Eden区与Survivor区的大小比值

GC_LOG="$SERVICE_HOME/logs/gc.$(date "+%Y%m%d%H%M%S.%N").log"

JVM_OPTS="$XMS $XMX $XMN $X_PERM_SIZE $X_MAX_PERM_SIZE $X_SERVIVOR_RATIO -XX:+UseConcMarkSweepGC -XX:+ParallelRefProcEnabled -XX:+UseParNewGC -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xloggc:$GC_LOG -XX:CMSInitiatingOccupancyFraction=40 -XX:+UseCMSInitiatingOccupancyOnly -XX:+CMSClassUnloadingEnabled -XX:+DisableExplicitGC -XX:+PrintPromotionFailure -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$SERVICE_HOME/logs/oom.dump -Dlog4j2.formatMsgNoLookups=true"

# 接入新的覆盖率方案 20250311
if [[ -f ./jacoco-files/scripts/control.sh ]]; then
  source ./jacoco-files/scripts/control.sh
  cover_control
  JVM_OPTS="${JVM_OPTS} $JACOCO_PARAMS"
fi