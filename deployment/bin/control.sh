#! /bin/bash

# 获取脚本地址
SERVICE_HOME="${BASH_SOURCE-$0}"
SERVICE_HOME="$(dirname "${SERVICE_HOME}")"
SERVICE_HOME="$(cd "${SERVICE_HOME}"; pwd)"

# 实际项目配置；文件在项目中的路径为：${project-name}/deployment/bin/xxx/project_env.sh
source ${SERVICE_HOME}/bin/project_env.sh
# jvm配置；文件在项目中的路径为：${project-name}/deployment/bin/xxx/jvm_options.sh
# 这个会区分环境，如jvm_options_sim.sh
source ${SERVICE_HOME}/bin/jvm_options.sh

# 获取机器所处的环境
pay_module_cluster_file="${SERVICE_HOME}/.deploy/service.cluster.txt" #us01-v，pus01-v pus01-v pus01-pre-v

pay_module_cluster=""

config_env="env/local"

# 参数获取
action="$@"

function checkParam(){
    if [[ -z "${action}" ]]; then
        echo "no action[start|restart|stop]"
        exit 1
    fi
}

function checkEnv() {
    if [[ ! -f "${pay_module_cluster_file}" ]]; then
        echo "cluster ${pay_module_cluster_file} is not file"
        exit 1
    fi
}

function init() {
    # 获取机器环境
    getEnv
}

function getEnv(){
    pay_module_cluster=`cat ${pay_module_cluster_file}`
}

function getPid() {
  pidInfo=( $( ps -ax|grep -v 'grep'|grep java | grep  ${PROJECT_JAR_NAME} ))
  if [ ! -n "$pidInfo" ]; then
      echo 0;
  else
      echo $pidInfo|awk '{print $1}'
  fi
}

function stopActionHandler() {
    local pid=`getPid`
    if [ ${pid} -ne 0 ];then
        kill -9 ${pid}
        echo "${pid} is stopped"
    else
        echo "can not find ${pid}, maybe it is already stopped"
    fi
}

function selectEnvDir() {
    case ${pay_module_cluster} in
      "us01-sim"* )
          source ${SERVICE_HOME}/bin/jvm_options_sim.sh
          config_env="env/us01-sim"
          ;;
      "pus01-v" )
          source ${SERVICE_HOME}/bin/jvm_options.sh
          config_env="env/pus01-prod";
          unzip -o /home/<USER>/skywalking/skywalking-agent.zip -d /home/<USER>/skywalking
          nohup /opt/aws/aws-otel-collector/bin/aws-otel-collector --config /opt/aws/aws-otel-collector/adot-config-pus01.yml start >/dev/null 2>&1 &
          ;;
      "pus01" )
          source ${SERVICE_HOME}/bin/jvm_options.sh
          config_env="env/pus01-prod";
          unzip -o /home/<USER>/skywalking/skywalking-agent.zip -d /home/<USER>/skywalking
          nohup /opt/aws/aws-otel-collector/bin/aws-otel-collector --config /opt/aws/aws-otel-collector/adot-config-pus01.yml start >/dev/null 2>&1 &
          ;;
      "pus03-v" )
          source ${SERVICE_HOME}/bin/jvm_options.sh
          config_env="env/pus03-prod";
          unzip -o /home/<USER>/skywalking/skywalking-agent.zip -d /home/<USER>/skywalking
          nohup /opt/aws/aws-otel-collector/bin/aws-otel-collector --config /opt/aws/aws-otel-collector/adot-config-pus03.yml start >/dev/null 2>&1 &
          ;;
      "pus03" )
          source ${SERVICE_HOME}/bin/jvm_options.sh
          config_env="env/pus03-prod";
          unzip -o /home/<USER>/skywalking/skywalking-agent.zip -d /home/<USER>/skywalking
          nohup /opt/aws/aws-otel-collector/bin/aws-otel-collector --config /opt/aws/aws-otel-collector/adot-config-pus03.yml start >/dev/null 2>&1 &
          ;;
      "pus01-pre-v" )
          source ${SERVICE_HOME}/bin/jvm_options.sh
          config_env="env/pus01-pre";
          unzip -o /home/<USER>/skywalking/skywalking-agent.zip -d /home/<USER>/skywalking
          nohup /opt/aws/aws-otel-collector/bin/aws-otel-collector --config /opt/aws/aws-otel-collector/adot-config-pus01.yml start >/dev/null 2>&1 &
          ;;
      *)
         echo "unknown pay cluster ${pay_module_cluster}"
         exit 1
    esac
}

function startActionHandler() {
    local pid=`getPid`

    if [ $pid -ne 0 ];then
        echo "warn: $SERVICE_NAME already start! (pid=$pid)"
    else
      selectEnvDir
      startCmd="$JAVA_HOME/bin/java $JVM_OPTS -jar ${PROJECT_JAR_NAME} --spring.config.additional-location=classpath:/${config_env}/"
      echo "startCmd="$startCmd
      nohup $JAVA_HOME/bin/java $JVM_OPTS -DCLUSTER=${pay_module_cluster} -jar ${PROJECT_JAR_NAME} --spring.config.additional-location=classpath:/${config_env}/ >>server.out.$(date "+%Y%m%d%H") 2>&1 &
      pid=`getPid`
      if [ $pid -ne 0 ]; then
         echo "(pid=${pid}) [OK]"
      else
         echo "[Failed]"
      fi
    fi
}

# 启动命令
checkParam
checkEnv
init
if [[ "${action}" == "stop" ]]; then
    stopActionHandler
elif [[ "${action}" == "start" ]]; then
    startActionHandler
elif [[ "${action}" == "restart" ]]; then
    stopActionHandler
    startActionHandler
fi
