#!/usr/bin/env bash
set -e

mvn clean

# 当前的 module 模块名
MODULE_NAME=erp-adaptor-bootstrap

# build.sh所在文件的目录创建一个output
SCRIPT_DIR="${BASH_SOURCE-$0}"
SCRIPT_DIR="$(dirname "${SCRIPT_DIR}")"
SCRIPT_DIR="$(cd "${SCRIPT_DIR}"; pwd)"
OUTPUT_DIR="${SCRIPT_DIR}/output"
SHELL_DIR="${OUTPUT_DIR}/bin"

export JAVA_HOME="/usr/local/jdk1.8.0_411"
export MAVEN_OPTS="-Xms256m -Xmx1028m -Xss20m"

BUILD_DIR=${SCRIPT_DIR}/..


# -U:强制检查所有SNAPSHOT依赖
# -e:构建异常时，打印完整的stack trace
# -B:批处理模式构建项目
BUILD_COMMAND="mvn package -Dmaven.test.skip=true -B -e -U -am -pl ${MODULE_NAME}"
#BUILD_COMMAND="mvn package -B -e -U -am -pl ${MODULE_NAME}"

cd ${BUILD_DIR}
${BUILD_COMMAND}

rm -rf ${OUTPUT_DIR}
mkdir -p ${OUTPUT_DIR}
mkdir -p ${SHELL_DIR}

# 接入新版覆盖率方案 20250311
pwd
if [[ "${COV}" == "yes" ]]; then
    cover_tool_url="https://artifactory.intra.xiaojukeji.com/artifactory/nuwa-local/cover/cover-tools-java.tar.gz"
    curl -sSL ${cover_tool_url} | tar -xz
    source ./cover-tools-java/scripts/build.sh
    cover_build
    cp -r ./output/jacoco-files/ ${OUTPUT_DIR}/
fi

cp -rf ${SCRIPT_DIR}/target ${OUTPUT_DIR}/
cp ${BUILD_DIR}/deployment/bin/control.sh ${OUTPUT_DIR}/
cp ${BUILD_DIR}/deployment/bin/jvm_options.sh ${SHELL_DIR}/
cp ${BUILD_DIR}/deployment/bin/jvm_options_sim.sh ${SHELL_DIR}/
cp ${BUILD_DIR}/deployment/bin/${MODULE_NAME}/project_env.sh ${SHELL_DIR}/
cp ${SCRIPT_DIR}/target/${MODULE_NAME}.jar ${OUTPUT_DIR}/
cp ${SCRIPT_DIR}/check_status.sh ${OUTPUT_DIR}/

