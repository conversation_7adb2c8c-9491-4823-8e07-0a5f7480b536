<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xiaoju.corebanking</groupId>
        <artifactId>erp-adaptor</artifactId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>erp-adaptor-bootstrap</artifactId>
    <version>0.0.1-SNAPSHOT</version>


    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-expression</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context-support</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-webmvc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-expression</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>

        <!-- spring 测试 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
        </dependency>

        <dependency>
            <groupId>kms</groupId>
            <artifactId>didi-java-sdk-kms</artifactId>
        </dependency>
        <!--单测依赖模块 start-->
        <dependency>
            <groupId>com.xiaoju.corebanking</groupId>
            <artifactId>erp-adaptor-service</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xiaoju.corebanking</groupId>
            <artifactId>erp-adaptor-repository</artifactId>
        </dependency>

        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-all</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.jacoco</groupId>
            <artifactId>org.jacoco.agent</artifactId>
            <classifier>runtime</classifier>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-launcher</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-commons</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.testable</groupId>
            <artifactId>testable-all</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>erp-adaptor-bootstrap</finalName>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
            </testResource>
            <testResource>
                <directory>src/main/resources</directory>
            </testResource>
        </testResources>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <configuration>
                    <destFile>target/jacoco-ut.exec</destFile>
                    <dataFile>target/jacoco-ut.exec</dataFile>
                    <output>file</output>
                    <append>true</append>
                    <excludes>
                        <exclude>com/xiaoju/corebanking/erp/adaptor/common/**</exclude>
                        <exclude>com/xiaoju/corebanking/erp/adaptor/controller/**/**</exclude>
                        <exclude>com/xiaoju/corebanking/erp/adaptor/job/**/**</exclude>
                        <exclude>com/xiaoju/corebanking/erp/adaptor/config/**/**</exclude>
                        <exclude>com/xiaoju/corebanking/erp/adaptor/interceptor/**/**</exclude>
                        <exclude>com/xiaoju/corebanking/erp/adaptor/jwt/**/**</exclude>
                        <exclude>com/xiaoju/corebanking/erp/adaptor/pipeine/**/**</exclude>

                        <exclude>com/xiaoju/corebanking/erp/adaptor/repository/mybatis/**</exclude>
                        <exclude>com/xiaoju/corebanking/erp/adaptor/repository/domain/**</exclude>
                        <exclude>com/xiaoju/corebanking/erp/adaptor/service/common/MinioFileOperator.class</exclude>
                        <exclude>com/xiaoju/corebanking/erp/adaptor/service/common/AbstractDataSyncService.class</exclude>
                        <exclude>com/xiaoju/corebanking/erp/adaptor/service/file/impl/S3FileOperationServiceImpl.class</exclude>
                        <exclude>com/xiaoju/corebanking/erp/adaptor/service/file/impl/SofiGlSubjectSyncServiceImpl.class</exclude>
                        <exclude>com/xiaoju/corebanking/erp/adaptor/service/file/impl/SofiGlAcctHistSyncServiceImpl.class</exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <id>report-aggregate</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report-aggregate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${surfire.version}</version>
                <configuration>
                    <forkCount>3</forkCount>
                    <reuseForks>false</reuseForks>
                    <testFailureIgnore>true</testFailureIgnore>
                </configuration>
            </plugin>

        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>central</id>
            <name>artifactory-main-releases</name>
            <url>
                http://artifactory.intra.xiaojukeji.com:80/artifactory/libs-release
            </url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>artifactory-main-snapshots</name>
            <url>
                http://artifactory.intra.xiaojukeji.com:80/artifactory/libs-snapshot
            </url>
        </snapshotRepository>
    </distributionManagement>

</project>