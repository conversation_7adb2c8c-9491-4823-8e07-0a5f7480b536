package com.xiaoju.corebanking.erp.adaptor.config;


import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
@ConditionalOnProperty(value = "swagger.enabled", havingValue = "true")
public class SwaggerConfig {

    @Bean
    public OpenAPI openApi() {
        return new OpenAPI()
                .info(new Info().title("默认").description("默认").version("1.0.0"));

    }

    @Bean
    public GroupedOpenApi createApi() {
        return GroupedOpenApi.builder().group("默认").displayName("默认")
                .addOpenApiCustomiser(openApi -> openApi.info(new Info().title("默认").version("1.0.0")))
                .packagesToScan("com.xiaoju.corebanking.erp.adaptor.controller")
                .pathsToMatch("/**")
                .build();

    }
}
