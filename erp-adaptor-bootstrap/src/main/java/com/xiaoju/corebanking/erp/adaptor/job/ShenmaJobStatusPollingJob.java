package com.xiaoju.corebanking.erp.adaptor.job;

import com.xiaoju.corebanking.erp.adaptor.common.enums.SourceSysEnum;
import com.xiaoju.corebanking.erp.adaptor.service.polling.StatusPollingService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 轮询job状态的任务
 * @date 2025/5/20 10:30
 */
@Slf4j
@Service
public class ShenmaJobStatusPollingJob extends IJobHandler {

    @Resource
    private StatusPollingService statusPollingService;

    /**
     * 轮询load_request_id状态
     */
    @XxlJob("shenmaJobStatusPollingJob")
    public void execute() {
        String param = XxlJobHelper.getJobParam();
        log.info("开始执行ShenmaJobStatusPollingJob, 轮询load_request_id状态");
        statusPollingService.pollLoadRequestStatus(SourceSysEnum.SHEN_MA.getCode(),param);
        log.info("execute shenmaJobStatusPollingJob end");
    }
} 