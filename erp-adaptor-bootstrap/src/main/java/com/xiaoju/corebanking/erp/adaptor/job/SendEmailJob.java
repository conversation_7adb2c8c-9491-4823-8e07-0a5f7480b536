package com.xiaoju.corebanking.erp.adaptor.job;


import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.common.domain.ErpAdaptorMessageRequest;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiEmailDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderQueryDO;
import com.xiaoju.corebanking.erp.adaptor.service.common.MinioFileOperator;
import com.xiaoju.corebanking.erp.adaptor.service.file.FusionFileProcessService;
import com.xiaoju.corebanking.erp.adaptor.service.fusion.FusionAuthService;
import com.xiaoju.corebanking.erp.adaptor.service.inter.SofiGlInterfaceService;
import com.xiaoju.corebanking.erp.adaptor.service.message.MessageGateway;
import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import com.xiaoju.digitalbank.util.tools.ElvishDateUtils;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.FileWriter;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;


@Slf4j
@Service
public class SendEmailJob {
    @Autowired
    private SofiGlInterfaceService sofiGlInterfaceService;
    @Autowired
    private MessageGateway messageGateway;
    @Autowired
    private MinioFileOperator minioFileOperator;
    @XxlJob("sendEmailJob")
    public void sendEmailJob() {
        try {
            //nolint
            String processDay = ElvishDateUtils.formatToYYYYMMDD_Compact(System.currentTimeMillis());
            //查询当天的数据
            List<SofiEmailDO> list =  sofiGlInterfaceService.queryCountInterfaceEmailData(processDay);
            //nolint
            String strDate = ElvishDateUtils.formatToYYYYMMDDHHMMSS_Compact(System.currentTimeMillis());
            String fileName = strDate + CommonConstant.CSV_EXTENSION;
            CSVPrinter printer = new CSVPrinter(new FileWriter(fileName), CSVFormat.DEFAULT);
            printer.printRecord("Source System","File Name","Status","Count");
            for (SofiEmailDO sofiEmailDO : list) {
                printer.printRecord(sofiEmailDO.getSourceSys(),sofiEmailDO.getFileName(),sofiEmailDO.getProcessStatus(),sofiEmailDO.getCount());
            }
            // 关闭printer
            printer.flush();
            printer.close();
            InputStream input = Files.newInputStream(Paths.get(fileName));
            minioFileOperator.upload(fileName,input);
            String fileUrl = minioFileOperator.getUrl(fileName);
            ErpAdaptorMessageRequest erpAdaptorMessageRequest = new ErpAdaptorMessageRequest();
            List<String> attachements = new ArrayList<>();
            attachements.add(fileUrl);
            erpAdaptorMessageRequest.setAttachments(attachements);
            messageGateway.sendEmail(erpAdaptorMessageRequest);
            log.info("sendEmailJob success");
        } catch (Exception e) {
            log.error("pushFusionDataJob exception errorNO ={}", CommonErrorNo.FAIL, e);
        }
    }
}
