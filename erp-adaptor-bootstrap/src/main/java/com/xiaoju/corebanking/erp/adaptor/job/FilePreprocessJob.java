package com.xiaoju.corebanking.erp.adaptor.job;

import com.xiaoju.corebanking.erp.adaptor.service.file.FilePreprocessService;
import com.xiaoju.digitalbank.ddd.po.ExecResult;
import com.xiaoju.godson.common.utils.JsonUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description STEP1 文件预处理任务
 * @date 2025/5/16 16:50
 */
@Service
@Slf4j
public class FilePreprocessJob extends IJobHandler {

    @Value("${fusion.path.safi}")
    private String filePath;

    @Resource
    private FilePreprocessService filePreprocessService;

    @XxlJob("filePreprocessJob")
    public void execute() {
        log.info("execute filePreprocessJob start");
        String param = XxlJobHelper.getJobParam();
        ExecResult result = filePreprocessService.processFile(filePath,param);
        log.info("execute filePreprocessJob end，result:{}", JsonUtil.toString(result));
    }
}
