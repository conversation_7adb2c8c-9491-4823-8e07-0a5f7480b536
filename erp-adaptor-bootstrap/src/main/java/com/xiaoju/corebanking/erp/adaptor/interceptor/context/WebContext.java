package com.xiaoju.corebanking.erp.adaptor.interceptor.context;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> @since 2024/11/18 12:51
 */
public class WebContext {

    /**
     * 语言
     */
    public static final String CONTEXT_LANG = "lang";

    /**
     * APP ID
     */
    public static final String CONTEXT_APP_ID = "appid";

    /**
     * 上下文存储
     */
    private static final ThreadLocal<ConcurrentHashMap<String, String>> WEB_CONTEXT = ThreadLocal.withInitial(() -> new ConcurrentHashMap(4));

    /**
     * 上下文变量设置
     */
//    public static void put(String key, String value){
//        if (StringUtils.isAnyBlank(key, value)) {
//            return;
//        }
//        getContextMap().put(key, value);
//    }

    /**
     * 获取语言
     */
    public static String getLang() {
        return getContextMap().get(CONTEXT_LANG);
    }

    /**
     * 获取APP ID
     */
//    public static Integer getAppId() {
//        return CommonUtils.parseInt(getContextMap().get(CONTEXT_APP_ID));
//    }

    /**
     * 获取上下文数据
     */
    private static Map<String, String> getContextMap() {
        return WEB_CONTEXT.get();
    }

    /**
     * 清空上下文
     */
    public static void clear(){
        WEB_CONTEXT.remove();
    }
}

