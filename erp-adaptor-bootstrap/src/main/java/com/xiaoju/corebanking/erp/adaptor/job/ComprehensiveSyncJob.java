package com.xiaoju.corebanking.erp.adaptor.job;

import com.xiaoju.corebanking.erp.adaptor.controller.dto.SyncTaskResultDTO;
import com.xiaoju.corebanking.erp.adaptor.service.file.SofiGlAcctHistSyncService;
import com.xiaoju.corebanking.erp.adaptor.service.file.SofiGlSubjectSyncService;
import com.xiaoju.corebanking.erp.adaptor.service.file.VoucherGroupSyncService;
import com.xiaoju.digitalbank.ddd.po.ExecResult;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * Voucher Group、sofi_gl_acct_hist、sofi_gl_subject文件处理
 * <AUTHOR>
 * @date 2025/1/16
 */
@Slf4j
@Service
public class ComprehensiveSyncJob extends IJobHandler {

    @Value("${fusion.path.shenma}")
    private String basePath;

    @Resource
    private SofiGlSubjectSyncService sofiGlSubjectSyncService;

    @Resource
    private VoucherGroupSyncService voucherGroupSyncService;

    @Resource
    private SofiGlAcctHistSyncService sofiGlAcctHistSyncService;
    @Autowired
    @Qualifier("comprebenSivePool")
    private ThreadPoolTaskExecutor  comprebenSivePool;

    @XxlJob("comprehensiveSyncJob")
    @Override
    public void execute() {
        log.info("execute comprehensiveSyncJob start");
        String param = XxlJobHelper.getJobParam();

        try {
            CompletableFuture<SyncTaskResultDTO> sofiGlSubjectTask = CompletableFuture.supplyAsync(() ->
                    executeSofiGlSubjectSync(param), comprebenSivePool);

            CompletableFuture<SyncTaskResultDTO> voucherGroupTask = CompletableFuture.supplyAsync(() ->
                    executeVoucherGroupSync(param), comprebenSivePool);

            CompletableFuture<SyncTaskResultDTO> sofiGlAcctHistTask = CompletableFuture.supplyAsync(() ->
                    executeSofiGlAcctHistSync(param), comprebenSivePool);

            CompletableFuture<Void> allTasks = CompletableFuture.allOf(
                    sofiGlSubjectTask, voucherGroupTask, sofiGlAcctHistTask);

            allTasks.get(30, TimeUnit.MINUTES);

            List<SyncTaskResultDTO> results = new ArrayList<>();
            results.add(sofiGlSubjectTask.get());
            results.add(voucherGroupTask.get());
            results.add(sofiGlAcctHistTask.get());

            processResults(results);

        } catch (Exception e) {
            log.error("execute comprehensiveSyncJob exception", e);
            XxlJobHelper.handleFail("文件处理异常: " + e.getMessage());
        } finally {
            comprebenSivePool.shutdown();
        }
    }

    /**
     * 执行SofiGlSubject
     */
    private SyncTaskResultDTO executeSofiGlSubjectSync(String param) {
        String taskName = "SofiGlSubject";
        try {
            ExecResult result = sofiGlSubjectSyncService.processSofiGlSubjectFile(basePath, param);
            return new SyncTaskResultDTO(taskName, result, null);
        } catch (Exception e) {
            log.error("{}文件处理执行异常", taskName, e);
            return new SyncTaskResultDTO(taskName, null, e);
        }
    }

    /**
     * 执行VoucherGroup
     */
    private SyncTaskResultDTO executeVoucherGroupSync(String param) {
        String taskName = "VoucherGroup";
        try {
            ExecResult result = voucherGroupSyncService.processVoucherGroupMapping(basePath, param);
            return new SyncTaskResultDTO(taskName, result, null);
        } catch (Exception e) {
            log.error("{}文件处理执行异常", taskName, e);
            return new SyncTaskResultDTO(taskName, null, e);
        }
    }

    /**
     * 执行SofiGlAcctHist
     */
    private SyncTaskResultDTO executeSofiGlAcctHistSync(String param) {
        String taskName = "SofiGlAcctHist";
        try {
            ExecResult result = sofiGlAcctHistSyncService.processSofiGlAcctHistFile(basePath, param);
            return new SyncTaskResultDTO(taskName, result, null);
        } catch (Exception e) {
            log.error("{}文件处理执行异常", taskName, e);
            return new SyncTaskResultDTO(taskName, null, e);
        }
    }

    private void processResults(List<SyncTaskResultDTO> results) {
        int successCount = 0;
        int failCount = 0;
        List<String> successTasks = new ArrayList<>();
        List<String> failTasks = new ArrayList<>();

        for (SyncTaskResultDTO taskResult : results) {
            if (taskResult.isSuccess()) {
                successCount++;
                successTasks.add(taskResult.getTaskName());
            } else {
                failCount++;
                failTasks.add(taskResult.getTaskName() + ": " + taskResult.getErrorMessage());
            }
        }

        log.info("文件处理执行完成 - 成功: {}, 失败: {}", successCount, failCount);

        if (failCount == 0) {
            String message = String.format("文件处理全部成功 (共%d个任务): %s", successCount, String.join(", ", successTasks));
            log.info("execute comprehensiveSyncJob end，result: 全部成功");
            XxlJobHelper.handleSuccess(message);
        } else if (successCount > 0) {
            String message = String.format("文件处理部分成功 - 成功%d个: [%s], 失败%d个: [%s]", successCount, String.join(", ", successTasks), failCount, String.join("; ", failTasks));
            log.warn("execute comprehensiveSyncJob end，result: 部分成功");
            XxlJobHelper.handleFail(message);
        } else {
            String message = String.format("文件处理全部失败 (共%d个任务): %s", failCount, String.join("; ", failTasks));
            log.error("execute comprehensiveSyncJob end，result: 全部失败");
            XxlJobHelper.handleFail(message);
        }
    }
} 