package com.xiaoju.corebanking.erp.adaptor.job;


import com.xiaoju.corebanking.erp.adaptor.common.enums.SourceSysEnum;
import com.xiaoju.corebanking.erp.adaptor.common.utils.ErpUtil;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SummaryResultDO;
import com.xiaoju.corebanking.erp.adaptor.service.file.FusionFileProcessService;
import com.xiaoju.corebanking.erp.adaptor.service.fusion.FusionAuthService;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlAcctHistService;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlInterfaceCommonService;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlShenMaService;
import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
public class CommonPushFusionDataJob {
    @Autowired
    private SofiGlInterfaceCommonService sofiGlInterfaceCommonService;

    @Autowired
    private FusionFileProcessService fusionFileProcessService;
    @Autowired
    private FusionAuthService fusionAuthService;

    @Autowired
    private SofiGlShenMaService sofiGlShenMaService;

    @Autowired
    private SofiGlAcctHistService sofiGlAcctHistService;

    @Value("${fusion.jwt.username}")
    private String username;
    @Autowired
    ErpUtil erpUtil;


    @XxlJob("commonPushFusionDataJob")
    public void commonPushFusionDataJob() {
        try {
            String param = XxlJobHelper.getJobParam();
            String finalProcessDay;
            if (StringUtils.isEmpty(param)) {
                finalProcessDay = erpUtil.getDateDirFromInput("");
            } else {
                finalProcessDay = param;
            }
            int pageNum = 1;
            int pageSize = 500;
            //初始化token
            String token = fusionAuthService.generateJwtToken(username);
            //1.进行汇总金额校验
            List<SummaryResultDO> summaryResultShenma = sofiGlShenMaService.queryShenMaSummary(finalProcessDay);
            List<SummaryResultDO> summaryResultAcctHis = sofiGlAcctHistService.queryAcctHisSummary(finalProcessDay);
            boolean allIncluded =  summaryResultShenma.stream().allMatch(summaryResultAcctHis::contains);
            if(!allIncluded) {
                log.info("不相等，邮件告警");
            }
            while (true) {
                log.info("validatedAndMappedDataJob start request={}=", finalProcessDay);
                List<Long> groupIds = sofiGlInterfaceCommonService.queryGroupIds(pageNum, pageSize, finalProcessDay);
                if (CollectionUtils.isEmpty(groupIds)) {
                    break;
                }
                fusionFileProcessService.processCSVFileToCommon(SourceSysEnum.SHEN_MA.getCode(), finalProcessDay, groupIds, token);
                pageNum++;
                log.info("pushData pageNum:{}", pageNum);
            }
        } catch (Exception e) {
            log.error("pushFusionDataJob exception errorNO ={}", CommonErrorNo.FAIL, e);
        }
    }
}
