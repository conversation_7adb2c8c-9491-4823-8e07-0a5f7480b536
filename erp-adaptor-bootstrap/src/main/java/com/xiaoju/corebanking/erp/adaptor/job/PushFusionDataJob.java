package com.xiaoju.corebanking.erp.adaptor.job;


import com.xiaoju.corebanking.erp.adaptor.common.utils.ErpUtil;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderQueryDO;
import com.xiaoju.corebanking.erp.adaptor.service.file.FusionFileProcessService;
import com.xiaoju.corebanking.erp.adaptor.service.fusion.FusionAuthService;
import com.xiaoju.corebanking.erp.adaptor.service.inter.SofiGlInterfaceService;
import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Slf4j
@Service
public class PushFusionDataJob {
    private static final Integer PAGE_SIZE = 500;

    @Autowired
    private SofiGlInterfaceService sofiGlInterfaceService;

    @Autowired
    private FusionFileProcessService fusionFileProcessService;
    @Autowired
    private FusionAuthService fusionAuthService;
    @Value("${fusion.jwt.username}")
    private String username;
    @Autowired
    ErpUtil erpUtil;


    @XxlJob("pushFusionDataJob")
    public void pushFusionDataJob() {
        try {
            String param = XxlJobHelper.getJobParam();
            String finalProcessDay;
            if (StringUtils.isEmpty(param)) {
                    finalProcessDay = erpUtil.getDateDirFromInput("");
            } else {
                    finalProcessDay = param;
            }
            int pageNum = 1;
            //初始化token
            String token = fusionAuthService.generateJwtToken(username);
            SofiGlInterfaceHeaderQueryDO sofiGlInterfaceHeaderQueryDO = new SofiGlInterfaceHeaderQueryDO();
            sofiGlInterfaceHeaderQueryDO.setPageSize(PAGE_SIZE);
            sofiGlInterfaceHeaderQueryDO.setProcessDay(finalProcessDay);
            while (true) {
                sofiGlInterfaceHeaderQueryDO.setPageNum(pageNum);
                log.info("validatedAndMappedDataJob start request={}=", sofiGlInterfaceHeaderQueryDO);
                List<Long> groupIds = sofiGlInterfaceService.queryGroupId(sofiGlInterfaceHeaderQueryDO);
                if(CollectionUtils.isEmpty(groupIds)){
                    break;
                }
                fusionFileProcessService.processCSVFile(groupIds,token);
                pageNum++;
                log.info("pushData pageNum:{}", pageNum);
            }
        } catch (Exception e) {
            log.error("pushFusionDataJob exception errorNO ={}", CommonErrorNo.FAIL, e);
        }
    }
}
