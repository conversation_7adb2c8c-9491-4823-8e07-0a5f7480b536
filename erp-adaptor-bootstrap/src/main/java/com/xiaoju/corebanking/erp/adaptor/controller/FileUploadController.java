package com.xiaoju.corebanking.erp.adaptor.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RestController
@Slf4j
public class FileUploadController {

    @Value("${file.local.fallback.path:/home/<USER>/erp-adaptor/file/}")
    private String localFallbackPath;


    /**
     * 上传文件到指定基础路径下的今日日期文件夹中
     * 例如：basePath=/home/<USER>/erp-adaptor/file/，会自动创建 /home/<USER>/erp-adaptor/file/20250605/ 文件夹并上传文件
     *
     * @param files 上传的文件
     * @return 上传结果
     */
    @PostMapping("/uploadWithDate")
    public ResponseEntity<Map<String, Object>> uploadFileWithDate(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam(value = "date", required = false) String dateParam) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (files == null || files.length == 0) {
                response.put("success", false);
                response.put("message", "上传文件不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 确保基础目录存在
            Path baseDir = Paths.get(localFallbackPath);
            if (!Files.exists(baseDir)) {
                Files.createDirectories(baseDir);
                log.info("创建基础目录: {}", baseDir);
            }

            String dateFolder;
            if (dateParam != null && !dateParam.trim().isEmpty()) {
                try {
                    LocalDate.parse(dateParam, DateTimeFormatter.ofPattern("yyyyMMdd"));
                    dateFolder = dateParam.trim();
                    log.info("使用指定日期: {}", dateFolder);
                } catch (Exception e) {
                    response.put("success", false);
                    response.put("message", "日期格式错误，请使用yyyyMMdd格式，例如：20250826");
                    return ResponseEntity.badRequest().body(response);
                }
            } else {
                dateFolder = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                log.info("使用当前日期: {}", dateFolder);
            }
            
            // 构建完整路径：基础路径/日期文件夹/文件名
            Path dateFolderPath = baseDir.resolve(dateFolder);
            
            // 确保日期文件夹存在
            if (!Files.exists(dateFolderPath)) {
                Files.createDirectories(dateFolderPath);
                log.info("创建日期目录: {}", dateFolderPath);
            }
            
            List<Map<String, Object>> uploadResults = new ArrayList<>();
            List<String> successFiles = new ArrayList<>();
            List<String> failedFiles = new ArrayList<>();
            
            for (MultipartFile file : files) {
                Map<String, Object> fileResult = new HashMap<>();
                fileResult.put("filename", file.getOriginalFilename());
                
                try {
                    if (file.isEmpty()) {
                        fileResult.put("success", false);
                        fileResult.put("message", "文件为空，跳过上传");
                        failedFiles.add(file.getOriginalFilename());
                    } else {
                        Path targetPath = dateFolderPath.resolve(file.getOriginalFilename());
                        
                        Files.copy(file.getInputStream(), targetPath, StandardCopyOption.REPLACE_EXISTING);
                        String md5 = calculateMD5(file.getBytes());
                        
                        fileResult.put("success", true);
                        fileResult.put("message", "上传成功");
                        fileResult.put("localPath", targetPath.toString());
                        fileResult.put("fileSize", file.getSize());
                        fileResult.put("md5", md5);
                        
                        successFiles.add(file.getOriginalFilename());
                        log.info("文件上传成功: {} -> {}, MD5: {}", file.getOriginalFilename(), targetPath, md5);
                    }
                } catch (IOException e) {
                    fileResult.put("success", false);
                    fileResult.put("message", "上传失败: " + e.getMessage());
                    failedFiles.add(file.getOriginalFilename());
                    log.error("文件上传失败: {}", file.getOriginalFilename(), e);
                } catch (NoSuchAlgorithmException e) {
                    fileResult.put("success", false);
                    fileResult.put("message", "计算MD5失败: " + e.getMessage());
                    failedFiles.add(file.getOriginalFilename());
                    log.error("计算MD5失败: {}", file.getOriginalFilename(), e);
                }
                
                uploadResults.add(fileResult);
            }
            
            response.put("success", failedFiles.isEmpty());
            response.put("message", String.format("上传完成，成功：%d个，失败：%d个", 
                                                successFiles.size(), failedFiles.size()));
            response.put("basePath", localFallbackPath);
            response.put("dateFolder", dateFolder);
            response.put("fullPath", localFallbackPath + dateFolder + "/");
            response.put("totalFiles", files.length);
            response.put("successCount", successFiles.size());
            response.put("failedCount", failedFiles.size());
            response.put("successFiles", successFiles);
            response.put("failedFiles", failedFiles);
            response.put("details", uploadResults);
            
            return ResponseEntity.ok(response);
            
        } catch (IOException e) {
            log.error("文件上传失败", e);
            response.put("success", false);
            response.put("message", "文件上传失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 计算文件的MD5值
     * @param fileBytes 文件字节数组
     * @return MD5字符串
     */
    private String calculateMD5(byte[] fileBytes) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] digest = md.digest(fileBytes);
        
        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }



    /**
     * 查看指定目录下的文件列表
     * @return 文件列表
     */
    @GetMapping("/list")
    public ResponseEntity<Map<String, Object>> listFiles() {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            Path dirPath = Paths.get(localFallbackPath);
            
            if (!Files.exists(dirPath)) {
                response.put("success", false);
                response.put("message", "目录不存在: " + dirPath);
                return ResponseEntity.badRequest().body(response);
            }
            
            if (!Files.isDirectory(dirPath)) {
                response.put("success", false);
                response.put("message", "指定路径不是目录: " + dirPath);
                return ResponseEntity.badRequest().body(response);
            }
            
            Map<String, Object> fileInfo = new HashMap<>();
            Files.list(dirPath).forEach(path -> {
                try {
                    Map<String, Object> info = new HashMap<>();
                    info.put("isDirectory", Files.isDirectory(path));
                    info.put("size", Files.isDirectory(path) ? 0 : Files.size(path));
                    info.put("lastModified", Files.getLastModifiedTime(path).toString());
                    fileInfo.put(path.getFileName().toString(), info);
                } catch (IOException e) {
                    log.warn("获取文件信息失败: {}", path, e);
                }
            });
            
            response.put("success", true);
            response.put("message", "获取文件列表成功");
            response.put("dir", localFallbackPath);
            response.put("localDir", dirPath.toString());
            response.put("files", fileInfo);
            
            return ResponseEntity.ok(response);
            
        } catch (IOException e) {
            log.error("列出文件失败: {}", localFallbackPath, e);
            response.put("success", false);
            response.put("message", "列出文件失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 删除指定文件
     * @param filename 要删除的文件名
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    public ResponseEntity<Map<String, Object>> deleteFile(@RequestParam("filename") String filename) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            
            Path targetPath = Paths.get(localFallbackPath, filename);
            
            if (!Files.exists(targetPath)) {
                response.put("success", false);
                response.put("message", "文件不存在: " + targetPath);
                return ResponseEntity.badRequest().body(response);
            }
            
            Files.delete(targetPath);
            log.info("文件删除成功: {}", targetPath);
            
            response.put("success", true);
            response.put("message", "文件删除成功");
            response.put("filename", filename);
            response.put("localPath", targetPath.toString());
            
            return ResponseEntity.ok(response);
            
        } catch (IOException e) {
            log.error("文件删除失败: {}/{}", localFallbackPath, filename, e);
            response.put("success", false);
            response.put("message", "文件删除失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
} 