package com.xiaoju.corebanking.erp.adaptor.controller;

import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.enums.SourceSysEnum;
import com.xiaoju.corebanking.erp.adaptor.common.errorno.ErpAdaptorErrorNo;
import com.xiaoju.corebanking.erp.adaptor.common.exception.ErpAdaptorBusinessException;
import com.xiaoju.corebanking.erp.adaptor.common.utils.ErpUtil;
import com.xiaoju.corebanking.erp.adaptor.common.utils.LogUtils;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.*;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValueExtraPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SofiGlInterfaceHeaderCustomerMapper;
import com.xiaoju.corebanking.erp.adaptor.service.file.FusionFileProcessService;
import com.xiaoju.corebanking.erp.adaptor.service.fusion.CoaValidationService;
import com.xiaoju.corebanking.erp.adaptor.service.fusion.FusionAuthService;
import com.xiaoju.corebanking.erp.adaptor.service.fusion.FusionStatusService;
import com.xiaoju.corebanking.erp.adaptor.service.header.SofiGlInterfaceHeaderService;
import com.xiaoju.corebanking.erp.adaptor.service.inter.SofiGlInterfaceService;
import com.xiaoju.corebanking.erp.adaptor.service.inter.SofiGlInterfaceValidatedService;
import com.xiaoju.corebanking.erp.adaptor.service.transaction.SofiGlInterfaceTransactionService;
import com.xiaoju.corebanking.erp.adaptor.service.twotab.TwoTabValueService;
import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import com.xiaoju.digitalbank.errorno.MessageErrorNo;
import com.xiaoju.digitalbank.util.tools.ElvishDateUtils;
import com.xiaoju.godson.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/18 14:44
 */
@RestController
@RequestMapping("/secondTest")
@Slf4j
public class ErpSecondTestController {
    private static Integer PAGE_SIZE = 500;

    @Autowired
    private SofiGlInterfaceHeaderService sofiGlInterfaceHeaderService;

    @Autowired
    private SofiGlInterfaceService sofiGlInterfaceService;

    @Autowired
    private SofiGlInterfaceTransactionService sofiGlInterfaceTransactionService;

    @Autowired
    private SofiGlInterfaceValidatedService sofiGlInterfaceValidatedService;

    @Autowired
    private TwoTabValueService twoTabValueService;

    @Autowired
    private CoaValidationService coaValidationService;

    @Autowired
    private FusionStatusService fusionStatusService;

    @Autowired
    private FusionAuthService fusionAuthService;
    @Value("${fusion.jwt.username}")
    private String username;
    @Autowired
    private FusionFileProcessService fusionFileProcessService;

    @Autowired
    @Qualifier("validateThreadPool")
    private ThreadPoolTaskExecutor validateThreadPool;


    @Resource
    @Qualifier("sofiGlInterfaceHeaderCustomerMapper")
    SofiGlInterfaceHeaderCustomerMapper sofiGlInterfaceHeaderCustomerMapper;

    @Autowired
    ErpUtil erpUtil;

    @PostMapping("/getGroupId")
    public String getGroupId(String processDay) {
        if (StringUtils.isEmpty(processDay)) {
            processDay = erpUtil.getDateDirFromInput("");
        }
        return JsonUtil.toString(sofiGlInterfaceHeaderCustomerMapper.selectGroupId(processDay,SourceSysEnum.SAFI.getCode()));
    }

    @PostMapping("/validateData")
    public CompletableFuture<Void> validateData(String processDay) {
        String finalProcessDay;
        try {
            if (StringUtils.isEmpty(processDay)) {
                finalProcessDay = erpUtil.getDateDirFromInput("");
            } else {
                finalProcessDay = processDay;
            }
            // 初始化token
            String token = fusionAuthService.generateJwtToken(username);
            Map<String, List<CuxTwoTabValueExtraPO>> cache = initMapping();
            Map<String, Map<String, CuxTwoTabValueExtraPO>> result = cache.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,  // 保持外层Map的key不变
                            entry -> entry.getValue().stream()  // 处理内层List
                                    .collect(Collectors.toMap(
                                            CuxTwoTabValueExtraPO::getValue1,  // 内层Map的key
                                            Function.identity(),               // 内层Map的value
                                            (existing, replacement) -> existing  // 处理重复key
                                    ))
                    ));
            List<CompletableFuture<Void>> pageFutures = new ArrayList<>();  // 改为页面级future列表
            //查询groupId
            List<Long> groupIds = sofiGlInterfaceHeaderService.findGroupIdByProcessDay(finalProcessDay, SourceSysEnum.SAFI.getCode());
            for (Long groupId : groupIds) {
//            Long groupId = 20003985L;
//            List<Long> groupIds = new ArrayList<>();
//            groupIds.add(groupId);

                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    log.info("当前线程名字：" + Thread.currentThread().getName());
                    try {
                        List<SofiGlInterfaceDO> glInterfaceList = sofiGlInterfaceService.querySofiGlInterfaceByGroupIdAndProcessDay(groupId, finalProcessDay);
                        process(glInterfaceList, token, groupId, result);
                    } catch (ErpAdaptorBusinessException e) {
                        log.error("validatedAndMappedDataJob error: {}", ErpAdaptorErrorNo.SYSTEM_ERROR_DEFAULT_FAIL, e);
                        throw new ErpAdaptorBusinessException(MessageErrorNo.SYSTEM_ERROR_DEFAULT_FAIL);
                    }
                }, validateThreadPool).exceptionally(e -> {
                    log.error("groupId:{},validatedAndMappedDataJob error: {}", groupId, ErpAdaptorErrorNo.SYSTEM_ERROR_DEFAULT_FAIL, e);
                    CompletableFuture<Void> failedFuture = new CompletableFuture<>();
                    failedFuture.completeExceptionally(e);
//                    return failedFuture;
                    return null;
                });
                pageFutures.add(future);
            }
            // 在所有processSecond完成后执行validateCoa
            return CompletableFuture.allOf(pageFutures.toArray(new CompletableFuture[0]))
                    .thenRun(() -> sofiGlInterfaceService.validateCoa(finalProcessDay))
                    .exceptionally(e -> {
                        log.error("Error after all processSecond operations: {}", e.getMessage(), e);
                        return null;
                    });
        } catch (Exception e) {
            log.error("validatedAndMappedDataJob error: {}", ErpAdaptorErrorNo.SYSTEM_ERROR_DEFAULT_FAIL, e);
            CompletableFuture<Void> failedFuture = new CompletableFuture<>();
            failedFuture.completeExceptionally(e);
            return failedFuture;
        }

    }

    @PostMapping("/pushData")
    public String pushData(String processDay) {
        String finalProcessDay;
        if (StringUtils.isEmpty(processDay)) {
            finalProcessDay = erpUtil.getDateDirFromInput("");
        } else {
            finalProcessDay = processDay;
        }
        try {
            int pageNum = 1;
            SofiGlInterfaceHeaderQueryDO sofiGlInterfaceHeaderQueryDO = new SofiGlInterfaceHeaderQueryDO();
            sofiGlInterfaceHeaderQueryDO.setPageSize(PAGE_SIZE);
            sofiGlInterfaceHeaderQueryDO.setProcessDay(finalProcessDay);
            //初始化token
            String token = fusionAuthService.generateJwtToken(username);
            while (true) {
                sofiGlInterfaceHeaderQueryDO.setPageNum(pageNum);
                log.info("validatedAndMappedDataJob start request={}=", sofiGlInterfaceHeaderQueryDO);
                List<Long> groupIds = sofiGlInterfaceService.queryGroupId(sofiGlInterfaceHeaderQueryDO);
                if (CollectionUtils.isEmpty(groupIds)) {
                    break;
                }
                fusionFileProcessService.processCSVFile(groupIds, token);
                pageNum++;
                log.info("pushData pageNum:{}", pageNum);

            }
        } catch (Exception e) {
            log.error("pushFusionDataJob exception errorNO ={}", CommonErrorNo.FAIL, e);
        }
        return "success";
    }

    private List<ValidationRequestDO> buildRequests(SofiGlInterfaceDO sofiGlInterfaceDO) {
        List<ValidationRequestDO> requests = new ArrayList<>();
        ValidationRequestDO validationRequestDO = new ValidationRequestDO();
        validationRequestDO.setLedgerName(sofiGlInterfaceDO.getLedgerName());
        validationRequestDO.setSegment1(sofiGlInterfaceDO.getSegment1());
        validationRequestDO.setSegment2(sofiGlInterfaceDO.getSegment2());
        validationRequestDO.setSegment3(sofiGlInterfaceDO.getSegment3());
        validationRequestDO.setSegment4(sofiGlInterfaceDO.getSegment4());
        validationRequestDO.setSegment5(sofiGlInterfaceDO.getSegment5());
        validationRequestDO.setSegment6(sofiGlInterfaceDO.getSegment6());
        validationRequestDO.setSegment7(sofiGlInterfaceDO.getSegment7());
        validationRequestDO.setSegment8(sofiGlInterfaceDO.getSegment8());
        validationRequestDO.setSegment9(sofiGlInterfaceDO.getSegment9());
        validationRequestDO.setSegment10(sofiGlInterfaceDO.getSegment10());
        requests.add(validationRequestDO);
        return requests;
    }

    private Map<String, List<CuxTwoTabValueExtraPO>> initMapping() {
        List<CuxTwoTabValueExtraPO> cuxTwoTabValuePOS = twoTabValueService.selectAllByFormCode();
        return cuxTwoTabValuePOS.stream().collect(Collectors.groupingBy(CuxTwoTabValueExtraPO::getFormCode));
    }

    private void process(List<SofiGlInterfaceDO> sofiGlInterfaceDOs, String token, Long groupId, Map<String, Map<String, CuxTwoTabValueExtraPO>> cache) {
        SofiGlInterfaceHeaderDO headerDO = new SofiGlInterfaceHeaderDO();
        headerDO.setGroupId(groupId);
        Map<Long, List<SofiGlInterfaceDO>> map = sofiGlInterfaceDOs.stream()
                    .collect(Collectors.groupingBy(SofiGlInterfaceDO::getPolizaId));
        for (SofiGlInterfaceDO sofiGlInterfaceDO : sofiGlInterfaceDOs) {
            headerDO.setProcessDay(sofiGlInterfaceDO.getProcessDay());
            headerDO.setExternalReference(sofiGlInterfaceDO.getPolizaId() + "");
            if (ProcessStatusEnum.MAP_FAILED.equals(sofiGlInterfaceDO.getProcessStatus()) || ProcessStatusEnum.NEW.equals(sofiGlInterfaceDO.getProcessStatus())) {
                sofiGlInterfaceTransactionService.updateInterfaceAndInterfaceHeader(headerDO, sofiGlInterfaceDO, ProcessStatusEnum.PROCESSING);
            }

            //3.数据mapping
            String errorMapMessage = sofiGlInterfaceValidatedService.mappedResult(sofiGlInterfaceDO, cache);
            log.info("polizaid {} mapping result:{}", sofiGlInterfaceDO.getPolizaId(), errorMapMessage);
            //4.验证VALIDATA
            String errorMessage = sofiGlInterfaceValidatedService.validated(sofiGlInterfaceDO, map);
            log.info("polizaid {} validated result:{}", sofiGlInterfaceDO.getPolizaId(), errorMessage);
            if (StringUtils.isNotEmpty(errorMessage) && !ErpAdaptorErrorNo.IMP_ERR_ERROR.getErrorMsg().equals(errorMessage)) {
                ///说明验证失败了/更新状态为VALIDATE_FAILED
                sofiGlInterfaceDO.setProcessMessage(errorMessage);
                headerDO.setProcessMessage(errorMessage);
                sofiGlInterfaceTransactionService.updateInterfaceAndInterfaceHeaderByPolizaId(headerDO, sofiGlInterfaceDO, ProcessStatusEnum.VALIDATE_FAILED);
                continue;
            } else {
                sofiGlInterfaceTransactionService.updateInterfaceAndInterfaceHeader(headerDO, sofiGlInterfaceDO, ProcessStatusEnum.VALIDATED);
            }
            if (StringUtils.isNotEmpty(errorMapMessage) && !ErpAdaptorErrorNo.IMP_ERR_ERROR.getErrorMsg().equals(errorMapMessage)) {
                ///说明map 失败了
                sofiGlInterfaceDO.setProcessMessage(errorMapMessage);
                headerDO.setProcessMessage(errorMapMessage);
                sofiGlInterfaceTransactionService.updateInterfaceAndInterfaceHeaderByPolizaId(headerDO, sofiGlInterfaceDO, ProcessStatusEnum.MAP_FAILED);
                continue;
            }
            //4.1 验证fushion 期间是否打开
            //初始化
            //nolint
            long date = ElvishDateUtils.parseDateTime(sofiGlInterfaceDO.getFecha(), "yyyy-MM").getTime();
            //nolint
            String periodName = ElvishDateUtils.formatToYYYY_MM_Dash(date);
            ConcurrentHashMap<String, Boolean> booleanMap = new ConcurrentHashMap<>();
            booleanMap.computeIfAbsent(
                    periodName,
                    key -> fusionStatusService.queryFusionOpenStatus(periodName, String.valueOf(sofiGlInterfaceDO.getLedgerId()), token).get(periodName)
            );
            log.info("polizaId=:{},periodName={}, booleanMap=:{}", sofiGlInterfaceDO.getPolizaId(), periodName, booleanMap);
            if (!booleanMap.get(periodName)) {
                //MappedFaild
                sofiGlInterfaceDO.setProcessMessage("fusion status not open");
                headerDO.setProcessMessage("fusion status not open");
                sofiGlInterfaceTransactionService.updateInterfaceAndInterfaceHeaderByPolizaId(headerDO, sofiGlInterfaceDO, ProcessStatusEnum.MAP_FAILED);
            }
        }
    }


}
