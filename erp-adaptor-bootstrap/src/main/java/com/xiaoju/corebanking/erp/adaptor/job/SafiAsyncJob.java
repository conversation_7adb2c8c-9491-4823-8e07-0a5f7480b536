package com.xiaoju.corebanking.erp.adaptor.job;

import com.xiaoju.corebanking.erp.adaptor.pipeline.SafiTaskPipeline;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description: 类描述
 * @author: didi
 **/
@Slf4j
@Service
public class SafiAsyncJob {
    @Resource
    private SafiTaskPipeline safiTaskPipeline;
    @XxlJob("safiAsyncJob")
    public void safiAsyncJob(String processDay) {
        safiTaskPipeline.runSafiTaskAsync(processDay);
    }

}
