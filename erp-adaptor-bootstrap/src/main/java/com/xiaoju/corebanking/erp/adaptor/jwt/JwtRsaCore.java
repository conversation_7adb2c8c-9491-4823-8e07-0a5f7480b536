package com.xiaoju.corebanking.erp.adaptor.jwt;


import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.xiaoju.corebanking.erp.adaptor.config.JwtConfig;
import org.springframework.stereotype.Component;

import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.Date;

@Component
public class JwtRsaCore {
    private final JwtConfig jwtConfig;
    private static final long DEFAULT_EXPIRATION = 3600 * 1000; // 1小时

    public JwtRsaCore(JwtConfig jwtConfig) {
        this.jwtConfig = jwtConfig;
    }

    private RSAPublicKey getPublicKey() throws Exception {
        String publicKeyPEM = jwtConfig.getPublicKey()
            .replace("-----BEGIN PUBLIC KEY-----", "")
            .replace("-----END PUBLIC KEY-----", "")
            .replaceAll("\\s", "");

        byte[] encoded = Base64.getDecoder().decode(publicKeyPEM);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(encoded);
        return (RSAPublicKey) keyFactory.generatePublic(keySpec);
    }

    private RSAPrivateKey getPrivateKey() throws Exception {
        String privateKeyPEM = jwtConfig.getPrivateKey()
            .replace("-----BEGIN PRIVATE KEY-----", "")
            .replace("-----END PRIVATE KEY-----", "")
            .replaceAll("\\s", "");

        byte[] encoded = Base64.getDecoder().decode(privateKeyPEM);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(encoded);
        return (RSAPrivateKey) keyFactory.generatePrivate(keySpec);
    }

    public boolean validateCredentials(String username, String password) {
        return jwtConfig.getUsername().equals(username) &&
            jwtConfig.getPassword().equals(password);
    }

    public String generateToken(String username, String password) {
        if (!validateCredentials(username, password)) {
            throw new RuntimeException("Invalid credentials");
        }

        try {
            RSAPrivateKey privateKey = getPrivateKey();
            Algorithm algorithm = Algorithm.RSA256(null, privateKey);

            return JWT.create()
                .withIssuer(jwtConfig.getIssuer())
                .withSubject(username)
                .withIssuedAt(new Date())
                .withExpiresAt(new Date(System.currentTimeMillis() + DEFAULT_EXPIRATION))
                .sign(algorithm);
        } catch (Exception e) {
            throw new RuntimeException("Token generation failed", e);
        }
    }

    public DecodedJWT verifyToken(String token) {
        try {
            RSAPublicKey publicKey = getPublicKey();
            Algorithm algorithm = Algorithm.RSA256(publicKey, null);
            JWTVerifier verifier = JWT.require(algorithm)
                .withIssuer(jwtConfig.getIssuer())
                .build();
            return verifier.verify(token);
        } catch (Exception e) {
            throw new RuntimeException("Token verification failed: " + e.getMessage());
        }
    }
}
