package com.xiaoju.corebanking.erp.adaptor.controller.dto;

import com.xiaoju.digitalbank.ddd.po.ExecResult;

/**
 * <AUTHOR>
 * @date 2025/8/7 17:50
 */
public class SyncTaskResultDTO {
    private String taskName;
    private ExecResult execResult;
    private Exception exception;

    public SyncTaskResultDTO(String taskName, ExecResult execResult, Exception exception) {
        this.taskName = taskName;
        this.execResult = execResult;
        this.exception = exception;
    }

    public boolean isSuccess() {
        return exception == null && execResult != null && execResult.isSuccess();
    }

    public String getTaskName() {
        return taskName;
    }

    public String getErrorMessage() {
        if (exception != null) {
            return exception.getMessage();
        }
        if (execResult != null && !execResult.isSuccess()) {
            return execResult.getDebugMessage();
        }
        return "未知错误";
    }
}
