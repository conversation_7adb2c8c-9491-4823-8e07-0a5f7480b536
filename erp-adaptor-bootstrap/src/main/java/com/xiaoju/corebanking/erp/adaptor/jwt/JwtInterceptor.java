package com.xiaoju.corebanking.erp.adaptor.jwt;


import com.alibaba.fastjson.JSONObject;
import com.auth0.jwt.interfaces.DecodedJWT;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.xiaoju.corebanking.erp.adaptor.common.erp.RestResponse;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

@Component
public class JwtInterceptor implements HandlerInterceptor {
    private final JwtRsaCore jwtRsaCore;

    public JwtInterceptor(JwtRsaCore jwtRsaCore) {
        this.jwtRsaCore = jwtRsaCore;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        JwtRequired jwtRequired = handlerMethod.getMethodAnnotation(JwtRequired.class);
        if (jwtRequired == null) {
            jwtRequired = handlerMethod.getBeanType().getAnnotation(JwtRequired.class);
        }

        if (jwtRequired == null) {
            return true;
        }

        String token = request.getHeader("token");
        if (token == null || token.isEmpty()) {
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            response.getWriter().write(JSONObject.toJSONString(RestResponse.failWithMsg("No token provided")));
            return false;
        }

        try {
            DecodedJWT decodedJWT = jwtRsaCore.verifyToken(token);
            request.setAttribute("jwt", decodedJWT);
            return true;
        } catch (Exception e) {
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            response.getWriter().write(JSONObject.toJSONString(RestResponse.failWithMsg("Invalid token")));
            return false;
        }
    }
}
