package com.xiaoju.corebanking.erp.adaptor.controller;

import com.xiaoju.corebanking.erp.adaptor.common.erp.RestResponse;
import com.xiaoju.corebanking.erp.adaptor.controller.dto.SofiGlInterfaceUpdateRequestDTO;
import com.xiaoju.corebanking.erp.adaptor.service.inter.SofiGlInterfaceService;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/sofi-interface")
@Slf4j
public class SofiGlInterfaceController {

    @Autowired
    private SofiGlInterfaceService sofiGlInterfaceService;

    /**
     * 根据polizaId查询接口数据
     */
    @GetMapping("/{polizaId}")
    public RestResponse<SofiGlInterfaceDO> getInterface(@PathVariable("polizaId") Long polizaId, @PathVariable("detallePolizaId") Long detallePolizaId) {
        log.info("查询接口数据, polizaId={}", polizaId);
        SofiGlInterfaceDO result = sofiGlInterfaceService.selectByPolizaIdAndDetalle(polizaId,detallePolizaId);
        return RestResponse.okWithData(result);
    }

    /**
     * 更新接口表数据，备份his表
     */
    @PostMapping("/updateInterfaceBackup")
    public RestResponse<SofiGlInterfaceDO> updateInterfaceWithBackup(@RequestBody SofiGlInterfaceUpdateRequestDTO request) {
        log.info("更新接口表数据，备份his表, request={}", request);

        if (request.getPolizaId() == null) {
            return RestResponse.failWithMsg("polizaId不能为空");
        }

        SofiGlInterfaceDO updateDO = new SofiGlInterfaceDO();
        BeanUtils.copyProperties(request, updateDO);

        SofiGlInterfaceDO result = sofiGlInterfaceService.updateInterfaceWithBackup(request.getPolizaId(),request.getDetallePolizaId(), updateDO);

        return RestResponse.okWithData(result);
    }
} 