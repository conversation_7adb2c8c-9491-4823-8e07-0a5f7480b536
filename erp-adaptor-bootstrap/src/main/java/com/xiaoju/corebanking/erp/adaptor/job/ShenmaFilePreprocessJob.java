package com.xiaoju.corebanking.erp.adaptor.job;

import com.xiaoju.corebanking.erp.adaptor.service.file.ShenmaFilePreprocessService;
import com.xiaoju.digitalbank.ddd.po.ExecResult;
import com.xiaoju.godson.common.utils.JsonUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/7/3
 */
@Service
@Slf4j
public class ShenmaFilePreprocessJob extends IJobHandler {

    @Value("${fusion.path.shenma}")
    private String shenmaFilePath;

    @Resource
    private ShenmaFilePreprocessService shenmaFilePreprocessService;

    @XxlJob("shenmaFilePreprocessJob")
    public void execute() {
        log.info("execute shenmaFilePreprocessJob start");
        String param = XxlJobHelper.getJobParam();
        ExecResult result = shenmaFilePreprocessService.processShenmaFile(shenmaFilePath, param);
        log.info("execute shenmaFilePreprocessJob end，result:{}", JsonUtil.toString(result));
    }
} 