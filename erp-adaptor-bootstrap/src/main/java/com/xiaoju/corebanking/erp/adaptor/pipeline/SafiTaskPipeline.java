package com.xiaoju.corebanking.erp.adaptor.pipeline;

import com.xiaoju.corebanking.erp.adaptor.job.PushFusionDataJob;
import com.xiaoju.corebanking.erp.adaptor.job.ValidatedAndMappedDataJob;
import com.xiaoju.corebanking.erp.adaptor.service.file.FilePreprocessService;
import com.xiaoju.corebanking.erp.adaptor.service.polling.StatusPollingService;
import com.xiaoju.digitalbank.ddd.po.ExecResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
public class SafiTaskPipeline {

    @Resource
    private FilePreprocessService filePreprocessService;

    @Resource
    private ValidatedAndMappedDataJob validatedAndMappedDataJob;

    @Resource
    private PushFusionDataJob pushFusionDataJob;

    @Resource
    private StatusPollingService statusPollingService;

    @Resource
    @Qualifier("pipelineThreadPool")
    private ThreadPoolTaskExecutor pipelineThreadPool;

    @Value("${fusion.path.safi}")
    private String filePath;


    public void runSafiTaskSync(String processDay) {
        try {
            log.info("处理日期：{}", processDay);
            // 1. 处理文件
            processFile(processDay);

            // 2. 验证数据，这个里面包含了异步任务，需要阻塞
            CompletableFuture<Void> validateFuture = validatedAndMappedDataJob.validatedAndMappedDataJob();
            validateFuture.join();

            // 3. 推送数据，这个是同步任务，没问题，会按顺序结束
            pushFusionDataJob.pushFusionDataJob();

            // 4. 检查状态并提交，这个上来只检查一次状态
            //checkStatusAndSubmitEssRequest();

            // 4. 轮询
            statusPollingService.pollLoadRequestStatusAsync();


            log.info("流水线执行成功");
        } catch (Exception ex) {
            log.error("流水线执行异常", ex);
            throw new RuntimeException("流水线失败: " + ex.getMessage());
        }
    }

    public CompletableFuture<Void> runSafiTaskAsync(String processDay) {
        return CompletableFuture.runAsync(() -> {
            try {
                log.info("处理日期：{}", processDay);
                // 1. 处理文件
                processFile(processDay);
    
                // 2. 验证数据，这个里面包含了异步任务，需要阻塞
                CompletableFuture<Void> validateFuture = validatedAndMappedDataJob.validatedAndMappedDataJob();
                validateFuture.join();
    
                // 3. 推送数据，这个是同步任务，没问题，会按顺序结束
                pushFusionDataJob.pushFusionDataJob();
    
                // 4. 轮询
                statusPollingService.pollLoadRequestStatusAsync();
    
                log.info("流水线执行成功");
            } catch (Exception ex) {
                log.error("流水线执行异常", ex);
                throw new RuntimeException("流水线失败: " + ex.getMessage());
            }
        }, pipelineThreadPool);
    }

    public ExecResult processFile(String processDay) {
        String filePath = "/Users/<USER>/erp-adaptor/safi";
        return filePreprocessService.processFile(filePath,processDay);
    }

}
