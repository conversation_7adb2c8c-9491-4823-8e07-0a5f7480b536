package com.xiaoju.corebanking.erp.adaptor.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class ErpAdaptorThreadPoolConfig {

    @Value("${fusion.polling.thread-pool.size:10}")
    private int statusPollingThreadPoolSize;
    
    @Bean(name = "pipelineThreadPool")
    public ThreadPoolTaskExecutor pipelineThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(5);
        // 最大线程数 (根据需求设置，可以等于核心线程数或更大)
        executor.setMaxPoolSize(10);
        // 队列容量
        executor.setQueueCapacity(200);
        // 线程名前缀
        executor.setThreadNamePrefix("pipeline-pool-");
        // 等待所有任务完成再关闭
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 等待终止的最长时间
        executor.setAwaitTerminationSeconds(600);
        // 核心线程不允许超时退出
        executor.setAllowCoreThreadTimeOut(false);
        // 使用CallerRunsPolicy策略，当队列满时由调用线程执行任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    @Bean(name = "statusPollingThreadPool")
    public ThreadPoolTaskExecutor statusPollingThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(statusPollingThreadPoolSize);
        executor.setMaxPoolSize(statusPollingThreadPoolSize);
        executor.setQueueCapacity(200);
        executor.setThreadNamePrefix("erp-status-polling-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(600);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    @Bean(name = "validateThreadPool")
    public ThreadPoolTaskExecutor validateThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(10);
        // 最大线程数 (根据需求设置，可以等于核心线程数或更大)
        executor.setMaxPoolSize(50);
        // 队列容量
        executor.setQueueCapacity(200);
        // 线程名前缀
        executor.setThreadNamePrefix("validate-pool-");
        // 等待所有任务完成再关闭
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 等待终止的最长时间
        executor.setAwaitTerminationSeconds(600);
        // 核心线程不允许超时退出
        executor.setAllowCoreThreadTimeOut(false);
        // 使用CallerRunsPolicy策略，当队列满时由调用线程执行任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
    @Bean(name = "shenMaValidatedThreadPool")
    public ThreadPoolTaskExecutor shenMaValidatedThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(10);
        // 最大线程数 (根据需求设置，可以等于核心线程数或更大)
        executor.setMaxPoolSize(50);
        // 队列容量
        executor.setQueueCapacity(200);
        // 线程名前缀
        executor.setThreadNamePrefix("process-pool-");
        // 等待所有任务完成再关闭
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 等待终止的最长时间
        executor.setAwaitTerminationSeconds(600);
        // 核心线程不允许超时退出
        executor.setAllowCoreThreadTimeOut(false);
        // 使用CallerRunsPolicy策略，当队列满时由调用线程执行任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
    @Bean(name = "comprebenSivePool")
    public ThreadPoolTaskExecutor comprebenSivePool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(5);
        // 最大线程数 (根据需求设置，可以等于核心线程数或更大)
        executor.setMaxPoolSize(10);
        // 队列容量
        executor.setQueueCapacity(200);
        // 线程名前缀
        executor.setThreadNamePrefix("pipeline-pool-");
        // 等待所有任务完成再关闭
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 等待终止的最长时间
        executor.setAwaitTerminationSeconds(600);
        // 核心线程不允许超时退出
        executor.setAllowCoreThreadTimeOut(false);
        // 使用CallerRunsPolicy策略，当队列满时由调用线程执行任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}
