package com.xiaoju.corebanking.erp.adaptor.controller;


import com.xiaoju.corebanking.erp.adaptor.common.erp.RestResponse;
import com.xiaoju.corebanking.erp.adaptor.jwt.JwtRsaCore;
import com.xiaoju.corebanking.erp.adaptor.service.fusion.FusionAuthService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1")
public class TokenController {


    @Autowired
    private FusionAuthService fusionAuthService;

    @Value("${fusion.jwt.username}")
    private String authUser;
    @Value("${fusion.jwt.password}")
    private String authPass;

    private final JwtRsaCore jwtRsaCore;


    public TokenController(JwtRsaCore jwtRsaCore) {
        this.jwtRsaCore = jwtRsaCore;
    }

    @GetMapping("/token")
    public RestResponse<String> getToken(@RequestParam String username, @RequestParam String password) {
        try {
            return RestResponse.okWithData(jwtRsaCore.generateToken(username, password));
        } catch (Exception e) {
            return RestResponse.failWithData(e.getMessage());
        }
    }

    @GetMapping("/fusion/token")
    @Validated
    public ResponseEntity<RestResponse<?>> generateToken(@RequestParam String username,
                                                         @RequestParam String password) {

        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
            return ResponseEntity.ok(RestResponse
                    .failWithData("username/password不能为空"));
        }

        if (!username.equals(authUser)) {
            return ResponseEntity.ok(RestResponse
                    .failWithData("未授权的用户"));
        }

        if (!password.equals(authPass)) {
            return ResponseEntity.ok(RestResponse
                    .failWithData("密码错误"));
        }

        String token = fusionAuthService.generateJwtToken(username);
        return ResponseEntity.ok(RestResponse
                .okWithData(token));

    }
}
