package com.xiaoju.corebanking.erp.adaptor.job;


import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.enums.SourceSysEnum;
import com.xiaoju.corebanking.erp.adaptor.common.errorno.ErpAdaptorErrorNo;
import com.xiaoju.corebanking.erp.adaptor.common.exception.ErpAdaptorBusinessException;
import com.xiaoju.corebanking.erp.adaptor.common.utils.ErpUtil;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceCommonDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SummaryResultDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValueExtraPO;
import com.xiaoju.corebanking.erp.adaptor.service.fusion.FusionAuthService;
import com.xiaoju.corebanking.erp.adaptor.service.fusion.FusionStatusService;
import com.xiaoju.corebanking.erp.adaptor.service.header.SofiGlInterfaceHeaderService;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlInterfaceCommonService;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlShenMaService;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlShenmaValidatedService;
import com.xiaoju.corebanking.erp.adaptor.service.transaction.SofiShenMaTransactionService;
import com.xiaoju.corebanking.erp.adaptor.service.twotab.TwoTabValueService;
import com.xiaoju.digitalbank.errorno.MessageErrorNo;
import com.xiaoju.digitalbank.util.tools.ElvishDateUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Service
public class ShenMaValidatedAndMappedDataJob {

    @Autowired
    private SofiGlInterfaceHeaderService sofiGlInterfaceHeaderService;

    @Autowired
    private SofiGlShenMaService sofiGlShenMaService;

    @Autowired
    private SofiShenMaTransactionService sofiShenMaTransactionService;

    @Autowired
    private SofiGlShenmaValidatedService sofiGlShenmaValidatedService;

    @Autowired
    private TwoTabValueService twoTabValueService;

    @Autowired
    private FusionStatusService fusionStatusService;

    @Autowired
    private SofiGlInterfaceCommonService sofiGlInterfaceCommonService;

    @Autowired
    private FusionAuthService fusionAuthService;
    @Value("${fusion.jwt.username}")
    private String username;
    @Autowired
    ErpUtil erpUtil;

    @Autowired
    @Qualifier("shenMaValidatedThreadPool")
    private ThreadPoolTaskExecutor shenMaValidatedThreadPool;

    @XxlJob("shenMaValidatedAndMappedDataJob")
    public CompletableFuture<Void> shenMaValidatedAndMappedDataJob() {
        String param = XxlJobHelper.getJobParam();
        log.info("shenma date param:{}", param);
        String finalProcessDay;
        try {
            if (StringUtils.isEmpty(param)) {
                finalProcessDay = erpUtil.getDateDirFromInput("");
            } else {
                finalProcessDay = param;
            }
            // 初始化token
            String token = fusionAuthService.generateJwtToken(username);
            Map<String, List<CuxTwoTabValueExtraPO>> cache = initMapping();
            Map<String, Map<String, CuxTwoTabValueExtraPO>> result = cache.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,  // 保持外层Map的key不变
                            entry -> entry.getValue().stream()  // 处理内层List
                                    .collect(Collectors.toMap(
                                            CuxTwoTabValueExtraPO::getValue1,  // 内层Map的key
                                            Function.identity(),               // 内层Map的value
                                            (existing, replacement) -> existing  // 处理重复key
                                    ))
                    ));
            List<CompletableFuture<Void>> pageFutures = new ArrayList<>();  // 改为页面级future列表
            //先判断vouchergroup  是否有借贷金额不相等的记录
            List<SummaryResultDO> summaryResultDOS = sofiGlShenMaService.queryShenMaSumByVoucherGroup(finalProcessDay);
            log.info("vouchergroup summaryResultDOS:{}", summaryResultDOS);
            if (CollectionUtils.isNotEmpty(summaryResultDOS)) {
                    //说明借贷金额不相等，校验失败
                    for(SummaryResultDO summaryResultDO : summaryResultDOS) {
                        SofiGlInterfaceHeaderDO headerDO = new SofiGlInterfaceHeaderDO();
                        headerDO.setProcessDay(summaryResultDO.getProcessDay());
                        headerDO.setExternalReference(summaryResultDO.getLinkReference());
                        headerDO.setGroupId(summaryResultDO.getGroupId());
                        SofiGlShenmaDO sofiGlShenmaDO = new SofiGlShenmaDO();
                        sofiGlShenmaDO.setProcessMessage("vouchergroup sum not equals" + summaryResultDO.getVoucherGroup());
                        sofiGlShenmaDO.setProcessDay(summaryResultDO.getProcessDay());
                        sofiGlShenmaDO.setLinkReference(summaryResultDO.getLinkReference());
                        sofiGlShenmaDO.setVoucherGroup(summaryResultDO.getVoucherGroup());
                        sofiShenMaTransactionService.updateShenMaInterfaceAndHeader(headerDO, sofiGlShenmaDO, ProcessStatusEnum.VALIDATE_FAILED);
                    }
            }
            //查询groupId
            List<Long> groupIds = sofiGlInterfaceHeaderService.findGroupIdByProcessDay(finalProcessDay, SourceSysEnum.SHEN_MA.getCode());
            for (Long groupId : groupIds) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        List<SofiGlShenmaDO> glShenmaDOList = sofiGlShenMaService.querySofiGlShenmaByGroupIdAndProcessDay(groupId, finalProcessDay);
                        process(glShenmaDOList, token, groupId, result);
                    } catch (Exception e) {
                        log.error("validatedAndMappedDataJob error: {}", ErpAdaptorErrorNo.SYSTEM_ERROR_DEFAULT_FAIL, e);
                        throw new ErpAdaptorBusinessException(MessageErrorNo.SYSTEM_ERROR_DEFAULT_FAIL);
                    }
                }, shenMaValidatedThreadPool).exceptionally(e -> {
                    log.error("groupId:{},validatedAndMappedDataJob error: {}", groupId, ErpAdaptorErrorNo.SYSTEM_ERROR_DEFAULT_FAIL, e);
                    CompletableFuture<Void> failedFuture = new CompletableFuture<>();
                    failedFuture.completeExceptionally(e);
                    return null;
                });
                pageFutures.add(future);
            }
            log.info("validateData job success end,pageFutrues size {}", pageFutures.size());
            // 在所有process完成后执行validateCoa
            return CompletableFuture.allOf(pageFutures.toArray(new CompletableFuture[0]))
                    .thenRun(() -> sofiGlShenMaService.validateCoa(finalProcessDay))
                    .exceptionally(e -> {
                        log.error("Error after all processSecond operations: {}", e.getMessage(), e);
                        return null;
                    });
        } catch (Exception e) {
            log.error("validatedAndMappedDataJob error: {}", ErpAdaptorErrorNo.SYSTEM_ERROR_DEFAULT_FAIL, e);
            CompletableFuture<Void> failedFuture = new CompletableFuture<>();
            failedFuture.completeExceptionally(e);
            return failedFuture;
        }

    }

    private void process(List<SofiGlShenmaDO> glShenmaDOList, String token, Long groupId, Map<String, Map<String, CuxTwoTabValueExtraPO>> cache) {
        SofiGlInterfaceHeaderDO headerDO = new SofiGlInterfaceHeaderDO();
        headerDO.setGroupId(groupId);
        List<SofiGlInterfaceCommonDO> list = new ArrayList<>();
        ConcurrentHashMap<String, Integer> map = new ConcurrentHashMap<>();
        for (SofiGlShenmaDO sofiGlShenmaDO : glShenmaDOList) {
            map.merge(sofiGlShenmaDO.getExternalReference(), 1, Integer::sum);
            headerDO.setProcessDay(sofiGlShenmaDO.getProcessDay());
            headerDO.setExternalReference(sofiGlShenmaDO.getExternalReference());
            if(ProcessStatusEnum.VALIDATE_FAILED.equals(sofiGlShenmaDO.getProcessStatus())) {
                // 验证失败的数据不需要后续处理
                continue;
            }
            //状态改为processing
            sofiShenMaTransactionService.updateShenMaInterfaceAndHeader(headerDO, sofiGlShenmaDO, ProcessStatusEnum.PROCESSING);
            //3.数据Mapping
            SofiGlInterfaceCommonDO commonDO = new SofiGlInterfaceCommonDO();

            //nolint
            long date = ElvishDateUtils.parseDateTime(sofiGlShenmaDO.getPostDate(), "yyyy-MM").getTime();
            //nolint
            String periodName = ElvishDateUtils.formatToYYYY_MM_Dash(date);
            commonDO.setPeriodName(periodName);
            String errorMapMessage = sofiGlShenmaValidatedService.mappedResult(sofiGlShenmaDO, commonDO, cache);
            log.info("mapping linkReference {} mapping result:{}", sofiGlShenmaDO.getLinkReference(), errorMapMessage);

            //4.验证VALIDATA
            String errorMessage = sofiGlShenmaValidatedService.validated(sofiGlShenmaDO);
            log.info("validated linkReference {} validated result:{}", sofiGlShenmaDO.getLinkReference(), errorMessage);

            if (StringUtils.isNotEmpty(errorMessage) && !ErpAdaptorErrorNo.IMP_ERR_ERROR.getErrorMsg().equals(errorMessage)) {
                ///说明验证失败了/更新状态为VALIDATE_FAILED
                sofiGlShenmaDO.setProcessMessage(errorMessage);
                headerDO.setProcessMessage(errorMessage);
                sofiShenMaTransactionService.updateShenMaInterfaceAndHeader(headerDO, sofiGlShenmaDO, ProcessStatusEnum.VALIDATE_FAILED);
                continue;
            } else {
                sofiShenMaTransactionService.updateShenMaInterfaceAndHeader(headerDO, sofiGlShenmaDO, ProcessStatusEnum.VALIDATED);
            }

            if (StringUtils.isNotEmpty(errorMapMessage) && !ErpAdaptorErrorNo.IMP_ERR_ERROR.getErrorMsg().equals(errorMapMessage)) {
                ///说明map 失败了
                sofiGlShenmaDO.setProcessMessage(errorMapMessage);
                headerDO.setProcessMessage(errorMapMessage);
                sofiShenMaTransactionService.updateShenMaInterfaceAndHeader(headerDO, sofiGlShenmaDO, ProcessStatusEnum.MAP_FAILED);
                continue;
            }
            //4.1 验证fushion 期间是否打开
            ConcurrentHashMap<String, Boolean> booleanMap = new ConcurrentHashMap<>();
            booleanMap.computeIfAbsent(
                    periodName,
                    key -> fusionStatusService.queryFusionOpenStatus(periodName, String.valueOf(commonDO.getLedgerId()), token).get(periodName)
            );
            log.info("linkReference:{},periodName:{}, booleanMap:{}", sofiGlShenmaDO.getLinkReference(), periodName, booleanMap);
            if (!booleanMap.get(periodName)) {
                //MappedFaild
                sofiGlShenmaDO.setProcessMessage("fusion status not open");
                headerDO.setProcessMessage("fusion status not open");
                sofiShenMaTransactionService.updateShenMaInterfaceAndHeader(headerDO, sofiGlShenmaDO, ProcessStatusEnum.MAP_FAILED);
            }
            list.add(bulidSofiGlInterfaceCommonDO(sofiGlShenmaDO, commonDO, map));
            //落库common表
            if (list.size() >= CommonConstant.PAGE_SIZE) {
                sofiGlInterfaceCommonService.batchInsert(list);
                list.clear();
            }
        }
        if (CollectionUtils.isNotEmpty(list)) {
            log.info("list size {}", list.size());
            sofiGlInterfaceCommonService.batchInsert(list);
        }
        log.info("save common success end ");
    }

    private Map<String, List<CuxTwoTabValueExtraPO>> initMapping() {
        List<CuxTwoTabValueExtraPO> cuxTwoTabValuePOS = twoTabValueService.selectAllByFormCode();
        return cuxTwoTabValuePOS.stream().collect(Collectors.groupingBy(CuxTwoTabValueExtraPO::getFormCode));
    }

    private SofiGlInterfaceCommonDO bulidSofiGlInterfaceCommonDO(SofiGlShenmaDO sofiGlShenmaDO, SofiGlInterfaceCommonDO commonDO, ConcurrentHashMap<String, Integer> map) {
        commonDO.setSystemCode(SourceSysEnum.SHEN_MA.getCode());
        commonDO.setProcessDay(sofiGlShenmaDO.getProcessDay());
        //nolint
        commonDO.setAccountingDate(ElvishDateUtils.parseDateTime(sofiGlShenmaDO.getPostDate(), "yyyy-MM-dd"));
        commonDO.setAttributeCategory(CommonConstant.CORE_BANKING);
        commonDO.setJournalCategory(sofiGlShenmaDO.getVoucherGroup());
        commonDO.setSegment2(CommonConstant.SHENMA_SEGMENT2);
        commonDO.setSegment4(CommonConstant.ZERO);
        commonDO.setSegment7(CommonConstant.ZERO);
        commonDO.setSegment8(sofiGlShenmaDO.getIntercompany());
        commonDO.setSegment9(CommonConstant.ZERO);
        commonDO.setSegment10(CommonConstant.ZERO);
        commonDO.setEnteredDr(sofiGlShenmaDO.getEnteredDebitAmount());
        commonDO.setEnteredCr(sofiGlShenmaDO.getEnteredCreditAmount());
        commonDO.setAccountedDr(sofiGlShenmaDO.getEnteredDebitAmount());
        commonDO.setAccountedCr(sofiGlShenmaDO.getEnteredCreditAmount());
        commonDO.setGroupId(sofiGlShenmaDO.getGroupId());
        commonDO.setCurrencyConversionRate(sofiGlShenmaDO.getFlatRate());
        commonDO.setCurrencyConversionType("");
        commonDO.setReference1(commonDO.getSegment1() + "_" + sofiGlShenmaDO.getGroupId());
        commonDO.setReference2(commonDO.getSegment1() + "_" + commonDO.getPeriodName() + "_" + commonDO.getJournalCategory());
        commonDO.setReference4(commonDO.getSegment1() + "_" + commonDO.getPeriodName() + "_"+ CommonConstant.SHEN_MA_JOURNAL_SOURCE + "_" + sofiGlShenmaDO.getProcessDay() + "_"+ commonDO.getJournalCategory());
        commonDO.setReference5(sofiGlShenmaDO.getExternalReference());
        commonDO.setReference6(commonDO.getPeriodName() + "_" +sofiGlShenmaDO.getVoucherGroup());
        commonDO.setReference8("");
        commonDO.setReference9("");
        commonDO.setReference10(commonDO.getSegment1() + "_" + commonDO.getPeriodName() + "_" + CommonConstant.SHEN_MA_JOURNAL_SOURCE + "_" + sofiGlShenmaDO.getLinkReference());
        commonDO.setReference21(CommonConstant.SHEN_MA_JOURNAL_SOURCE);
        commonDO.setReference22(commonDO.getPeriodName() + "_" +sofiGlShenmaDO.getVoucherGroup());
        commonDO.setReference23(String.valueOf(map.get(sofiGlShenmaDO.getExternalReference()) * 10));
        commonDO.setReference24("");
        commonDO.setReference25("");
        commonDO.setReference26("");
        commonDO.setReference27("");
        commonDO.setReference28("");
        commonDO.setReference29("");
        commonDO.setReference30("");
        commonDO.setHeaderAttribute1("");
        commonDO.setHeaderAttribute2("");
        commonDO.setHeaderAttribute3("");
        commonDO.setHeaderAttribute4("");
        commonDO.setHeaderAttribute5("");
        commonDO.setHeaderAttribute6("");
        commonDO.setHeaderAttribute7("");
        commonDO.setHeaderAttribute8("");
        commonDO.setHeaderAttribute9("");
        commonDO.setHeaderAttribute10("");
        commonDO.setHeaderAttribute11("");
        commonDO.setHeaderAttribute12("");
        commonDO.setHeaderAttribute13("");
        commonDO.setHeaderAttribute14("");
        commonDO.setHeaderAttribute15("");
        commonDO.setAttributeCategory3("");
        commonDO.setLineAttribute1("");
        commonDO.setLineAttribute2(sofiGlShenmaDO.getAmtType());
        commonDO.setLineAttribute3(sofiGlShenmaDO.getTranType());
        commonDO.setLineAttribute4(sofiGlShenmaDO.getEventType());
        commonDO.setLineAttribute5(sofiGlShenmaDO.getClientType());
        commonDO.setLineAttribute6(sofiGlShenmaDO.getInlandOffshore());
        commonDO.setLineAttribute7(CommonConstant.ATTRIBUTE7);
        commonDO.setLineAttribute8("");
        commonDO.setLineAttribute9(sofiGlShenmaDO.getChannelSeqNo());
        commonDO.setLineAttribute10("");
        commonDO.setLineAttribute11("");
        commonDO.setLineAttribute12("");
        commonDO.setLineAttribute13("");
        commonDO.setLineAttribute14("");
        commonDO.setLineAttribute15("");
        commonDO.setLineAttribute16("");
        commonDO.setLineAttribute17("");
        commonDO.setLineAttribute18("");
        commonDO.setLineAttribute19("");
        commonDO.setLineAttribute20("");
        commonDO.setProcessStatus(sofiGlShenmaDO.getProcessStatus());
        commonDO.setProcessMessage("");
        commonDO.setJeHeaderId(0L);
        commonDO.setJournalName("");
        commonDO.setJeLineNum(0L);
        commonDO.setDocumentId(0L);
        commonDO.setLoadRequestId(0L);
        commonDO.setImportRequestId(0L);
        commonDO.setObjectVersionNumber(1L);
        commonDO.setCreatedBy("");
        commonDO.setLastModifiedBy("");
        commonDO.setJournalSourceName(CommonConstant.SHEN_MA_JOURNAL_SOURCE);
        commonDO.setJournalSource(CommonConstant.SHEN_MA_JOURNAL_SOURCE);
        return commonDO;
    }
}
