package com.xiaoju.corebanking.erp.adaptor;

import com.xiaoju.godson.degrade.config.DegradeAutoConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * monitor 的入口方法
 *
 * <AUTHOR>
@Slf4j
@SpringBootApplication(scanBasePackages = {"com.xiaoju.corebanking","com.xiaoju.godson.web.advice"}, exclude = {DegradeAutoConfig.class})
@EnableAspectJAutoProxy
@EnableTransactionManagement
public class ErpAdaptorApplication {
    public static void main(String[] args) {
        SpringApplication.run(ErpAdaptorApplication.class, args);

        log.info("ErpAdaptorApplication start success>>>>>>>");
    }

}
