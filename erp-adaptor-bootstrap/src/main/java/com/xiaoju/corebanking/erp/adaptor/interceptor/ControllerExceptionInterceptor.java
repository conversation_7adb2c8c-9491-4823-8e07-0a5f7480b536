package com.xiaoju.corebanking.erp.adaptor.interceptor;

import com.xiaoju.digitalbank.entity.ReturnMsg;
import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import com.xiaoju.digitalbank.errorno.MessageErrorNo;
import com.xiaoju.digitalbank.exception.BaseException;
import com.xiaoju.godson.metric.MetricsUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ValidationException;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@ControllerAdvice
public class ControllerExceptionInterceptor {

    /**
     * BaseException异常捕获，返回格式化信息
     */
    @ResponseBody
    @ExceptionHandler(BaseException.class)
    public ReturnMsg handleBaseException(HttpServletRequest request, BaseException e) {
        log.error("ControllerExceptionInterceptor throws BaseException, {}, debugMsg={}", e.getErrorNo(), e.getDebugMsg());
        metricBaseException(request, e);  // 上报异常
        if (CommonErrorNo.isDependError(e.getErrorNo())) {
            e.setErrorNo(CommonErrorNo.FAIL);
        }
        return ReturnMsg.build(e.getErrorNo());
    }

    /**
     * IllegalArgumentException异常捕获，返回格式化信息
     */
    @ResponseBody
    @ExceptionHandler(IllegalArgumentException.class)
    public ReturnMsg handleIllegalArgumentException(HttpServletRequest request, IllegalArgumentException e) {
        log.error("ControllerExceptionInterceptor throws IllegalArgumentException, {}", CommonErrorNo.PARAM_ERROR, e);
        metricException(request, e);  // 上报异常
        return ReturnMsg.build(CommonErrorNo.PARAM_ERROR);
    }

    /**
     * MethodArgumentNotValidException参数校验异常异常捕获，返回格式化信息
     */
    @ResponseBody
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ReturnMsg handleMethodArgumentNotValidException(HttpServletRequest request, MethodArgumentNotValidException e) {
        log.error("ControllerExceptionInterceptor throws MethodArgumentNotValidException, {}", CommonErrorNo.PARAM_ERROR, e);
        metricException(request, e);  // 上报异常
        List<String> errorList = e.getBindingResult().getAllErrors().stream().map(ObjectError::getDefaultMessage).collect(Collectors.toList());
        return ReturnMsg.build(CommonErrorNo.PARAM_ERROR.getErrorNo(), errorList.toString());
    }

    /**
     * ValidationException参数校验异常异常捕获，返回格式化信息
     */
    @ResponseBody
    @ExceptionHandler(ValidationException.class)
    public ReturnMsg handleMethodArgumentNotValidException(HttpServletRequest request, ValidationException e) {
        log.error("ControllerExceptionInterceptor throws ValidationException, {}", CommonErrorNo.PARAM_ERROR, e);
        metricException(request, e);  // 上报异常
        String message = e.getMessage();
        return ReturnMsg.build(CommonErrorNo.PARAM_ERROR.getErrorNo(), message);
    }

    /**
     * DuplicateKeyException异常捕获，返回格式化信息
     */
    @ResponseBody
    @ExceptionHandler(DuplicateKeyException.class)
    public ReturnMsg handleDuplicateKeyExceptionException(HttpServletRequest request, DuplicateKeyException e) {
        log.error("ControllerExceptionInterceptor throws DuplicateKeyExceptionException, {}", MessageErrorNo.VALIDATION_DUPLICATED_TRANSACTION, e);
        metricException(request, e);  // 上报异常
        return ReturnMsg.build(MessageErrorNo.VALIDATION_DUPLICATED_TRANSACTION);
    }

    /**
     * 兜底拦截所有异常，返回数据
     */
    @ResponseBody
    @ExceptionHandler(Throwable.class)
    public ReturnMsg handleThrowable(HttpServletRequest request, Throwable e) {
        log.error("ControllerExceptionInterceptor throws Throwable, {}, exception={}", CommonErrorNo.SERVER_ERROR, e.getLocalizedMessage(), e);
        metricException(request, e);  // 上报异常
        return ReturnMsg.build(CommonErrorNo.SERVER_ERROR);
    }

    /**
     * 打印基础异常
     */
    private void metricBaseException(HttpServletRequest request, BaseException e) {
        try {
            HashMap<String, String> labelMap = new HashMap<>(8);
            labelMap.put("errorNo", String.valueOf(e.getErrorNo().getErrorNo()));
            labelMap.put("errorMsg", e.getErrorNo().getErrorMsg());
            labelMap.put("url", request.getRequestURI());
            MetricsUtil.count("digitalbank_message_base_exception", 1, labelMap);
        } catch (Throwable throwable) {
            log.error("ControllerExceptionInterceptor print exception error, {}", CommonErrorNo.METRICS_SEND_ERROR, throwable);
        }
    }

    /**
     * 打印异常
     */
    private void metricException(HttpServletRequest request, Throwable e) {
        try {
            HashMap<String, String> labelMap = new HashMap<>(8);
            labelMap.put("exception", e.getClass().getName());
            labelMap.put("url", request.getRequestURI());
            MetricsUtil.count("digitalbank_message_exception", 1, labelMap);
        } catch (Throwable throwable) {
            log.error("ControllerExceptionInterceptor print exception error, {}", CommonErrorNo.METRICS_SEND_ERROR, throwable);
        }
    }
}

