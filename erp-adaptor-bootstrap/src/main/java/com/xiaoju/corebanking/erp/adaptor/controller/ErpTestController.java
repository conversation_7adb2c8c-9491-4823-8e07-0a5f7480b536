package com.xiaoju.corebanking.erp.adaptor.controller;

import com.xiaoju.corebanking.erp.adaptor.common.apollo.AwsS3Config;
import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.common.domain.ErpAdaptorMessageRequest;
import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.enums.SourceSysEnum;
import com.xiaoju.corebanking.erp.adaptor.common.errorno.ErpAdaptorErrorNo;
import com.xiaoju.corebanking.erp.adaptor.common.exception.ErpAdaptorBusinessException;
import com.xiaoju.corebanking.erp.adaptor.common.utils.ErpUtil;
import com.xiaoju.corebanking.erp.adaptor.pipeline.SafiTaskPipeline;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.*;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValueExtraPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHeaderPOExample;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHisPOExample;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SofiGlAcctHistCustomerMapper;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SofiGlInterfaceCommonCustomerMapper;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SofiGlShenmaCustomerMapper;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SofiGlShenmaHisCustomerMapper;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlFileControlPOMapper;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfaceHeaderPOMapper;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfaceHisPOMapper;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfacePOMapper;
import com.xiaoju.corebanking.erp.adaptor.service.common.MinioFileOperator;
import com.xiaoju.corebanking.erp.adaptor.service.file.*;
import com.xiaoju.corebanking.erp.adaptor.service.fusion.CoaValidationService;
import com.xiaoju.corebanking.erp.adaptor.service.fusion.FusionAuthService;
import com.xiaoju.corebanking.erp.adaptor.service.fusion.FusionStatusService;
import com.xiaoju.corebanking.erp.adaptor.service.header.SofiGlInterfaceHeaderService;
import com.xiaoju.corebanking.erp.adaptor.service.inter.FusionResultService;
import com.xiaoju.corebanking.erp.adaptor.service.inter.SofiGlInterfaceService;
import com.xiaoju.corebanking.erp.adaptor.service.inter.SofiGlInterfaceValidatedService;
import com.xiaoju.corebanking.erp.adaptor.service.message.MessageGateway;
import com.xiaoju.corebanking.erp.adaptor.service.polling.StatusPollingService;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlAcctHistService;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlInterfaceCommonService;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlShenMaService;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlShenmaValidatedService;
import com.xiaoju.corebanking.erp.adaptor.service.transaction.SofiGlInterfaceTransactionService;
import com.xiaoju.corebanking.erp.adaptor.service.transaction.SofiShenMaTransactionService;
import com.xiaoju.corebanking.erp.adaptor.service.twotab.SequenceService;
import com.xiaoju.corebanking.erp.adaptor.service.twotab.TwoTabValueService;
import com.xiaoju.digitalbank.ddd.po.ExecResult;
import com.xiaoju.digitalbank.entity.ReturnMsg;
import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import com.xiaoju.digitalbank.errorno.MessageErrorNo;
import com.xiaoju.digitalbank.util.tools.ElvishDateUtils;
import com.xiaoju.godson.common.utils.JsonUtil;
import com.xxl.job.core.context.XxlJobHelper;
import io.minio.errors.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/18 14:44
 */
@RestController
@RequestMapping("/test")
@Slf4j
public class ErpTestController {
    private static Integer PAGE_SIZE = 500;

    @Autowired
    private SofiGlInterfaceHeaderService sofiGlInterfaceHeaderService;

    @Autowired
    private SofiGlInterfaceService sofiGlInterfaceService;

    @Autowired
    private SofiGlInterfaceTransactionService sofiGlInterfaceTransactionService;

    @Autowired
    private SofiGlInterfaceValidatedService sofiGlInterfaceValidatedService;

    @Autowired
    private TwoTabValueService twoTabValueService;

    @Autowired
    private CoaValidationService coaValidationService;

    @Autowired
    private FusionStatusService fusionStatusService;


    @Resource
    private FilePreprocessService filePreprocessService;
    @Resource
    private FusionResultService fusionResultService;
    @Resource
    private StatusPollingService statusPollingService;

    @Autowired
    private FusionAuthService fusionAuthService;
    @Value("${fusion.jwt.username}")
    private String username;
    @Autowired
    private FusionFileProcessService fusionFileProcessService;

    @Autowired
    @Qualifier("validateThreadPool")
    private ThreadPoolTaskExecutor validateThreadPool;

    @Resource
    @Qualifier("sofiGlInterfaceHeaderPOMapper")
    private SofiGlInterfaceHeaderPOMapper sofiGlInterfaceHeaderMapper;

    @Resource
    @Qualifier("sofiGlInterfacePOMapper")
    private SofiGlInterfacePOMapper sofiGlInterfacePOMapper;

    @Resource
    @Qualifier("sofiGlFileControlPOMapper")
    private SofiGlFileControlPOMapper sofiGlFileControlPOMapper;

    @Resource
    @Qualifier("sofiGlInterfaceHisPOMapper")
    private SofiGlInterfaceHisPOMapper sofiGlInterfaceHisPOMapper;

    @Resource
    private SafiTaskPipeline safiTaskPipeline;

    @Autowired
    private MessageGateway messageGateway;

    @Autowired
    private MinioFileOperator minioFileOperator;

    @Resource
    private SequenceService sequenceService;

    @Autowired
    ErpUtil erpUtil;
    @Autowired
    private SofiGlShenMaService sofiGlShenMaService;
    @Autowired
    @Qualifier("shenMaValidatedThreadPool")
    private ThreadPoolTaskExecutor shenMaValidatedThreadPool;

    @Autowired
    private SofiShenMaTransactionService sofiShenMaTransactionService;

    @Autowired
    private SofiGlShenmaValidatedService sofiGlShenmaValidatedService;
    @Autowired
    private SofiGlInterfaceCommonService sofiGlInterfaceCommonService;
    @Autowired
    private SofiGlAcctHistService sofiGlAcctHistService;
    @GetMapping("/sequenceProcess")
    public void sequenceProcess() {
//        sequenceService.createSequence("SAFI_GROUP_ID",20000000);
//        sequenceService.createSequence("SHENMA_GROUP_ID",30000000);

        System.out.println("nextval方法调用结果：" + sequenceService.nextval("SAFI_GROUP_ID"));
    }

    @GetMapping("/findByIdBetweenHeader")
    public ReturnMsg findByIdBetween(@RequestParam Long endId) {
        SofiGlInterfaceHeaderPOExample example = new SofiGlInterfaceHeaderPOExample();
        example.createCriteria().andIdLessThanOrEqualTo(endId);
        int i = sofiGlInterfaceHeaderMapper.deleteByExample(example);
        return ReturnMsg.success(i);
    }

    @GetMapping("/findByIdInterface")
    public ReturnMsg findById(@RequestParam Long endId) {
        int i1 = sofiGlInterfacePOMapper.deleteByIdRange(endId);
        return ReturnMsg.success(i1);
    }

    @GetMapping("/findByIdControl")
    public ReturnMsg findByIdHis(@RequestParam Long endId) {
        int i1 = sofiGlFileControlPOMapper.deleteByControl(endId);
        return ReturnMsg.success(i1);
    }

    @GetMapping("/findByIdHis")
    public ReturnMsg findByIdHis1(@RequestParam Long endId) {
        SofiGlInterfaceHisPOExample example = new SofiGlInterfaceHisPOExample();
        example.createCriteria().andIdLessThanOrEqualTo(endId);
        int i1 = sofiGlInterfaceHisPOMapper.deleteByExample(example);
        return ReturnMsg.success(i1);
    }

    @GetMapping("/process-day")
    public void processFileAtDay(@RequestParam(required = false) String processDay) {
        safiTaskPipeline.processFile(processDay);
    }

    @PostMapping("/pipeline")
    public void pipeline(@RequestParam(required = false) String processDay) {
        safiTaskPipeline.runSafiTaskSync(processDay);
    }

    @PostMapping("/pipeline-async")
    public void pipelineAsync(@RequestParam(required = false) String processDay) {
        safiTaskPipeline.runSafiTaskAsync(processDay);
    }


    @GetMapping("/listObjects")
    public void listObjects() throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException {
        List<String> strings = minioFileOperator.listObjects("ireconciliation/reports/channel/stp/payoin/transaction");
        strings.forEach(System.out::println);
    }

    @Resource
    private AwsS3Config awsS3Config;


    @PostMapping("/validateData")
    public CompletableFuture<Void> validateData(String processDay) {
        String finalProcessDay;
        try {
            if (StringUtils.isEmpty(processDay)) {
                finalProcessDay = erpUtil.getDateDirFromInput("");
            } else {
                finalProcessDay = processDay;
            }
            // 初始化token
            String token = fusionAuthService.generateJwtToken(username);
            Map<String, List<CuxTwoTabValueExtraPO>> cache = initMapping();
            Map<String, Map<String, CuxTwoTabValueExtraPO>> result = cache.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,  // 保持外层Map的key不变
                            entry -> entry.getValue().stream()  // 处理内层List
                                    .collect(Collectors.toMap(
                                            CuxTwoTabValueExtraPO::getValue1,  // 内层Map的key
                                            Function.identity(),               // 内层Map的value
                                            (existing, replacement) -> existing  // 处理重复key
                                    ))
                    ));
            List<CompletableFuture<Void>> pageFutures = new ArrayList<>();  // 改为页面级future列表
            //查询groupId
            List<Long> groupIds = sofiGlInterfaceHeaderService.findGroupIdByProcessDay(finalProcessDay,SourceSysEnum.SAFI.getCode());
            for (Long groupId : groupIds) {
//            Long groupId = 20003985L;
//            List<Long> groupIds = new ArrayList<>();
//            groupIds.add(groupId);

                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    log.info("当前线程名字：" + Thread.currentThread().getName());
                    try {
                        List<SofiGlInterfaceDO> glInterfaceList = sofiGlInterfaceService.querySofiGlInterfaceByGroupIdAndProcessDay(groupId, finalProcessDay);
                        process(glInterfaceList, token, groupId, result);
                    } catch (ErpAdaptorBusinessException e) {
                        log.error("validatedAndMappedDataJob error: {}", ErpAdaptorErrorNo.SYSTEM_ERROR_DEFAULT_FAIL, e);
                        throw new ErpAdaptorBusinessException(MessageErrorNo.SYSTEM_ERROR_DEFAULT_FAIL);
                    }
                }, validateThreadPool).exceptionally(e -> {
                    log.error("groupId:{},validatedAndMappedDataJob error: {}", groupId, ErpAdaptorErrorNo.SYSTEM_ERROR_DEFAULT_FAIL, e);
                    CompletableFuture<Void> failedFuture = new CompletableFuture<>();
                    failedFuture.completeExceptionally(e);
//                    return failedFuture;
                    return null;
                });
                pageFutures.add(future);
            }
            // 在所有processSecond完成后执行validateCoa
            return CompletableFuture.allOf(pageFutures.toArray(new CompletableFuture[0]))
                    .thenRun(() -> sofiGlInterfaceService.validateCoa(finalProcessDay))
                    .exceptionally(e -> {
                        log.error("Error after all processSecond operations: {}", e.getMessage(), e);
                        return null;
                    });
        } catch (Exception e) {
            log.error("validatedAndMappedDataJob error: {}", ErpAdaptorErrorNo.SYSTEM_ERROR_DEFAULT_FAIL, e);
            CompletableFuture<Void> failedFuture = new CompletableFuture<>();
            failedFuture.completeExceptionally(e);
            return failedFuture;
        }

    }

    @PostMapping("/pushData")
    public String pushData(String processDay) {

        try {
            String finalProcessDay;
            if (StringUtils.isEmpty(processDay)) {
                finalProcessDay = erpUtil.getDateDirFromInput("");
            } else {
                finalProcessDay = processDay;
            }

            int pageNum = 1;
            //初始化token
            String token = fusionAuthService.generateJwtToken(username);
            SofiGlInterfaceHeaderQueryDO sofiGlInterfaceHeaderQueryDO = new SofiGlInterfaceHeaderQueryDO();
            sofiGlInterfaceHeaderQueryDO.setPageSize(PAGE_SIZE);
            sofiGlInterfaceHeaderQueryDO.setProcessDay(finalProcessDay);
            while (true) {
                sofiGlInterfaceHeaderQueryDO.setPageNum(pageNum);
                log.info("validatedAndMappedDataJob start request={}=", sofiGlInterfaceHeaderQueryDO);
                List<Long> groupIds = sofiGlInterfaceService.queryGroupId(sofiGlInterfaceHeaderQueryDO);
                if(CollectionUtils.isEmpty(groupIds)){
                    break;
                }
                fusionFileProcessService.processCSVFile(groupIds,token);
                pageNum++;
                log.info("pushData pageNum:{}", pageNum);
            }
        } catch (Exception e) {
            log.error("pushFusionDataJob exception errorNO ={}", CommonErrorNo.FAIL, e);
        }
        return "success";
    }

    @PostMapping("/sendEmail")
    public String sendEmail(@RequestParam String processDay) throws IOException {
        List<SofiEmailDO> list = sofiGlInterfaceService.queryCountInterfaceEmailData(processDay);
        //nolint
        String strDate = ElvishDateUtils.formatToYYYYMMDDHHMMSS_Compact(System.currentTimeMillis());
        String fileName = strDate + CommonConstant.CSV_EXTENSION;
        CSVPrinter printer = new CSVPrinter(new FileWriter(fileName), CSVFormat.DEFAULT);
        printer.printRecord("Source System", "File Name", "Status", "Count");
        for (SofiEmailDO sofiEmailDO : list) {
            printer.printRecord(sofiEmailDO.getSourceSys(), sofiEmailDO.getFileName(), sofiEmailDO.getProcessStatus(), sofiEmailDO.getCount());
        }
        // 关闭printer
        printer.flush();
        printer.close();
        InputStream input = Files.newInputStream(Paths.get(fileName));
        minioFileOperator.upload(fileName, input);
        String fileUrl = minioFileOperator.getUrl(fileName);
        ErpAdaptorMessageRequest erpAdaptorMessageRequest = new ErpAdaptorMessageRequest();
        List<String> attachements = new ArrayList<>();
        attachements.add("http://s3.amazon.com/file/11");
        attachements.add(fileUrl);
        erpAdaptorMessageRequest.setAttachments(attachements);
        messageGateway.sendEmail(erpAdaptorMessageRequest);
        return JsonUtil.toString(list);
    }


    @PostMapping("/validateDataShenMa")
    public CompletableFuture<Void> validateDataShenMa(String processDay) {
        String param = XxlJobHelper.getJobParam();
        String finalProcessDay = processDay;
        try {
//            if (StringUtils.isEmpty(param)) {
//                finalProcessDay = erpUtil.getDateDirFromInput("");
//            } else {
//                finalProcessDay = processDay;
//            }
            // 初始化token
            String token = fusionAuthService.generateJwtToken(username);
            Map<String, List<CuxTwoTabValueExtraPO>> cache = initMapping();
            Map<String, Map<String, CuxTwoTabValueExtraPO>> result = cache.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,  // 保持外层Map的key不变
                            entry -> entry.getValue().stream()  // 处理内层List
                                    .collect(Collectors.toMap(
                                            CuxTwoTabValueExtraPO::getValue1,  // 内层Map的key
                                            Function.identity(),               // 内层Map的value
                                            (existing, replacement) -> existing  // 处理重复key
                                    ))
                    ));
            List<CompletableFuture<Void>> pageFutures = new ArrayList<>();  // 改为页面级future列表
            //先判断vouchergroup  是否有借贷金额不相等的记录
            List<SummaryResultDO> summaryResultDOS = sofiGlShenMaService.queryShenMaSumByVoucherGroup(finalProcessDay);
            log.info("vouchergroup summaryResultDOS:{}", summaryResultDOS);
            if (CollectionUtils.isNotEmpty(summaryResultDOS)) {
                //说明借贷金额不相等，校验失败
                for(SummaryResultDO summaryResultDO : summaryResultDOS) {
                    SofiGlInterfaceHeaderDO headerDO = new SofiGlInterfaceHeaderDO();
                    headerDO.setProcessDay(summaryResultDO.getProcessDay());
                    headerDO.setExternalReference(summaryResultDO.getLinkReference());
                    headerDO.setGroupId(summaryResultDO.getGroupId());
                    SofiGlShenmaDO sofiGlShenmaDO = new SofiGlShenmaDO();
                    sofiGlShenmaDO.setProcessMessage("vouchergroup sum not equals" + summaryResultDO.getVoucherGroup());
                    sofiGlShenmaDO.setProcessDay(summaryResultDO.getProcessDay());
                    sofiGlShenmaDO.setLinkReference(summaryResultDO.getLinkReference());
                    sofiGlShenmaDO.setVoucherGroup(summaryResultDO.getVoucherGroup());
                    sofiShenMaTransactionService.updateShenMaInterfaceAndHeader(headerDO, sofiGlShenmaDO, ProcessStatusEnum.VALIDATE_FAILED);
                }
            }
            //查询groupId
            List<Long> groupIds = sofiGlInterfaceHeaderService.findGroupIdByProcessDay(finalProcessDay, SourceSysEnum.SHEN_MA.getCode());
            for (Long groupId : groupIds) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        List<SofiGlShenmaDO> glShenmaDOList = sofiGlShenMaService.querySofiGlShenmaByGroupIdAndProcessDay(groupId, finalProcessDay);
                        processShenma(glShenmaDOList, token, groupId, result);
                    } catch (Exception e) {
                        log.error("validatedAndMappedDataJob error: {}", ErpAdaptorErrorNo.SYSTEM_ERROR_DEFAULT_FAIL, e);
                        throw new ErpAdaptorBusinessException(MessageErrorNo.SYSTEM_ERROR_DEFAULT_FAIL);
                    }
                }, shenMaValidatedThreadPool).exceptionally(e -> {
                    log.error("groupId:{},validatedAndMappedDataJob error: {}", groupId, ErpAdaptorErrorNo.SYSTEM_ERROR_DEFAULT_FAIL, e);
                    CompletableFuture<Void> failedFuture = new CompletableFuture<>();
                    failedFuture.completeExceptionally(e);
                    return null;
                });
                pageFutures.add(future);
            }
            log.info("validateData job success end,pageFutrues size {}", pageFutures.size());
            // 在所有process完成后执行validateCoa
            return CompletableFuture.allOf(pageFutures.toArray(new CompletableFuture[0]))
                    .thenRun(() -> sofiGlShenMaService.validateCoa(finalProcessDay))
                    .exceptionally(e -> {
                        log.error("Error after all processSecond operations: {}", e.getMessage(), e);
                        return null;
                    });
        } catch (Exception e) {
            log.error("validatedAndMappedDataJob error: {}", ErpAdaptorErrorNo.SYSTEM_ERROR_DEFAULT_FAIL, e);
            CompletableFuture<Void> failedFuture = new CompletableFuture<>();
            failedFuture.completeExceptionally(e);
            return failedFuture;
        }


    }
    @PostMapping("/pushShenMaData")
    public String pushShenMaData(String processDay) {
        try {
            String param = XxlJobHelper.getJobParam();
            String finalProcessDay = processDay;
//            if (StringUtils.isEmpty(param)) {
//                finalProcessDay = erpUtil.getDateDirFromInput("");
//            } else {
//                finalProcessDay = processDay;
//            }
            int pageNum = 1;
            int pageSize = 500;
            //初始化token
            String token = fusionAuthService.generateJwtToken(username);
            //1.进行汇总金额校验
            List<SummaryResultDO> summaryResultShenma = sofiGlShenMaService.queryShenMaSummary(finalProcessDay);
            List<SummaryResultDO> summaryResultAcctHis = sofiGlAcctHistService.queryAcctHisSummary(finalProcessDay);
            boolean allIncluded =  summaryResultShenma.stream().allMatch(summaryResultAcctHis::contains);
            if(!allIncluded) {
                log.info("不相等，邮件告警");
            }
//
            while (true) {
                log.info("validatedAndMappedDataJob start request={}=", finalProcessDay);
                List<Long> groupIds = sofiGlInterfaceCommonService.queryGroupIds(pageNum, pageSize, finalProcessDay);
                if (CollectionUtils.isEmpty(groupIds)) {
                    break;
                }
                fusionFileProcessService.processCSVFileToCommon(SourceSysEnum.SHEN_MA.getCode(), finalProcessDay, groupIds, token);
                pageNum++;
                log.info("pushData pageNum:{}", pageNum);
            }
        } catch (Exception e) {
            log.error("pushFusionDataJob exception errorNO ={}", CommonErrorNo.FAIL, e);
        }
        return "success";
    }
    private List<ValidationRequestDO> buildRequests(SofiGlInterfaceDO sofiGlInterfaceDO) {
        List<ValidationRequestDO> requests = new ArrayList<>();
        ValidationRequestDO validationRequestDO = new ValidationRequestDO();
        validationRequestDO.setLedgerName(sofiGlInterfaceDO.getLedgerName());
        validationRequestDO.setSegment1(sofiGlInterfaceDO.getSegment1());
        validationRequestDO.setSegment2(sofiGlInterfaceDO.getSegment2());
        validationRequestDO.setSegment3(sofiGlInterfaceDO.getSegment3());
        validationRequestDO.setSegment4(sofiGlInterfaceDO.getSegment4());
        validationRequestDO.setSegment5(sofiGlInterfaceDO.getSegment5());
        validationRequestDO.setSegment6(sofiGlInterfaceDO.getSegment6());
        validationRequestDO.setSegment7(sofiGlInterfaceDO.getSegment7());
        validationRequestDO.setSegment8(sofiGlInterfaceDO.getSegment8());
        validationRequestDO.setSegment9(sofiGlInterfaceDO.getSegment9());
        validationRequestDO.setSegment10(sofiGlInterfaceDO.getSegment10());
        requests.add(validationRequestDO);
        return requests;
    }

    private Map<String, List<CuxTwoTabValueExtraPO>> initMapping() {
        List<CuxTwoTabValueExtraPO> cuxTwoTabValuePOS = twoTabValueService.selectAllByFormCode();
        return cuxTwoTabValuePOS.stream().collect(Collectors.groupingBy(CuxTwoTabValueExtraPO::getFormCode));
    }

    private void process(List<SofiGlInterfaceDO> sofiGlInterfaceDOs, String token, Long groupId, Map<String, Map<String, CuxTwoTabValueExtraPO>> cache) {
        SofiGlInterfaceHeaderDO headerDO = new SofiGlInterfaceHeaderDO();
        headerDO.setGroupId(groupId);
        Map<Long, List<SofiGlInterfaceDO>> map = sofiGlInterfaceDOs.stream()
                .collect(Collectors.groupingBy(SofiGlInterfaceDO::getPolizaId));
        for (SofiGlInterfaceDO sofiGlInterfaceDO : sofiGlInterfaceDOs) {
            headerDO.setProcessDay(sofiGlInterfaceDO.getProcessDay());
            headerDO.setExternalReference(sofiGlInterfaceDO.getPolizaId() + "");
            if (ProcessStatusEnum.MAP_FAILED.equals(sofiGlInterfaceDO.getProcessStatus()) || ProcessStatusEnum.NEW.equals(sofiGlInterfaceDO.getProcessStatus())) {
                sofiGlInterfaceTransactionService.updateInterfaceAndInterfaceHeader(headerDO, sofiGlInterfaceDO, ProcessStatusEnum.PROCESSING);
            }

            //3.数据mapping
            String errorMapMessage = sofiGlInterfaceValidatedService.mappedResult(sofiGlInterfaceDO, cache);
            log.info("polizaid {} mapping result:{}", sofiGlInterfaceDO.getPolizaId(), errorMapMessage);
            //4.验证VALIDATA
            String errorMessage = sofiGlInterfaceValidatedService.validated(sofiGlInterfaceDO, map);
            log.info("polizaid {} validated result:{}", sofiGlInterfaceDO.getPolizaId(), errorMessage);
            if (StringUtils.isNotEmpty(errorMessage) && !ErpAdaptorErrorNo.IMP_ERR_ERROR.getErrorMsg().equals(errorMessage)) {
                ///说明验证失败了/更新状态为VALIDATE_FAILED
                sofiGlInterfaceDO.setProcessMessage(errorMessage);
                headerDO.setProcessMessage(errorMessage);
                sofiGlInterfaceTransactionService.updateInterfaceAndInterfaceHeaderByPolizaId(headerDO, sofiGlInterfaceDO, ProcessStatusEnum.VALIDATE_FAILED);
                continue;
            } else {
                sofiGlInterfaceTransactionService.updateInterfaceAndInterfaceHeader(headerDO, sofiGlInterfaceDO, ProcessStatusEnum.VALIDATED);
            }
            if (StringUtils.isNotEmpty(errorMapMessage) && !ErpAdaptorErrorNo.IMP_ERR_ERROR.getErrorMsg().equals(errorMapMessage)) {
                ///说明map 失败了
                sofiGlInterfaceDO.setProcessMessage(errorMapMessage);
                headerDO.setProcessMessage(errorMapMessage);
                sofiGlInterfaceTransactionService.updateInterfaceAndInterfaceHeaderByPolizaId(headerDO, sofiGlInterfaceDO, ProcessStatusEnum.MAP_FAILED);
                continue;
            }
            //4.1 验证fushion 期间是否打开
            //初始化
            //nolint
            long date = ElvishDateUtils.parseDateTime(sofiGlInterfaceDO.getFecha(), "yyyy-MM").getTime();
            //nolint
            String periodName = ElvishDateUtils.formatToYYYY_MM_Dash(date);
            ConcurrentHashMap<String, Boolean> booleanMap = new ConcurrentHashMap<>();
            booleanMap.computeIfAbsent(
                    periodName,
                    key -> fusionStatusService.queryFusionOpenStatus(periodName, String.valueOf(sofiGlInterfaceDO.getLedgerId()), token).get(periodName)
            );
            log.info("polizaId=:{},periodName={}, booleanMap=:{}", sofiGlInterfaceDO.getPolizaId(), periodName, booleanMap);
            if (!booleanMap.get(periodName)) {
                //MappedFaild
                sofiGlInterfaceDO.setProcessMessage("fusion status not open");
                headerDO.setProcessMessage("fusion status not open");
                sofiGlInterfaceTransactionService.updateInterfaceAndInterfaceHeaderByPolizaId(headerDO, sofiGlInterfaceDO, ProcessStatusEnum.MAP_FAILED);
            }
        }
    }

    private void processShenma(List<SofiGlShenmaDO> glShenmaDOList, String token, Long groupId, Map<String, Map<String, CuxTwoTabValueExtraPO>> cache) {
        SofiGlInterfaceHeaderDO headerDO = new SofiGlInterfaceHeaderDO();
        headerDO.setGroupId(groupId);
        List<SofiGlInterfaceCommonDO> list = new ArrayList<>();
        ConcurrentHashMap<String, Integer> map = new ConcurrentHashMap<>();
        for (SofiGlShenmaDO sofiGlShenmaDO : glShenmaDOList) {
            map.merge(sofiGlShenmaDO.getExternalReference(), 1, Integer::sum);
            headerDO.setProcessDay(sofiGlShenmaDO.getProcessDay());
            headerDO.setExternalReference(sofiGlShenmaDO.getExternalReference());
            if(ProcessStatusEnum.VALIDATE_FAILED.equals(sofiGlShenmaDO.getProcessStatus())) {
                // 验证失败的数据不需要后续处理
                continue;
            }
            //状态改为processing
            sofiShenMaTransactionService.updateShenMaInterfaceAndHeader(headerDO, sofiGlShenmaDO, ProcessStatusEnum.PROCESSING);
            //3.数据Mapping
            SofiGlInterfaceCommonDO commonDO = new SofiGlInterfaceCommonDO();

            //nolint
            long date = ElvishDateUtils.parseDateTime(sofiGlShenmaDO.getPostDate(), "yyyy-MM").getTime();
            //nolint
            String periodName = ElvishDateUtils.formatToYYYY_MM_Dash(date);
            commonDO.setPeriodName(periodName);
            String errorMapMessage = sofiGlShenmaValidatedService.mappedResult(sofiGlShenmaDO, commonDO, cache);
            log.info("mapping linkReference {} mapping result:{}", sofiGlShenmaDO.getLinkReference(), errorMapMessage);

            //4.验证VALIDATA
            String errorMessage = sofiGlShenmaValidatedService.validated(sofiGlShenmaDO);
            log.info("validated linkReference {} validated result:{}", sofiGlShenmaDO.getLinkReference(), errorMessage);

            if (StringUtils.isNotEmpty(errorMessage) && !ErpAdaptorErrorNo.IMP_ERR_ERROR.getErrorMsg().equals(errorMessage)) {
                ///说明验证失败了/更新状态为VALIDATE_FAILED
                sofiGlShenmaDO.setProcessMessage(errorMessage);
                headerDO.setProcessMessage(errorMessage);
                sofiShenMaTransactionService.updateShenMaInterfaceAndHeader(headerDO, sofiGlShenmaDO, ProcessStatusEnum.VALIDATE_FAILED);
                continue;
            } else {
                sofiShenMaTransactionService.updateShenMaInterfaceAndHeader(headerDO, sofiGlShenmaDO, ProcessStatusEnum.VALIDATED);
            }

            if (StringUtils.isNotEmpty(errorMapMessage) && !ErpAdaptorErrorNo.IMP_ERR_ERROR.getErrorMsg().equals(errorMapMessage)) {
                ///说明map 失败了
                sofiGlShenmaDO.setProcessMessage(errorMapMessage);
                headerDO.setProcessMessage(errorMapMessage);
                sofiShenMaTransactionService.updateShenMaInterfaceAndHeader(headerDO, sofiGlShenmaDO, ProcessStatusEnum.MAP_FAILED);
                continue;
            }
            //4.1 验证fushion 期间是否打开
            ConcurrentHashMap<String, Boolean> booleanMap = new ConcurrentHashMap<>();
            booleanMap.computeIfAbsent(
                    periodName,
                    key -> fusionStatusService.queryFusionOpenStatus(periodName, String.valueOf(commonDO.getLedgerId()), token).get(periodName)
            );
            log.info("linkReference:{},periodName:{}, booleanMap:{}", sofiGlShenmaDO.getLinkReference(), periodName, booleanMap);
            if (!booleanMap.get(periodName)) {
                //MappedFaild
                sofiGlShenmaDO.setProcessMessage("fusion status not open");
                headerDO.setProcessMessage("fusion status not open");
                sofiShenMaTransactionService.updateShenMaInterfaceAndHeader(headerDO, sofiGlShenmaDO, ProcessStatusEnum.MAP_FAILED);
            }
            list.add(bulidSofiGlInterfaceCommonDO(sofiGlShenmaDO, commonDO, map));
            //落库common表
            if (list.size() >= CommonConstant.PAGE_SIZE) {
                sofiGlInterfaceCommonService.batchInsert(list);
                list.clear();
            }
        }
        if (CollectionUtils.isNotEmpty(list)) {
            log.info("list size {}", list.size());
            sofiGlInterfaceCommonService.batchInsert(list);
        }
        log.info("save common success end ");
    }

    private SofiGlInterfaceCommonDO bulidSofiGlInterfaceCommonDO(SofiGlShenmaDO sofiGlShenmaDO, SofiGlInterfaceCommonDO commonDO, ConcurrentHashMap<String, Integer> map) {
        commonDO.setSystemCode(SourceSysEnum.SHEN_MA.getCode());
        commonDO.setProcessDay(sofiGlShenmaDO.getProcessDay());
        //nolint
        commonDO.setAccountingDate(ElvishDateUtils.parseDateTime(sofiGlShenmaDO.getPostDate(), "yyyy-MM-dd"));
        commonDO.setAttributeCategory(CommonConstant.CORE_BANKING);
        commonDO.setJournalCategory(sofiGlShenmaDO.getVoucherGroup());
        commonDO.setSegment2(CommonConstant.SHENMA_SEGMENT2);
        commonDO.setSegment4(CommonConstant.ZERO);
        commonDO.setSegment7(CommonConstant.ZERO);
        commonDO.setSegment8(sofiGlShenmaDO.getIntercompany());
        commonDO.setSegment9(CommonConstant.ZERO);
        commonDO.setSegment10(CommonConstant.ZERO);
        commonDO.setEnteredDr(sofiGlShenmaDO.getEnteredDebitAmount());
        commonDO.setEnteredCr(sofiGlShenmaDO.getEnteredCreditAmount());
        commonDO.setAccountedDr(sofiGlShenmaDO.getEnteredDebitAmount());
        commonDO.setAccountedCr(sofiGlShenmaDO.getEnteredCreditAmount());
        commonDO.setGroupId(sofiGlShenmaDO.getGroupId());
        commonDO.setCurrencyConversionRate(sofiGlShenmaDO.getFlatRate());
        commonDO.setCurrencyConversionType("");
        commonDO.setReference1(commonDO.getSegment1() + "_" + sofiGlShenmaDO.getGroupId());
        commonDO.setReference2(commonDO.getSegment1() + "_" + commonDO.getPeriodName() + "_" + commonDO.getJournalCategory());
        commonDO.setReference4(commonDO.getSegment1() + "_" + commonDO.getPeriodName() + "_"+ CommonConstant.SHEN_MA_JOURNAL_SOURCE + "_" + sofiGlShenmaDO.getProcessDay() + "_"+ commonDO.getJournalCategory());
        commonDO.setReference5(sofiGlShenmaDO.getExternalReference());
        commonDO.setReference6(commonDO.getPeriodName() + "_" +sofiGlShenmaDO.getVoucherGroup());
        commonDO.setReference8("");
        commonDO.setReference9("");
        commonDO.setReference10(commonDO.getSegment1() + "_" + commonDO.getPeriodName() + "_" + CommonConstant.SHEN_MA_JOURNAL_SOURCE + "_" + sofiGlShenmaDO.getLinkReference());
        commonDO.setReference21(CommonConstant.SHEN_MA_JOURNAL_SOURCE);
        commonDO.setReference22(commonDO.getPeriodName() + "_" +sofiGlShenmaDO.getVoucherGroup());
        commonDO.setReference23(String.valueOf(map.get(sofiGlShenmaDO.getExternalReference()) * 10));
        commonDO.setReference24("");
        commonDO.setReference25("");
        commonDO.setReference26("");
        commonDO.setReference27("");
        commonDO.setReference28("");
        commonDO.setReference29("");
        commonDO.setReference30("");
        commonDO.setHeaderAttribute1("");
        commonDO.setHeaderAttribute2("");
        commonDO.setHeaderAttribute3("");
        commonDO.setHeaderAttribute4("");
        commonDO.setHeaderAttribute5("");
        commonDO.setHeaderAttribute6("");
        commonDO.setHeaderAttribute7("");
        commonDO.setHeaderAttribute8("");
        commonDO.setHeaderAttribute9("");
        commonDO.setHeaderAttribute10("");
        commonDO.setHeaderAttribute11("");
        commonDO.setHeaderAttribute12("");
        commonDO.setHeaderAttribute13("");
        commonDO.setHeaderAttribute14("");
        commonDO.setHeaderAttribute15("");
        commonDO.setAttributeCategory3("");
        commonDO.setLineAttribute1("");
        commonDO.setLineAttribute2(sofiGlShenmaDO.getAmtType());
        commonDO.setLineAttribute3(sofiGlShenmaDO.getTranType());
        commonDO.setLineAttribute4(sofiGlShenmaDO.getEventType());
        commonDO.setLineAttribute5(sofiGlShenmaDO.getClientType());
        commonDO.setLineAttribute6(sofiGlShenmaDO.getInlandOffshore());
        commonDO.setLineAttribute7(CommonConstant.ATTRIBUTE7);
        commonDO.setLineAttribute8("");
        commonDO.setLineAttribute9(sofiGlShenmaDO.getChannelSeqNo());
        commonDO.setLineAttribute10("");
        commonDO.setLineAttribute11("");
        commonDO.setLineAttribute12("");
        commonDO.setLineAttribute13("");
        commonDO.setLineAttribute14("");
        commonDO.setLineAttribute15("");
        commonDO.setLineAttribute16("");
        commonDO.setLineAttribute17("");
        commonDO.setLineAttribute18("");
        commonDO.setLineAttribute19("");
        commonDO.setLineAttribute20("");
        commonDO.setProcessStatus(sofiGlShenmaDO.getProcessStatus());
        commonDO.setProcessMessage("");
        commonDO.setJeHeaderId(0L);
        commonDO.setJournalName("");
        commonDO.setJeLineNum(0L);
        commonDO.setDocumentId(0L);
        commonDO.setLoadRequestId(0L);
        commonDO.setImportRequestId(0L);
        commonDO.setObjectVersionNumber(1L);
        commonDO.setCreatedBy("");
        commonDO.setLastModifiedBy("");
        commonDO.setJournalSourceName(CommonConstant.SHEN_MA_JOURNAL_SOURCE);
        commonDO.setJournalSource(CommonConstant.SHEN_MA_JOURNAL_SOURCE);
        return commonDO;
    }

    @Autowired
    private SofiGlSubjectSyncService sofiGlSubjectSyncService;

    @Autowired
    private VoucherGroupSyncService voucherGroupSyncService;

    @Autowired
    private SofiGlAcctHistSyncService sofiGlAcctHistSyncService;

    @Autowired
    private ShenmaFilePreprocessService shenmaFilePreprocessService;

    @Value("${fusion.path:1")
    private String filePath;

    /**
     * 【safi】执行文件预处理
     */
    @PostMapping("/preprocess-safi")
    public ReturnMsg<ExecResult> preprocessFile(@RequestParam(value = "param", required = false) String param) {
        ExecResult result = filePreprocessService.processFile(filePath, param);
        log.info("execute filePreprocess end，result: {}", JsonUtil.toString(result));
        return ReturnMsg.success(result);
    }

    /**
     * 【safi】轮训状态
     */
    @PostMapping("/pollLoadRequestStatus")
    public ReturnMsg<ExecResult> pollLoadRequestStatus(@RequestParam(value = "param", required = false) String param) {
        ExecResult execResult = statusPollingService.pollLoadRequestStatus(SourceSysEnum.SAFI.getCode(), param);
        return ReturnMsg.success(execResult);
    }

    /**
     * 【safi】funcion返回结果处理
     */
    @PostMapping("/handleFusionResultJob")
    public void execute(@RequestParam(value = "param", required = false) String param) {
        fusionResultService.handleFusionResultJob(SourceSysEnum.SAFI.getCode(),param);
    }

    /**
     * 【shenma】执行文件预处理
     */
    @PostMapping("/preprocess")
    public ReturnMsg<ExecResult> preprocessShenmaFile(@RequestParam(value = "param", required = false) String param) {
        log.info("execute shenmaFilePreprocess start, param: {}", param);
        ExecResult result = shenmaFilePreprocessService.processShenmaFile(filePath, param);
        log.info("execute shenmaFilePreprocess end，result: {}", JsonUtil.toString(result));
        return ReturnMsg.success(result);
    }


    /**
     * 【shenma】轮训状态
     */
    @PostMapping("/pollShenmaLoadRequestStatus")
    public ReturnMsg<ExecResult> pollShenmaLoadRequestStatus(@RequestParam(value = "param", required = false) String param) {
        ExecResult execResult = statusPollingService.pollLoadRequestStatus(SourceSysEnum.SHEN_MA.getCode(), param);
        return ReturnMsg.success(execResult);
    }

    /**
     * 【shenma】funcion返回结果处理
     */
    @PostMapping("/handleShenmaFusionResultJob")
    public void handleShenmaFusionResultJob(@RequestParam(value = "param", required = false) String param) {
        fusionResultService.handleFusionResultJob(SourceSysEnum.SHEN_MA.getCode(), param);
    }


    /**
     * 【shenma】执行SofiGlSubject同步
     */
    @PostMapping("/sofi-gl-subject")
    public ReturnMsg<ExecResult> syncSofiGlSubject(@RequestParam(value = "param", required = false) String param) {
        String taskName = "SofiGlSubject";
        log.info("syncSofiGlSubject {} sync start, param: {}", taskName, param);
        ExecResult result = sofiGlSubjectSyncService.processSofiGlSubjectFile(filePath, param);
        log.info("syncSofiGlSubject {} sync end, success: {}", taskName, result.isSuccess());
        return ReturnMsg.success(result);
    }

    /**
     * 【shenma】执行VoucherGroup同步
     */
    @PostMapping("/voucher-group")
    public ReturnMsg<ExecResult> syncVoucherGroup(@RequestParam(value = "param", required = false) String param) {
        String taskName = "VoucherGroup";
        log.info("syncVoucherGroup {} sync start, param: {}", taskName, param);
        ExecResult result = voucherGroupSyncService.processVoucherGroupMapping(filePath, param);
        log.info("syncVoucherGroup {} sync end, success: {}", taskName, result.isSuccess());
        return ReturnMsg.success(result);
    }

    /**
     * 【shenma】执行SofiGlAcctHist同步
     */
    @PostMapping("/sofi-gl-acct-hist")
    public ReturnMsg<ExecResult> syncSofiGlAcctHist(@RequestParam(value = "param", required = false) String param) {
        String taskName = "SofiGlAcctHist";
        log.info("syncSofiGlAcctHist {} sync start, param: {}", taskName, param);
        ExecResult result = sofiGlAcctHistSyncService.processSofiGlAcctHistFile(filePath, param);
        log.info("syncSofiGlAcctHist {} sync end, success: {}", taskName, result.isSuccess());
        return ReturnMsg.success(result);
    }

    @Resource
    @Qualifier("sofiGlShenmaCustomerMapper")
    private SofiGlShenmaCustomerMapper sofiGlShenmaCustomerMapper;

    @Resource
    @Qualifier("sofiGlInterfaceCommonCustomerMapper")
    private SofiGlInterfaceCommonCustomerMapper sofiGlInterfaceCommonCustomerMapper;

    @Resource
    @Qualifier("sofiGlShenmaHisCustomerMapper")
    private SofiGlShenmaHisCustomerMapper sofiGlShenmaHisCustomerMapper;

    @Resource
    @Qualifier("sofiGlAcctHistCustomerMapper")
    private SofiGlAcctHistCustomerMapper sofiGlAcctHistCustomerMapper;

    @PostMapping("/delete-shenma-data")
    public ReturnMsg<Integer> deleteShenmaData(@RequestParam(required = false) String processDay) {
        try {
            int deleteCount = sofiGlShenmaCustomerMapper.deleteByProcessDay(processDay);
            log.info("删除sofi_gl_shenma表数据完成，删除条数: {}", deleteCount);
            return ReturnMsg.success(deleteCount);
        } catch (Exception e) {
            log.error("删除sofi_gl_shenma表数据失败", e);
            return ReturnMsg.build(CommonErrorNo.FAIL.getErrorNo(), "删除失败: " + e.getMessage());
        }
    }

    @PostMapping("/delete-interface-common-data")
    public ReturnMsg<Integer> deleteInterfaceCommonData(@RequestParam(required = false) String processDay) {
        try {
            log.info("开始删除sofi_gl_interface_common表数据，processDay: {}", processDay);
            int deleteCount = sofiGlInterfaceCommonCustomerMapper.deleteByProcessDay(processDay);
            log.info("删除sofi_gl_interface_common表数据完成，删除条数: {}", deleteCount);
            return ReturnMsg.success(deleteCount);
        } catch (Exception e) {
            log.error("删除sofi_gl_interface_common表数据失败", e);
            return ReturnMsg.build(CommonErrorNo.FAIL.getErrorNo(), "删除失败: " + e.getMessage());
        }
    }

    @PostMapping("/delete-shenma-his-data")
    public ReturnMsg<Integer> deleteShenmaHisData(@RequestParam(required = false) String processDay) {
        try {
            log.info("开始删除sofi_gl_shenma_his表数据，processDay: {}", processDay);
            int deleteCount = sofiGlShenmaHisCustomerMapper.deleteByProcessDay(processDay);
            log.info("删除sofi_gl_shenma_his表数据完成，删除条数: {}", deleteCount);
            return ReturnMsg.success(deleteCount);
        } catch (Exception e) {
            log.error("删除sofi_gl_shenma_his表数据失败", e);
            return ReturnMsg.build(CommonErrorNo.FAIL.getErrorNo(), "删除失败: " + e.getMessage());
        }
    }

    @PostMapping("/delete-acct-hist-data")
    public ReturnMsg<Integer> deleteAcctHistData(@RequestParam(required = false) String processDay) {
        try {
            log.info("开始删除sofi_gl_acct_hist表数据，processDay: {}", processDay);
            int deleteCount = sofiGlAcctHistCustomerMapper.deleteByProcessDay(processDay);
            log.info("删除sofi_gl_acct_hist表数据完成，删除条数: {}", deleteCount);
            return ReturnMsg.success(deleteCount);
        } catch (Exception e) {
            log.error("删除sofi_gl_acct_hist表数据失败", e);
            return ReturnMsg.build(CommonErrorNo.FAIL.getErrorNo(), "删除失败: " + e.getMessage());
        }
    }
}
