package com.xiaoju.corebanking.erp.adaptor.config;

import com.github.pagehelper.PageInterceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Properties;

/**
 * @Description: 类描述
 * @author: zhangcc
 * @date: 2024/9/12$
 **/
@Configuration
public class PageHelperConfiguration {
    @Autowired
    private List<SqlSessionFactory> sqlSessionFactoryList;

    @PostConstruct
    public void addPageInterceptor() {
        //Pagehelper自定义配置,为避免和autoconfigure的配置弄混,不放到配置文件中!
        Properties properties = new Properties();
        properties.put("helper-dialect", "mysql");
        properties.put("reasonable", true);
        properties.put("support-methods-arguments", true);


        PageInterceptor interceptor = new PageInterceptor();
        interceptor.setProperties(properties);
        for (SqlSessionFactory sqlSessionFactory : this.sqlSessionFactoryList) {
            sqlSessionFactory.getConfiguration().addInterceptor(interceptor);
        }
    }
}
