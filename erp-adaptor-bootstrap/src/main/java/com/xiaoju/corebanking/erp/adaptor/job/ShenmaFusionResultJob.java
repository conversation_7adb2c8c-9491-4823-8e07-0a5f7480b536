package com.xiaoju.corebanking.erp.adaptor.job;

import com.xiaoju.corebanking.erp.adaptor.common.enums.SourceSysEnum;
import com.xiaoju.corebanking.erp.adaptor.service.inter.FusionResultService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description fuction接口回写
 * @date 2025/5/23 16:55
 */
@Slf4j
@Service
public class ShenmaFusionResultJob extends IJobHandler {

    @Resource
    private FusionResultService fusionResultService;

    @XxlJob("shenmaFusionResultJob")
    public void execute() {
        try {
            String param = XxlJobHelper.getJobParam();
            fusionResultService.handleFusionResultJob(SourceSysEnum.SHEN_MA.getCode(),param);
        } catch (Exception e) {
            log.error("shenmaFusionResultJob: 执行任务异常", e);
        }
    }
}
