package com.xiaoju.corebanking.erp.adaptor.controller.dto;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class SofiGlInterfaceUpdateRequestDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 单据ID（不可修改）
     */
    private Long polizaId;

    /**
     * 文件ID
     */
    private Long fileId;

    /**
     * 处理日期
     */
    private String processDay;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 批次ID
     */
    private String batchId;

    /**
     * 明细ID
     */
    private Long detallePolizaId;

    /**
     * 来源系统
     */
    private String sourceSys;

    /**
     * 企业ID
     */
    private Long empresaId;

    /**
     * 日期
     */
    private String fecha;

    /**
     * 成本中心ID
     */
    private Long centroCostoId;

    /**
     * 账户完整信息
     */
    private String cuentaCompleta;

    /**
     * 工具
     */
    private Long instrumento;

    /**
     * 货币ID
     */
    private Long monedaId;

    /**
     * 描述
     */
    private String descripcion;

    /**
     * 引用
     */
    private String referencia;

    /**
     * 过账程序
     */
    private String procedimientoCont;

    /**
     * 工具类型ID
     */
    private String tipoInstrumentoId;

    /**
     * RFC
     */
    private String rfc;

    /**
     * 总发票金额
     */
    private BigDecimal totalFactura;

    /**
     * 发票UUID
     */
    private String folioUuid;

    /**
     * 用户
     */
    private Long usuario;

    /**
     * 当前日期
     */
    private String fechaActual;

    /**
     * IP地址
     */
    private String direccionIp;

    /**
     * 程序ID
     */
    private String programaId;

    /**
     * 分支机构
     */
    private Long sucursal;

    /**
     * 交易编号
     */
    private Long numTransaccion;

    /**
     * 分类账ID
     */
    private Long ledgerId;

    /**
     * 货币代码
     */
    private String currencyCode;

    /**
     * 日记账类别
     */
    private String journalCategory;

    /**
     * 日记账来源
     */
    private String journalSource;

    /**
     * 分段1
     */
    private String segment1;

    /**
     * 分段2
     */
    private String segment2;

    /**
     * 分段3
     */
    private String segment3;

    /**
     * 分段4
     */
    private String segment4;

    /**
     * 分段5
     */
    private String segment5;

    /**
     * 分段6
     */
    private String segment6;

    /**
     * 分段7
     */
    private String segment7;

    /**
     * 分段8
     */
    private String segment8;

    /**
     * 分段9
     */
    private String segment9;

    /**
     * 分段10
     */
    private String segment10;

    /**
     * 组ID
     */
    private Long groupId;

    /**
     * 货币转换日期
     */
    private Date currencyConversionDate;

    /**
     * 货币转换率
     */
    private BigDecimal currencyConversionRate;

    /**
     * 用户货币转换类型
     */
    private String userCurrencyConversionType;

    /**
     * 处理状态
     */
    private ProcessStatusEnum processStatus;

    /**
     * 处理消息
     */
    private String processMessage;

    /**
     * 日记账头ID
     */
    private Long jeHeaderId;

    /**
     * 日记账名称
     */
    private String journalName;

    /**
     * 日记账行号
     */
    private Long jeLineNum;

    /**
     * 文档ID
     */
    private Long documentId;
} 