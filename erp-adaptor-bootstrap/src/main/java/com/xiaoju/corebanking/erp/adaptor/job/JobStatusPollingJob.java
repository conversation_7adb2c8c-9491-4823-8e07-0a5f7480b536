package com.xiaoju.corebanking.erp.adaptor.job;

import com.xiaoju.corebanking.erp.adaptor.common.enums.SourceSysEnum;
import com.xiaoju.corebanking.erp.adaptor.service.polling.StatusPollingService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 轮询job状态的任务
 * @date 2025/5/20 10:30
 */
@Slf4j
@Service
public class JobStatusPollingJob extends IJobHandler {

    @Resource
    private StatusPollingService statusPollingService;

    /**
     * 轮询load_request_id状态
     */
    @XxlJob("loadRequestStatusPollingJob")
    public void execute() {
        log.info("开始执行loadRequestStatusPollingJob, 轮询load_request_id状态");
        String param = XxlJobHelper.getJobParam();
        statusPollingService.pollLoadRequestStatus(SourceSysEnum.SAFI.getCode(),param);
        log.info("execute loadRequestStatusPollingJob end");
    }
} 