package com.xiaoju.corebanking.erp.adaptor.controller;


import com.xiaoju.corebanking.erp.adaptor.common.erp.RestResponse;
import com.xiaoju.corebanking.erp.adaptor.service.twotab.CuxTwoTabHeaderService;
import com.xiaoju.corebanking.erp.adaptor.service.twotab.TwoTabValueService;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.CuxTwoTabHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.CuxTwoTabValueDO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/api/v1/two-tab")
public class TwoTabController {

    @Resource
    CuxTwoTabHeaderService cuxTwoTabHeaderService;


    @Resource
    TwoTabValueService twoTabValueService;

    @PostMapping("/header")
    private RestResponse<?> createHeader(@RequestBody CuxTwoTabHeaderDO header) {
        cuxTwoTabHeaderService.insertHeader(header);
        return RestResponse.ok();
    }

    @PostMapping("/header/update-or-create")
    private RestResponse<?> saveHeader(@RequestBody CuxTwoTabHeaderDO header) {
        cuxTwoTabHeaderService.saveHeader(header);
        return RestResponse.ok();
    }

    @GetMapping("/header")
    private RestResponse<?> getHeader(@RequestParam("formCode") String formCode) {
        return RestResponse.okWithData(cuxTwoTabHeaderService.findByFormCode(formCode));
    }

    @GetMapping("/headers")
    private RestResponse<?> getAllHeaders() {
        return RestResponse.okWithData(cuxTwoTabHeaderService.selectAllHeaders());
    }

    @GetMapping("/values/{formCode}")
    private RestResponse<?> getAllValues(@PathVariable String formCode) {
        return RestResponse.okWithData(twoTabValueService.selectByFormCode(formCode));
    }

    @PostMapping("/value")
    private RestResponse<?> syncValue(@RequestBody CuxTwoTabValueDO valueDO) {
        return RestResponse.okWithData(twoTabValueService.insertValue(valueDO));
    }

    @PostMapping("/values")
    private RestResponse<?> syncValues(@RequestBody List<CuxTwoTabValueDO> list) {
        return RestResponse.okWithData(twoTabValueService.syncValues(list));
    }

}
