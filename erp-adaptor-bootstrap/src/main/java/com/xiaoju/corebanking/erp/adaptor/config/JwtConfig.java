package com.xiaoju.corebanking.erp.adaptor.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "jwt")
public class JwtConfig {
    private String issuer;
    private String username;
    private String password;
    private String publicKey;
    private String privateKey;
}
