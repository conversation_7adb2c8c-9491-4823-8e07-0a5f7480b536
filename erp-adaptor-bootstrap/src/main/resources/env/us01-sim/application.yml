# 服务配置
server:
  port: 9109
  servlet:
    context-path: /erp
  tomcat:
    connection-timeout: 60000
    max-http-form-post-size: 200MB


# 这里不能乱写，ddmq服务发现严格依赖该标识，测试：test，线上/仿真：prod，预发：pre
# sim环境染色,新增SimOnline,只是用于标注同轨环境，与生产环境无关
env: SimOnline

godson:
  # 用于配置mysql的多数据源
  datasource:
    erp:
      url: ***************************************************************************************************************
      username: ue_erp_adaptor_og9ln1_rw
      password: A0WQZOzbuoRgw2s
      rlogger: true
      hikari:
        connectionTimeout: 3000 # 等待连接池的连接的最长时间，单位毫秒，默认30秒
        minimumIdle: 5
        maximumPoolSize: 10
        idleTimeout: 60000 # 允许连接在池中闲置的最长时间，单位毫秒，默认10分钟
        maxLifetime: 180000 # 控制池中连接的最大生存期，单位毫秒，默认30分钟

  # 继承了mybaits.xml，不需要再额外去配置
  mybaits:
    erp:
      mapper-locations: 'classpath:mapper/generated/*Mapper.xml,classpath:mapper/customer/*Mapper.xml,classpath:mapper/task/*Mapper.xml'
      base-package: com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper
xxl:
  job:
    accessToken: fintech_xxl_nhXhx00p3xHfEA2CtQITy2oD
    admin:
      addresses: http://************:32695/xxl-job-admin/
    executor:
      appname: erp-adaptor-task
      logpath: /tmp/home/<USER>/erp-adaptor/logs
      logretentiondays: 30
      port: 9999

dirpc:
  # ！！！等同于dirpc.properties的配置，参考 http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=*********
  # 连接模式，支持配置 Short-Connection(短连接)、Persistent-Connection(长连接)，默认是短连接
  connectionMode: Short-Connection
  # dirpc日志中请求耗时的单位，支持配置 millisecond(毫秒)、second(秒)，默认是毫秒
  latencyUnit: millisecond
  # 指定dirpcConsumer.xml的路径
  configPathFirst: false
  # 指定为当前目录，线上会去读取.deploy文件
  deployPath: ./
  httpServices:
    default: # 默认配置
      contentType: json #支持类型
      soTimeout: 3000
      connectionTimeout: 2000
      retryCount: 0 # 必须设置为0!!!

#  disf!fintech-sofi-digitalbank-digitalbank_user: # user disf配置
#    contentType: json
#    soTimeout: 200
#    connectionTimeout: 200
#    retryCount: 0 # 必须设置为0!!!
#  disf!fintech-sofi-digitalbank-digitalbank_id: # id disf配置
#    contentType: json
#    soTimeout: 1000
#    connectionTimeout: 1000
#    retryCount: 0 # 必须设置为0!!!
#  disf!fintech-sofi-digitalbank-digitalbank_gateway: # gateway disf配置
#    contentType: json
#    soTimeout: 1000
#    connectionTimeout: 1000
#    retryCount: 0 # 必须设置为0!!!

# log4j2相关配置
logging:
  config: classpath:/env/us01-sim/log4j2.xml

sofi-obs:
  metrics:
    moduleName: erp_adaptor_bootstrap
  db-enabled: false


fusion:
  url:
    coa-validation-url: https://ibxzjb-test.fa.ocs.oraclecloud.com:443/fscmService/AccountCombinationService
    common-url: https://ibxzjb-test.fa.ocs.oraclecloud.com:443
    status-path: https://ibxzjb-test.fa.ocs.oraclecloud.com/fscmRestApi/resources/***********/erpintegrations
    journal-import-path: https://ibxzjb-test.fa.ocs.oraclecloud.com/fscmRestApi/resources/***********/erpintegrations
  soap:
    url: https://ibxzjb-test.fa.ocs.oraclecloud.com:443/xmlpserver/services/v2/ReportService
    report-path: /Custom/Financials/Integration/XXDD_GL_JOURNALS_REL_DM.xdm
  jwt:
    username: didi_ip
    password: Oracle123456!
    issuer: didi
    exp: 60
    public-key: classpath:cert/pub.pem
    private-key: classpath:cert/jwt.der
  path:
    safi: /home/<USER>/erp-adaptor/file/
    shenma: /home/<USER>/erp-adaptor/file/

jwt:
  public-key: |
    -----BEGIN PUBLIC KEY-----
    MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhqwYLrQnDhFi4KbaiK6Pgg73rLCPwn+D
    xXjjxPdpC3PwzhLM82YXt52z5vc5eE42L9XQGGeuOO4IyrD02tWCSa99OMs/JsDJF8VMZGqNivDn
    in7cuqHNV/vj+McXxMX7DCH8Uu1zXg1XbROPYEFOcij3CrMFa2YbaMes6YQcS58L+x1XU/Amg+MO
    OB6qfO3PRy/yiQ1Jw8lR9FLaWp2IgXIuRDH1SU6wlrhPCDoQpd5Irab4AJEv5EJ82Zwyl5Ppxy5C
    +5XIe80l+VCCam8fzceT/FAYjmD/ykXMvJTl+WBYGukNRyyGA6ZrRyegrrfyGbrSTJbn/6QkeTTO
    2TkwpwIDAQAB
    -----END PUBLIC KEY-----

  private-key: |
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  issuer: erp-adaptor
  username: erp-adaptor
  password: xyzabc

# HTTP
http:
  timeToLive: 20
  maxTotal: 2000
  maxPerRoute: 800
  connectTimeout: 3000
  readTimeout: 10000
  connectionRequestTimeout: 200
  retryCount: 3
  requestSentRetryEnabled: false