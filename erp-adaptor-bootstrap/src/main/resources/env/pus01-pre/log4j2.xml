<?xml version="1.0" encoding="UTF-8"?>
<!-- 官方文档：http://logging.apache.org/log4j/2.x/index.html -->
<!-- 应用文档：https://www.jianshu.com/p/d13c2e50a89c  -->

<!-- status="WARN" :用于设置log4j2自身内部日志的信息输出级别，默认是OFF-->
<!-- packages:项目中自定义过滤器需要让log4j加载到，多个目录以,分割 -->
<!-- monitorInterval:间隔秒数,自动检测配置文件的变更和重新加载配置   -->
<Configuration status="WARN" packages="com.xiaoju.godson.log.log4j2" monitorInterval="60">

    <!--自定义一些常量，之后使用${变量名}引用-->
    <Properties>
        <Property name="log-path">/home/<USER>/erp-adaptor/logs/</Property>
    </Properties>

    <PrivacyFilter level="info"
                   ignoreNames="content,destinationNumber,DestinationPhoneNumber,recipient,ccList,ToAddresses,CcAddresses,messageReceiver,receiver,message_receiver,message_content,ccReceivers,documentId,documentType,documentCountryCode,clientLevel,phone,phoneCountryCode,firstName,middleName,lastName,birthday,gender,birthplace,postcode,country,state,stateCode,municipality,municipalityCode,originalMunicipalityCode,neighborhood,neighborhoodCode,originalNeighborhoodCode,street,externalNumber,internalNumber,documentCountry,phone,email,documentType,MessageBody,verificationCode,messageContent,templateParam"
                   onMatch="ACCEPT" onMisMatch="DENY" useNewVersion="true"/>

    <!-- appenders:定义输出内容,输出格式,输出方式,日志保存策略等,常用其下三种标签[console,File,RollingFile] -->
    <Appenders>
        <!--  name:Appender的名称，immediateFlush:接收到日志事件后是否立马刷盘(同步模式下才生效)，fileName:日志存储路径，filePattern:历史日志封存路径   -->
        <!--  RollingRandomAccessFileAppender内部使用ByteBuffer+RandomAccessFile原理，性能高，但始终处于缓存状态，无法关闭  -->
        <RollingRandomAccessFile name="business-log" fileName="${log-path}/business.log" immediateFlush="false"
                                 filePattern="${log-path}/business.log.%d{yyyyMMddHH}">
            <!-- 日志输出格式 -->
            <PatternLayout>
                <pattern>[%p][%d{yyyy-MM-dd'T'HH:mm:ss.SSS}][%F:%L] %dg%pm%n</pattern>
            </PatternLayout>

            <!-- 日志滚动策略 -->
            <Policies>
                <!--  时间滚动策略，interval=1表示每隔1小时产生新文件，modulate=true表示以0点作为偏移时间  -->
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="error-log" fileName="${log-path}/error.log" immediateFlush="false"
                                 filePattern="${log-path}/error.log.%d{yyyyMMddHH}">
            <PatternLayout>
                <pattern>[%p][%d{yyyy-MM-dd'T'HH:mm:ss.SSS}][%F:%L] %dg%pm%n</pattern>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
        </RollingRandomAccessFile>
    </Appenders>

    <Loggers>
        <!--  name可以是包路径，也可以是自定义名称。最后通过logger=LoggerFacotry.getLogger(xxx)来进行选择性过滤  -->
        <!--  includelocation=true,可以显示文件/方法名/行数等信息，不过对性能有影响  -->
        <!--  多余性，是否要将过滤出来的信息再输入到root中  -->
        <!--  level，All < Trace < Debug < Info < Warn < Error < Fatal < OFF，程序会打印高于或等于所设置级别的日志 -->

        <AsyncLogger name="com.xiaoju.onekey" level="warn" includeLocation="true" additivity="true">
            <AppenderRef ref="business-log"/>
        </AsyncLogger>

        <AsyncLogger name="com.xiaojukeji.carrera" level="warn" includeLocation="true" additivity="true">
            <AppenderRef ref="business-log"/>
        </AsyncLogger>


        <AsyncRoot level="info" includeLocation="true">
            <AppenderRef ref="business-log"/>
            <AppenderRef ref="error-log" level="error"/>
        </AsyncRoot>

    </Loggers>
</Configuration>
