<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <!-- 数据库驱动:选择你的本地硬盘上面的数据库驱动包-->
    <classPathEntry
            location="/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.23/mysql-connector-java-8.0.23.jar"/>
    <context id="cashier" targetRuntime="MyBatis3">

        <plugin type="org.mybatis.generator.plugins.RowBoundsPlugin"/>
        <plugin type="org.mybatis.generator.plugins.CaseInsensitiveLikePlugin"/>
        <plugin type="org.mybatis.generator.plugins.EqualsHashCodePlugin"/>
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>
        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"/>
        <plugin type="org.mybatis.generator.plugins.MapperAnnotationPlugin"/>

        <commentGenerator>
            <property name="suppressDate" value="true"/>
            <!-- 是否去除自动生成的注释 true：是 ： false:否 -->
            <property name="suppressAllComments" value="true"/>
            <property name="addRemarkComments" value="true"/>

        </commentGenerator>
        <!--数据库链接URL，用户名、密码 -->
        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="************************************************"
                        userId="didi_y2CO" password="43hTdtJJf">
            <property name="nullCatalogMeansCurrent" value="true"/>
        </jdbcConnection>
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>
        <!-- 生成模型的包名和位置-->
        <javaModelGenerator
                targetPackage="com.xiaoju.corebanking.erp.repository.mybatis.domain"
                targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>
        <!-- 生成映射文件的包名和位置-->
        <sqlMapGenerator targetPackage="main.resources.mapper.generated"
                         targetProject="src">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>
        <!-- 生成DAO的包名和位置-->
        <javaClientGenerator type="XMLMAPPER"
                             targetPackage="com.xiaoju.corebanking.erp.repository.mybatis.mapper.generated"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <!-- 这里就是 DB 的表，-->

        <!-- v2相关
         <table tableName="cux_two_tab_headers" domainObjectName="CuxTwoTabHeadersPO" enableCountByExample="false"
                enableUpdateByExample="true"
                enableDeleteByExample="true"
                enableSelectByExample="true"
                selectByExampleQueryId="true">
             <generatedKey column="id" sqlStatement="MySql" identity="true"/>
         </table>



         <table tableName="cux_two_tab_lines" domainObjectName="CuxTwoTabLinesPO" enableCountByExample="false"
                enableUpdateByExample="true"
                enableDeleteByExample="true"
                enableSelectByExample="true"
                selectByExampleQueryId="true">
             <generatedKey column="id" sqlStatement="MySql" identity="true"/>
         </table>

         <table tableName="cux_two_tab_values" domainObjectName="CuxTwoTabValuePO" enableCountByExample="false"
                enableUpdateByExample="true"
                enableDeleteByExample="true"
                enableSelectByExample="true"
                selectByExampleQueryId="true">
             <generatedKey column="id" sqlStatement="MySql" identity="true"/>
         </table>
-->
       <table tableName="sofi_gl_shenma" domainObjectName="SofiGlShenmaPO" enableCountByExample="false"
               enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <!-- v2相关

        <table tableName="refund_amount_control" domainObjectName="RefundAmountControlPO" enableCountByExample="false"
               enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>



        <table tableName="refund_amount_control" domainObjectName="RefundAmountControlPO" enableCountByExample="false"
               enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <table tableName="trade" domainObjectName="TradePO" enableCountByExample="false"
               enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
            <columnOverride column="trade_status" javaType="java.lang.Integer"/>
        </table>
        -->
        <!--
        <table tableName="trade_adjust" domainObjectName="TradeAdjustEntity" enableCountByExample="false"
               enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">
            <columnOverride column="status" javaType="java.lang.Integer"/>
        </table>
        <table tableName="trade_pay" domainObjectName="TradePayEntity" enableCountByExample="false"
               enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
            <columnOverride column="status" javaType="java.lang.Integer"/>
            <columnOverride column="sub_status" javaType="java.lang.Integer"/>
        </table>
        <table tableName="trade_pay_refund" domainObjectName="TradePayRefundEntity"
               enableCountByExample="false"
               enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
            <columnOverride column="status" javaType="java.lang.Integer"/>
            <columnOverride column="sub_status" javaType="java.lang.Integer"/>
            <columnOverride column="initiated_source" javaType="java.lang.Integer"/>
        </table>
        <table tableName="trade_transfer" domainObjectName="TradeTransferEntity" enableCountByExample="false"
               enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">
            <columnOverride column="status" javaType="java.lang.Integer"/>
            <columnOverride column="sub_status" javaType="java.lang.Integer"/>
        </table>
        <table tableName="trade_transfer_refund" domainObjectName="TradeTransferRefundEntity"
               enableCountByExample="false"
               enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">
            <columnOverride column="status" javaType="java.lang.Integer"/>
            <columnOverride column="initiated_source" javaType="java.lang.Integer"/>
            <columnOverride column="sub_status" javaType="java.lang.Integer"/>
        </table>-->
        <!--<table tableName="trade_index" domainObjectName="TradeIndexEntity"
               enableCountByExample="false"
               enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">
        </table>-->
        <!--<table tableName="user_payee" domainObjectName="UserPayeeEntity" enableCountByExample="false"
               enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">
            <columnOverride column="status" javaType="java.lang.Integer"/>
        </table>-->

        <!-- 限额限次相关表 -->
        <!--<table tableName="compliance_limit_rule" domainObjectName="ComplianceLimitRuleEntity" enableCountByExample="false"
               enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">
        </table>-->

        <!-- 业务核验相关表 -->
        <!--<table tableName="verify" domainObjectName="VerifyEntity" enableCountByExample="false"
               enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">
        </table>
        <table tableName="verify_list" domainObjectName="VerifyListEntity" enableCountByExample="false"
               enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">
        </table>
        <table tableName="verify_trigger" domainObjectName="VerifyTriggerEntity" enableCountByExample="false"
               enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">
        </table>-->

    </context>
</generatorConfiguration>