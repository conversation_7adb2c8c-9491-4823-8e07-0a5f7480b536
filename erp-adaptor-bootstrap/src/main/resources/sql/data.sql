-- auto-generated definition
create table sofi_gl_file_control
(
    id                    bigint unsigned auto_increment comment 'ID'
        primary key,
    system_code           varchar(10)  default ''                not null comment '系统代码',
    process_day           varchar(10)  default ''                not null comment '分区yyyymmdd',
    file_name             varchar(50)  default ''                not null comment '文件名',
    file_size             bigint(10)   default 0                 not null comment '文件大小',
    file_count            bigint(10)   default 0                 not null comment '文件记录数',
    process_status        varchar(50)  default ''                not null comment '文件处理状态',
    process_message       varchar(200) default ''                not null comment '错误信息',
    object_version_number int(10)      default 1                 not null comment '版本号',
    creation_date         datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by            varchar(50)  default ''                not null comment '创建人ldap',
    last_modify_date      datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    last_modified_by      varchar(50)  default ''                not null comment '更新人ldap',
    constraint uniq_file_name
        unique (system_code, file_name)
)
    comment 'GL文件控制表' collate = utf8mb4_general_ci;



-- auto-generated definition
create table sofi_gl_interface
(
    id                            bigint(10) unsigned auto_increment comment 'ID'
        primary key,
    file_id                       bigint(10) unsigned default 0                 not null comment '文件ID',
    process_day                   varchar(50)         default ''                not null comment '分区yyyymmdd',
    file_name                     varchar(50)         default ''                not null comment '文件名',
    batch_id                      varchar(10)         default ''                not null comment '批次ID,yyyymmdd+每次运行+1',
    detalle_poliza_id             bigint(10)          default 0                 not null comment '序号',
    source_sys                    varchar(50)         default ''                not null comment '来源系统',
    empresa_id                    bigint(10)          default 0                 not null comment '公司 ID',
    poliza_id                     bigint(10)          default 0                 not null comment '保单ID，既日记帐编号，业务唯一，一个保单ID可以有多行借贷',
    fecha                         varchar(10)         default '0'               not null comment '日期',
    centro_costo_id               bigint(10)          default 0                 not null comment '成本中心 ID',
    cuenta_completa               varchar(50)         default ''                not null comment '完整账户',
    instrumento                   bigint(10)          default 0                 not null comment '金融工具',
    moneda_id                     bigint(10)          default 0                 not null comment '货币 ID',
    cargos                        decimal(12, 2)      default 0.00              not null comment '借方金额',
    abonos                        decimal(12, 2)      default 0.00              not null comment '贷方金额',
    descripcion                   varchar(255)        default ''                not null comment '描述',
    referencia                    varchar(50)         default ''                not null comment '参考',
    procedimiento_cont            varchar(50)         default ''                not null comment '会计程序',
    tipo_instrumento_id           varchar(5)          default ''                not null comment '金融工具类型',
    rfc                           varchar(50)         default ''                not null comment '注册地址',
    total_factura                 decimal(12, 2)      default 0.00              not null comment '总发票金额',
    folio_uuid                    varchar(50)         default ''                not null comment '发票 UUID',
    usuario                       bigint(10)          default 0                 not null comment '用户',
    fecha_actual                  varchar(20)         default ''                not null comment '实际日期',
    direccion_ip                  varchar(50)         default ''                not null comment 'IP 地址',
    programa_id                   varchar(50)         default ''                not null comment '程序 ID',
    sucursal                      bigint(10)          default 0                 not null comment '分公司',
    num_transaccion               bigint(10)          default 0                 not null comment '交易编号',
    ledger_id                     bigint(10)          default 0                 not null comment '账套 ID',
    ledger_name                   varchar(30)         default ''                not null comment '账套名称',
    currency_code                 varchar(3)          default ''                not null comment '币种',
    journal_category              varchar(50)         default ''                not null comment '日记账类别',
    journal_source                varchar(50)         default ''                not null comment '日记账来源',
    journal_source_name           varchar(50)         default ''                not null comment 'fusion日记帐je_source_name',
    segment1                      varchar(150)        default ''                not null comment 'segment1',
    segment2                      varchar(150)        default ''                not null comment 'segment2',
    segment3                      varchar(150)        default ''                not null comment 'segment3',
    segment4                      varchar(150)        default ''                not null comment 'segment4',
    segment5                      varchar(150)        default ''                not null comment 'segment5',
    segment6                      varchar(150)        default ''                not null comment 'segment6',
    segment7                      varchar(150)        default ''                not null comment 'segment7',
    segment8                      varchar(150)        default ''                not null comment 'segment8',
    segment9                      varchar(150)        default ''                not null comment 'segment9',
    segment10                     varchar(150)        default ''                not null comment 'segment10',
    group_id                      bigint(10)          default 0                 not null comment 'group_id，fusion批次',
    currency_conversion_date      datetime            default CURRENT_TIMESTAMP not null comment '汇率日期',
    currency_conversion_rate      decimal(10, 4)      default 1.0000            not null comment '汇率',
    user_currency_conversion_type varchar(30)         default ''                not null comment '汇率类型',
    process_status                varchar(50)         default 'N'               not null comment '状态',
    process_message               varchar(500)        default ''                not null comment '处理消息',
    je_header_id                  bigint(10)          default 0                 not null comment '日记帐ID',
    journal_name                  varchar(100)        default ''                not null comment '日记帐名称',
    je_line_num                   bigint(10)          default 0                 not null comment '日记帐行号',
    document_id                   bigint(10)          default 0                 not null comment 'UCM documentID',
    load_request_id               bigint(10)          default 0                 not null comment 'load Request ID',
    import_request_id             bigint(10)          default 0                 not null comment '导日记帐 Request ID',
    object_version_number         bigint(10)          default 1                 not null comment '版本号',
    creation_date                 datetime            default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by                    varchar(50)         default ''                not null comment '创建人ldap',
    last_modify_date              datetime            default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    last_modified_by              varchar(50)         default ''                not null comment '更新人ldap',
    constraint uniq_detalle_poliza_id
        unique (poliza_id, detalle_poliza_id)
)
    comment 'GL接口表' collate = utf8mb4_general_ci;

create index idx_gl_interface_coa
    on sofi_gl_interface (process_day, process_status, ledger_name, segment1, segment2, segment3, segment4);

create index idx_gl_interface_n1
    on sofi_gl_interface (process_day, file_name, poliza_id, batch_id);

create index idx_gl_interface_n2
    on sofi_gl_interface (process_day, process_status);




-- auto-generated definition
create table sofi_gl_interface_common
(
    id                       bigint(10) unsigned auto_increment comment 'ID'
        primary key,
    system_code              varchar(30)    default ''                not null comment '系统代码',
    process_day              varchar(10)    default ''                not null comment '分区yyyymmdd',
    accounting_date          date                                     not null comment 'gl日期',
    period_name              varchar(10)    default ''                not null comment '期间',
    ledger_id                bigint(10)     default 0                 not null comment '账套 ID',
    ledger_name              varchar(30)    default ''                not null comment '账套名称',
    currency_code            varchar(3)     default ''                not null comment '币种',
    journal_category         varchar(50)    default ''                not null comment '日记账类别',
    journal_source           varchar(50)    default ''                not null comment '日记账来源',
    journal_source_name      varchar(50)    default ''                not null comment 'fusion日记帐je_source_name',
    reference1               varchar(100)   default ''                not null comment '批名',
    reference2               varchar(240)   default ''                not null comment '批说明',
    reference3               varchar(100)   default ''                not null comment '行参考',
    reference4               varchar(100)   default ''                not null comment '日记帐名称',
    reference5               varchar(240)   default ''                not null comment '日记帐说明',
    reference6               varchar(100)   default ''                not null comment '日记帐参考，external_reference',
    reference7               varchar(100)   default ''                not null comment 'Journal Entry Reversal flag',
    reference8               varchar(100)   default ''                not null comment 'Journal Entry Reversal Period',
    reference9               varchar(100)   default ''                not null comment 'Journal Reversal Method',
    reference10              varchar(240)   default ''                not null comment '行说明',
    reference21              varchar(100)   default ''                not null comment 'reference21',
    reference22              varchar(240)   default ''                not null comment 'reference22',
    reference23              varchar(100)   default ''                not null comment 'reference23',
    reference24              varchar(100)   default ''                not null comment 'reference24',
    reference25              varchar(240)   default ''                not null comment 'reference25',
    reference26              varchar(100)   default ''                not null comment 'reference26',
    reference27              varchar(100)   default ''                not null comment 'reference27',
    reference28              varchar(100)   default ''                not null comment 'reference28',
    reference29              varchar(100)   default ''                not null comment 'reference29',
    reference30              varchar(240)   default ''                not null comment 'reference30',
    segment1                 varchar(50)    default ''                not null comment 'segment1',
    segment2                 varchar(50)    default ''                not null comment 'segment2',
    segment3                 varchar(50)    default ''                not null comment 'segment3',
    segment4                 varchar(50)    default ''                not null comment 'segment4',
    segment5                 varchar(50)    default ''                not null comment 'segment5',
    segment6                 varchar(50)    default ''                not null comment 'segment6',
    segment7                 varchar(50)    default ''                not null comment 'segment7',
    segment8                 varchar(50)    default ''                not null comment 'segment8',
    segment9                 varchar(50)    default ''                not null comment 'segment9',
    segment10                varchar(50)    default ''                not null comment 'segment10',
    entered_dr               decimal(20, 4) default 0.0000            not null comment '原币借方金额',
    entered_cr               decimal(20, 4) default 0.0000            not null comment '原币贷方金额',
    accounted_dr             decimal(20, 4) default 0.0000            not null comment '本位币借方金额',
    accounted_cr             decimal(20, 4) default 0.0000            not null comment '本位币贷方金额',
    group_id                 bigint(15)     default 0                 not null comment 'group_id，fusion批次',
    currency_conversion_date datetime       default CURRENT_TIMESTAMP not null comment '汇率日期',
    currency_conversion_rate decimal(19, 6) default 0.000000          not null comment '汇率',
    currency_conversion_type varchar(30)    default ''                not null comment '汇率类型',
    attribute_category       varchar(100)   default ''                not null comment 'header context',
    header_attribute1        varchar(150)   default ''                not null comment '头弹性域1',
    header_attribute2        varchar(150)   default ''                not null comment '头弹性域2',
    header_attribute3        varchar(150)   default ''                not null comment '头弹性域3',
    header_attribute4        varchar(150)   default ''                not null comment '头弹性域4',
    header_attribute5        varchar(150)   default ''                not null comment '头弹性域5',
    header_attribute6        varchar(150)   default ''                not null comment '头弹性域6',
    header_attribute7        varchar(150)   default ''                not null comment '头弹性域7',
    header_attribute8        varchar(150)   default ''                not null comment '头弹性域8',
    header_attribute9        varchar(150)   default ''                not null comment '头弹性域9',
    header_attribute10       varchar(150)   default ''                not null comment '头弹性域10',
    header_attribute11       varchar(150)   default ''                not null comment '头弹性域11',
    header_attribute12       varchar(150)   default ''                not null comment '头弹性域12',
    header_attribute13       varchar(150)   default ''                not null comment '头弹性域13',
    header_attribute14       varchar(150)   default ''                not null comment '头弹性域14',
    header_attribute15       varchar(150)   default ''                not null comment '头弹性域15',
    attribute_category3      varchar(100)   default ''                not null comment 'line context',
    line_attribute1          varchar(150)   default ''                not null comment '行弹性域1',
    line_attribute2          varchar(150)   default ''                not null comment '行弹性域2',
    line_attribute3          varchar(150)   default ''                not null comment '行弹性域3',
    line_attribute4          varchar(150)   default ''                not null comment '行弹性域4',
    line_attribute5          varchar(150)   default ''                not null comment '行弹性域5',
    line_attribute6          varchar(150)   default ''                not null comment '行弹性域6',
    line_attribute7          varchar(150)   default ''                not null comment '行弹性域7',
    line_attribute8          varchar(150)   default ''                not null comment '行弹性域8',
    line_attribute9          varchar(150)   default ''                not null comment '行弹性域9',
    line_attribute10         varchar(150)   default ''                not null comment '行弹性域10',
    line_attribute11         varchar(150)   default ''                not null comment '行弹性域11',
    line_attribute12         varchar(150)   default ''                not null comment '行弹性域12',
    line_attribute13         varchar(150)   default ''                not null comment '行弹性域13',
    line_attribute14         varchar(150)   default ''                not null comment '行弹性域14',
    line_attribute15         varchar(150)   default ''                not null comment '行弹性域15',
    line_attribute16         varchar(150)   default ''                not null comment '行弹性域16',
    line_attribute17         varchar(150)   default ''                not null comment '行弹性域17',
    line_attribute18         varchar(150)   default ''                not null comment '行弹性域18',
    line_attribute19         varchar(150)   default ''                not null comment '行弹性域19',
    line_attribute20         varchar(150)   default ''                not null comment '行弹性域20',
    process_status           varchar(15)    default 'NEW'             not null comment '状态',
    process_message          varchar(500)   default ''                not null comment '处理消息',
    je_header_id             bigint(15)     default 0                 not null comment '日记帐ID',
    journal_name             varchar(100)   default ''                not null comment '日记帐名称',
    je_line_num              bigint(10)     default 0                 not null comment '日记帐行号',
    document_id              bigint(15)     default 0                 not null comment 'UCM documentID',
    load_request_id          bigint(15)     default 0                 not null comment 'load Request ID',
    import_request_id        bigint(15)     default 0                 not null comment '导日记帐 Request ID',
    object_version_number    bigint(10)     default 1                 not null comment '版本号',
    creation_date            datetime       default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by               varchar(50)    default ''                not null comment '创建人ldap',
    last_modify_date         datetime       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    last_modified_by         varchar(50)    default ''                not null comment '更新人ldap'
)
    comment 'GL对接fusion接口表' collate = utf8mb4_general_ci;

create index idx_external_reference
    on sofi_gl_interface_common (reference6);

create index idx_je_header_id
    on sofi_gl_interface_common (je_header_id);

create index idx_period_name
    on sofi_gl_interface_common (period_name);

create index idx_sgp
    on sofi_gl_interface_common (system_code, process_day, group_id, process_status);



-- auto-generated definition
create table sofi_gl_interface_common_his
(
    id                       bigint(10) unsigned auto_increment comment 'ID'
        primary key,
    system_code              varchar(30)    default ''                not null comment '系统代码',
    process_day              varchar(10)    default ''                not null comment '分区yyyymmdd',
    accounting_date          date                                     not null comment 'gl日期',
    period_name              varchar(10)    default ''                not null comment '期间',
    ledger_id                bigint(10)     default 0                 not null comment '账套 ID',
    ledger_name              varchar(30)    default ''                not null comment '账套名称',
    currency_code            varchar(3)     default ''                not null comment '币种',
    journal_category         varchar(50)    default ''                not null comment '日记账类别',
    journal_source           varchar(50)    default ''                not null comment '日记账来源',
    journal_source_name      varchar(50)    default ''                not null comment 'fusion日记帐je_source_name',
    reference1               varchar(100)   default ''                not null comment '批名',
    reference2               varchar(240)   default ''                not null comment '批说明',
    reference3               varchar(100)   default ''                not null comment '行参考',
    reference4               varchar(100)   default ''                not null comment '日记帐名称',
    reference5               varchar(240)   default ''                not null comment '日记帐说明',
    reference6               varchar(100)   default ''                not null comment '日记帐参考，external_reference',
    reference7               varchar(100)   default ''                not null comment 'Journal Entry Reversal flag',
    reference8               varchar(100)   default ''                not null comment 'Journal Entry Reversal Period',
    reference9               varchar(100)   default ''                not null comment 'Journal Reversal Method',
    reference10              varchar(240)   default ''                not null comment '行说明',
    reference21              varchar(100)   default ''                not null comment 'reference21',
    reference22              varchar(240)   default ''                not null comment 'reference22',
    reference23              varchar(100)   default ''                not null comment 'reference23',
    reference24              varchar(100)   default ''                not null comment 'reference24',
    reference25              varchar(240)   default ''                not null comment 'reference25',
    reference26              varchar(100)   default ''                not null comment 'reference26',
    reference27              varchar(100)   default ''                not null comment 'reference27',
    reference28              varchar(100)   default ''                not null comment 'reference28',
    reference29              varchar(100)   default ''                not null comment 'reference29',
    reference30              varchar(240)   default ''                not null comment 'reference30',
    segment1                 varchar(50)    default ''                not null comment 'segment1',
    segment2                 varchar(50)    default ''                not null comment 'segment2',
    segment3                 varchar(50)    default ''                not null comment 'segment3',
    segment4                 varchar(50)    default ''                not null comment 'segment4',
    segment5                 varchar(50)    default ''                not null comment 'segment5',
    segment6                 varchar(50)    default ''                not null comment 'segment6',
    segment7                 varchar(50)    default ''                not null comment 'segment7',
    segment8                 varchar(50)    default ''                not null comment 'segment8',
    segment9                 varchar(50)    default ''                not null comment 'segment9',
    segment10                varchar(50)    default ''                not null comment 'segment10',
    entered_dr               decimal(20, 4) default 0.0000            not null comment '原币借方金额',
    entered_cr               decimal(20, 4) default 0.0000            not null comment '原币贷方金额',
    accounted_dr             decimal(20, 4) default 0.0000            not null comment '本位币借方金额',
    accounted_cr             decimal(20, 4) default 0.0000            not null comment '本位币贷方金额',
    group_id                 bigint(15)     default 0                 not null comment 'group_id，fusion批次',
    currency_conversion_date datetime       default CURRENT_TIMESTAMP not null comment '汇率日期',
    currency_conversion_rate decimal(19, 6) default 0.000000          not null comment '汇率',
    currency_conversion_type varchar(30)    default ''                not null comment '汇率类型',
    attribute_category       varchar(100)   default ''                not null comment 'header context',
    header_attribute1        varchar(150)   default ''                not null comment '头弹性域1',
    header_attribute2        varchar(150)   default ''                not null comment '头弹性域2',
    header_attribute3        varchar(150)   default ''                not null comment '头弹性域3',
    header_attribute4        varchar(150)   default ''                not null comment '头弹性域4',
    header_attribute5        varchar(150)   default ''                not null comment '头弹性域5',
    header_attribute6        varchar(150)   default ''                not null comment '头弹性域6',
    header_attribute7        varchar(150)   default ''                not null comment '头弹性域7',
    header_attribute8        varchar(150)   default ''                not null comment '头弹性域8',
    header_attribute9        varchar(150)   default ''                not null comment '头弹性域9',
    header_attribute10       varchar(150)   default ''                not null comment '头弹性域10',
    header_attribute11       varchar(150)   default ''                not null comment '头弹性域11',
    header_attribute12       varchar(150)   default ''                not null comment '头弹性域12',
    header_attribute13       varchar(150)   default ''                not null comment '头弹性域13',
    header_attribute14       varchar(150)   default ''                not null comment '头弹性域14',
    header_attribute15       varchar(150)   default ''                not null comment '头弹性域15',
    attribute_category3      varchar(100)   default ''                not null comment 'line context',
    line_attribute1          varchar(150)   default ''                not null comment '行弹性域1',
    line_attribute2          varchar(150)   default ''                not null comment '行弹性域2',
    line_attribute3          varchar(150)   default ''                not null comment '行弹性域3',
    line_attribute4          varchar(150)   default ''                not null comment '行弹性域4',
    line_attribute5          varchar(150)   default ''                not null comment '行弹性域5',
    line_attribute6          varchar(150)   default ''                not null comment '行弹性域6',
    line_attribute7          varchar(150)   default ''                not null comment '行弹性域7',
    line_attribute8          varchar(150)   default ''                not null comment '行弹性域8',
    line_attribute9          varchar(150)   default ''                not null comment '行弹性域9',
    line_attribute10         varchar(150)   default ''                not null comment '行弹性域10',
    line_attribute11         varchar(150)   default ''                not null comment '行弹性域11',
    line_attribute12         varchar(150)   default ''                not null comment '行弹性域12',
    line_attribute13         varchar(150)   default ''                not null comment '行弹性域13',
    line_attribute14         varchar(150)   default ''                not null comment '行弹性域14',
    line_attribute15         varchar(150)   default ''                not null comment '行弹性域15',
    line_attribute16         varchar(150)   default ''                not null comment '行弹性域16',
    line_attribute17         varchar(150)   default ''                not null comment '行弹性域17',
    line_attribute18         varchar(150)   default ''                not null comment '行弹性域18',
    line_attribute19         varchar(150)   default ''                not null comment '行弹性域19',
    line_attribute20         varchar(150)   default ''                not null comment '行弹性域20',
    process_status           varchar(15)    default 'NEW'             not null comment '状态',
    process_message          varchar(500)   default ''                not null comment '处理消息',
    je_header_id             bigint(15)     default 0                 not null comment '日记帐ID',
    journal_name             varchar(100)   default ''                not null comment '日记帐名称',
    je_line_num              bigint(10)     default 0                 not null comment '日记帐行号',
    document_id              bigint(15)     default 0                 not null comment 'UCM documentID',
    load_request_id          bigint(15)     default 0                 not null comment 'load Request ID',
    import_request_id        bigint(15)     default 0                 not null comment '导日记帐 Request ID',
    object_version_number    bigint(10)     default 1                 not null comment '版本号',
    creation_date            datetime       default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by               varchar(50)    default ''                not null comment '创建人ldap',
    last_modify_date         datetime       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    last_modified_by         varchar(50)    default ''                not null comment '更新人ldap'
)
    comment 'GL对接fusion接口备份表' collate = utf8mb4_general_ci;



-- auto-generated definition
create table sofi_gl_interface_header
(
    id                    bigint(10) unsigned auto_increment comment 'ID'
        primary key,
    system_code           varchar(10)  default ''                not null comment '系统代码',
    process_day           varchar(50)  default ''                not null comment '分区yyyymmdd',
    batch_id              varchar(10)  default ''                not null comment '批次ID,yyyymmdd+每次运行+1',
    external_reference    varchar(50)  default ''                not null comment '外部系统唯一单号',
    journal_count         bigint(10)   default 0                 not null comment '保单行数',
    process_status        varchar(100) default ''                not null comment '处理状态',
    process_message       varchar(200) default ''                not null comment '错误信息',
    object_version_number int(10)      default 1                 not null comment '版本号',
    creation_date         datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by            varchar(50)  default ''                not null comment '创建人ldap',
    last_modify_date      datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    last_modified_by      varchar(50)  default ''                not null comment '更新人ldap',
    group_id              bigint(10)   default 0                 not null comment 'group_id，fusion批次',
    constraint uniq_external_reference
        unique (system_code, process_day, group_id, external_reference)
)
    comment 'GL接口头表' collate = utf8mb4_general_ci;

create index idx_header_spgp
    on sofi_gl_interface_header (system_code, process_day, group_id, process_status);



-- auto-generated definition
create table sofi_gl_interface_his
(
    id                            bigint(10) unsigned auto_increment comment 'ID'
        primary key,
    file_id                       bigint(10) unsigned default 0                 not null comment '文件ID',
    process_day                   varchar(50)         default ''                not null comment '分区yyyymmdd',
    file_name                     varchar(50)         default ''                not null comment '文件名',
    batch_id                      varchar(10)         default ''                not null comment '批次ID,yyyymmdd+每次运行+1',
    detalle_poliza_id             bigint(10)          default 0                 not null comment '序号',
    source_sys                    varchar(50)         default ''                not null comment '来源系统',
    empresa_id                    bigint(10)          default 0                 not null comment '公司 ID',
    poliza_id                     bigint(10)          default 0                 not null comment '保单ID，既日记帐编号，业务唯一，一个保单ID可以有多行借贷',
    fecha                         varchar(10)         default '0'               not null comment '日期',
    centro_costo_id               bigint(10)          default 0                 not null comment '成本中心 ID',
    cuenta_completa               varchar(50)         default ''                not null comment '完整账户',
    instrumento                   bigint(10)          default 0                 not null comment '金融工具',
    moneda_id                     bigint(10)          default 0                 not null comment '货币 ID',
    cargos                        decimal(10, 2)      default 0.00              not null comment '借方金额',
    abonos                        decimal(10, 2)      default 0.00              not null comment '贷方金额',
    descripcion                   varchar(255)        default ''                not null comment '描述',
    referencia                    varchar(50)         default ''                not null comment '参考',
    procedimiento_cont            varchar(50)         default ''                not null comment '会计程序',
    tipo_instrumento_id           varchar(5)          default ''                not null comment '金融工具类型',
    rfc                           varchar(50)         default ''                not null comment '注册地址',
    total_factura                 decimal(10, 2)      default 0.00              not null comment '总发票金额',
    folio_uuid                    varchar(50)         default ''                not null comment '发票 UUID',
    usuario                       bigint(10)          default 0                 not null comment '用户',
    fecha_actual                  varchar(20)         default ''                not null comment '实际日期',
    direccion_ip                  varchar(50)         default ''                not null comment 'IP 地址',
    programa_id                   varchar(50)         default ''                not null comment '程序 ID',
    sucursal                      bigint(10)          default 0                 not null comment '分公司',
    num_transaccion               bigint(10)          default 0                 not null comment '交易编号',
    ledger_id                     bigint(10)          default 0                 not null comment '账套 ID',
    currency_code                 varchar(3)          default ''                not null comment '币种',
    journal_category              varchar(50)         default ''                not null comment '日记账类别',
    journal_source                varchar(50)         default ''                not null comment '日记账来源',
    segment1                      varchar(150)        default ''                not null comment 'segment1',
    segment2                      varchar(150)        default ''                not null comment 'segment2',
    segment3                      varchar(150)        default ''                not null comment 'segment3',
    segment4                      varchar(150)        default ''                not null comment 'segment4',
    segment5                      varchar(150)        default ''                not null comment 'segment5',
    segment6                      varchar(150)        default ''                not null comment 'segment6',
    segment7                      varchar(150)        default ''                not null comment 'segment7',
    segment8                      varchar(150)        default ''                not null comment 'segment8',
    segment9                      varchar(150)        default ''                not null comment 'segment9',
    segment10                     varchar(150)        default ''                not null comment 'segment10',
    group_id                      bigint(10)          default 0                 not null comment 'group_id，fusion批次',
    currency_conversion_date      datetime            default CURRENT_TIMESTAMP not null comment '汇率日期',
    currency_conversion_rate      decimal(10, 4)      default 1.0000            not null comment '汇率',
    user_currency_conversion_type varchar(30)         default ''                not null comment '汇率类型',
    process_status                varchar(100)                                  null,
    process_message               varchar(500)        default ''                not null comment '处理消息',
    je_header_id                  bigint(10)          default 0                 not null comment '日记帐ID',
    journal_name                  varchar(100)        default ''                not null comment '日记帐名称',
    je_line_num                   bigint(10)          default 0                 not null comment '日记帐行号',
    document_id                   bigint(10)          default 0                 not null comment 'UCM documentID',
    load_request_id               bigint(10)          default 0                 not null comment 'load Request ID',
    import_request_id             bigint(10)          default 0                 not null comment '导日记帐 Request ID',
    object_version_number         bigint(10)          default 1                 not null comment '版本号',
    creation_date                 datetime            default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by                    varchar(50)         default ''                not null comment '创建人ldap',
    last_modify_date              datetime            default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    last_modified_by              varchar(50)         default ''                not null comment '更新人ldap'
)
    comment 'GL接口表' collate = utf8mb4_general_ci;

create index idx_gl_interface_n1
    on sofi_gl_interface_his (process_day, file_name, poliza_id, batch_id);




-- auto-generated definition
create table sofi_gl_shenma
(
    id                    bigint(10) unsigned auto_increment comment 'ID'
        primary key,
    process_day           varchar(10)    default ''                not null comment '分区yyyymmdd',
    file_name             varchar(50)    default ''                not null comment '文件名',
    link_reference        varchar(50)    default ''                not null comment '汇总md5',
    source_branch         varchar(100)   default ''                not null comment '本方机构号',
    ccy                   varchar(10)    default ''                not null comment '币种',
    gl_code               varchar(50)    default ''                not null comment '科目代码',
    entered_debit_amount  decimal(38, 2) default 0.00              not null comment '原币借方',
    entered_credit_amount decimal(38, 2) default 0.00              not null comment '原币贷方',
    profit_center         varchar(50)    default ''                not null comment '利润中心',
    source_module         varchar(50)    default ''                not null comment '源模块',
    client_type           varchar(50)    default ''                not null comment '集团客户分类',
    amt_type              varchar(50)    default ''                not null comment '金额类型',
    tran_type             varchar(50)    default ''                not null comment '交易类型',
    event_type            varchar(50)    default ''                not null comment '事件类型',
    prod_type             varchar(50)    default ''                not null comment '产品类型',
    post_date             varchar(50)    default ''                not null comment '入账日期',
    value_date            varchar(50)    default ''                not null comment '记账日期',
    narrative             varchar(240)   default ''                not null comment '摘要',
    channel_seq_no        varchar(50)    default ''                not null comment '渠道流水号',
    intercompany          varchar(50)    default ''                not null comment '公司间段',
    flat_rate             decimal(10, 6) default 0.000000          not null comment '平盘汇率',
    cust_rate             decimal(10, 6) default 0.000000          not null comment '对客汇率',
    inland_offshore       varchar(50)    default ''                not null comment '客户境内境外标志',
    client_no             varchar(50)    default ''                not null comment '客户号',
    seq_no                varchar(50)    default ''                not null comment '序号',
    system_id             varchar(20)    default ''                not null comment '系统ID',
    company               varchar(50)    default ''                not null comment '法人',
    group_client          varchar(20)    default ''                not null comment '集团往来',
    voucher_group         varchar(100)   default ''                not null comment '凭证分组',
    group_id              bigint(10)     default 0                 not null comment 'group_id，fusion批次',
    process_status        varchar(10)    default 'NEW'             not null comment '状态',
    process_message       varchar(500)   default ''                not null comment '处理消息',
    object_version_number bigint(10)     default 1                 not null comment '版本号',
    creation_date         datetime       default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by            varchar(50)    default ''                not null comment '创建人ldap',
    last_modify_date      datetime       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    last_modified_by      varchar(50)    default ''                not null comment '更新人ldap',
    cr_dr_ind             varchar(200)   default ''                not null
)
    comment '神码GL接口前置表' collate = utf8mb4_general_ci;

create index idx_link_reference
    on sofi_gl_shenma (process_day, voucher_group, link_reference);



-- auto-generated definition
create table sofi_gl_shenma_his
(
    id                    bigint(10) unsigned auto_increment comment 'ID'
        primary key,
    process_day           varchar(10)    default ''                not null comment '分区yyyymmdd',
    file_name             varchar(50)    default ''                not null comment '文件名',
    link_reference        varchar(50)    default ''                not null comment '汇总md5',
    source_branch         varchar(100)   default ''                not null comment '本方机构号',
    ccy                   varchar(10)    default ''                not null comment '币种',
    gl_code               varchar(50)    default ''                not null comment '科目代码',
    entered_debit_amount  decimal(38, 2) default 0.00              not null comment '原币借方',
    entered_credit_amount decimal(38, 2) default 0.00              not null comment '原币贷方',
    profit_center         varchar(50)    default ''                not null comment '利润中心',
    source_module         varchar(50)    default ''                not null comment '源模块',
    client_type           varchar(50)    default ''                not null comment '集团客户分类',
    amt_type              varchar(50)    default ''                not null comment '金额类型',
    tran_type             varchar(50)    default ''                not null comment '交易类型',
    event_type            varchar(50)    default ''                not null comment '事件类型',
    prod_type             varchar(50)    default ''                not null comment '产品类型',
    post_date             varchar(50)    default ''                not null comment '入账日期',
    value_date            varchar(50)    default ''                not null comment '记账日期',
    narrative             varchar(240)   default ''                not null comment '摘要',
    channel_seq_no        varchar(50)    default ''                not null comment '渠道流水号',
    intercompany          varchar(50)    default ''                not null comment '公司间段',
    flat_rate             decimal(10, 6) default 0.000000          not null comment '平盘汇率',
    cust_rate             decimal(10, 6) default 0.000000          not null comment '对客汇率',
    inland_offshore       varchar(50)    default ''                not null comment '客户境内境外标志',
    client_no             varchar(50)    default ''                not null comment '客户号',
    seq_no                varchar(50)    default ''                not null comment '序号',
    system_id             varchar(20)    default ''                not null comment '系统ID',
    company               varchar(50)    default ''                not null comment '法人',
    group_client          varchar(20)    default ''                not null comment '集团往来',
    voucher_group         varchar(100)   default ''                not null comment '凭证分组',
    group_id              bigint(10)     default 0                 not null comment 'group_id，fusion批次',
    process_status        varchar(10)    default 'NEW'             not null comment '状态',
    process_message       varchar(500)   default ''                not null comment '处理消息',
    object_version_number bigint(10)     default 1                 not null comment '版本号',
    creation_date         datetime       default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by            varchar(50)    default ''                not null comment '创建人ldap',
    last_modify_date      datetime       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    last_modified_by      varchar(50)    default ''                not null comment '更新人ldap'
)
    comment '神码GL接口前置表' collate = utf8mb4_general_ci;

create index idx_process_day
    on sofi_gl_shenma_his (process_day);



-- auto-generated definition
create table sofi_gl_subject
(
    subject_code        varchar(20)   not null comment '科目代码 |科目代码',
    subject_desc        varchar(100)  null comment '科目描述 |科目描述',
    subject_desc_en     varchar(200)  null comment '科目英文描述 |科目英文描述',
    control_subject     varchar(20)   null comment '上级科目 |上级科目',
    bspl_type           char          not null comment '会计科目大分类 |会计科目大分类|a-资产, l-负债, i-收入, e-支出, c-表外备抵, m-备忘备抵',
    gl_type             char          not null comment '总账类型|总账类型i-内部n-往账v-来账r-零售账户|i-内部,n-往账,v-来账,r-零售账户 ',
    subject_type        char          not null comment '科目类型 |科目类型|c-控制类科目, s-记账类科目',
    balance_way         char          not null comment '余额方向|余额方向  a-实际b-双向不轧差c-贷方d-借方|a-实际,b-双向不轧差,c-贷方,d-借方',
    subject_status      char          not null comment '科目状态 |科目状态|a-有效, d-删除',
    subject_level       char          null comment '科目级别 |科目级别',
    manual_account      char          null comment '是否允许手工记账 |是否允许手工记账y-是n-否|y-是,n-否',
    special_bookkeeping char          null comment '特殊记账标识',
    od_facility         char          null comment '是否可透支|是否可透支y-是n-否|y-是,n-否',
    range_no            varchar(3)    null comment '科目范围 |科目范围',
    subject_set_no      varchar(20)   not null comment '科目套号|科目套号',
    revalue_rate_type   varchar(10)   null comment '月结重估汇率类型',
    system_id           varchar(20)   null comment '系统id|业务范围编码',
    measurement_attr    varchar(50)   null comment '计量属性',
    item_segregation    varchar(2)    null comment '项目划分|项目划分ni-非货币性项目mi-货币性项目|ni-非货币性项目,mi-货币性项目',
    company             varchar(20)   not null comment '法人|法人',
    tran_timestamp      varchar(26)   null comment '交易时间戳|交易时间戳',
    pay_rec             char          null comment '收付标志|收付标志|p-付 ,r-收',
    subject_remark      varchar(1500) null comment '科目说明|科目说明',
    primary key (subject_code, subject_set_no)
)
    comment '会计科目表|会计科目表';

