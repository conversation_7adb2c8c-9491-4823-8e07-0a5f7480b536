package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.converter.SofiGlInterfaceModelConverter;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiEmailDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderQueryDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfacePO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfacePOExample;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SofiGlInterfaceCustomerMapper;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfaceHisPOMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
* <p> SofiGlInterfaceRepositoryImpl Tester. </p>
* <p> 2025-06-06 11:40:17.956 </p>
*
* <AUTHOR>
* @version 0.0.1-SNAPSHOT
*/
@ExtendWith(MockitoExtension.class)
public class SofiGlInterfaceRepositoryImplTest {
    @InjectMocks
    private SofiGlInterfaceRepositoryImpl sofiGlInterfaceRepository;
    @Mock
    private SofiGlInterfaceModelConverter sofiGlInterfaceModelConverter;

    @Mock
    private SofiGlInterfaceCustomerMapper sofiGlInterfaceCustomerMapper;

    @Mock
    private SofiGlInterfaceHisPOMapper sofiGlInterfaceHisPOMapper;
    /**
    *
    * Method: insertSelective(sofiGlInterfaceDO)
    */
    @Test
    public void insertSelectiveTest() throws Exception {
        SofiGlInterfaceDO sofiGlInterfaceDO = new SofiGlInterfaceDO();
        sofiGlInterfaceDO.setId(1L);
        sofiGlInterfaceDO.setFileId(1L);
        sofiGlInterfaceDO.setProcessDay("2025");
        sofiGlInterfaceDO.setFileName("filename");
        sofiGlInterfaceDO.setBatchId("1");
        sofiGlInterfaceDO.setDetallePolizaId(1L);
        sofiGlInterfaceDO.setSourceSys("safi");
        sofiGlInterfaceDO.setEmpresaId(1L);
        sofiGlInterfaceDO.setPolizaId(1L);
        sofiGlInterfaceDO.setFecha("2025-06");
        sofiGlInterfaceDO.setCentroCostoId(1L);
        sofiGlInterfaceDO.setCuentaCompleta("1");
        sofiGlInterfaceDO.setInstrumento(1L);
        sofiGlInterfaceDO.setMonedaId(1L);
        sofiGlInterfaceDO.setCargos(new BigDecimal(1));
        sofiGlInterfaceDO.setAbonos(new BigDecimal(1));
        sofiGlInterfaceDO.setDescripcion("1");
        sofiGlInterfaceDO.setReferencia("1");
        sofiGlInterfaceDO.setProcedimientoCont("1");
        sofiGlInterfaceDO.setTipoInstrumentoId("1");
        sofiGlInterfaceDO.setRfc("1");
        sofiGlInterfaceDO.setTotalFactura(new BigDecimal(1));
        sofiGlInterfaceDO.setFolioUuid("1");
        sofiGlInterfaceDO.setUsuario(1L);
        sofiGlInterfaceDO.setFechaActual("1");
        sofiGlInterfaceDO.setDireccionIp("1");
        sofiGlInterfaceDO.setProgramaId("1");
        sofiGlInterfaceDO.setSucursal(1L);
        sofiGlInterfaceDO.setNumTransaccion(1L);
        sofiGlInterfaceDO.setLedgerId(1L);
        sofiGlInterfaceDO.setLedgerName("1");
        sofiGlInterfaceDO.setCurrencyCode("1");
        sofiGlInterfaceDO.setJournalCategory("1");
        sofiGlInterfaceDO.setJournalSource("1");
        sofiGlInterfaceDO.setSegment1("1");
        sofiGlInterfaceDO.setSegment2("1");
        sofiGlInterfaceDO.setSegment3("1");
        sofiGlInterfaceDO.setSegment4("1");
        sofiGlInterfaceDO.setSegment5("1");
        sofiGlInterfaceDO.setSegment6("1");
        sofiGlInterfaceDO.setSegment7("1");
        sofiGlInterfaceDO.setSegment8("1");
        sofiGlInterfaceDO.setSegment9("1");
        sofiGlInterfaceDO.setSegment10("1");
        sofiGlInterfaceDO.setGroupId(1L);
        sofiGlInterfaceDO.setCurrencyConversionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setCurrencyConversionRate(new BigDecimal(1));
        sofiGlInterfaceDO.setUserCurrencyConversionType("1");
        sofiGlInterfaceDO.setProcessStatus(ProcessStatusEnum.NEW);
        sofiGlInterfaceDO.setProcessMessage("1");
        sofiGlInterfaceDO.setJeHeaderId(1L);
        sofiGlInterfaceDO.setJournalName("1");
        sofiGlInterfaceDO.setJeLineNum(1L);
        sofiGlInterfaceDO.setDocumentId(1L);
        sofiGlInterfaceDO.setLoadRequestId(1L);
        sofiGlInterfaceDO.setImportRequestId(1L);
        sofiGlInterfaceDO.setObjectVersionNumber(1L);
        sofiGlInterfaceDO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setCreatedBy("1");
        sofiGlInterfaceDO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setLastModifiedBy("1");
        sofiGlInterfaceDO.setJournalSourceName("1");
        sofiGlInterfaceRepository.insertSelective(sofiGlInterfaceDO);
    }

    /**
    *
    * Method: selectByProcessDayAndStatus(processDay, code)
    */
    @Test
    public void selectByProcessDayAndStatusTest() throws Exception {
        String processDay="11";
        String code="11";
        sofiGlInterfaceRepository.selectByProcessDayAndStatus(processDay,code);
    }
    @Test
    public void selectByProcessDayAndStatusTest2() throws Exception {
        String processDay="";
        String code="";
        sofiGlInterfaceRepository.selectByProcessDayAndStatus(processDay,code);
    }
    @Test
    public void selectByProcessDayAndStatusTest3() throws Exception {
        String processDay="11";
        String code="11";
        List<SofiGlInterfacePO> poList =new ArrayList<>();
        SofiGlInterfacePO sofiGlInterfacePO = new SofiGlInterfacePO();
        sofiGlInterfacePO.setId(1L);
        sofiGlInterfacePO.setFileId(1L);
        sofiGlInterfacePO.setProcessDay("2025");
        sofiGlInterfacePO.setFileName("filename");
        sofiGlInterfacePO.setBatchId("1");
        sofiGlInterfacePO.setDetallePolizaId(1L);
        sofiGlInterfacePO.setSourceSys("safi");
        sofiGlInterfacePO.setEmpresaId(1L);
        sofiGlInterfacePO.setPolizaId(1L);
        sofiGlInterfacePO.setFecha("2025-06");
        sofiGlInterfacePO.setCentroCostoId(1L);
        sofiGlInterfacePO.setCuentaCompleta("1");
        sofiGlInterfacePO.setInstrumento(1L);
        sofiGlInterfacePO.setMonedaId(1L);
        sofiGlInterfacePO.setCargos(new BigDecimal(1));
        sofiGlInterfacePO.setAbonos(new BigDecimal(1));
        sofiGlInterfacePO.setDescripcion("1");
        sofiGlInterfacePO.setReferencia("1");
        sofiGlInterfacePO.setProcedimientoCont("1");
        sofiGlInterfacePO.setTipoInstrumentoId("1");
        sofiGlInterfacePO.setRfc("1");
        sofiGlInterfacePO.setTotalFactura(new BigDecimal(1));
        sofiGlInterfacePO.setFolioUuid("1");
        sofiGlInterfacePO.setUsuario(1L);
        sofiGlInterfacePO.setFechaActual("1");
        sofiGlInterfacePO.setDireccionIp("1");
        sofiGlInterfacePO.setProgramaId("1");
        sofiGlInterfacePO.setSucursal(1L);
        sofiGlInterfacePO.setNumTransaccion(1L);
        sofiGlInterfacePO.setLedgerId(1L);
        sofiGlInterfacePO.setLedgerName("1");
        sofiGlInterfacePO.setCurrencyCode("1");
        sofiGlInterfacePO.setJournalCategory("1");
        sofiGlInterfacePO.setJournalSource("1");
        sofiGlInterfacePO.setSegment1("1");
        sofiGlInterfacePO.setSegment2("1");
        sofiGlInterfacePO.setSegment3("1");
        sofiGlInterfacePO.setSegment4("1");
        sofiGlInterfacePO.setSegment5("1");
        sofiGlInterfacePO.setSegment6("1");
        sofiGlInterfacePO.setSegment7("1");
        sofiGlInterfacePO.setSegment8("1");
        sofiGlInterfacePO.setSegment9("1");
        sofiGlInterfacePO.setSegment10("1");
        sofiGlInterfacePO.setGroupId(1L);
        sofiGlInterfacePO.setCurrencyConversionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfacePO.setCurrencyConversionRate(new BigDecimal(1));
        sofiGlInterfacePO.setUserCurrencyConversionType("1");
        sofiGlInterfacePO.setProcessStatus(ProcessStatusEnum.NEW.getCode());
        sofiGlInterfacePO.setProcessMessage("1");
        sofiGlInterfacePO.setJeHeaderId(1L);
        sofiGlInterfacePO.setJournalName("1");
        sofiGlInterfacePO.setJeLineNum(1L);
        sofiGlInterfacePO.setDocumentId(1L);
        sofiGlInterfacePO.setLoadRequestId(1L);
        sofiGlInterfacePO.setImportRequestId(1L);
        sofiGlInterfacePO.setObjectVersionNumber(1L);
        sofiGlInterfacePO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfacePO.setCreatedBy("1");
        sofiGlInterfacePO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfacePO.setLastModifiedBy("1");
        sofiGlInterfacePO.setJournalSourceName("1");
        SofiGlInterfacePO sofiGlInts = new SofiGlInterfacePO();
        sofiGlInts.setProcessDay("1");
        poList.add(sofiGlInts);
//        SofiGlInterfacePOExample example = new SofiGlInterfacePOExample();
//        example.createCriteria()
//                .andProcessDayEqualTo("1")
//                .andProcessStatusEqualTo("1");
        when(sofiGlInterfaceCustomerMapper.selectByExample(any())).thenReturn(poList);
        sofiGlInterfaceRepository.selectByProcessDayAndStatus("1","1");
    }

    /**
    *
    * Method: selectByPolizaIdAndDetalle(polizaId, detallePolizaId)
    */
    @Test
    public void selectByPolizaIdAndDetalleTest() throws Exception {
        Long polizaId = 1L;
        Long detallePolizaId =  1L;
        sofiGlInterfaceRepository.selectByPolizaIdAndDetalle(polizaId,detallePolizaId);
    }

    /**
    *
    * Method: batchInsertInterface(interfaceList)
    */
    @Test
    public void batchInsertInterfaceTest() throws Exception {
        List<SofiGlInterfaceDO> interfaceList = new ArrayList<>();
        sofiGlInterfaceRepository.batchInsertInterface(interfaceList);
    }
    @Test
    public void batchInsertInterfaceTest2() throws Exception {
        List<SofiGlInterfaceDO> interfaceList = new ArrayList<>();
        SofiGlInterfaceDO sofiGlInterfaceDO = new SofiGlInterfaceDO();
        sofiGlInterfaceDO.setId(1L);
        sofiGlInterfaceDO.setFileId(1L);
        sofiGlInterfaceDO.setProcessDay("2025");
        sofiGlInterfaceDO.setFileName("filename");
        sofiGlInterfaceDO.setBatchId("1");
        sofiGlInterfaceDO.setDetallePolizaId(1L);
        sofiGlInterfaceDO.setSourceSys("safi");
        sofiGlInterfaceDO.setEmpresaId(1L);
        sofiGlInterfaceDO.setPolizaId(1L);
        sofiGlInterfaceDO.setFecha("2025-06");
        sofiGlInterfaceDO.setCentroCostoId(1L);
        sofiGlInterfaceDO.setCuentaCompleta("1");
        sofiGlInterfaceDO.setInstrumento(1L);
        sofiGlInterfaceDO.setMonedaId(1L);
        sofiGlInterfaceDO.setCargos(new BigDecimal(1));
        sofiGlInterfaceDO.setAbonos(new BigDecimal(1));
        sofiGlInterfaceDO.setDescripcion("1");
        sofiGlInterfaceDO.setReferencia("1");
        sofiGlInterfaceDO.setProcedimientoCont("1");
        sofiGlInterfaceDO.setTipoInstrumentoId("1");
        sofiGlInterfaceDO.setRfc("1");
        sofiGlInterfaceDO.setTotalFactura(new BigDecimal(1));
        sofiGlInterfaceDO.setFolioUuid("1");
        sofiGlInterfaceDO.setUsuario(1L);
        sofiGlInterfaceDO.setFechaActual("1");
        sofiGlInterfaceDO.setDireccionIp("1");
        sofiGlInterfaceDO.setProgramaId("1");
        sofiGlInterfaceDO.setSucursal(1L);
        sofiGlInterfaceDO.setNumTransaccion(1L);
        sofiGlInterfaceDO.setLedgerId(1L);
        sofiGlInterfaceDO.setLedgerName("1");
        sofiGlInterfaceDO.setCurrencyCode("1");
        sofiGlInterfaceDO.setJournalCategory("1");
        sofiGlInterfaceDO.setJournalSource("1");
        sofiGlInterfaceDO.setSegment1("1");
        sofiGlInterfaceDO.setSegment2("1");
        sofiGlInterfaceDO.setSegment3("1");
        sofiGlInterfaceDO.setSegment4("1");
        sofiGlInterfaceDO.setSegment5("1");
        sofiGlInterfaceDO.setSegment6("1");
        sofiGlInterfaceDO.setSegment7("1");
        sofiGlInterfaceDO.setSegment8("1");
        sofiGlInterfaceDO.setSegment9("1");
        sofiGlInterfaceDO.setSegment10("1");
        sofiGlInterfaceDO.setGroupId(1L);
        sofiGlInterfaceDO.setCurrencyConversionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setCurrencyConversionRate(new BigDecimal(1));
        sofiGlInterfaceDO.setUserCurrencyConversionType("1");
        sofiGlInterfaceDO.setProcessStatus(ProcessStatusEnum.NEW);
        sofiGlInterfaceDO.setProcessMessage("1");
        sofiGlInterfaceDO.setJeHeaderId(1L);
        sofiGlInterfaceDO.setJournalName("1");
        sofiGlInterfaceDO.setJeLineNum(1L);
        sofiGlInterfaceDO.setDocumentId(1L);
        sofiGlInterfaceDO.setLoadRequestId(1L);
        sofiGlInterfaceDO.setImportRequestId(1L);
        sofiGlInterfaceDO.setObjectVersionNumber(1L);
        sofiGlInterfaceDO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setCreatedBy("1");
        sofiGlInterfaceDO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setLastModifiedBy("1");
        sofiGlInterfaceDO.setJournalSourceName("1");
        interfaceList.add(sofiGlInterfaceDO);
        List<SofiGlInterfacePO> existingRecords = new ArrayList<>();
                when(sofiGlInterfaceCustomerMapper.findByIndexFields(
                        sofiGlInterfaceDO.getProcessDay(), sofiGlInterfaceDO.getFileName(), sofiGlInterfaceDO.getPolizaId(), sofiGlInterfaceDO.getDetallePolizaId())).thenReturn(existingRecords);
        SofiGlInterfacePO sofiGlInterfacePO = new SofiGlInterfacePO();
        sofiGlInterfacePO.setId(1L);
        sofiGlInterfacePO.setFileId(1L);
        sofiGlInterfacePO.setProcessDay("2025");
        sofiGlInterfacePO.setFileName("filename");
        sofiGlInterfacePO.setBatchId("1");
        sofiGlInterfacePO.setDetallePolizaId(1L);
        sofiGlInterfacePO.setSourceSys("safi");
        sofiGlInterfacePO.setEmpresaId(1L);
        sofiGlInterfacePO.setPolizaId(1L);
        sofiGlInterfacePO.setFecha("2025-06");
        sofiGlInterfacePO.setCentroCostoId(1L);
        sofiGlInterfacePO.setCuentaCompleta("1");
        sofiGlInterfacePO.setInstrumento(1L);
        sofiGlInterfacePO.setMonedaId(1L);
        sofiGlInterfacePO.setCargos(new BigDecimal(1));
        sofiGlInterfacePO.setAbonos(new BigDecimal(1));
        sofiGlInterfacePO.setDescripcion("1");
        sofiGlInterfacePO.setReferencia("1");
        sofiGlInterfacePO.setProcedimientoCont("1");
        sofiGlInterfacePO.setTipoInstrumentoId("1");
        sofiGlInterfacePO.setRfc("1");
        sofiGlInterfacePO.setTotalFactura(new BigDecimal(1));
        sofiGlInterfacePO.setFolioUuid("1");
        sofiGlInterfacePO.setUsuario(1L);
        sofiGlInterfacePO.setFechaActual("1");
        sofiGlInterfacePO.setDireccionIp("1");
        sofiGlInterfacePO.setProgramaId("1");
        sofiGlInterfacePO.setSucursal(1L);
        sofiGlInterfacePO.setNumTransaccion(1L);
        sofiGlInterfacePO.setLedgerId(1L);
        sofiGlInterfacePO.setLedgerName("1");
        sofiGlInterfacePO.setCurrencyCode("1");
        sofiGlInterfacePO.setJournalCategory("1");
        sofiGlInterfacePO.setJournalSource("1");
        sofiGlInterfacePO.setSegment1("1");
        sofiGlInterfacePO.setSegment2("1");
        sofiGlInterfacePO.setSegment3("1");
        sofiGlInterfacePO.setSegment4("1");
        sofiGlInterfacePO.setSegment5("1");
        sofiGlInterfacePO.setSegment6("1");
        sofiGlInterfacePO.setSegment7("1");
        sofiGlInterfacePO.setSegment8("1");
        sofiGlInterfacePO.setSegment9("1");
        sofiGlInterfacePO.setSegment10("1");
        sofiGlInterfacePO.setGroupId(1L);
        sofiGlInterfacePO.setCurrencyConversionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfacePO.setCurrencyConversionRate(new BigDecimal(1));
        sofiGlInterfacePO.setUserCurrencyConversionType("1");
        sofiGlInterfacePO.setProcessStatus(ProcessStatusEnum.NEW.getCode());
        sofiGlInterfacePO.setProcessMessage("1");
        sofiGlInterfacePO.setJeHeaderId(1L);
        sofiGlInterfacePO.setJournalName("1");
        sofiGlInterfacePO.setJeLineNum(1L);
        sofiGlInterfacePO.setDocumentId(1L);
        sofiGlInterfacePO.setLoadRequestId(1L);
        sofiGlInterfacePO.setImportRequestId(1L);
        sofiGlInterfacePO.setObjectVersionNumber(1L);
        sofiGlInterfacePO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfacePO.setCreatedBy("1");
        sofiGlInterfacePO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfacePO.setLastModifiedBy("1");
        sofiGlInterfacePO.setJournalSourceName("1");
        existingRecords.add(sofiGlInterfacePO);
        when(sofiGlInterfaceModelConverter.convert(sofiGlInterfacePO)).thenReturn(sofiGlInterfaceDO);

        sofiGlInterfaceRepository.batchInsertInterface(interfaceList);
    }

    /**
    *
    * Method: queryGroupId(sofiGlInterfaceHeaderQueryDO)
    */
    @Test
    public void queryGroupIdTest() throws Exception {
        SofiGlInterfaceHeaderQueryDO queryDO = new SofiGlInterfaceHeaderQueryDO();
        queryDO.setPageNum(1);
        queryDO.setPageSize(10);
        sofiGlInterfaceRepository.queryGroupId(queryDO);
    }

    /**
    *
    * Method: queryGLInterfaceData(sofiGlInterfaceDO)
    */
    @Test
    public void queryGLInterfaceDataTest() throws Exception {
        SofiGlInterfaceDO sofiGlInterfaceDO = new SofiGlInterfaceDO();
        sofiGlInterfaceDO.setId(1L);
        sofiGlInterfaceDO.setFileId(1L);
        sofiGlInterfaceDO.setProcessDay("2025");
        sofiGlInterfaceDO.setFileName("filename");
        sofiGlInterfaceDO.setBatchId("1");
        sofiGlInterfaceDO.setDetallePolizaId(1L);
        sofiGlInterfaceDO.setSourceSys("safi");
        sofiGlInterfaceDO.setEmpresaId(1L);
        sofiGlInterfaceDO.setPolizaId(1L);
        sofiGlInterfaceDO.setFecha("2025-06");
        sofiGlInterfaceDO.setCentroCostoId(1L);
        sofiGlInterfaceDO.setCuentaCompleta("1");
        sofiGlInterfaceDO.setInstrumento(1L);
        sofiGlInterfaceDO.setMonedaId(1L);
        sofiGlInterfaceDO.setCargos(new BigDecimal(1));
        sofiGlInterfaceDO.setAbonos(new BigDecimal(1));
        sofiGlInterfaceDO.setDescripcion("1");
        sofiGlInterfaceDO.setReferencia("1");
        sofiGlInterfaceDO.setProcedimientoCont("1");
        sofiGlInterfaceDO.setTipoInstrumentoId("1");
        sofiGlInterfaceDO.setRfc("1");
        sofiGlInterfaceDO.setTotalFactura(new BigDecimal(1));
        sofiGlInterfaceDO.setFolioUuid("1");
        sofiGlInterfaceDO.setUsuario(1L);
        sofiGlInterfaceDO.setFechaActual("1");
        sofiGlInterfaceDO.setDireccionIp("1");
        sofiGlInterfaceDO.setProgramaId("1");
        sofiGlInterfaceDO.setSucursal(1L);
        sofiGlInterfaceDO.setNumTransaccion(1L);
        sofiGlInterfaceDO.setLedgerId(1L);
        sofiGlInterfaceDO.setLedgerName("1");
        sofiGlInterfaceDO.setCurrencyCode("1");
        sofiGlInterfaceDO.setJournalCategory("1");
        sofiGlInterfaceDO.setJournalSource("1");
        sofiGlInterfaceDO.setSegment1("1");
        sofiGlInterfaceDO.setSegment2("1");
        sofiGlInterfaceDO.setSegment3("1");
        sofiGlInterfaceDO.setSegment4("1");
        sofiGlInterfaceDO.setSegment5("1");
        sofiGlInterfaceDO.setSegment6("1");
        sofiGlInterfaceDO.setSegment7("1");
        sofiGlInterfaceDO.setSegment8("1");
        sofiGlInterfaceDO.setSegment9("1");
        sofiGlInterfaceDO.setSegment10("1");
        sofiGlInterfaceDO.setGroupId(1L);
        sofiGlInterfaceDO.setCurrencyConversionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setCurrencyConversionRate(new BigDecimal(1));
        sofiGlInterfaceDO.setUserCurrencyConversionType("1");
        sofiGlInterfaceDO.setProcessStatus(ProcessStatusEnum.NEW);
        sofiGlInterfaceDO.setProcessMessage("1");
        sofiGlInterfaceDO.setJeHeaderId(1L);
        sofiGlInterfaceDO.setJournalName("1");
        sofiGlInterfaceDO.setJeLineNum(1L);
        sofiGlInterfaceDO.setDocumentId(1L);
        sofiGlInterfaceDO.setLoadRequestId(1L);
        sofiGlInterfaceDO.setImportRequestId(1L);
        sofiGlInterfaceDO.setObjectVersionNumber(1L);
        sofiGlInterfaceDO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setCreatedBy("1");
        sofiGlInterfaceDO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setLastModifiedBy("1");
        sofiGlInterfaceDO.setJournalSourceName("1");
        sofiGlInterfaceRepository.queryGLInterfaceData(sofiGlInterfaceDO);
    }

    /**
    *
    * Method: selectCargosAndAbonosDO(polizaId)
    */
    @Test
    public void selectCargosAndAbonosDOTest() throws Exception {
        Long poid=1L;
        sofiGlInterfaceRepository.selectCargosAndAbonosDO(poid);
    }

    /**
    *
    * Method: selectSofiGlInterfaceByPolizaId(polizaIds)
    */
    @Test
    public void selectSofiGlInterfaceByPolizaIdTest() throws Exception {
        List<Long> polizaIds =new ArrayList<>();
        polizaIds.add(1L);
        polizaIds.add(2L);
        polizaIds.add(3L);
        sofiGlInterfaceRepository.selectSofiGlInterfaceByExternalReference(polizaIds);
    }

    /**
    *
    * Method: updateInterface(sofiGlInterfaceDO, update)
    */
    @Test
    public void updateInterfaceTest() throws Exception {
        SofiGlInterfaceDO sofiGlInterfaceDO = new SofiGlInterfaceDO();
        SofiGlInterfaceDO update = new SofiGlInterfaceDO();
        sofiGlInterfaceDO.setObjectVersionNumber(1L);
        sofiGlInterfaceDO.setDetallePolizaId(1L);
        sofiGlInterfaceDO.setProcessStatus(ProcessStatusEnum.PROCESSING);
        update.setProcessStatus(ProcessStatusEnum.PROCESSING);
        sofiGlInterfaceRepository.updateInterface(sofiGlInterfaceDO,update);
    }
    @Test
    public void updateInterfaceByPolizaId(){
        SofiGlInterfaceDO sofiGlInterfaceDO = new SofiGlInterfaceDO();
        SofiGlInterfaceDO update = new SofiGlInterfaceDO();
        sofiGlInterfaceDO.setObjectVersionNumber(1L);
        sofiGlInterfaceDO.setPolizaId(1L);
        sofiGlInterfaceDO.setProcessStatus(ProcessStatusEnum.PROCESSING);
        update.setProcessStatus(ProcessStatusEnum.PROCESSING);
        sofiGlInterfaceRepository.updateInterfaceByPolizaId(sofiGlInterfaceDO,update);
    }
    /**
    *
    * Method: updateByExampleSelective(record, example)
    */
    @Test
    public void updateByExampleSelectiveTest() throws Exception {
        SofiGlInterfacePO record = new SofiGlInterfacePO();
        SofiGlInterfacePOExample example = new SofiGlInterfacePOExample();
        sofiGlInterfaceRepository.updateByExampleSelective(record,example);
    }

    /**
    *
    * Method: selectByPolizaIdAndDay(polizaId, processDay)
    */
    @Test
    public void selectByPolizaIdAndDayTest() throws Exception {
        sofiGlInterfaceRepository.selectByPolizaIdAndDay(null,"",0L);
    }
    @Test
    public void selectByPolizaIdAndDayTest2() throws Exception {
        Long polizaId = 1L;
        String processDay = "22";
        sofiGlInterfaceRepository.selectByPolizaIdAndDay(polizaId,processDay,anyLong());

        sofiGlInterfaceRepository.selectSofiGlInterfaceByPolizaIdsAndProcessDay(Arrays.asList("1"),null);
    }
    @Test
    public void selectByPolizaIdAndDayTest3() throws Exception {
        Long polizaId = 1L;
        String processDay = "22";
        List<SofiGlInterfacePO> poList = new ArrayList<>();
        SofiGlInterfacePO sofiGlInterfacePO = new SofiGlInterfacePO();
        poList.add(sofiGlInterfacePO);
        when(sofiGlInterfaceCustomerMapper.selectByExample(any())).thenReturn(poList);

        sofiGlInterfaceRepository.selectByPolizaIdAndDay(polizaId,processDay,anyLong());

        sofiGlInterfaceRepository.selectSofiGlInterfaceByPolizaIdsAndProcessDay(Arrays.asList("1"),processDay);
    }

    @Test
    public void selectCountInterfaceEmailDataTest() throws Exception {
        String processDay = "20250613";
        List<SofiEmailDO> sofiEmailDOList = new ArrayList<>();
        SofiEmailDO sofiEmailDO = new SofiEmailDO();
        sofiEmailDO.setCount(1L);
        sofiEmailDO.setFileName("file");
        sofiEmailDO.setSourceSys("s");
        sofiEmailDO.setProcessStatus("s");
        sofiEmailDOList.add(sofiEmailDO);
        when(sofiGlInterfaceCustomerMapper.selectConutInterfaceEmailData(processDay)).thenReturn(sofiEmailDOList);
        final List<SofiEmailDO> result = sofiGlInterfaceRepository.selectCountInterfaceEmailData(processDay);
        assertEquals(sofiEmailDOList,result);

    }
    @Test
    void selectSofiGlInterfaceByGroupIdAndProcessDayTest() {
        Long groupId = 1L;
        String processDay = "2025-08-15";
        sofiGlInterfaceRepository.selectSofiGlInterfaceByGroupIdAndProcessDay(groupId,processDay);
    }
    @Test
    void selectSofiGlInterfaceByGroupIdAndProcessDayNotTest() {
        Long groupId = 1L;
        String processDay = "2025-08-15";
        List<SofiGlInterfacePO> poList = new ArrayList<>();
        SofiGlInterfacePO po = new SofiGlInterfacePO();
        po.setProcessDay(processDay);
        poList.add(po);
        when(sofiGlInterfaceCustomerMapper.selectSofiGlInterfaceByGroupIdAndProcessDay(groupId,processDay)).thenReturn(poList);

        sofiGlInterfaceRepository.selectSofiGlInterfaceByGroupIdAndProcessDay(groupId,processDay);
    }
    @Test
    void countByProcessDayAndStatusTest() {
        String status = "new";
        String processDay = "2025-08-15";
        sofiGlInterfaceRepository.countByProcessDayAndStatus(processDay,status);
    }
    @Test
    void countByProcessDayAndStatusNullTest() {
        String status = "";
        String processDay = "";
        sofiGlInterfaceRepository.countByProcessDayAndStatus(processDay,status);
    }
    @Test
    void selectGroupByPolizaIdTest() {
        String status = "new";
        String processDay = "2025-08-15";
        sofiGlInterfaceRepository.selectGroupByPolizaId(processDay,status);
    }

}
