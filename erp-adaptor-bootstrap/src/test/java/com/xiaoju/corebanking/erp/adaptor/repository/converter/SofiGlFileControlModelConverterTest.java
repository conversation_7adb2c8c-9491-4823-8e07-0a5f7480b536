package com.xiaoju.corebanking.erp.adaptor.repository.converter;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlFileControlDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlFileControlPO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Calendar;
import java.util.GregorianCalendar;

import static org.junit.Assert.assertEquals;

/**
* <p> SofiGlFileControlModelConverter Tester. </p>
* <p> 2025-06-06 11:40:17.959 </p>
*
* <AUTHOR>
* @version 0.0.1-SNAPSHOT
*/
@ExtendWith(MockitoExtension.class)
public class SofiGlFileControlModelConverterTest {
    @InjectMocks
    private SofiGlFileControlModelConverter converter;

    /**
    *
    * Method: convert(sofiGlFileControlDO)
    */
    @Test
    public void convertTest() throws Exception {
        //init
        SofiGlFileControlPO sofiGlFileControlPO = new SofiGlFileControlPO();
        sofiGlFileControlPO.setProcessDay("2025");
        sofiGlFileControlPO.setFileName("fileName");
        sofiGlFileControlPO.setFileSize(1L);
        sofiGlFileControlPO.setFileCount(1L);
        sofiGlFileControlPO.setProcessStatus("NEW");
        sofiGlFileControlPO.setSystemCode("shenma");
        sofiGlFileControlPO.setProcessMessage("");
        sofiGlFileControlPO.setObjectVersionNumber(2);
        sofiGlFileControlPO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlFileControlPO.setCreatedBy("1");
        sofiGlFileControlPO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlFileControlPO.setLastModifiedBy("1");

        SofiGlFileControlDO sofiGlFileControlDO = new SofiGlFileControlDO();
        sofiGlFileControlDO.setProcessDay("2025");
        sofiGlFileControlDO.setFileName("fileName");
        sofiGlFileControlDO.setFileSize(1L);
        sofiGlFileControlDO.setFileCount(1L);
        sofiGlFileControlDO.setProcessStatus("NEW");
        sofiGlFileControlDO.setSystemCode("shenma");
        sofiGlFileControlDO.setProcessMessage("");
        sofiGlFileControlDO.setObjectVersionNumber(1);
        sofiGlFileControlDO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlFileControlDO.setCreatedBy("1");
        sofiGlFileControlDO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlFileControlDO.setLastModifiedBy("1");

        SofiGlFileControlPO result = converter.convert(sofiGlFileControlDO);
        assertEquals(result,sofiGlFileControlPO);
    }
    @Test
    public void convertTestNull() throws Exception {
        //init
        SofiGlFileControlPO sofiGlFileControlPO = new SofiGlFileControlPO();
        sofiGlFileControlPO.setProcessDay("2025");
        sofiGlFileControlPO.setFileName("fileName");
        sofiGlFileControlPO.setFileSize(1L);
        sofiGlFileControlPO.setFileCount(1L);
        sofiGlFileControlPO.setProcessStatus("NEW");
        sofiGlFileControlPO.setProcessMessage("");
        sofiGlFileControlPO.setObjectVersionNumber(2);
        sofiGlFileControlPO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlFileControlPO.setCreatedBy("1");
        sofiGlFileControlPO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlFileControlPO.setLastModifiedBy("1");

        SofiGlFileControlDO sofiGlFileControlDO = null;

        SofiGlFileControlPO result = converter.convert(sofiGlFileControlDO);
        assertEquals(result,null);
    }
    /**
    *
    * Method: convert(sofiGlFileControlPO)
    */
    @Test
    public void convertSofiGlFileControlPOTest() throws Exception {
        //init
        SofiGlFileControlDO fileControlDO = new SofiGlFileControlDO();
        fileControlDO.setProcessDay("2025");
        fileControlDO.setFileName("fileName");
        fileControlDO.setFileSize(1L);
        fileControlDO.setFileCount(1L);
        fileControlDO.setProcessStatus("NEW");
        fileControlDO.setProcessMessage("");
        fileControlDO.setObjectVersionNumber(1);
        fileControlDO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fileControlDO.setCreatedBy("1");
        fileControlDO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fileControlDO.setLastModifiedBy("1");

        SofiGlFileControlPO sofiGlFileControlPO = new SofiGlFileControlPO();
        sofiGlFileControlPO.setProcessDay("2025");
        sofiGlFileControlPO.setFileName("fileName");
        sofiGlFileControlPO.setFileSize(1L);
        sofiGlFileControlPO.setFileCount(1L);
        sofiGlFileControlPO.setProcessStatus("NEW");
        sofiGlFileControlPO.setProcessMessage("");
        sofiGlFileControlPO.setObjectVersionNumber(1);
        sofiGlFileControlPO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlFileControlPO.setCreatedBy("1");
        sofiGlFileControlPO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlFileControlPO.setLastModifiedBy("1");

        SofiGlFileControlDO result = converter.convert(sofiGlFileControlPO);
        assertEquals(result,fileControlDO);
    }


}
