package com.xiaoju.corebanking.erp.adaptor.service.fusion;

import com.alibaba.fastjson.JSONObject;
import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.godson.http.entity.HttpClientConnection;
import com.xiaoju.godson.http.entity.HttpClientResponse;
import com.xiaoju.godson.http.util.HttpClientUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

/**
 * <p> FunsionStatusService Tester. </p>
 * <p> 2025-06-07 21:04:14.072 </p>
 *
 * <AUTHOR>
 * @version 0.0.1-SNAPSHOT
 */
@ExtendWith(MockitoExtension.class)
public class FusionStatusServiceTest {

    @InjectMocks
    private FusionStatusService fusionStatusService;

    @Mock
    private RestTemplate restTemplate;

    @Mock
    private HttpClientConnection httpClientConnection;

    @Mock
    private HttpClientResponse httpClientResponse;

    private MockedStatic<HttpClientUtil> mockedHttpClientUtil;

    private final String PERIOD_NAME = "2023-01";
    private final String TOKEN = "test-token";
    private final String TEST_URL = "http://test-fusion-url.com";


    @BeforeEach
    void setUp() throws NoSuchFieldException, IllegalAccessException {
        java.lang.reflect.Field fusionSoapUrlField = FusionStatusService.class.getDeclaredField("statusUrl");
        fusionSoapUrlField.setAccessible(true);
        fusionSoapUrlField.set(fusionStatusService, "http://test.soap.url");

        fusionStatusService.map.clear();

        try {
            if (mockedHttpClientUtil != null) {
                mockedHttpClientUtil.close();
            }
            mockedHttpClientUtil = mockStatic(HttpClientUtil.class);
        } catch (Exception e) {
            mockedHttpClientUtil = null;
        }
    }

    @AfterEach
    void tearDown() {
        fusionStatusService.map.clear();
        if (mockedHttpClientUtil != null) {
            try {
                mockedHttpClientUtil.close();
            } catch (Exception ignored) {
            }
            mockedHttpClientUtil = null;
        }
    }


    /**
     * Method: queryFusionOpenStatus(periodName, sofiGlInterfaceDO, token)
     */
    @Test
    public void queryFusionOpenStatusTest() throws Exception {
        SofiGlInterfaceDO sofiGlInterfaceDO = new SofiGlInterfaceDO();
        sofiGlInterfaceDO.setLedgerId(123L);

        Map<String, Boolean> testMap = fusionStatusService.map;
        testMap.put(PERIOD_NAME, true);

        Map<String, Boolean> result = fusionStatusService.queryFusionOpenStatus(PERIOD_NAME, sofiGlInterfaceDO.getLedgerId()+"", TOKEN);

        assertNotNull(result);
    }

    @Test
    public void testQueryFusionOpenStatus_OpenPeriod() throws IOException {
        SofiGlInterfaceDO sofiGlInterfaceDO = new SofiGlInterfaceDO();
        sofiGlInterfaceDO.setLedgerId(123L);

        lenient().when(HttpClientUtil.get(anyString())).thenReturn(httpClientConnection);
        lenient().when(httpClientConnection.addHeader(anyString(), anyString())).thenReturn(httpClientConnection);
        lenient().when(httpClientConnection.bodyJson(anyString())).thenReturn(httpClientConnection);
        lenient().when(httpClientConnection.execute()).thenReturn(httpClientResponse);
        lenient().when(httpClientResponse.isSuccess()).thenReturn(false);

        Map<String, Boolean> result = fusionStatusService.queryFusionOpenStatus(PERIOD_NAME, sofiGlInterfaceDO.getLedgerId()+"", TOKEN);

    }

    @Test
    public void testQueryFusionOpenStatus_HttpError() throws IOException {
        SofiGlInterfaceDO sofiGlInterfaceDO = new SofiGlInterfaceDO();
        sofiGlInterfaceDO.setLedgerId(123L);

        lenient().when(HttpClientUtil.get(anyString())).thenReturn(httpClientConnection);
        lenient().when(httpClientConnection.addHeader(anyString(), anyString())).thenReturn(httpClientConnection);
        lenient().when(httpClientConnection.bodyJson(anyString())).thenReturn(httpClientConnection);
        lenient().when(httpClientConnection.execute()).thenThrow(new IOException("Connection error"));
        lenient().when(httpClientResponse.isSuccess()).thenReturn(false);

        try {
            fusionStatusService.queryFusionOpenStatus(PERIOD_NAME, sofiGlInterfaceDO.getLedgerId()+"", TOKEN);
        } catch (Exception e) {

        }
    }

    @Test
    public void testQueryFusionOpenStatus_ClosedPeriod() throws IOException {
        SofiGlInterfaceDO sofiGlInterfaceDO = new SofiGlInterfaceDO();
        sofiGlInterfaceDO.setLedgerId(123L);

        String expectedUrl = TEST_URL + CommonConstant.STATUSAPI + CommonConstant.Q +
                CommonConstant.APPLICATION + CommonConstant.LEDGERID +
                sofiGlInterfaceDO.getLedgerId() + ";PeriodNameId=" + PERIOD_NAME +
                "&fields=ClosingStatus&onlyData=true&links=canonical";

        lenient().when(HttpClientUtil.get(anyString())).thenReturn(httpClientConnection);
        lenient().when(httpClientConnection.addHeader(anyString(), anyString())).thenReturn(httpClientConnection);
        lenient().when(httpClientConnection.bodyJson(anyString())).thenReturn(httpClientConnection);
        when(httpClientConnection.execute()).thenReturn(httpClientResponse);
        when(httpClientResponse.isSuccess()).thenReturn(true);

        JSONObject responseJson = createMockResponseJson("C");
        when(httpClientResponse.getString()).thenReturn(responseJson.toJSONString());

        Map<String, Boolean> result = fusionStatusService.queryFusionOpenStatus(PERIOD_NAME, sofiGlInterfaceDO.getLedgerId()+"", TOKEN);

        assertNotNull(result);
    }

    private JSONObject createMockResponseJson(String closingStatus) {
        JSONObject itemJson = new JSONObject();
        itemJson.put("ClosingStatus", closingStatus);

        JSONObject responseJson = new JSONObject();
        List<JSONObject> items = new ArrayList<>();
        items.add(itemJson);
        responseJson.put("items", items);

        return responseJson;
    }


}
