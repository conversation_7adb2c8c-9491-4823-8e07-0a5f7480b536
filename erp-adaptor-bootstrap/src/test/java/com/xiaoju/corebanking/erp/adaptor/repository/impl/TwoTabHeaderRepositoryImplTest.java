package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.CuxTwoTabHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabHeadersPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.CuxTwoTabHeadersPOMapper;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
* <p> TwoTabHeaderRepositoryImpl Tester. </p>
* <p> 2025-06-06 11:40:17.954 </p>
*
* <AUTHOR>
* @version 0.0.1-SNAPSHOT
*/
@ExtendWith(MockitoExtension.class)
public class TwoTabHeaderRepositoryImplTest {
    @InjectMocks
    private TwoTabHeaderRepositoryImpl twoTabHeaderRepository;

    @Mock
    private CuxTwoTabHeadersPOMapper cuxTwoTabHeadersPOMapper;
    /**
    *
    * Method: selectByFormCode(formCode)
    */
    @Test
    public void selectByFormCodeTest() throws Exception {
        String formCode = "CURRENCY_MAPPINGS";
        twoTabHeaderRepository.selectByFormCode(formCode);
    }

    /**
    *
    * Method: selectAll()
    */
    @Test
    public void selectAllTest() throws Exception {
        twoTabHeaderRepository.selectAll();
    }

    /**
    *
    * Method: updateByExample(header)
    */
    @Test
    public void updateByExampleTest() throws Exception {
        CuxTwoTabHeaderDO cuxTwoTabHeaderDO = new CuxTwoTabHeaderDO();
        twoTabHeaderRepository.updateByExample(cuxTwoTabHeaderDO);
    }

    /**
    *
    * Method: saveHeader(header)
    */
    @Test
    public void saveHeaderTest() throws Exception {
        CuxTwoTabHeaderDO cuxTwoTabHeaderDO = new CuxTwoTabHeaderDO();
        Assert.assertThrows(RuntimeException.class,()->twoTabHeaderRepository.saveHeader(cuxTwoTabHeaderDO));
    }
    @Test
    public void saveHeaderTest1() throws Exception {
        CuxTwoTabHeaderDO header = new CuxTwoTabHeaderDO();
        header.setFormCode("1");
       twoTabHeaderRepository.saveHeader(header);
    }
    @Test
    public void saveHeaderTest2() throws Exception {
        CuxTwoTabHeaderDO header = new CuxTwoTabHeaderDO();
        header.setFormCode("1");
        List<CuxTwoTabHeadersPO> list = new ArrayList<>();
        CuxTwoTabHeadersPO po = new CuxTwoTabHeadersPO();
        po.setFormCode("1");
        list.add(po);
        when(cuxTwoTabHeadersPOMapper.selectByExample(any())).thenReturn(list);
        twoTabHeaderRepository.saveHeader(header);
    }

    /**
    *
    * Method: insertByExample(header)
    */
    @Test
    public void insertByExampleTest() throws Exception {
        CuxTwoTabHeaderDO header = new CuxTwoTabHeaderDO();
        header.setDateTo(null);
        twoTabHeaderRepository.insertByExample(header);
    }


}
