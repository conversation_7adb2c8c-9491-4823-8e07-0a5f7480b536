package com.xiaoju.corebanking.erp.adaptor.service.inter.impl;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.enums.SourceSysEnum;
import com.xiaoju.corebanking.erp.adaptor.common.utils.ErpUtil;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceCommonDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;
import com.xiaoju.corebanking.erp.adaptor.service.header.SofiGlInterfaceHeaderService;
import com.xiaoju.corebanking.erp.adaptor.service.polling.StatusPollingService;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlInterfaceCommonService;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlShenMaService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestTemplate;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * <p> FusionResultServiceImpl Tester. </p>
 * <p> 2025-06-07 21:04:14.058 </p>
 *
 * <AUTHOR>
 * @version 0.0.1-SNAPSHOT
 */
@ExtendWith(MockitoExtension.class)
public class FusionResultServiceImplTest {

    @Mock
    private SofiGlInterfaceHeaderService sofiGlInterfaceHeaderService;

    @Mock
    private SofiGlInterfaceRepository sofiGlInterfaceRepository;

    @Mock
    private RestTemplate restTemplate;

    @Mock
    private StatusPollingService statusPollingService;

    @Mock
    private ErpUtil erpUtil;

    @InjectMocks
    private FusionResultServiceImpl fusionResultService;

    @Mock
    SofiGlShenMaService sofiGlShenMaService;
    @Mock
    private SofiGlInterfaceCommonService sofiGlInterfaceCommonService;
    @BeforeEach
    void setUp() throws NoSuchFieldException, IllegalAccessException {
        java.lang.reflect.Field fusionSoapUrlField = FusionResultServiceImpl.class.getDeclaredField("fusionSoapUrl");
        fusionSoapUrlField.setAccessible(true);
        fusionSoapUrlField.set(fusionResultService, "http://test.soap.url");

        java.lang.reflect.Field fusionUsernameField = FusionResultServiceImpl.class.getDeclaredField("fusionUsername");
        fusionUsernameField.setAccessible(true);
        fusionUsernameField.set(fusionResultService, "testUser");

        java.lang.reflect.Field fusionPasswordField = FusionResultServiceImpl.class.getDeclaredField("fusionPassword");
        fusionPasswordField.setAccessible(true);
        fusionPasswordField.set(fusionResultService, "testPass");

        java.lang.reflect.Field reportPathField = FusionResultServiceImpl.class.getDeclaredField("reportPath");
        reportPathField.setAccessible(true);
        reportPathField.set(fusionResultService, "/Custom/Financials/General Ledger/Journal Import/Journal Import Report");
    }

    /**
     * Method: processSingleFusionResult(headerDO)
     */
    @Test
    public void processSingleFusionResultTest() throws Exception {
        try (MockedStatic<Base64> mockedBase64 = mockStatic(Base64.class)) {
            Base64.Decoder decoderMock = mock(Base64.Decoder.class);

            mockedBase64.when(Base64::getDecoder).thenReturn(decoderMock);
            lenient().when(decoderMock.decode(anyString())).thenReturn("testDecodedContent".getBytes());

            lenient().when(sofiGlInterfaceRepository.queryGLInterfaceData(any())).thenReturn(Arrays.asList(new SofiGlInterfaceDO() {{
                setProcessStatus(ProcessStatusEnum.PROCESSING);
                setProcessMessage("testProcessMessage");
            }}));

            lenient().doNothing().when(sofiGlInterfaceRepository).updateInterface(any(), any());

            SofiGlInterfaceHeaderDO headerDO = new SofiGlInterfaceHeaderDO();
            headerDO.setGroupId(123L);
            SofiGlInterfaceDO sofiGlInterfaceDO = new SofiGlInterfaceDO();
            sofiGlInterfaceDO.setGroupId(123L);
            fusionResultService.processSingleFusionResult(Arrays.asList(sofiGlInterfaceDO),Arrays.asList(headerDO));

            lenient().when(restTemplate.postForObject(any(), any(), any(), Optional.ofNullable(any()))).thenReturn("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\"\n" +
                    "                  xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">\n" +
                    "    <soapenv:Body>\n" +
                    "        <runDataModelResponse xmlns=\"http://xmlns.oracle.com/oxp/service/v2\">\n" +
                    "            <runDataModelReturn>\n" +
                    "                <metaDataList xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:nil=\"true\"/>\n" +
                    "                <reportBytes>\n" +
                    "                    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\n" +
                    "                </reportBytes>\n" +
                    "                <reportContentType>text/xml</reportContentType>\n" +
                    "                <reportFileID xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:nil=\"true\"/>\n" +
                    "                <reportLocale xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:nil=\"true\"/>\n" +
                    "            </runDataModelReturn>\n" +
                    "        </runDataModelResponse>\n" +
                    "    </soapenv:Body>\n" +
                    "</soapenv:Envelope>");

            fusionResultService.processSingleFusionResult(Arrays.asList(sofiGlInterfaceDO),Arrays.asList(headerDO));
        }


    }


    @Test
    public void testHandleFusionResultJob_NoHeaderData() {
        SofiGlInterfaceHeaderDO queryDO = new SofiGlInterfaceHeaderDO();
        queryDO.setProcessStatus(ProcessStatusEnum.IMPORTED);

        String processDay = "20230101";
        when(erpUtil.getDateDirFromInput(anyString())).thenReturn(processDay);
        when(sofiGlInterfaceHeaderService.queryHeaderData(any(SofiGlInterfaceHeaderDO.class),anyString()))
                .thenReturn(Collections.emptyList());

        fusionResultService.handleFusionResultJob("safi","");
    }

    @Test
    public void testHandleFusionResultJob_NoValidHeaderData() {
        SofiGlInterfaceHeaderDO queryDO = new SofiGlInterfaceHeaderDO();
        queryDO.setProcessStatus(ProcessStatusEnum.IMPORTED);

        String processDay = "20230101";
        lenient().when(erpUtil.getDateDirFromInput(anyString())).thenReturn(processDay);

        SofiGlInterfaceHeaderDO headerDO = new SofiGlInterfaceHeaderDO();
        headerDO.setExternalReference("1111");
        headerDO.setProcessDay(processDay);
        headerDO.setGroupId(2222L);

        List<SofiGlInterfaceHeaderDO> headerList = Arrays.asList(headerDO);
        lenient().when(sofiGlInterfaceHeaderService.queryHeaderData(any(SofiGlInterfaceHeaderDO.class),anyString()))
                .thenReturn(headerList);

        lenient().when(sofiGlInterfaceRepository.selectByPolizaIdAndDay(any(), anyString(),any()))
                .thenReturn(Collections.emptyList());

        fusionResultService.handleFusionResultJob("safi","");

    }

    @Test
    public void testHandleFusionResultJob_StatusCheckFailed() {
        SofiGlInterfaceHeaderDO queryDO = new SofiGlInterfaceHeaderDO();
        queryDO.setProcessStatus(ProcessStatusEnum.IMPORTED);

        String processDay = "20230101";
        lenient().when(erpUtil.getDateDirFromInput(anyString())).thenReturn(processDay);

        SofiGlInterfaceHeaderDO headerDO = new SofiGlInterfaceHeaderDO();
        headerDO.setExternalReference("333");
        headerDO.setProcessDay(processDay);
        headerDO.setGroupId(333L);

        List<SofiGlInterfaceHeaderDO> headerList = Arrays.asList(headerDO);
        lenient().when(sofiGlInterfaceHeaderService.queryHeaderData(any(SofiGlInterfaceHeaderDO.class),anyString()))
                .thenReturn(headerList);

        SofiGlInterfaceDO interfaceDO = new SofiGlInterfaceDO();
        interfaceDO.setLoadRequestId(333L);
        List<SofiGlInterfaceDO> interfaceList = Arrays.asList(interfaceDO);

        lenient().when(sofiGlInterfaceRepository.selectByPolizaIdAndDay(333L, processDay,3L)).thenReturn(interfaceList);

        lenient().when(statusPollingService.checkRequestStatus(333L)).thenReturn(false);

        fusionResultService.handleFusionResultJob("safi","");
    }

    @Test
    public void testHandleFusionResultJob_Success() {
        SofiGlInterfaceHeaderDO queryDO = new SofiGlInterfaceHeaderDO();
        queryDO.setProcessStatus(ProcessStatusEnum.IMPORTED);

        String processDay = "20230101";
        lenient().when(erpUtil.getDateDirFromInput(anyString())).thenReturn(processDay);

        SofiGlInterfaceHeaderDO headerDO = new SofiGlInterfaceHeaderDO();
        headerDO.setExternalReference("333");
        headerDO.setProcessDay(processDay);
        headerDO.setGroupId(333L);

        List<SofiGlInterfaceHeaderDO> headerList = Arrays.asList(headerDO);
        lenient().when(sofiGlInterfaceHeaderService.queryHeaderData(any(SofiGlInterfaceHeaderDO.class),anyString()))
                .thenReturn(headerList);

        SofiGlInterfaceDO interfaceDO = new SofiGlInterfaceDO();
        interfaceDO.setLoadRequestId(333L);
        List<SofiGlInterfaceDO> interfaceList = Arrays.asList(interfaceDO);

        lenient().when(sofiGlInterfaceRepository.selectByPolizaIdAndDay(1L, processDay,2L))
                .thenReturn(interfaceList);

        lenient().when(statusPollingService.checkRequestStatus(333L)).thenReturn(true);

        fusionResultService.handleFusionResultJob("safi","");
    }

    @Test
    public void testHandleFusionResultJob_Success1() {
        try (MockedStatic<Base64> mockedBase64 = mockStatic(Base64.class)) {
            Base64.Decoder decoderMock = mock(Base64.Decoder.class);

            mockedBase64.when(Base64::getDecoder).thenReturn(decoderMock);
            lenient().when(decoderMock.decode(anyString())).thenReturn("<?xml version=\"1.0\" encoding=\"UTF-8\"?> <!--Generated by Oracle Analytics Publisher -Dataengine, datamodel:_Custom_Financials_Integration_XXDD_GL_JOURNALS_REL_DM_xdm --> <DATA_DS><G_1> <FUSION_ID>40801</FUSION_ID><SOURCE_ID>1610012</SOURCE_ID><STATUS>S</STATUS><MSG/><ACCT_SEQ_VALUE/> </G_1> </DATA_DS>".getBytes());
            SofiGlInterfaceHeaderDO queryDO = new SofiGlInterfaceHeaderDO();
            queryDO.setProcessStatus(ProcessStatusEnum.IMPORTED);

            String processDay = "20230101";
            lenient().when(erpUtil.getDateDirFromInput(anyString())).thenReturn(processDay);

            SofiGlInterfaceHeaderDO headerDO = new SofiGlInterfaceHeaderDO();
            headerDO.setExternalReference("333");
            headerDO.setProcessDay(processDay);
            headerDO.setGroupId(333L);

            List<SofiGlInterfaceHeaderDO> headerList = Arrays.asList(headerDO);
            lenient().when(sofiGlInterfaceHeaderService.queryHeaderData(any(SofiGlInterfaceHeaderDO.class),anyString()))
                    .thenReturn(headerList);

            SofiGlInterfaceDO interfaceDO = new SofiGlInterfaceDO();
            interfaceDO.setLoadRequestId(333L);
            List<SofiGlInterfaceDO> interfaceList = Arrays.asList(interfaceDO);

            lenient().when(sofiGlInterfaceRepository.selectByPolizaIdAndDay(1L, processDay,2L))
                    .thenReturn(interfaceList);

            lenient().when(statusPollingService.checkRequestStatus(333L)).thenReturn(true);

            when(sofiGlInterfaceRepository.selectSofiGlInterfaceByPolizaIdsAndProcessDay(anyList(),anyString())).thenReturn(Collections.singletonList(new SofiGlInterfaceDO() {{
                setLoadRequestId(333L);
                setProcessStatus(ProcessStatusEnum.IMPORTED);
                setGroupId(333L);
            }}));

            lenient().when(restTemplate.postForObject(any(), any(), any(), Optional.ofNullable(any()))).thenReturn("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\"\n" +
                    "                  xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">\n" +
                    "    <soapenv:Body>\n" +
                    "        <runDataModelResponse xmlns=\"http://xmlns.oracle.com/oxp/service/v2\">\n" +
                    "            <runDataModelReturn>\n" +
                    "                <metaDataList xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:nil=\"true\"/>\n" +
                    "                <reportBytes>\n" +
                    "                    PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPCEtLUdlbmVyYXRlZCBieSBPcmFjbGUgQW5hbHl0aWNzIFB1Ymxpc2hlciAtRGF0YWVuZ2luZSwgZGF0YW1vZGVsOl9DdXN0b21fRmluYW5jaWFsc19JbnRlZ3JhdGlvbl9YWEREX0dMX0pPVVJOQUxTX1JFTF9ETV94ZG0gLS0+CjxEQVRBX0RTPjxHXzE+CjxGVVNJT05fSUQ+NDA4MDE8L0ZVU0lPTl9JRD48U09VUkNFX0lEPjE2MTAwMTI8L1NPVVJDRV9JRD48U1RBVFVTPlM8L1NUQVRVUz48TVNHLz48QUNDVF9TRVFfVkFMVUUvPgo8L0dfMT4KPC9EQVRBX0RTPg==\n" +
                    "                </reportBytes>\n" +
                    "                <reportContentType>text/xml</reportContentType>\n" +
                    "                <reportFileID xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:nil=\"true\"/>\n" +
                    "                <reportLocale xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:nil=\"true\"/>\n" +
                    "            </runDataModelReturn>\n" +
                    "        </runDataModelResponse>\n" +
                    "    </soapenv:Body>\n" +
                    "</soapenv:Envelope>");

            fusionResultService.handleFusionResultJob("safi","");

            lenient().when(decoderMock.decode(anyString())).thenReturn("<?xml version=\"1.0\" encoding=\"UTF-8\"?> <!--Generated by Oracle Analytics Publisher -Dataengine, datamodel:_Custom_Financials_Integration_XXDD_GL_JOURNALS_REL_DM_xdm --> <DATA_DS><G_1> <FUSION_ID>40801</FUSION_ID><SOURCE_ID>1610012</SOURCE_ID><STATUS>E</STATUS><MSG/><ACCT_SEQ_VALUE/> </G_1> </DATA_DS>".getBytes());
            try {
                fusionResultService.handleFusionResultJob("safi","");
            } catch (Exception e) {

            }

            lenient().when(decoderMock.decode(anyString())).thenReturn("<?xml version=\"1.0\" encoding=\"UTF-8\"?> <!--Generated by Oracle Analytics Publisher -Dataengine, datamodel:_Custom_Financials_Integration_XXDD_GL_JOURNALS_REL_DM_xdm --> <DATA_DS><G_1> <FUSION_ID>40801</FUSION_ID><SOURCE_ID>1610012</SOURCE_ID><STATUS>P</STATUS><MSG/><ACCT_SEQ_VALUE/> </G_1> </DATA_DS>".getBytes());
            try {
                fusionResultService.handleFusionResultJob("safi","");
            } catch (Exception e) {

            }

            lenient().when(restTemplate.postForObject(any(), any(), any(), Optional.ofNullable(any()))).thenThrow(RuntimeException.class);
            try {
                fusionResultService.handleFusionResultJob("safi","");
            } catch (Exception e) {

            }
        }

    }

    @Test
    public void testHandleFusionResultJob_NullLoadRequestId() {
        SofiGlInterfaceHeaderDO queryDO = new SofiGlInterfaceHeaderDO();
        queryDO.setProcessStatus(ProcessStatusEnum.IMPORTED);

        String processDay = "20230101";
        lenient().when(erpUtil.getDateDirFromInput(anyString())).thenReturn(processDay);

        SofiGlInterfaceHeaderDO headerDO = new SofiGlInterfaceHeaderDO();
        headerDO.setExternalReference("333");
        headerDO.setProcessDay(processDay);
        headerDO.setGroupId(333L);

        List<SofiGlInterfaceHeaderDO> headerList = Arrays.asList(headerDO);
        lenient().when(sofiGlInterfaceHeaderService.queryHeaderData(any(SofiGlInterfaceHeaderDO.class),anyString()))
                .thenReturn(headerList);

        SofiGlInterfaceDO interfaceDO = new SofiGlInterfaceDO();
        interfaceDO.setLoadRequestId(null);
        List<SofiGlInterfaceDO> interfaceList = Arrays.asList(interfaceDO);

        lenient().when(sofiGlInterfaceRepository.selectByPolizaIdAndDay(1L,processDay,2L))
                .thenReturn(interfaceList);

        fusionResultService.handleFusionResultJob("safi","");
    }

    @Test
    void testProcessSingleFusionResultForShenma() {
        List<SofiGlInterfaceCommonDO> interfaceDataList = new ArrayList<>();
        SofiGlInterfaceCommonDO commonDO = new SofiGlInterfaceCommonDO();
        commonDO.setLoadRequestId(1L);
        interfaceDataList.add(commonDO);
        List<SofiGlInterfaceHeaderDO> headerList = new ArrayList<>();
        fusionResultService.processSingleFusionResultForShenma(interfaceDataList,headerList);
    }

    @Test
    void testProcessSingleFusionResultForShenmaNot() {
        try (MockedStatic<Base64> mockedBase64 = mockStatic(Base64.class)) {
            Base64.Decoder decoderMock = mock(Base64.Decoder.class);

            mockedBase64.when(Base64::getDecoder).thenReturn(decoderMock);
            lenient().when(decoderMock.decode(anyString())).thenReturn("<?xml version=\"1.0\" encoding=\"UTF-8\"?> <!--Generated by Oracle Analytics Publisher -Dataengine, datamodel:_Custom_Financials_Integration_XXDD_GL_JOURNALS_REL_DM_xdm --> <DATA_DS><G_1> <FUSION_ID>40801</FUSION_ID><SOURCE_ID>1610012</SOURCE_ID><STATUS>S</STATUS><MSG/><ACCT_SEQ_VALUE/> </G_1> </DATA_DS>".getBytes());

            List<SofiGlInterfaceCommonDO> interfaceDataList = new ArrayList<>();
            SofiGlInterfaceCommonDO commonDO = new SofiGlInterfaceCommonDO();
            commonDO.setLoadRequestId(1L);
            interfaceDataList.add(commonDO);
            List<SofiGlInterfaceHeaderDO> headerList = new ArrayList<>();
            SofiGlInterfaceHeaderDO headerDO = new SofiGlInterfaceHeaderDO();
            headerDO.setGroupId(1L);
            headerDO.setProcessDay("");
            headerList.add(headerDO);
            when(statusPollingService.checkRequestStatus(interfaceDataList.get(0).getLoadRequestId())).thenReturn(true);
            when(restTemplate.postForObject(any(), any(), any(), Optional.ofNullable(any()))).thenReturn("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\"\n" +
                    "                  xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">\n" +
                    "    <soapenv:Body>\n" +
                    "        <runDataModelResponse xmlns=\"http://xmlns.oracle.com/oxp/service/v2\">\n" +
                    "            <runDataModelReturn>\n" +
                    "                <metaDataList xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:nil=\"true\"/>\n" +
                    "                <reportBytes>\n" +
                    "                    PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPCEtLUdlbmVyYXRlZCBieSBPcmFjbGUgQW5hbHl0aWNzIFB1Ymxpc2hlciAtRGF0YWVuZ2luZSwgZGF0YW1vZGVsOl9DdXN0b21fRmluYW5jaWFsc19JbnRlZ3JhdGlvbl9YWEREX0dMX0pPVVJOQUxTX1JFTF9ETV94ZG0gLS0+CjxEQVRBX0RTPjxHXzE+CjxGVVNJT05fSUQ+NDA4MDE8L0ZVU0lPTl9JRD48U09VUkNFX0lEPjE2MTAwMTI8L1NPVVJDRV9JRD48U1RBVFVTPlM8L1NUQVRVUz48TVNHLz48QUNDVF9TRVFfVkFMVUUvPgo8L0dfMT4KPC9EQVRBX0RTPg==\n" +
                    "                </reportBytes>\n" +
                    "                <reportContentType>text/xml</reportContentType>\n" +
                    "                <reportFileID xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:nil=\"true\"/>\n" +
                    "                <reportLocale xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:nil=\"true\"/>\n" +
                    "            </runDataModelReturn>\n" +
                    "        </runDataModelResponse>\n" +
                    "    </soapenv:Body>\n" +
                    "</soapenv:Envelope>");
            List<SofiGlShenmaDO> shenmaList = new ArrayList<>();
            SofiGlShenmaDO sofiGlShenmaDO = new SofiGlShenmaDO();
            sofiGlShenmaDO.setGroupId(1L);
            shenmaList.add(sofiGlShenmaDO);
            when(sofiGlShenMaService.queryByPolizaIdsAndProcessDay(anyList(), anyString())).thenReturn(shenmaList);

            fusionResultService.processSingleFusionResultForShenma(interfaceDataList, headerList);
        }
    }

    @Test
    public void testHandleFusionResultJob_NoValidHeaderData_Shenma() {
        SofiGlInterfaceHeaderDO queryDO = new SofiGlInterfaceHeaderDO();
        queryDO.setProcessStatus(ProcessStatusEnum.IMPORTED);

        String processDay = "20230101";
        lenient().when(erpUtil.getDateDirFromInput(anyString())).thenReturn(processDay);

        SofiGlInterfaceHeaderDO headerDO = new SofiGlInterfaceHeaderDO();
        headerDO.setExternalReference("1111");
        headerDO.setProcessDay(processDay);
        headerDO.setGroupId(2222L);

        List<SofiGlInterfaceHeaderDO> headerList = Arrays.asList(headerDO);
        lenient().when(sofiGlInterfaceHeaderService.queryHeaderData(any(SofiGlInterfaceHeaderDO.class),anyString()))
                .thenReturn(headerList);

        lenient().when(sofiGlInterfaceRepository.selectByPolizaIdAndDay(any(), anyString(),any()))
                .thenReturn(Collections.emptyList());
        List<SofiGlInterfaceCommonDO> interfaceList = new ArrayList<>();
        when(sofiGlInterfaceCommonService.queryByReference5AndProcessDay(anyList(), anyString())).thenReturn(interfaceList);

        fusionResultService.handleFusionResultJob(SourceSysEnum.SHEN_MA.getCode(), "");

    }

}
