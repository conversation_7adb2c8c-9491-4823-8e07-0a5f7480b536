package com.xiaoju.corebanking.erp.adaptor.repository.converter;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaHisDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlShenmaHisPO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;

import static net.sf.json.test.JSONAssert.assertEquals;

/**
 * @Description: 类描述
 * @author: didi
 **/
@ExtendWith(MockitoExtension.class)
public class SofiGlShenmaHisModelConverterTest {
    @InjectMocks
    SofiGlShenmaHisModelConverter converter;
    String dateStrs = "2025-08-08 12:11:11";
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    BigDecimal amount = new BigDecimal(1);

    final static String TEST ="test";

    @Test
    public void convertToDoTest() throws Exception{
        SofiGlShenmaHisDO expect = new SofiGlShenmaHisDO();
        expect.setId(1L);
        expect.setProcessDay(TEST);
        expect.setFileName(TEST);
        expect.setLinkReference(TEST);
        expect.setSourceBranch(TEST);
        expect.setCcy(TEST);
        expect.setGlCode(TEST);
        expect.setEnteredDebitAmount(amount);
        expect.setEnteredCreditAmount(amount);
        expect.setProfitCenter(TEST);
        expect.setSourceModule(TEST);
        expect.setClientType(TEST);
        expect.setAmtType(TEST);
        expect.setTranType(TEST);
        expect.setEventType(TEST);
        expect.setProdType(TEST);
        expect.setPostDate(TEST);
        expect.setValueDate(TEST);
        expect.setNarrative(TEST);
        expect.setChannelSeqNo(TEST);
        expect.setIntercompany(TEST);
        expect.setFlatRate(amount);
        expect.setCustRate(amount);
        expect.setInlandOffshore(TEST);
        expect.setClientNo(TEST);
        expect.setSeqNo(TEST);
        expect.setSystemId(TEST);
        expect.setCompany(TEST);
        expect.setGroupClient(TEST);
        expect.setVoucherGroup(TEST);
        expect.setGroupId(1L);
        expect.setProcessStatus(ProcessStatusEnum.NEW);
        expect.setProcessMessage(TEST);
        expect.setObjectVersionNumber(1L);
        expect.setCreationDate(sdf.parse(dateStrs));
        expect.setCreatedBy(TEST);
        expect.setLastModifyDate(sdf.parse(dateStrs));
        expect.setLastModifiedBy(TEST);

        SofiGlShenmaHisPO sofiGlShenmaHisPO = new SofiGlShenmaHisPO();

        sofiGlShenmaHisPO.setId(1L);
        sofiGlShenmaHisPO.setProcessDay(TEST);
        sofiGlShenmaHisPO.setFileName(TEST);
        sofiGlShenmaHisPO.setLinkReference(TEST);
        sofiGlShenmaHisPO.setSourceBranch(TEST);
        sofiGlShenmaHisPO.setCcy(TEST);
        sofiGlShenmaHisPO.setGlCode(TEST);
        sofiGlShenmaHisPO.setEnteredDebitAmount(amount);
        sofiGlShenmaHisPO.setEnteredCreditAmount(amount);
        sofiGlShenmaHisPO.setProfitCenter(TEST);
        sofiGlShenmaHisPO.setSourceModule(TEST);
        sofiGlShenmaHisPO.setClientType(TEST);
        sofiGlShenmaHisPO.setAmtType(TEST);
        sofiGlShenmaHisPO.setTranType(TEST);
        sofiGlShenmaHisPO.setEventType(TEST);
        sofiGlShenmaHisPO.setProdType(TEST);
        sofiGlShenmaHisPO.setPostDate(TEST);
        sofiGlShenmaHisPO.setValueDate(TEST);
        sofiGlShenmaHisPO.setNarrative(TEST);
        sofiGlShenmaHisPO.setChannelSeqNo(TEST);
        sofiGlShenmaHisPO.setIntercompany(TEST);
        sofiGlShenmaHisPO.setFlatRate(amount);
        sofiGlShenmaHisPO.setCustRate(amount);
        sofiGlShenmaHisPO.setInlandOffshore(TEST);
        sofiGlShenmaHisPO.setClientNo(TEST);
        sofiGlShenmaHisPO.setSeqNo(TEST);
        sofiGlShenmaHisPO.setSystemId(TEST);
        sofiGlShenmaHisPO.setCompany(TEST);
        sofiGlShenmaHisPO.setGroupClient(TEST);
        sofiGlShenmaHisPO.setVoucherGroup(TEST);
        sofiGlShenmaHisPO.setGroupId(1L);
        sofiGlShenmaHisPO.setProcessStatus(ProcessStatusEnum.NEW.getCode());
        sofiGlShenmaHisPO.setProcessMessage(TEST);
        sofiGlShenmaHisPO.setObjectVersionNumber(1L);
        sofiGlShenmaHisPO.setCreationDate(sdf.parse(dateStrs));
        sofiGlShenmaHisPO.setCreatedBy(TEST);
        sofiGlShenmaHisPO.setLastModifyDate(sdf.parse(dateStrs));
        sofiGlShenmaHisPO.setLastModifiedBy(TEST);

        SofiGlShenmaHisDO result =  converter.convert(sofiGlShenmaHisPO);

        assertEquals(expect,result);
    }
    @Test
    public void convertToPoTest() throws Exception{


        SofiGlShenmaHisPO sofiGlShenmaHisPO = new SofiGlShenmaHisPO();
        sofiGlShenmaHisPO.setProcessDay(TEST);
        sofiGlShenmaHisPO.setFileName(TEST);
        sofiGlShenmaHisPO.setLinkReference(TEST);
        sofiGlShenmaHisPO.setSourceBranch(TEST);
        sofiGlShenmaHisPO.setCcy(TEST);
        sofiGlShenmaHisPO.setGlCode(TEST);
        sofiGlShenmaHisPO.setEnteredDebitAmount(amount);
        sofiGlShenmaHisPO.setEnteredCreditAmount(amount);
        sofiGlShenmaHisPO.setProfitCenter(TEST);
        sofiGlShenmaHisPO.setSourceModule(TEST);
        sofiGlShenmaHisPO.setClientType(TEST);
        sofiGlShenmaHisPO.setAmtType(TEST);
        sofiGlShenmaHisPO.setTranType(TEST);
        sofiGlShenmaHisPO.setEventType(TEST);
        sofiGlShenmaHisPO.setProdType(TEST);
        sofiGlShenmaHisPO.setPostDate(TEST);
        sofiGlShenmaHisPO.setValueDate(TEST);
        sofiGlShenmaHisPO.setNarrative(TEST);
        sofiGlShenmaHisPO.setChannelSeqNo(TEST);
        sofiGlShenmaHisPO.setIntercompany(TEST);
        sofiGlShenmaHisPO.setFlatRate(amount);
        sofiGlShenmaHisPO.setCustRate(amount);
        sofiGlShenmaHisPO.setInlandOffshore(TEST);
        sofiGlShenmaHisPO.setClientNo(TEST);
        sofiGlShenmaHisPO.setSeqNo(TEST);
        sofiGlShenmaHisPO.setSystemId(TEST);
        sofiGlShenmaHisPO.setCompany(TEST);
        sofiGlShenmaHisPO.setGroupClient(TEST);
        sofiGlShenmaHisPO.setVoucherGroup(TEST);
        sofiGlShenmaHisPO.setGroupId(1L);
        sofiGlShenmaHisPO.setProcessStatus(ProcessStatusEnum.NEW.getCode());
        sofiGlShenmaHisPO.setProcessMessage(TEST);
        sofiGlShenmaHisPO.setObjectVersionNumber(1L);
        sofiGlShenmaHisPO.setCreationDate(sdf.parse(dateStrs));
        sofiGlShenmaHisPO.setCreatedBy(TEST);
        sofiGlShenmaHisPO.setLastModifyDate(sdf.parse(dateStrs));
        sofiGlShenmaHisPO.setLastModifiedBy(TEST);



        SofiGlShenmaHisDO expect = new SofiGlShenmaHisDO();
        expect.setProcessDay(TEST);
        expect.setFileName(TEST);
        expect.setLinkReference(TEST);
        expect.setSourceBranch(TEST);
        expect.setCcy(TEST);
        expect.setGlCode(TEST);
        expect.setEnteredDebitAmount(amount);
        expect.setEnteredCreditAmount(amount);
        expect.setProfitCenter(TEST);
        expect.setSourceModule(TEST);
        expect.setClientType(TEST);
        expect.setAmtType(TEST);
        expect.setTranType(TEST);
        expect.setEventType(TEST);
        expect.setProdType(TEST);
        expect.setPostDate(TEST);
        expect.setValueDate(TEST);
        expect.setNarrative(TEST);
        expect.setChannelSeqNo(TEST);
        expect.setIntercompany(TEST);
        expect.setFlatRate(amount);
        expect.setCustRate(amount);
        expect.setInlandOffshore(TEST);
        expect.setClientNo(TEST);
        expect.setSeqNo(TEST);
        expect.setSystemId(TEST);
        expect.setCompany(TEST);
        expect.setGroupClient(TEST);
        expect.setVoucherGroup(TEST);
        expect.setGroupId(1L);
        expect.setProcessStatus(ProcessStatusEnum.NEW);
        expect.setProcessMessage(TEST);
        expect.setObjectVersionNumber(1L);
        expect.setCreationDate(sdf.parse(dateStrs));
        expect.setCreatedBy(TEST);
        expect.setLastModifyDate(sdf.parse(dateStrs));
        expect.setLastModifiedBy(TEST);
        SofiGlShenmaHisPO result =  converter.convert(expect);

        assertEquals(sofiGlShenmaHisPO,result);
    }
}
