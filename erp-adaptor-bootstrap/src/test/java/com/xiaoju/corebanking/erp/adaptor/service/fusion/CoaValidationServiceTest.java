package com.xiaoju.corebanking.erp.adaptor.service.fusion;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.ValidationRequestDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.ValidationResultDO;
import com.xiaoju.godson.http.entity.HttpClientConnection;
import com.xiaoju.godson.http.entity.HttpClientResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * <p> CoaValidationService Tester. </p>
 * <p> 2025-06-07 21:04:14.050 </p>
 *
 * <AUTHOR>
 * @version 0.0.1-SNAPSHOT
 */
@ExtendWith(MockitoExtension.class)
public class CoaValidationServiceTest {

    @InjectMocks
    private CoaValidationService coaValidationService;

    @Mock
    FusionAuthService fusionAuthService;

    @Mock
    RestTemplate restTemplate;

    private HttpClientConnection mockHttpClientConnection;
    private HttpClientResponse mockHttpClientResponse;

    @BeforeEach
    void setUp() throws NoSuchFieldException, IllegalAccessException {
        java.lang.reflect.Field fusionSoapUrlField = CoaValidationService.class.getDeclaredField("caoValidationUrl");
        fusionSoapUrlField.setAccessible(true);
        fusionSoapUrlField.set(coaValidationService, "http://test.soap.url");

        java.lang.reflect.Field fusionUsernameField = CoaValidationService.class.getDeclaredField("username");
        fusionUsernameField.setAccessible(true);
        fusionUsernameField.set(coaValidationService, "username");

        mockHttpClientConnection = mock(HttpClientConnection.class);
        mockHttpClientResponse = mock(HttpClientResponse.class);
    }

    /**
     * Method: validateCoa(requests)
     */
    @Test
    public void validateCoaTest() throws Exception {
        ValidationRequestDO request1 = new ValidationRequestDO();

        ValidationRequestDO request2 = new ValidationRequestDO();

        List<ValidationRequestDO> requests = Arrays.asList(request1, request2);

        String mockToken = "mock-jwt-token";
        when(fusionAuthService.generateJwtToken(anyString())).thenReturn(mockToken);

        String mockResponse = "<soap:Envelope><soap:Body><ValidateResponse>...</ValidateResponse></soap:Body></soap:Envelope>";
        ResponseEntity<String> mockResponseEntity = ResponseEntity.ok(mockResponse);
        HttpHeaders headers = createHeaders("12312");

        HttpEntity<String> request = new HttpEntity<>("12313", headers);
        when(restTemplate.postForEntity("123", request, String.class))
                .thenReturn(mockResponseEntity);

        try {
            coaValidationService.validateCoa(requests);
        } catch (Exception e) {

        }
    }

    private HttpHeaders createHeaders(String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_XML);
        headers.set("Authorization", "Bearer " + token);
        return headers;
    }

    @Test
    void testParseResponse_ShouldReturnParsedResults() throws Exception {
        String xml = "<ns2:Envelope xmlns:ns1=\"http://xmlns.oracle.com/apps/financials/generalLedger/accounts/codeCombinations/accountCombinationService/\" " +
                "xmlns:ns2=\"http://xmlns.oracle.com/apps/financials/generalLedger/accounts/codeCombinations/accountCombinationService/types/\">" +
                "  <ns2:result>" +
                "    <ns1:Segment1>S1</ns1:Segment1>" +
                "    <ns1:Segment2>S2</ns1:Segment2>" +
                "    <ns1:Segment3>S3</ns1:Segment3>" +
                "    <ns1:Segment4>S4</ns1:Segment4>" +
                "    <ns1:Segment5>S5</ns1:Segment5>" +
                "    <ns1:Segment6>S6</ns1:Segment6>" +
                "    <ns1:Segment7>S7</ns1:Segment7>" +
                "    <ns1:Segment8>S8</ns1:Segment8>" +
                "    <ns1:Segment9>S9</ns1:Segment9>" +
                "    <ns1:Segment10>S10</ns1:Segment10>" +
                "    <ns1:LedgerName>LEDGER1</ns1:LedgerName>" +
                "    <ns1:Status>VALID</ns1:Status>" +
                "    <ns1:CcId>12345</ns1:CcId>" +
                "    <ns1:Error>None</ns1:Error>" +
                "    <ns1:ErrorCode>0</ns1:ErrorCode>" +
                "    <ns1:FromDate>2024-01-01</ns1:FromDate>" +
                "  </ns2:result>" +
                "</ns2:Envelope>";

        Method method = CoaValidationService.class.getDeclaredMethod("parseResponse", String.class);
        method.setAccessible(true);
        List<ValidationResultDO> results = (List<ValidationResultDO>) method.invoke(coaValidationService, xml);

        assertNotNull(results);
    }

    @Test
    void testParseResponse_EmptyNode_ShouldReturnNullValue() throws Exception {
        String xml = "<ns2:Envelope xmlns:ns1=\"http://xmlns.oracle.com/apps/financials/generalLedger/accounts/codeCombinations/accountCombinationService/\" " +
                "xmlns:ns2=\"http://xmlns.oracle.com/apps/financials/generalLedger/accounts/codeCombinations/accountCombinationService/types/\">" +
                "  <ns2:result>" +
                "    <ns1:Segment1/>" +
                "    <ns1:Segment2>ABC</ns1:Segment2>" +
                "  </ns2:result>" +
                "</ns2:Envelope>";
        Method method = CoaValidationService.class.getDeclaredMethod("parseResponse", String.class);
        method.setAccessible(true);
        List<ValidationResultDO> results = (List<ValidationResultDO>) method.invoke(coaValidationService, xml);

        ValidationResultDO result = results.get(0);

        assertNotNull(result.getSegment1());
        assertEquals("ABC", result.getSegment2());
    }

    @Test
    void testGetElementValue_ElementExists_ReturnsTextContent() throws Exception {
        Document doc = buildSampleDocument();
        Element root = doc.getDocumentElement();

        String value = (String) invokePrivateMethod("getElementValue", new Class[]{Element.class, String.class}, root, "ns1:testTag");
        assertEquals("TestValue", value);
    }

    @Test
    void testGetElementValue_ElementMissing_ReturnsNull() throws Exception {
        Document doc = buildSampleDocument();
        Element root = doc.getDocumentElement();

        String value = (String) invokePrivateMethod("getElementValue", new Class[]{Element.class, String.class}, root, "ns1:missingTag");
        assertNull(value);
    }

    private Object invokePrivateMethod(String methodName, Class<?>[] paramTypes, Object... params) throws Exception {
        Method method = CoaValidationService.class.getDeclaredMethod(methodName, paramTypes);
        method.setAccessible(true);
        return method.invoke(coaValidationService, params);
    }

    private Document buildSampleDocument() throws Exception {
        String xml = "<root xmlns:ns1=\"http://example.com/ns1\">" +
                "  <ns1:testTag>TestValue</ns1:testTag>" +
                "</root>";

        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        return builder.parse(new InputSource(new StringReader(xml)));
    }


}
