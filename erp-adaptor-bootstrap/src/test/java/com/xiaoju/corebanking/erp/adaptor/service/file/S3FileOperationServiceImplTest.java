package com.xiaoju.corebanking.erp.adaptor.service.file;

import com.xiaoju.corebanking.erp.adaptor.service.common.MinioFileOperator;
import com.xiaoju.corebanking.erp.adaptor.service.file.impl.S3FileOperationServiceImpl;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

/**
 * <p> S3FileOperationServiceImpl Tester. </p>
 * <p> 2025-06-07 21:04:14.081 </p>
 *
 * <AUTHOR>
 * @version 0.0.1-SNAPSHOT
 */
@ExtendWith(MockitoExtension.class)
public class S3FileOperationServiceImplTest {

    @InjectMocks
    private S3FileOperationServiceImpl s3FileOperationService;

    @Mock
    private MinioFileOperator minioFileOperator;

    private static final String TEST_FILE_PATH = "test/path/file.txt";
    private static final String TEST_FILE_CONTENT = "测试文件内容\n第二行内容";
    private static final String TEST_MD5 = DigestUtils.md5Hex(TEST_FILE_CONTENT);


    @BeforeEach
    void setUp() {
        ByteArrayInputStream testInputStream = new ByteArrayInputStream(TEST_FILE_CONTENT.getBytes(StandardCharsets.UTF_8));
        lenient().when(minioFileOperator.download(TEST_FILE_PATH)).thenReturn(testInputStream);
        lenient().when(minioFileOperator.checkObjectExists(TEST_FILE_PATH)).thenReturn(true);
    }

    /**
     * Method: checkFileExists(filePath)
     */
    @Test
    public void checkFileExistsTest() throws Exception {
        assertTrue(s3FileOperationService.checkFileExists(TEST_FILE_PATH));

        String nonExistentPath = "non/existent/path.txt";
        when(minioFileOperator.checkObjectExists(nonExistentPath)).thenReturn(false);
        assertFalse(s3FileOperationService.checkFileExists(nonExistentPath));

        when(minioFileOperator.checkObjectExists(nonExistentPath)).thenThrow(RuntimeException.class);
        assertFalse(s3FileOperationService.checkFileExists(nonExistentPath));

    }

    /**
     * Method: downloadFileAsBytes(filePath)
     */
    @Test
    public void downloadFileAsBytesTest() throws Exception {
        byte[] result = s3FileOperationService.downloadFileAsBytes(TEST_FILE_PATH);
        assertNotNull(result);

        assertNull(s3FileOperationService.downloadFileAsBytes(null));

        String failPath = "fail/path.txt";
        when(minioFileOperator.download(failPath)).thenReturn(null);
        assertNull(s3FileOperationService.downloadFileAsBytes(failPath));

        String exceptionPath = "exception/path.txt";
        when(minioFileOperator.download(exceptionPath)).thenThrow(RuntimeException.class);
        assertNull(s3FileOperationService.downloadFileAsBytes(exceptionPath));
    }

    /**
     * Method: readFileLines(filePath)
     */
    @Test
    public void readFileLinesTest() throws Exception {
        List<String> lines = s3FileOperationService.readFileLines(TEST_FILE_PATH);
        assertEquals(2, lines.size());

        List<String> limitedLines = s3FileOperationService.readFileLines(TEST_FILE_PATH, 1);
        assertNotNull(limitedLines);

        String exceptionPath = "exception/path.txt";
        when(minioFileOperator.download(exceptionPath)).thenThrow(new RuntimeException("测试异常"));
        List<String> emptyLines = s3FileOperationService.readFileLines(exceptionPath);
        assertTrue(emptyLines.isEmpty());
    }

    /**
     * Method: readFileLines(filePath, maxLines)
     */
    @Test
    public void readFileLinesFilePathMaxLinesTest() throws Exception {
        String firstLine = s3FileOperationService.readFileFirstLine(TEST_FILE_PATH);
        assertEquals("测试文件内容", firstLine);

        String emptyFilePath = "empty/file.txt";
        when(minioFileOperator.download(emptyFilePath)).thenReturn(new ByteArrayInputStream(new byte[0]));
        assertNull(s3FileOperationService.readFileFirstLine(emptyFilePath));
    }

    /**
     * Method: readFileFirstLine(filePath)
     */
    @Test
    public void readFileFirstLineTest() throws Exception {
        assertTrue(s3FileOperationService.verifyMd5(TEST_FILE_PATH, TEST_MD5));

        assertFalse(s3FileOperationService.verifyMd5(TEST_FILE_PATH, "wrongMd5"));

        assertFalse(s3FileOperationService.verifyMd5(null, TEST_MD5));
        assertFalse(s3FileOperationService.verifyMd5(TEST_FILE_PATH, null));

        String exceptionPath = "exception/path.txt";
        when(minioFileOperator.download(exceptionPath)).thenThrow(new RuntimeException("测试异常"));
        assertFalse(s3FileOperationService.verifyMd5(exceptionPath, TEST_MD5));
    }

    /**
     * Method: verifyMd5(filePath, expectedMd5)
     */
    @Test
    public void verifyMd5Test() throws Exception {
        String checkCsvContent = "filename,count\nfile1.txt,100\nfile2.txt,200\ninvalid_line\nfile3.txt,invalid";
        String checkCsvPath = "check.csv";
        when(minioFileOperator.download(checkCsvPath)).thenReturn(
                new ByteArrayInputStream(checkCsvContent.getBytes(StandardCharsets.UTF_8)));

        Map<String, Integer> result = s3FileOperationService.readSubfilesWithCount(checkCsvPath);
        assertEquals(2, result.size());

        String emptyFilePath = "empty/check.csv";
        when(minioFileOperator.download(emptyFilePath)).thenReturn(new ByteArrayInputStream(new byte[0]));
        Map<String, Integer> emptyResult = s3FileOperationService.readSubfilesWithCount(emptyFilePath);
        assertTrue(emptyResult.isEmpty());

        String exceptionPath = "exception/check.csv";
        when(minioFileOperator.download(exceptionPath)).thenThrow(new RuntimeException("测试异常"));
        Map<String, Integer> exceptionResult = s3FileOperationService.readSubfilesWithCount(exceptionPath);
        assertTrue(exceptionResult.isEmpty());
    }

    /**
     * Method: readSubfilesWithCount(checkCsvKey)
     */
    @Test
    public void readSubfilesWithCountTest() throws Exception {
        long size = s3FileOperationService.getFileSize(TEST_FILE_PATH);
        assertEquals(TEST_FILE_CONTENT.getBytes(StandardCharsets.UTF_8).length, size);

        s3FileOperationService.getFileSize(null);

        String failPath = "fail/path.txt";
        when(minioFileOperator.download(failPath)).thenReturn(null);
        s3FileOperationService.getFileSize(failPath);
    }

    /**
     * Method: getFileSize(filePath)
     */
    @Test
    public void getFileSizeTest() throws Exception {
        InputStream result = s3FileOperationService.download(TEST_FILE_PATH);
        assertNotNull(result);
    }
}
