package com.xiaoju.corebanking.erp.adaptor.service.file;

import com.xiaoju.corebanking.erp.adaptor.common.utils.ErpUtil;
import com.xiaoju.corebanking.erp.adaptor.service.file.impl.ShenmaFilePreprocessServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


@ExtendWith(MockitoExtension.class)
class ShenmaFilePreprocessServiceImplTest {
    @InjectMocks
    ShenmaFilePreprocessServiceImpl shenmaFilePreprocessService;
    @Mock
    private ShenmaDataProcessService shenmaDataProcessService;

    @Mock
    private ErpUtil erpUtil;
    @Test
    void processShenmaFile() {
        String filePath =  "2025-08-12";
        String param = "{\"aa\":\"bb\"}";
        shenmaFilePreprocessService.processShenmaFile(filePath,param);
    }

    @Test
    void processAllShenmaFiles() {
        String filePath =  "../../../../testFile";
        String processDay = "2025-08-12";
        shenmaFilePreprocessService.processAllShenmaFiles(filePath, processDay);
    }

    @Test
    void checkFileValidity() {
        String filePath =  "../../../../testFile";
        shenmaFilePreprocessService.checkFileValidity(filePath);
    }
}