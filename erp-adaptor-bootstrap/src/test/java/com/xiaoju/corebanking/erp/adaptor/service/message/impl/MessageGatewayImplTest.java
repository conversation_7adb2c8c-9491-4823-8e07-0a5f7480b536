package com.xiaoju.corebanking.erp.adaptor.service.message.impl;

import com.xiaoju.corebanking.erp.adaptor.common.apollo.ErpCommonApolloConfig;
import com.xiaoju.corebanking.erp.adaptor.common.domain.ErpAdaptorMessageRequest;
import com.xiaoju.corebanking.erp.adaptor.integration.MessageRpc;
import com.xiaoju.digitalbank.enums.CountryEnum;
import com.xiaoju.digitalbank.exception.BaseException;
import com.xiaoju.digitalbank.message.platform.req.PlatformSingleMessageRequest;
import com.xiaoju.digitalbank.message.platform.res.PlatformSingleMessageResponse;
import com.xiaoju.digitalbank.util.tools.ElvishDateUtils;
import com.xiaoju.godson.common.utils.JsonUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class MessageGatewayImplTest {
    private static final String EMPTY_JSON_STRING = "{}";
    @InjectMocks
    MessageGatewayImpl messageGateway;
    @Mock
    MessageRpc messageRpc;

    @Mock
    private ErpCommonApolloConfig erpCommonApolloConfig;

    @Test
    void sendEmail() {
        ErpAdaptorMessageRequest request = new ErpAdaptorMessageRequest();
        request.setAttachments(Arrays.asList("aafs"));

        PlatformSingleMessageResponse response = new PlatformSingleMessageResponse();
        when( messageRpc.sendMessagePush(buildPlatformMessageRequest(request))).thenReturn(response);
        when( messageRpc.checkMessageReceived(response)).thenReturn(true);
        messageGateway.sendEmail(request);
    }
    @Test
    void sendEmailNull() {
        ErpAdaptorMessageRequest request = new ErpAdaptorMessageRequest();
        request.setAttachments(Arrays.asList("aafs"));
        assertThrows( BaseException.class,()->messageGateway.sendEmail(request));

    }
    private PlatformSingleMessageRequest buildPlatformMessageRequest(ErpAdaptorMessageRequest erpAdaptorMessageRequest){
        PlatformSingleMessageRequest platformSingleMessageRequest = new PlatformSingleMessageRequest();
        //nolint
        String requestId = "ERP" + ElvishDateUtils.formatToYYYYMMDDHHMMSS_Compact(System.currentTimeMillis());
        platformSingleMessageRequest.setTemplateId(erpCommonApolloConfig.getTemplateId());
        platformSingleMessageRequest.setRequestId(requestId);
        platformSingleMessageRequest.setReceiver(erpCommonApolloConfig.getReceiver());
        platformSingleMessageRequest.setExtraInfo(JsonUtil.toString(erpAdaptorMessageRequest));
        platformSingleMessageRequest.setLang(CountryEnum.MEX.getI18n());
        platformSingleMessageRequest.setTemplateParam(EMPTY_JSON_STRING);
        return platformSingleMessageRequest;
    }
}