package com.xiaoju.corebanking.erp.adaptor.service.file;

import com.xiaoju.corebanking.erp.adaptor.common.utils.ErpUtil;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlFileControlRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceHeaderRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceRepository;
import com.xiaoju.corebanking.erp.adaptor.service.file.impl.FilePreprocessServiceImpl;
import com.xiaoju.digitalbank.ddd.po.ExecDataResult;
import com.xiaoju.digitalbank.ddd.po.ExecResult;
import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import org.apache.commons.lang.reflect.FieldUtils;
import org.junit.Rule;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.quality.Strictness;

import java.io.ByteArrayInputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * <p> FilePreprocessServiceImpl Tester. </p>
 * <p> 2025-06-07 21:04:14.078 </p>
 *
 * <AUTHOR>
 * @version 0.0.1-SNAPSHOT
 */
@ExtendWith(MockitoExtension.class)
public class FilePreprocessServiceImplTest {

    @Spy
    @InjectMocks
    private FilePreprocessServiceImpl filePreprocessService;

    @Mock
    private S3FileOperationService s3FileOperationService;

    @Mock
    private DataProcessService dataProcessService;

    @Mock
    private SofiGlFileControlRepository sofiGlFileControlRepository;

    @Mock
    private SofiGlInterfaceRepository sofiGlInterfaceRepository;

    @Mock
    private SofiGlInterfaceHeaderRepository sofiGlInterfaceHeaderRepository;

    @Mock
    private ErpUtil erpUtil;

    @Mock
    private ThreadPoolExecutor mockExecutor;

    @Mock
    private Future<Boolean> mockFuture;

    @Rule
    public MockitoRule rule = MockitoJUnit.rule().strictness(Strictness.LENIENT);


    /**
     *
     * Method: processFile(filePath)
     */
    @Test
    public void processFileTest() throws Exception {
        String inputPath = "test/path";
        String processDate = "20250701";
        String fullPath = "/tmp/test/path/20250701";

        lenient().when(erpUtil.getDateDirFromInput(inputPath)).thenReturn(processDate);
        lenient().when(erpUtil.buildFullPath(inputPath, processDate)).thenReturn(fullPath);
        lenient().when(filePreprocessService.checkFileValidity(fullPath)).thenReturn(true);

        ExecResult headerResult = ExecResult.success();
        lenient().when(dataProcessService.createInterfaceHeaderRecords(eq(processDate),
                eq(sofiGlInterfaceRepository), eq(sofiGlInterfaceHeaderRepository)))
                .thenReturn(headerResult);

        ExecResult result = filePreprocessService.processFile(inputPath,null);

        assertFalse(result.isSuccess());

        doReturn(true)
                .doReturn(true)
                .when(s3FileOperationService).checkFileExists(anyString());
        ExecResult result1 = filePreprocessService.processFile(inputPath,null);
        assertFalse(result1.isSuccess());

        lenient().when(s3FileOperationService.readFileFirstLine(anyString())).thenReturn("test");
        ExecResult result2 = filePreprocessService.processFile(inputPath,null);
        assertFalse(result2.isSuccess());

        lenient().when(s3FileOperationService.verifyMd5(anyString(), anyString())).thenReturn(true);
        ExecResult result3 = filePreprocessService.processFile(inputPath,null);
        assertFalse(result3.isSuccess());

        lenient().when(s3FileOperationService.readSubfilesWithCount(anyString())).thenReturn(new HashMap<String,  Integer>(){{
            put("test.csv", 10);
        }});

        lenient().when(dataProcessService.processSubFile(anyString(),anyString(), anyInt(), anyString(), any(), any()))
                .thenReturn(true);

        ExecResult result4 = filePreprocessService.processFile(inputPath,null);
        assertFalse(result4.isSuccess());

        lenient().when(s3FileOperationService.readFileFirstLine(anyString())).thenThrow(RuntimeException.class);
        ExecResult result5 = filePreprocessService.processFile(inputPath,null);
        assertFalse(result5.isSuccess());

    }


    @Test
    public void testGetAllFileWithTimeoutException() throws Exception {
        String filePath = "test/path/20230101";

        lenient().when(s3FileOperationService.readSubfilesWithCount(anyString())).thenReturn(new HashMap<String, Integer>() {{
            put("test1.csv", 100);
            put("test2.csv", 200);
        }});

        lenient().when(s3FileOperationService.checkFileExists(anyString())).thenReturn(true);

        lenient().when(s3FileOperationService.readFileFirstLine(anyString())).thenReturn("test");

        lenient().when(s3FileOperationService.verifyMd5(anyString(), anyString())).thenReturn(true);

        lenient().when(erpUtil.getDateDirFromInput(filePath)).thenReturn("20230101");

        lenient().when(s3FileOperationService.getFileSize(anyString())).thenReturn(1024L);

        String fileContent = "header1,header2,header3\nvalue1,value2,value3";
        lenient().when(s3FileOperationService.download(anyString())).thenAnswer(invocation ->
            new ByteArrayInputStream(fileContent.getBytes()));

        lenient().when(dataProcessService.processSubFile(anyString(),anyString(), anyInt(), anyString(), any(), any()))
                .thenReturn(true);

       filePreprocessService.processFile(filePath,null);
    }



    @Test
    public void testGetAllFileWithRealThreadPoolExecution() throws Exception {
        FilePreprocessServiceImpl realService = new FilePreprocessServiceImpl();

        FieldUtils.writeField(realService, "s3FileOperationService", s3FileOperationService, true);
        FieldUtils.writeField(realService, "dataProcessService", dataProcessService, true);
        FieldUtils.writeField(realService, "erpUtil", erpUtil, true);
        FieldUtils.writeField(realService, "sofiGlInterfaceRepository", sofiGlInterfaceRepository, true);
        FieldUtils.writeField(realService, "sofiGlFileControlRepository", sofiGlFileControlRepository, true);

        String filePath = "test/path/20230101";

        when(s3FileOperationService.readSubfilesWithCount(anyString())).thenReturn(new HashMap<String, Integer>() {{
            put("test1.csv", 100);
            put("test2.csv", 200);
        }});

        when(erpUtil.getDateDirFromInput(filePath)).thenReturn("20230101");

        doReturn(true)
                .when(dataProcessService).processSubFile(anyString(),eq("test1.csv"), anyInt(), anyString(), any(), any());
        doReturn(true)
                .when(dataProcessService).processSubFile(anyString(),eq("test2.csv"), anyInt(), anyString(), any(), any());

        filePreprocessService.processFile(any(),anyString());
    }

    @Test
    public void testThreadPoolShutdownTimeout() throws Exception {
        String filePath = "test/path";
        String processDate = "20240101";
        String fullPath = "/full/test/path";

        when(erpUtil.getDateDirFromInput(filePath)).thenReturn(processDate);
        when(erpUtil.buildFullPath(filePath, processDate)).thenReturn(fullPath);
        when(s3FileOperationService.checkFileExists(anyString())).thenReturn(true);
        when(s3FileOperationService.readFileFirstLine(anyString())).thenReturn("md5hash");
        when(s3FileOperationService.verifyMd5(anyString(), anyString())).thenReturn(true);

        Map<String, Integer> subFiles = new HashMap<>();
        subFiles.put("test.csv", 1);
        when(s3FileOperationService.readSubfilesWithCount(anyString())).thenReturn(subFiles);
        when(dataProcessService.processSubFile(anyString(),anyString(), anyInt(), anyString(), any(), any())).thenReturn(true);
        when(dataProcessService.createInterfaceHeaderRecords(anyString(), any(), any())).thenReturn(ExecResult.success());

        ExecResult result = filePreprocessService.processFile(filePath,null);

        assertFalse(result.isSuccess());
    }

    @Test
    void processFile_FileValidityCheckFails() {
        String filePath = "test/path";
        String processDate = "20240101";
        String fullPath = "/full/test/path";

        when(erpUtil.getDateDirFromInput(filePath)).thenReturn(processDate);
        when(erpUtil.buildFullPath(filePath, processDate)).thenReturn(fullPath);
        when(s3FileOperationService.checkFileExists(anyString())).thenReturn(false);

        ExecResult result = filePreprocessService.processFile(filePath,null);

        assertFalse(result.isSuccess());
    }

    @Test
    void getAllFile_Success() {
        String filePath = "test/path";
        String processDate = "20240101";
        Map<String, Integer> subFiles = new HashMap<>();
        subFiles.put("test1.csv", 100);
        subFiles.put("test2.csv", 200);

        when(erpUtil.getDateDirFromInput(filePath)).thenReturn(processDate);
        when(s3FileOperationService.readSubfilesWithCount(anyString())).thenReturn(subFiles);
        when(dataProcessService.processSubFile(anyString(),anyString(), anyInt(), anyString(), any(), any())).thenReturn(true);

        ExecDataResult<String> result = filePreprocessService.getAllFile(filePath);

        assertTrue(result.isSuccess());
    }

    @Test
    void getAllFile_EmptySubFilesList() {
        String filePath = "test/path";
        when(s3FileOperationService.readSubfilesWithCount(anyString())).thenReturn(new HashMap<>());

        ExecDataResult<String> result = filePreprocessService.getAllFile(filePath);

        assertFalse(result.isSuccess());
    }

    @Test
    void getAllFile_SubFileProcessingFails() {
        String filePath = "test/path";
        String processDate = "20240101";
        Map<String, Integer> subFiles = new HashMap<>();
        subFiles.put("test.csv", 100);

        when(erpUtil.getDateDirFromInput(filePath)).thenReturn(processDate);
        when(s3FileOperationService.readSubfilesWithCount(anyString())).thenReturn(subFiles);
        when(dataProcessService.processSubFile(anyString(),anyString(), anyInt(), anyString(), any(), any())).thenReturn(false);

        ExecDataResult<String> result = filePreprocessService.getAllFile(filePath);

        assertFalse(result.isSuccess());
    }

    @Test
    void getAllFile_ExceptionHandling() {
        String filePath = "test/path";
        when(s3FileOperationService.readSubfilesWithCount(anyString())).thenThrow(new RuntimeException("测试异常"));

        ExecDataResult<String> result = filePreprocessService.getAllFile(filePath);

        assertFalse(result.isSuccess());
    }

    @Test
    void processFile_Success() {
        String filePath = "test/path";
        String processDate = "20240101";
        String fullPath = "/full/test/path";

        when(erpUtil.getDateDirFromInput(filePath)).thenReturn(processDate);
        when(erpUtil.buildFullPath(filePath, processDate)).thenReturn(fullPath);
        doReturn(true).when(filePreprocessService).checkFileValidity(fullPath);

        ExecDataResult<String> getAllFileResult = ExecDataResult.success("success");
        doReturn(getAllFileResult).when(filePreprocessService).getAllFile(fullPath);

        when(dataProcessService.createInterfaceHeaderRecords(processDate,
                sofiGlInterfaceRepository, sofiGlInterfaceHeaderRepository))
                .thenReturn(ExecResult.success());

        ExecResult result = filePreprocessService.processFile(filePath,null);

        assertTrue(result.isSuccess());
    }

    @Test
    void processFile_GetAllFileFails() {
        String filePath = "test/path";
        String processDate = "20240101";
        String fullPath = "/full/test/path";

        when(erpUtil.getDateDirFromInput(filePath)).thenReturn(processDate);
        when(erpUtil.buildFullPath(filePath, processDate)).thenReturn(fullPath);
        doReturn(true).when(filePreprocessService).checkFileValidity(fullPath);

        ExecDataResult<String> getAllFileResult = ExecDataResult.error(CommonErrorNo.FILE_ERROR, "获取文件失败");
        doReturn(getAllFileResult).when(filePreprocessService).getAllFile(fullPath);

        ExecResult result = filePreprocessService.processFile(filePath,null);

        assertFalse(result.isSuccess());
    }

    @Test
    void processFile_CreateHeaderRecordsFails() {
        String filePath = "test/path";
        String processDate = "20240101";
        String fullPath = "/full/test/path";

        when(erpUtil.getDateDirFromInput(filePath)).thenReturn(processDate);
        when(erpUtil.buildFullPath(filePath, processDate)).thenReturn(fullPath);
        doReturn(true).when(filePreprocessService).checkFileValidity(fullPath);

        ExecDataResult<String> getAllFileResult = ExecDataResult.success("success");
        doReturn(getAllFileResult).when(filePreprocessService).getAllFile(fullPath);

        when(dataProcessService.createInterfaceHeaderRecords(processDate,
                sofiGlInterfaceRepository, sofiGlInterfaceHeaderRepository))
                .thenReturn(ExecResult.error(CommonErrorNo.DB_DATA_ERROR, "创建header记录失败"));

        ExecResult result = filePreprocessService.processFile(filePath,null);

        assertFalse(result.isSuccess());
    }

    @Test
    void processFile_ThrowsException() {
        String filePath = "test/path";
        when(erpUtil.getDateDirFromInput(filePath)).thenThrow(new RuntimeException("测试异常"));

        ExecResult result = filePreprocessService.processFile(filePath,null);

        assertFalse(result.isSuccess());
    }
}
