package com.xiaoju.corebanking.erp.adaptor.service.file.enums;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlAcctHistDO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static com.xiaoju.corebanking.erp.adaptor.service.file.enums.SofiGlAcctEnum.*;

@ExtendWith(MockitoExtension.class)
class SofiGlAcctEnumTest {


    @Test
    void setField() {
        SofiGlAcctHistDO entity = new SofiGlAcctHistDO();
        String fieldName = "fileName";
        String value = null;
        SofiGlAcctEnum.setField(entity,fieldName,value);
    }
    @Test
    void setFieldNotNull() {
        SofiGlAcctHistDO entity = new SofiGlAcctHistDO();
        String fieldName = SofiGlAcctEnum.ACCT_NAME.name();
        String value = "ACCT_NAME";
        SofiGlAcctEnum.setField(entity,fieldName,value);
        SofiGlAcctEnum.setField(entity,SofiGlAcctEnum.INTERNAL_KEY.name(),"INTERNAL_KEY");
        SofiGlAcctEnum.setField(entity, BRANCH.name(),"INTERNAL_KEY");
        SofiGlAcctEnum.setField(entity, CCY.name(),"INTERNAL_KEY");
        SofiGlAcctEnum.setField(entity, GL_CODE.name(),"INTERNAL_KEY");
        SofiGlAcctEnum.setField(entity, CLIENT_NO.name(),"INTERNAL_KEY");
        SofiGlAcctEnum.setField(entity, PROFIT_CENTER.name(),"INTERNAL_KEY");
        SofiGlAcctEnum.setField(entity,ACCT_CLOSE_REASON.name(),"ACCT_CLOSE_REASON");
        SofiGlAcctEnum.setField(entity,GL_ACCT_NO.name(),"ACCT_CLOSE_REASON");
        SofiGlAcctEnum.setField(entity,ACCT_STATUS.name(),"ACCT_CLOSE_REASON");

        SofiGlAcctEnum.setField(entity,SofiGlAcctEnum.ACCT_OPEN_DATE.name(),"ACCT_OPEN_DATE");
        SofiGlAcctEnum.setField(entity,SofiGlAcctEnum.OPEN_TRAN_DATE.name(),"OPEN_TRAN_DATE");
        SofiGlAcctEnum.setField(entity,SofiGlAcctEnum.ACCT_CLOSE_DATE.name(),"ACCT_CLOSE_DATE");
        SofiGlAcctEnum.setField(entity,SofiGlAcctEnum.ACTUAL_BAL.name(),"ACTUAL_BAL");
        SofiGlAcctEnum.setField(entity,SofiGlAcctEnum.DR_ACTUAL_BAL.name(),"DR_ACTUAL_BAL");
        SofiGlAcctEnum.setField(entity,SofiGlAcctEnum.CR_ACTUAL_BAL.name(),"CR_ACTUAL_BAL");

        SofiGlAcctEnum.setField(entity,SofiGlAcctEnum.PERIOD_NO.name(),"LAST_CHANGE_DATE");
        SofiGlAcctEnum.setField(entity,SofiGlAcctEnum.USER_ID.name(),"LAST_CHANGE_DATE");
        SofiGlAcctEnum.setField(entity,SofiGlAcctEnum.MANUAL_ACCOUNT.name(),"LAST_CHANGE_DATE");
        SofiGlAcctEnum.setField(entity,SofiGlAcctEnum.OD_FACILITY.name(),"LAST_CHANGE_DATE");
        SofiGlAcctEnum.setField(entity,SofiGlAcctEnum.ACCT_NAME.name(),"LAST_CHANGE_DATE");
        SofiGlAcctEnum.setField(entity,SofiGlAcctEnum.COMPANY.name(),"LAST_CHANGE_DATE");
        SofiGlAcctEnum.setField(entity,SofiGlAcctEnum.TRAN_TIMESTAMP.name(),"LAST_CHANGE_DATE");
        SofiGlAcctEnum.setField(entity,SofiGlAcctEnum.SYSTEM_ID.name(),"LAST_CHANGE_DATE");
        SofiGlAcctEnum.setField(entity,SofiGlAcctEnum.GROUP_CLIENT.name(),"LAST_CHANGE_DATE");


    }
    @Test
    void setFieldSEQ_NO() {
        SofiGlAcctHistDO entity = new SofiGlAcctHistDO();
        String value = "11";
        SofiGlAcctEnum.setField(entity, SEQ_NO.name(),value);
    }
    @Test
    void testDate() {
        SofiGlAcctHistDO entity = new SofiGlAcctHistDO();
        String value = "2025-08-08 08:08:08";
        SofiGlAcctEnum.setField(entity, ACCT_OPEN_DATE.name(),value);
        SofiGlAcctEnum.setField(entity, OPEN_TRAN_DATE.name(),value);
        SofiGlAcctEnum.setField(entity, ACCT_CLOSE_DATE.name(),value);
        SofiGlAcctEnum.setField(entity, LAST_CHANGE_DATE.name(),value);
        SofiGlAcctEnum.setField(entity, BACKUP_DATE.name(),value);



    }
    @Test
    void testBigdecimal() {
        SofiGlAcctHistDO entity = new SofiGlAcctHistDO();
        String value = "11";
        SofiGlAcctEnum.setField(entity, ACTUAL_BAL.name(),value);
        SofiGlAcctEnum.setField(entity, DR_ACTUAL_BAL.name(),value);
        SofiGlAcctEnum.setField(entity, CR_ACTUAL_BAL.name(),value);
        SofiGlAcctEnum.setField(entity, MTD_BAL.name(),value);
        SofiGlAcctEnum.setField(entity, YTD_BAL.name(),value);
        SofiGlAcctEnum.setField(entity, AGG_BAL_CTD.name(),value);
        SofiGlAcctEnum.setField(entity, AGG_BAL_MTD.name(),value);
        SofiGlAcctEnum.setField(entity, AGG_BAL_YTD.name(),value);
        SofiGlAcctEnum.setField(entity,SofiGlAcctEnum.DR_TRAN_AMT.name(),value);
        SofiGlAcctEnum.setField(entity,SofiGlAcctEnum.CR_TRAN_AMT.name(),value);

        SofiGlAcctEnum.setField(entity,SofiGlAcctEnum.LAST_ACTUAL_BAL.name(),value);
        SofiGlAcctEnum.setField(entity,SofiGlAcctEnum.LAST_DR_ACTUAL_BAL.name(),value);
        SofiGlAcctEnum.setField(entity,SofiGlAcctEnum.LAST_CR_ACTUAL_BAL.name(),value);
    }
    @Test
    void testInt() {
        SofiGlAcctHistDO entity = new SofiGlAcctHistDO();
        String value = "11";
        SofiGlAcctEnum.setField(entity, CTD_DAYS.name(),value);
        SofiGlAcctEnum.setField(entity, MTD_DAYS.name(),value);
        SofiGlAcctEnum.setField(entity, YTD_DAYS.name(),value);

    }

    @Test
    void values() {
        SofiGlAcctEnum[] values1 = INTERNAL_KEY.values();
        SofiGlAcctEnum[] values = ACCT_OPEN_DATE.values();
        SofiGlAcctEnum[] values2 = BRANCH.values();
        SofiGlAcctEnum[] values3 = CCY.values();
        SofiGlAcctEnum[] values4 = GL_CODE.values();
        SofiGlAcctEnum[] values5 = CLIENT_NO.values();
        SofiGlAcctEnum[] values6 = PROFIT_CENTER.values();
        SofiGlAcctEnum[] values7 = SEQ_NO.values();
        SofiGlAcctEnum[] values8 = ACCT_OPEN_DATE.values();
        SofiGlAcctEnum[] values9 = OPEN_TRAN_DATE.values();
        SofiGlAcctEnum[] values10 = ACCT_CLOSE_DATE.values();
        SofiGlAcctEnum[] values11 = ACCT_CLOSE_REASON.values();
        SofiGlAcctEnum[] values12 = GL_ACCT_NO.values();
        SofiGlAcctEnum[] values13 = ACCT_STATUS.values();
        SofiGlAcctEnum[] values14 = ACTUAL_BAL.values();
        SofiGlAcctEnum[] values15 = DR_ACTUAL_BAL.values();
        SofiGlAcctEnum[] values16 = CR_ACTUAL_BAL.values();
        SofiGlAcctEnum[] values17 = CTD_DAYS.values();
        SofiGlAcctEnum[] values18 = MTD_DAYS.values();
        SofiGlAcctEnum[] values19 = YTD_DAYS.values();

    }

    @Test
    void valueOf() {
        SofiGlAcctEnum.valueOf(SofiGlAcctEnum.ACCT_OPEN_DATE.name());
    }
}