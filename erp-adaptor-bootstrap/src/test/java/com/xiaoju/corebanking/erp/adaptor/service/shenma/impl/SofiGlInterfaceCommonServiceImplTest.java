package com.xiaoju.corebanking.erp.adaptor.service.shenma.impl;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceCommonRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceCommonDO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ExtendWith(MockitoExtension.class)
class SofiGlInterfaceCommonServiceImplTest {
    @InjectMocks
    SofiGlInterfaceCommonServiceImpl sofiGlInterfaceCommonService;
    @Mock
    private SofiGlInterfaceCommonRepository sofiGlInterfaceCommonRepository;

    @Test
    void insertSelective() {
        SofiGlInterfaceCommonDO sofiGlInterfaceCommonDO = new SofiGlInterfaceCommonDO();
        sofiGlInterfaceCommonDO.setProcessStatus(ProcessStatusEnum.PROCESSING);
        sofiGlInterfaceCommonService.insertSelective(sofiGlInterfaceCommonDO);
    }

    @Test
    void batchInsert() {
        List<SofiGlInterfaceCommonDO> list = new ArrayList<>();
        SofiGlInterfaceCommonDO sofiGlInterfaceCommonDO = new SofiGlInterfaceCommonDO();
        sofiGlInterfaceCommonDO.setProcessStatus(ProcessStatusEnum.PROCESSING);
        list.add(sofiGlInterfaceCommonDO);
        sofiGlInterfaceCommonService.batchInsert(list);
    }

    @Test
    void queryGroupIds() {
        String processDay = "2025-08-18";
        sofiGlInterfaceCommonService.queryGroupIds(1,20,processDay);
    }
    @Test
    void queryByReference5AndProcessDay() {
        List<String> externalReferenceList = new ArrayList<>();
        String processDay = "2025-08-18";
        sofiGlInterfaceCommonService.queryByReference5AndProcessDay(externalReferenceList,processDay);
    }
    @Test
    void updateInterfaceCommon() {
        SofiGlInterfaceCommonDO commonDO = new SofiGlInterfaceCommonDO();
        commonDO.setSystemCode("test");
        commonDO.setProcessDay("test");
        commonDO.setAccountingDate(new Date());
        commonDO.setPeriodName("test");
        commonDO.setLedgerId(1L);
        commonDO.setLedgerName("test");
        commonDO.setCurrencyCode("test");
        commonDO.setJournalCategory("test");
        commonDO.setJournalSource("test");
        commonDO.setJournalSourceName("test");
        commonDO.setReference1("test");
        commonDO.setReference2("test");
        commonDO.setReference3("test");
        commonDO.setReference4("test");
        commonDO.setReference5("test");
        commonDO.setReference6("test");
        commonDO.setReference7("test");
        commonDO.setReference8("test");
        commonDO.setReference9("test");
        commonDO.setReference10("test");
        commonDO.setReference21("test");
        commonDO.setReference22("test");
        commonDO.setReference23("test");
        commonDO.setReference24("test");
        commonDO.setReference25("test");
        commonDO.setReference26("test");
        commonDO.setReference27("test");
        commonDO.setReference28("test");
        commonDO.setReference29("test");
        commonDO.setReference30("test");
        commonDO.setSegment1("test");
        commonDO.setSegment2("test");
        commonDO.setSegment3("test");
        commonDO.setSegment4("test");
        commonDO.setSegment5("test");
        commonDO.setSegment6("test");
        commonDO.setSegment7("test");
        commonDO.setSegment8("test");
        commonDO.setSegment9("test");
        commonDO.setSegment10("test");
        commonDO.setEnteredDr(new BigDecimal(0));
        commonDO.setEnteredCr(new BigDecimal(0));
        commonDO.setAccountedDr(new BigDecimal(0));
        commonDO.setAccountedCr(new BigDecimal(0));
        commonDO.setGroupId(1L);
        commonDO.setCurrencyConversionDate(new Date());
        commonDO.setCurrencyConversionRate(new BigDecimal(0));
        commonDO.setCurrencyConversionType("test");
        commonDO.setAttributeCategory("test");
        commonDO.setHeaderAttribute1("test");
        commonDO.setHeaderAttribute2("test");
        commonDO.setHeaderAttribute3("test");
        commonDO.setHeaderAttribute4("test");
        commonDO.setHeaderAttribute5("test");
        commonDO.setHeaderAttribute6("test");
        commonDO.setHeaderAttribute7("test");
        commonDO.setHeaderAttribute8("test");
        commonDO.setHeaderAttribute9("test");
        commonDO.setHeaderAttribute10("test");
        commonDO.setHeaderAttribute11("test");
        commonDO.setHeaderAttribute12("test");
        commonDO.setHeaderAttribute13("test");
        commonDO.setHeaderAttribute14("test");
        commonDO.setHeaderAttribute15("test");
        commonDO.setAttributeCategory3("test");
        commonDO.setLineAttribute1("test");
        commonDO.setLineAttribute2("test");
        commonDO.setLineAttribute3("test");
        commonDO.setLineAttribute4("test");
        commonDO.setLineAttribute5("test");
        commonDO.setLineAttribute6("test");
        commonDO.setLineAttribute7("test");
        commonDO.setLineAttribute8("test");
        commonDO.setLineAttribute9("test");
        commonDO.setLineAttribute10("test");
        commonDO.setLineAttribute11("test");
        commonDO.setLineAttribute12("test");
        commonDO.setLineAttribute13("test");
        commonDO.setLineAttribute14("test");
        commonDO.setLineAttribute15("test");
        commonDO.setLineAttribute16("test");
        commonDO.setLineAttribute17("test");
        commonDO.setLineAttribute18("test");
        commonDO.setLineAttribute19("test");
        commonDO.setLineAttribute20("test");
        commonDO.setProcessStatus(ProcessStatusEnum.PROCESSING);
        commonDO.setProcessMessage("test");
        commonDO.setJeHeaderId(1L);
        commonDO.setJournalName("test");
        commonDO.setJeLineNum(1L);
        commonDO.setDocumentId(1L);
        commonDO.setLoadRequestId(1L);
        commonDO.setImportRequestId(1L);
        commonDO.setObjectVersionNumber(1L);
        commonDO.setCreationDate(new Date());
        commonDO.setCreatedBy("test");
        commonDO.setLastModifyDate(new Date());
        commonDO.setLastModifiedBy("test");

        SofiGlInterfaceCommonDO commonDO2 = new SofiGlInterfaceCommonDO();
        commonDO2.setSystemCode("test");
        commonDO2.setProcessDay("test");
        commonDO2.setAccountingDate(new Date());
        commonDO2.setPeriodName("test");
        commonDO2.setLedgerId(1L);
        commonDO2.setLedgerName("test");
        commonDO2.setCurrencyCode("test");
        commonDO2.setJournalCategory("test");
        commonDO2.setJournalSource("test");
        commonDO2.setJournalSourceName("test");
        commonDO2.setReference1("test");
        commonDO2.setReference2("test");
        commonDO2.setReference3("test");
        commonDO2.setReference4("test");
        commonDO2.setReference5("test");
        commonDO2.setReference6("test");
        commonDO2.setReference7("test");
        commonDO2.setReference8("test");
        commonDO2.setReference9("test");
        commonDO2.setReference10("test");
        commonDO2.setReference21("test");
        commonDO2.setReference22("test");
        commonDO2.setReference23("test");
        commonDO2.setReference24("test");
        commonDO2.setReference25("test");
        commonDO2.setReference26("test");
        commonDO2.setReference27("test");
        commonDO2.setReference28("test");
        commonDO2.setReference29("test");
        commonDO2.setReference30("test");
        commonDO2.setSegment1("test");
        commonDO2.setSegment2("test");
        commonDO2.setSegment3("test");
        commonDO2.setSegment4("test");
        commonDO2.setSegment5("test");
        commonDO2.setSegment6("test");
        commonDO2.setSegment7("test");
        commonDO2.setSegment8("test");
        commonDO2.setSegment9("test");
        commonDO2.setSegment10("test");
        commonDO2.setEnteredDr(new BigDecimal(0));
        commonDO2.setEnteredCr(new BigDecimal(0));
        commonDO2.setAccountedDr(new BigDecimal(0));
        commonDO2.setAccountedCr(new BigDecimal(0));
        commonDO2.setGroupId(1L);
        commonDO2.setCurrencyConversionDate(new Date());
        commonDO2.setCurrencyConversionRate(new BigDecimal(0));
        commonDO2.setCurrencyConversionType("test");
        commonDO2.setAttributeCategory("test");
        commonDO2.setHeaderAttribute1("test");
        commonDO2.setHeaderAttribute2("test");
        commonDO2.setHeaderAttribute3("test");
        commonDO2.setHeaderAttribute4("test");
        commonDO2.setHeaderAttribute5("test");
        commonDO2.setHeaderAttribute6("test");
        commonDO2.setHeaderAttribute7("test");
        commonDO2.setHeaderAttribute8("test");
        commonDO2.setHeaderAttribute9("test");
        commonDO2.setHeaderAttribute10("test");
        commonDO2.setHeaderAttribute11("test");
        commonDO2.setHeaderAttribute12("test");
        commonDO2.setHeaderAttribute13("test");
        commonDO2.setHeaderAttribute14("test");
        commonDO2.setHeaderAttribute15("test");
        commonDO2.setAttributeCategory3("test");
        commonDO2.setLineAttribute1("test");
        commonDO2.setLineAttribute2("test");
        commonDO2.setLineAttribute3("test");
        commonDO2.setLineAttribute4("test");
        commonDO2.setLineAttribute5("test");
        commonDO2.setLineAttribute6("test");
        commonDO2.setLineAttribute7("test");
        commonDO2.setLineAttribute8("test");
        commonDO2.setLineAttribute9("test");
        commonDO2.setLineAttribute10("test");
        commonDO2.setLineAttribute11("test");
        commonDO2.setLineAttribute12("test");
        commonDO2.setLineAttribute13("test");
        commonDO2.setLineAttribute14("test");
        commonDO2.setLineAttribute15("test");
        commonDO2.setLineAttribute16("test");
        commonDO2.setLineAttribute17("test");
        commonDO2.setLineAttribute18("test");
        commonDO2.setLineAttribute19("test");
        commonDO2.setLineAttribute20("test");
        commonDO2.setProcessStatus(ProcessStatusEnum.PROCESSING);
        commonDO2.setProcessMessage("test");
        commonDO2.setJeHeaderId(1L);
        commonDO2.setJournalName("test");
        commonDO2.setJeLineNum(1L);
        commonDO2.setDocumentId(1L);
        commonDO2.setLoadRequestId(1L);
        commonDO2.setImportRequestId(1L);
        commonDO2.setObjectVersionNumber(1L);
        commonDO2.setCreationDate(new Date());
        commonDO2.setCreatedBy("test");
        commonDO2.setLastModifyDate(new Date());
        commonDO2.setLastModifiedBy("test");
        sofiGlInterfaceCommonService.updateInterfaceCommon(commonDO,commonDO2);
    }
}