package com.xiaoju.corebanking.erp.adaptor.service.inter.impl;

import com.github.pagehelper.PageInfo;
import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.CoaRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceHisRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.converter.SofiGlInterfaceModelConverter;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.*;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfacePO;
import com.xiaoju.corebanking.erp.adaptor.service.fusion.CoaValidationService;
import com.xiaoju.corebanking.erp.adaptor.service.inter.impl.SofiGlInterfaceServiceImpl;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

/**
 * <p> SofiGlInterfaceServiceImpl Tester. </p>
 * <p> 2025-06-07 21:04:14.051 </p>
 *
 * <AUTHOR>
 * @version 0.0.1-SNAPSHOT
 */
@ExtendWith(MockitoExtension.class)
public class SofiGlInterfaceServiceImplTest {
    @InjectMocks
    private SofiGlInterfaceServiceImpl sofiGlInterfaceService;

    @Mock
    private SofiGlInterfaceRepository sofiGlInterfaceRepository;

    @Mock
    private SofiGlInterfaceHisRepository sofiGlInterfaceHisRepository;

    @Mock
    private SofiGlInterfaceModelConverter sofiGlInterfaceModelConverter;
    @Mock
    CoaRepository coaRepository;

    @Mock
    CoaValidationService coaValidationService;


    /**
     * Method: updateInterface(sofiGlInterfaceDO, update)
     */
    @Test
    public void updateInterfaceTest() throws Exception {
        doNothing().when(sofiGlInterfaceRepository).updateInterface(any(SofiGlInterfaceDO.class), any(SofiGlInterfaceDO.class));

        sofiGlInterfaceService.updateInterface(new SofiGlInterfaceDO() {{
            setPolizaId(1L);
        }}, new SofiGlInterfaceDO() {{
            setProcessStatus(ProcessStatusEnum.SUCCESS);
        }});

    }

    /**
     * Method: queryByPolizaId(polizaIds)
     */
    @Test
    public void queryByPolizaIdTest() throws Exception {
        Long polizaId = 1L;
        Long detallePolizaId = 2L;
        SofiGlInterfacePO po = new SofiGlInterfacePO();
        List<SofiGlInterfacePO> poList = Collections.singletonList(po);
        SofiGlInterfaceDO expectedDO = new SofiGlInterfaceDO();

        when(sofiGlInterfaceRepository.selectByPolizaIdAndDetalle(polizaId, detallePolizaId)).thenReturn(poList);
        when(sofiGlInterfaceModelConverter.convert(po)).thenReturn(expectedDO);

        SofiGlInterfaceDO result = sofiGlInterfaceService.selectByPolizaIdAndDetalle(polizaId, detallePolizaId);

        assertEquals(expectedDO, result);
    }

    /**
     * Method: selectByPolizaIdAndDetalle(polizaId, detallePolizaId)
     */
    @Test
    public void selectByPolizaIdAndDetalleTest() throws Exception {
        Long polizaId = 1L;
        Long detallePolizaId = 2L;

        when(sofiGlInterfaceRepository.selectByPolizaIdAndDetalle(polizaId, detallePolizaId)).thenReturn(Collections.emptyList());

        SofiGlInterfaceDO result = sofiGlInterfaceService.selectByPolizaIdAndDetalle(polizaId, detallePolizaId);

        assertNull(result);
    }

    /**
     * Method: updateInterfaceWithBackup(polizaId, detallePolizaId, update)
     */
    @Test
    public void updateInterfaceWithBackupTest() throws Exception {
        Long polizaId = 1L;
        Long detallePolizaId = 2L;

        when(sofiGlInterfaceRepository.selectByPolizaIdAndDetalle(polizaId, detallePolizaId)).thenReturn(null);

        SofiGlInterfaceDO result = sofiGlInterfaceService.selectByPolizaIdAndDetalle(polizaId, detallePolizaId);

        assertNull(result);
    }

    /**
     * Method: queryGroupId(sofiGlInterfaceHeaderQueryDO)
     */
    @Test
    public void queryGroupIdTest() throws Exception {
        Long polizaId = 1L;
        Long detallePolizaId = 2L;
        SofiGlInterfaceDO update = new SofiGlInterfaceDO();
        SofiGlInterfacePO oldPO = new SofiGlInterfacePO();
        List<SofiGlInterfacePO> oldList = Collections.singletonList(oldPO);
        SofiGlInterfaceDO oldDO = new SofiGlInterfaceDO();
        SofiGlInterfacePO newPO = new SofiGlInterfacePO();
        List<SofiGlInterfacePO> newList = Collections.singletonList(newPO);
        SofiGlInterfaceDO expectedResult = new SofiGlInterfaceDO();

        when(sofiGlInterfaceRepository.selectByPolizaIdAndDetalle(polizaId, detallePolizaId))
                .thenReturn(oldList)
                .thenReturn(newList);
        when(sofiGlInterfaceModelConverter.convert(oldPO)).thenReturn(oldDO);
        when(sofiGlInterfaceModelConverter.convert(newPO)).thenReturn(expectedResult);

        SofiGlInterfaceDO result = sofiGlInterfaceService.updateInterfaceWithBackup(polizaId, detallePolizaId, update);

        assertEquals(expectedResult, result);
    }

    /**
     * Method: querySofiGlInterface(sofiGlInterfaceDO)
     */
    @Test
    public void querySofiGlInterfaceTest() throws Exception {
        Long polizaId = 1L;
        Long detallePolizaId = 2L;
        SofiGlInterfaceDO update = new SofiGlInterfaceDO();
        SofiGlInterfacePO oldPO = new SofiGlInterfacePO();
        List<SofiGlInterfacePO> oldList = Collections.singletonList(oldPO);
        SofiGlInterfaceDO oldDO = new SofiGlInterfaceDO();

        when(sofiGlInterfaceRepository.selectByPolizaIdAndDetalle(polizaId, detallePolizaId))
                .thenReturn(oldList)
                .thenReturn(Collections.emptyList());
        when(sofiGlInterfaceModelConverter.convert(oldPO)).thenReturn(oldDO);

        SofiGlInterfaceDO result = sofiGlInterfaceService.updateInterfaceWithBackup(polizaId, detallePolizaId, update);

        assertNull(result);
    }

    @Test
    public void testQueryGroupId() {
        SofiGlInterfaceHeaderQueryDO queryDO = new SofiGlInterfaceHeaderQueryDO();
        List<Long> expectedResult = Arrays.asList(1L, 2L, 3L);

        when(sofiGlInterfaceRepository.queryGroupId(queryDO)).thenReturn(expectedResult);

        List<Long> result = sofiGlInterfaceService.queryGroupId(queryDO);

        assertEquals(expectedResult, result);
    }

    @Test
    public void testQuerySofiGlInterface() {
        SofiGlInterfaceDO queryDO = new SofiGlInterfaceDO();
        List<SofiGlInterfaceDO> expectedResult = new ArrayList<>();
        expectedResult.add(new SofiGlInterfaceDO());

        when(sofiGlInterfaceRepository.queryGLInterfaceData(queryDO)).thenReturn(expectedResult);

        List<SofiGlInterfaceDO> result = sofiGlInterfaceService.querySofiGlInterface(queryDO);

        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryCargosAndAbonosDO() {
        Long polizaId = 1L;
        CargosAndAbonosDO expectedResult = new CargosAndAbonosDO();

        when(sofiGlInterfaceRepository.selectCargosAndAbonosDO(polizaId)).thenReturn(expectedResult);

        CargosAndAbonosDO result = sofiGlInterfaceService.queryCargosAndAbonosDO(polizaId);

        assertEquals(expectedResult, result);

        sofiGlInterfaceService.queryByPolizaId(anyList());
        sofiGlInterfaceService.queryByPolizaIdsAndProcessDay(anyList(), anyString());
    }

    @Test
    public void testQueryCountInterfaceEmailData() {
        String processDay = "20250613";
        List<SofiEmailDO> sofiEmailDOList = new ArrayList<>();
        SofiEmailDO sofiEmailDO = new SofiEmailDO();
        sofiEmailDO.setCount(1L);
        sofiEmailDO.setFileName("file");
        sofiEmailDO.setSourceSys("s");
        sofiEmailDO.setProcessStatus("s");
        sofiEmailDOList.add(sofiEmailDO);
        when(sofiGlInterfaceRepository.selectCountInterfaceEmailData(processDay)).thenReturn(sofiEmailDOList);
        List<SofiEmailDO> result = sofiGlInterfaceService.queryCountInterfaceEmailData(processDay);
        Assert.assertEquals(sofiEmailDOList, result);


    }
    @Test
    void validateCoaTest() {
        String processDay="2025-08-08";
        List<ValidationRequestDO> list = new ArrayList<>();
        ValidationRequestDO validationRequestDO = new ValidationRequestDO();
        validationRequestDO.setLedgerName("test");
        PageInfo<ValidationRequestDO> pageInfo = new PageInfo<>(list);

        when(coaRepository.pagingSelectCoaList(processDay, 1, 5000)).thenReturn(pageInfo);
        sofiGlInterfaceService.validateCoa(processDay);
    }

    @Test
    void validateCoaTest2() {
        String processDay="2025-08-08";
        List<ValidationRequestDO> list = new ArrayList<>();
        ValidationRequestDO validationRequestDO = new ValidationRequestDO();
        validationRequestDO.setLedgerName("test");
        list.add(validationRequestDO);
        PageInfo<ValidationRequestDO> pageInfo = new PageInfo<>(list);

        when(coaRepository.pagingSelectCoaList(processDay, 1, 5000)).thenReturn(pageInfo);

        List<ValidationResultDO> validationResultDOS = new ArrayList<>();
        ValidationResultDO resultDO = new ValidationResultDO();
        resultDO.setLedgerName("test");
        validationResultDOS.add(resultDO);
        when(coaValidationService.validateCoa(pageInfo.getList())).thenReturn(validationResultDOS);

        sofiGlInterfaceService.validateCoa(processDay);
    }
}
