package com.xiaoju.corebanking.erp.adaptor.service.twotab;

import com.xiaoju.corebanking.erp.adaptor.repository.TwoTabHeaderRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.CuxTwoTabHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabHeadersPO;
import com.xiaoju.corebanking.erp.adaptor.service.twotab.CuxTwoTabHeaderService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

/**
* <p> CuxTwoTabHeaderService Tester. </p>
* <p> 2025-06-07 21:04:14.084 </p>
*
* <AUTHOR>
* @version 0.0.1-SNAPSHOT
*/
@ExtendWith(MockitoExtension.class)
public class CuxTwoTabHeaderServiceTest {

    @InjectMocks
    private CuxTwoTabHeaderService cuxTwoTabHeaderService;

    @Mock
    TwoTabHeaderRepository twoTabHeaderRepository;

    /**
    *
    * Method: findByFormCode(fromCode)
    */
    @Test
    public void findByFormCodeTest() throws Exception {
        String formCode = "TEST_FORM";
        CuxTwoTabHeadersPO expectedHeader = new CuxTwoTabHeadersPO();
        expectedHeader.setFormCode(formCode);

        when(twoTabHeaderRepository.selectByFormCode(anyString())).thenReturn(expectedHeader);

        CuxTwoTabHeadersPO result = cuxTwoTabHeaderService.findByFormCode(formCode);

        assertNotNull(result);
    }

    /**
    *
    * Method: insertHeader(header)
    */
    @Test
    public void insertHeaderTest() throws Exception {
        CuxTwoTabHeaderDO headerDO = new CuxTwoTabHeaderDO();
        headerDO.setFormCode("NEW_FORM");

        CuxTwoTabHeadersPO expectedPO = new CuxTwoTabHeadersPO();
        expectedPO.setFormCode("NEW_FORM");

        when(twoTabHeaderRepository.insertByExample(any())).thenReturn(expectedPO);

        CuxTwoTabHeadersPO result = cuxTwoTabHeaderService.insertHeader(headerDO);

        assertNotNull(result);
    }

    /**
    *
    * Method: selectAllHeaders()
    */
    @Test
    public void selectAllHeadersTest() throws Exception {
        CuxTwoTabHeadersPO header1 = new CuxTwoTabHeadersPO();
        header1.setFormCode("FORM1");

        CuxTwoTabHeadersPO header2 = new CuxTwoTabHeadersPO();
        header2.setFormCode("FORM2");

        List<CuxTwoTabHeadersPO> expectedHeaders = Arrays.asList(header1, header2);

        when(twoTabHeaderRepository.selectAll()).thenReturn(expectedHeaders);

        List<CuxTwoTabHeadersPO> results = cuxTwoTabHeaderService.selectAllHeaders();

        assertNotNull(results);
    }

    /**
    *
    * Method: saveHeader(header)
    */
    @Test
    public void saveHeaderTest() throws Exception {
        CuxTwoTabHeaderDO headerDO = new CuxTwoTabHeaderDO();
        headerDO.setFormCode("SAVE_FORM");

        doNothing().when(twoTabHeaderRepository).saveHeader(any(CuxTwoTabHeaderDO.class));

        cuxTwoTabHeaderService.saveHeader(headerDO);
    }


}
