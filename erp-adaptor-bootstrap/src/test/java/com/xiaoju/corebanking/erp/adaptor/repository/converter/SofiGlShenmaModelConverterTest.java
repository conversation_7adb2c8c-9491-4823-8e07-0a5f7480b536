package com.xiaoju.corebanking.erp.adaptor.repository.converter;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlShenmaPO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;


import static org.junit.Assert.assertEquals;

/**
 * @Description: 类描述
 * @author: didi
 **/
@ExtendWith(MockitoExtension.class)
public class SofiGlShenmaModelConverterTest {
    @InjectMocks
    SofiGlShenmaModelConverter converter;

    @Test
    public void convertToDoTest() {
        SofiGlShenmaPO sofiGlShenmaPO = new SofiGlShenmaPO();
        sofiGlShenmaPO.setProcessDay("test");
        sofiGlShenmaPO.setFileName("test");
        sofiGlShenmaPO.setLinkReference("test");
        sofiGlShenmaPO.setSourceBranch("test");
        sofiGlShenmaPO.setCcy("test");
        sofiGlShenmaPO.setGlCode("test");
        sofiGlShenmaPO.setCrDrInd("test");
        sofiGlShenmaPO.setEnteredDebitAmount(new BigDecimal(1.2));
        sofiGlShenmaPO.setEnteredCreditAmount(new BigDecimal(1.2));
        sofiGlShenmaPO.setProfitCenter("test");
        sofiGlShenmaPO.setSourceModule("test");
        sofiGlShenmaPO.setClientType("test");
        sofiGlShenmaPO.setAmtType("test");
        sofiGlShenmaPO.setTranType("test");
        sofiGlShenmaPO.setEventType("test");
        sofiGlShenmaPO.setProdType("test");
        sofiGlShenmaPO.setPostDate("test");
        sofiGlShenmaPO.setValueDate("test");
        sofiGlShenmaPO.setNarrative("test");
        sofiGlShenmaPO.setChannelSeqNo("test");
        sofiGlShenmaPO.setIntercompany("test");
        sofiGlShenmaPO.setFlatRate(new BigDecimal(1));
        sofiGlShenmaPO.setCustRate(new BigDecimal(1));
        sofiGlShenmaPO.setInlandOffshore("test");
        sofiGlShenmaPO.setGroupId(1L);
        sofiGlShenmaPO.setProcessStatus(ProcessStatusEnum.NEW.getCode());
        sofiGlShenmaPO.setProcessMessage("test");
        sofiGlShenmaPO.setObjectVersionNumber(1L);
        String dateStr = "2025-08-08 12:11:11";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            sofiGlShenmaPO.setCreationDate(sdf.parse(dateStr));
            sofiGlShenmaPO.setLastModifyDate(sdf.parse(dateStr));
            sofiGlShenmaPO.setCreatedBy("test");
            sofiGlShenmaPO.setLastModifiedBy("test");
            sofiGlShenmaPO.setVoucherGroup("test");
            SofiGlShenmaDO result = converter.convert(sofiGlShenmaPO);

            SofiGlShenmaDO sofiGlShenmaDO = new SofiGlShenmaDO();
            sofiGlShenmaDO.setProcessDay("test");
            sofiGlShenmaDO.setFileName("test");
            sofiGlShenmaDO.setLinkReference("test");
            sofiGlShenmaDO.setSourceBranch("test");
            sofiGlShenmaDO.setCcy("test");
            sofiGlShenmaDO.setGlCode("test");
            sofiGlShenmaDO.setCrDrInd("test");
            sofiGlShenmaDO.setEnteredDebitAmount(new BigDecimal(1.2));
            sofiGlShenmaDO.setEnteredCreditAmount(new BigDecimal(1.2));
            sofiGlShenmaDO.setProfitCenter("test");
            sofiGlShenmaDO.setSourceModule("test");
            sofiGlShenmaDO.setClientType("test");
            sofiGlShenmaDO.setAmtType("test");
            sofiGlShenmaDO.setTranType("test");
            sofiGlShenmaDO.setEventType("test");
            sofiGlShenmaDO.setProdType("test");
            sofiGlShenmaDO.setPostDate("test");
            sofiGlShenmaDO.setValueDate("test");
            sofiGlShenmaDO.setNarrative("test");
            sofiGlShenmaDO.setChannelSeqNo("test");
            sofiGlShenmaDO.setIntercompany("test");
            sofiGlShenmaDO.setFlatRate(new BigDecimal(1));
            sofiGlShenmaDO.setCustRate(new BigDecimal(1));
            sofiGlShenmaDO.setInlandOffshore("test");
            sofiGlShenmaDO.setGroupId(1L);
            sofiGlShenmaDO.setProcessStatus(ProcessStatusEnum.NEW);
            sofiGlShenmaDO.setProcessMessage("test");
            sofiGlShenmaDO.setObjectVersionNumber(1L);
            sofiGlShenmaDO.setCreationDate(sdf.parse(dateStr));
            sofiGlShenmaDO.setCreatedBy("test");
            sofiGlShenmaDO.setLastModifyDate(sdf.parse(dateStr));
            sofiGlShenmaDO.setLastModifiedBy("test");
            sofiGlShenmaDO.setVoucherGroup("test");
            assertEquals(result, sofiGlShenmaDO);

        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

    }

    @Test
    public void convertToPoTest() {
        SofiGlShenmaDO sofiGlShenmaDO = new SofiGlShenmaDO();
        sofiGlShenmaDO.setProcessDay("test");
        sofiGlShenmaDO.setFileName("test");
        sofiGlShenmaDO.setLinkReference("test");

        sofiGlShenmaDO.setSourceBranch("test");
        sofiGlShenmaDO.setCcy("test");
        sofiGlShenmaDO.setGlCode("test");
        sofiGlShenmaDO.setCrDrInd("test");
        sofiGlShenmaDO.setEnteredDebitAmount(new BigDecimal(1));
        sofiGlShenmaDO.setEnteredCreditAmount(new BigDecimal(1));
        sofiGlShenmaDO.setProfitCenter("test");
        sofiGlShenmaDO.setSourceModule("test");
        sofiGlShenmaDO.setClientType("test");
        sofiGlShenmaDO.setAmtType("test");
        sofiGlShenmaDO.setTranType("test");
        sofiGlShenmaDO.setEventType("test");
        sofiGlShenmaDO.setProdType("test");
        sofiGlShenmaDO.setPostDate("test");
        sofiGlShenmaDO.setValueDate("test");
        sofiGlShenmaDO.setNarrative("test");
        sofiGlShenmaDO.setChannelSeqNo("test");
        sofiGlShenmaDO.setIntercompany("test");
        sofiGlShenmaDO.setFlatRate(new BigDecimal(1));
        sofiGlShenmaDO.setCustRate(new BigDecimal(1));
        sofiGlShenmaDO.setInlandOffshore("test");
        sofiGlShenmaDO.setGroupId(1L);
        sofiGlShenmaDO.setProcessStatus(ProcessStatusEnum.NEW);
        sofiGlShenmaDO.setProcessMessage("test");
        sofiGlShenmaDO.setObjectVersionNumber(1L);
        String dateStrs = "2025-08-08 12:11:11";
        SimpleDateFormat sdfs = new SimpleDateFormat("yyyy-MM-dd");
        try {
            sofiGlShenmaDO.setCreationDate(sdfs.parse(dateStrs));
            sofiGlShenmaDO.setLastModifyDate(sdfs.parse(dateStrs));
        }catch (Exception e){
            throw new RuntimeException();
        }
        sofiGlShenmaDO.setCreatedBy("test");
        sofiGlShenmaDO.setLastModifiedBy("test");
        sofiGlShenmaDO.setVoucherGroup("test");
        SofiGlShenmaPO result = converter.convert(sofiGlShenmaDO);

        SofiGlShenmaPO sofiGlShenmaPO = new SofiGlShenmaPO();
        sofiGlShenmaPO.setProcessDay("test");
        sofiGlShenmaPO.setFileName("test");
        sofiGlShenmaPO.setLinkReference("test");
        sofiGlShenmaPO.setSourceBranch("test");
        sofiGlShenmaPO.setCcy("test");
        sofiGlShenmaPO.setGlCode("test");
        sofiGlShenmaPO.setCrDrInd("test");
        sofiGlShenmaPO.setEnteredDebitAmount(new BigDecimal(1));
        sofiGlShenmaPO.setEnteredCreditAmount(new BigDecimal(1));
        sofiGlShenmaPO.setProfitCenter("test");
        sofiGlShenmaPO.setSourceModule("test");
        sofiGlShenmaPO.setClientType("test");
        sofiGlShenmaPO.setAmtType("test");
        sofiGlShenmaPO.setTranType("test");
        sofiGlShenmaPO.setEventType("test");
        sofiGlShenmaPO.setProdType("test");
        sofiGlShenmaPO.setPostDate("test");
        sofiGlShenmaPO.setValueDate("test");
        sofiGlShenmaPO.setNarrative("test");
        sofiGlShenmaPO.setChannelSeqNo("test");
        sofiGlShenmaPO.setIntercompany("test");
        sofiGlShenmaPO.setFlatRate(new BigDecimal(1));
        sofiGlShenmaPO.setCustRate(new BigDecimal(1));
        sofiGlShenmaPO.setInlandOffshore("test");
        sofiGlShenmaPO.setGroupId(1L);
        sofiGlShenmaPO.setProcessStatus(ProcessStatusEnum.NEW.getCode());
        sofiGlShenmaPO.setProcessMessage("test");
        sofiGlShenmaPO.setObjectVersionNumber(1L);
        String dateStr = "2025-08-08 12:11:11";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            sofiGlShenmaPO.setCreationDate(sdf.parse(dateStr));
            sofiGlShenmaPO.setLastModifyDate(sdf.parse(dateStr));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        sofiGlShenmaPO.setCreatedBy("test");
        sofiGlShenmaPO.setLastModifiedBy("test");
        sofiGlShenmaPO.setVoucherGroup("test");
        assertEquals(result, sofiGlShenmaPO);
    }
    @Test
    public void testCopyIgnoreNullValue() {
        SofiGlShenmaDO source = new SofiGlShenmaDO();
        SofiGlShenmaDO target = new SofiGlShenmaDO();
        converter.copyIgnoreNullValue(source,target);
    }
}
