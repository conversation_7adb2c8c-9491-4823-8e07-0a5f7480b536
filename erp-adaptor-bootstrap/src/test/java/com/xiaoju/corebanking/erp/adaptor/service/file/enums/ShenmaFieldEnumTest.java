package com.xiaoju.corebanking.erp.adaptor.service.file.enums;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlAcctHistDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static com.xiaoju.corebanking.erp.adaptor.service.file.enums.ShenmaFieldEnum.*;
import static com.xiaoju.corebanking.erp.adaptor.service.file.enums.SofiGlAcctEnum.ACTUAL_BAL;

/**
 * @Description: 类描述
 * @author: didi
 **/
@ExtendWith(MockitoExtension.class)
public class ShenmaFieldEnumTest {
    @Test
    void setField() {
        SofiGlShenmaDO entity = new SofiGlShenmaDO();
        String fieldName = "fileName";
        String value = null;
        ShenmaFieldEnum.setField(entity,fieldName,value);
    }
    @Test
    void setFieldNotNull() {
        SofiGlShenmaDO entity = new SofiGlShenmaDO();
        String value = "string";
        ShenmaFieldEnum.setField(entity,LINK_REFERENCE.name(),value);
        ShenmaFieldEnum.setField(entity,SOURCE_BRANCH.name(),value);
        ShenmaFieldEnum.setField(entity,SOURCE_BRANCH.name(),value);
        ShenmaFieldEnum.setField(entity,CCY.name(),value);
        ShenmaFieldEnum.setField(entity,GL_CODE.name(),value);
        ShenmaFieldEnum.setField(entity,CR_DR_IND.name(),value);
        ShenmaFieldEnum.setField(entity,CR_DR_IND.name(),value);
        ShenmaFieldEnum.setField(entity,PROFIT_CENTER.name(),value);
        ShenmaFieldEnum.setField(entity,CLIENT_TYPE.name(),value);
        ShenmaFieldEnum.setField(entity,AMT_TYPE.name(),value);
        ShenmaFieldEnum.setField(entity,TRAN_TYPE.name(),value);
        ShenmaFieldEnum.setField(entity,EVENT_TYPE.name(),value);
        ShenmaFieldEnum.setField(entity,PROD_TYPE.name(),value);
        ShenmaFieldEnum.setField(entity,POST_DATE.name(),value);
        ShenmaFieldEnum.setField(entity,INTERCOMPANY.name(),value);
        ShenmaFieldEnum.setField(entity,INLAND_OFFSHORE.name(),value);
        ShenmaFieldEnum.setField(entity,CLIENT_NO.name(),value);
        ShenmaFieldEnum.setField(entity,SEQ_NO.name(),value);
        ShenmaFieldEnum.setField(entity,SYSTEM_ID.name(),value);
        ShenmaFieldEnum.setField(entity,COMPANY.name(),value);
        ShenmaFieldEnum.setField(entity,GROUP_CLIENT.name(),value);
        ShenmaFieldEnum.setField(entity,VOUCHER_GROUP.name(),value);


    }
    @Test
    void testBigdecimal() {
        SofiGlShenmaDO entity = new SofiGlShenmaDO();
        String value = "11";
        ShenmaFieldEnum.setField(entity, ENTERED_DEBIT_AMOUNT.name(), value);
        ShenmaFieldEnum.setField(entity, ENTERED_CREDIT_AMOUNT.name(), value);
        ShenmaFieldEnum.setField(entity, FLAT_RATE.name(), value);
        ShenmaFieldEnum.setField(entity, CUST_RATE.name(), value);

    }
}
