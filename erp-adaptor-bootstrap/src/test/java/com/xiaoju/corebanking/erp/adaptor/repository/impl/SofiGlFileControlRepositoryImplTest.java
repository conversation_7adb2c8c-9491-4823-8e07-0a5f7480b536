package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.xiaoju.corebanking.erp.adaptor.repository.converter.SofiGlFileControlModelConverter;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlFileControlDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlFileControlPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SofiGlFileControlCustomerMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;

import static org.mockito.Mockito.when;


/**
* <p> SofiGlFileControlRepositoryImpl Tester. </p>
* <p> 2025-06-06 11:40:17.961 </p>
*
* <AUTHOR>
* @version 0.0.1-SNAPSHOT
*/
@ExtendWith(MockitoExtension.class)
public class SofiGlFileControlRepositoryImplTest {
    @InjectMocks
    SofiGlFileControlRepositoryImpl sofiGlFileControlRepository;
    @Mock
    private SofiGlFileControlCustomerMapper sofiGlFileControlCustomerMapper;

    @Mock
    private SofiGlFileControlModelConverter slfiGlFileControlModelConverter;

    /**
    *
    * Method: insertSelective(sofiGlFileControlDO)
    */
    @Test
    public void insertSelectiveTestEmpty() throws Exception {
        SofiGlFileControlDO sofiGlFileControlDO = new SofiGlFileControlDO();
        sofiGlFileControlDO.setProcessDay("2025");
        sofiGlFileControlDO.setFileName("fileName");
        sofiGlFileControlDO.setFileSize(1L);
        sofiGlFileControlDO.setFileCount(1L);
        sofiGlFileControlDO.setProcessStatus("NEW");
        sofiGlFileControlDO.setProcessMessage("");
        sofiGlFileControlDO.setObjectVersionNumber(1);
        sofiGlFileControlDO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlFileControlDO.setCreatedBy("1");
        sofiGlFileControlDO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlFileControlDO.setLastModifiedBy("1");
        sofiGlFileControlRepository.insertSelective(sofiGlFileControlDO);
    }
    @Test
    public void insertSelectiveTest() throws Exception {
        SofiGlFileControlDO sofiGlFileControlDO = new SofiGlFileControlDO();
        sofiGlFileControlDO.setProcessDay("2025");
        sofiGlFileControlDO.setFileName("fileName");
        sofiGlFileControlDO.setFileSize(1L);
        sofiGlFileControlDO.setFileCount(1L);
        sofiGlFileControlDO.setProcessStatus("NEW");
        sofiGlFileControlDO.setProcessMessage("");
        sofiGlFileControlDO.setObjectVersionNumber(1);
        sofiGlFileControlDO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlFileControlDO.setCreatedBy("1");
        sofiGlFileControlDO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlFileControlDO.setLastModifiedBy("1");

        List<SofiGlFileControlPO> poList = new ArrayList<>();
        SofiGlFileControlPO sofiGlFileControlPO = new SofiGlFileControlPO();
        sofiGlFileControlPO.setProcessDay(sofiGlFileControlDO.getProcessDay());
        sofiGlFileControlPO.setFileName(sofiGlFileControlDO.getFileName());
        sofiGlFileControlPO.setSystemCode("SHEN_MA");
        poList.add(sofiGlFileControlPO);

        when(sofiGlFileControlCustomerMapper.findByIndexFields(sofiGlFileControlDO.getProcessDay(), sofiGlFileControlDO.getFileName(), "SHEN_MA")).thenReturn(poList);
        
        sofiGlFileControlDO.setSystemCode("SHEN_MA");
        sofiGlFileControlRepository.insertSelective(sofiGlFileControlDO);
    }

    /**
    *
    * Method: updateByExampleSelective(sourceDO)
    */
    @Test
    public void updateByExampleSelectiveTest() throws Exception {
        SofiGlFileControlDO sofiGlFileControlDO = new SofiGlFileControlDO();
        sofiGlFileControlDO.setProcessDay("2025");
        sofiGlFileControlDO.setFileName("fileName");
        sofiGlFileControlDO.setFileSize(1L);
        sofiGlFileControlDO.setFileCount(1L);
        sofiGlFileControlDO.setProcessStatus("NEW");
        sofiGlFileControlDO.setProcessMessage("");
        sofiGlFileControlDO.setObjectVersionNumber(1);
        sofiGlFileControlDO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlFileControlDO.setCreatedBy("1");
        sofiGlFileControlDO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlFileControlDO.setLastModifiedBy("1");
        sofiGlFileControlDO.setSystemCode("SHEN_MA");
        sofiGlFileControlRepository.updateByExampleSelective(sofiGlFileControlDO);
    }

    /**
    *
    * Method: findByIndexFields(processDay, fileName)
    */
    @Test
    public void findByIndexFieldsTest() throws Exception {
        SofiGlFileControlDO sofiGlFileControlDO = new SofiGlFileControlDO();
        sofiGlFileControlDO.setProcessDay("2025");
        sofiGlFileControlDO.setFileName("fileName");
        sofiGlFileControlDO.setFileSize(1L);
        sofiGlFileControlDO.setFileCount(1L);
        sofiGlFileControlDO.setProcessStatus("NEW");
        sofiGlFileControlDO.setProcessMessage("");
        sofiGlFileControlDO.setObjectVersionNumber(1);
        sofiGlFileControlDO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlFileControlDO.setCreatedBy("1");
        sofiGlFileControlDO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlFileControlDO.setLastModifiedBy("1");
        sofiGlFileControlDO.setSystemCode("SHEN_MA");
        List<SofiGlFileControlPO> poList = new ArrayList<>();
        when(sofiGlFileControlCustomerMapper.findByIndexFields(sofiGlFileControlDO.getProcessDay(), sofiGlFileControlDO.getFileName(), sofiGlFileControlDO.getSystemCode())).thenReturn(poList);

        sofiGlFileControlRepository.findByIndexFields(sofiGlFileControlDO.getProcessDay(), sofiGlFileControlDO.getFileName(), sofiGlFileControlDO.getSystemCode());
    }
}
