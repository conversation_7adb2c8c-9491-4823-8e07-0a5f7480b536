package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.enums.SourceSysEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.converter.SofiGlInterfaceCommonModelConverter;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceCommonDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceCommonPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SofiGlInterfaceCommonCustomerMapper;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfaceCommonPOMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SofiGlInterfaceCommonRepositoryImplTest {
    @InjectMocks
    SofiGlInterfaceCommonRepositoryImpl repository;

    @Mock
    private SofiGlInterfaceCommonPOMapper sofiGlInterfaceCommonPOMapper;
    @Mock
    private SofiGlInterfaceCommonCustomerMapper sofiGlInterfaceCommonCustomerMapper;
    @Mock
    private SofiGlInterfaceCommonModelConverter sofiGlInterfaceCommonModelConverter;

    @Test
    void insertSelective() {
        SofiGlInterfaceCommonDO sofiGlInterfaceCommonDO = new SofiGlInterfaceCommonDO();
        sofiGlInterfaceCommonDO.setLastModifiedBy("test");
        repository.insertSelective(sofiGlInterfaceCommonDO);
    }

    @Test
    void batchInsert() {
        SofiGlInterfaceCommonDO sofiGlInterfaceCommonDO = new SofiGlInterfaceCommonDO();
        sofiGlInterfaceCommonDO.setLastModifiedBy("test");
        List<SofiGlInterfaceCommonDO> list = new ArrayList<>();
        list.add(sofiGlInterfaceCommonDO);
        repository.batchInsert(list);
    }

    @Test
    void queryGroupIds() {
        repository.queryGroupIds(1, 20, "2025-08-13");
    }

    @Test
    void querySofiGlInterfaceCommonDOList() {
        repository.querySofiGlInterfaceCommonDOList(SourceSysEnum.SHEN_MA.getCode(), "2025-08-12", 1L);
    }

    @Test
    void querySofiGlInterfaceCommonDOListNotNull() {
        List<SofiGlInterfaceCommonPO> poList = new ArrayList<>();
        SofiGlInterfaceCommonPO po = new SofiGlInterfaceCommonPO();
        po.setId(1L);
        poList.add(po);
        when(sofiGlInterfaceCommonCustomerMapper.selectSofiGlInterfaceCommonDOList(SourceSysEnum.SHEN_MA.getCode(), "2025-08-12", 1L)).thenReturn(poList);
        repository.querySofiGlInterfaceCommonDOList(SourceSysEnum.SHEN_MA.getCode(), "2025-08-12", 1L);
    }

    @Test
    void updateSofiGlInterfaceCommon() {
        SofiGlInterfaceCommonDO sofiGlInterfaceCommonDO = new SofiGlInterfaceCommonDO();
        sofiGlInterfaceCommonDO.setId(1L);
        repository.updateSofiGlInterfaceCommon(SourceSysEnum.SHEN_MA.getCode(), "2025-08-12", ProcessStatusEnum.LOAD_FAILED.getCode(), 1L, sofiGlInterfaceCommonDO);
    }
    @Test
    void updateInterfaceCommon() {
        SofiGlInterfaceCommonDO original = new SofiGlInterfaceCommonDO();
        SofiGlInterfaceCommonDO update = new SofiGlInterfaceCommonDO();
        repository.updateInterfaceCommon(original,update);
    }
    @Test
    void queryByReference5AndProcessDay(){
        List<String> externalReferenceList = new ArrayList<>();
        String processDay = "2025-08-21";
        List<SofiGlInterfaceCommonPO> poList = new ArrayList<>();
        when( sofiGlInterfaceCommonCustomerMapper.queryByReference5AndProcessDay(externalReferenceList, processDay)).thenReturn(poList);
        repository.queryByReference5AndProcessDay(externalReferenceList,processDay);
    }
    @Test
    void queryByReference5AndProcessDayNotNull(){
        List<String> externalReferenceList = new ArrayList<>();
        String processDay = "2025-08-21";
        List<SofiGlInterfaceCommonPO> poList = new ArrayList<>();
        SofiGlInterfaceCommonPO sofiGlInterfaceCommonPO = new SofiGlInterfaceCommonPO();
        sofiGlInterfaceCommonPO.setProcessDay(processDay);
        poList.add(sofiGlInterfaceCommonPO);
        when( sofiGlInterfaceCommonCustomerMapper.queryByReference5AndProcessDay(externalReferenceList, processDay)).thenReturn(poList);
        repository.queryByReference5AndProcessDay(externalReferenceList,processDay);
    }
}