package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlShenmaHisRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.converter.SofiGlShenmaModelConverter;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.CargosAndAbonosDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlShenmaPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SofiGlShenmaCustomerMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SofiGlShenmaRepositoryImplTest {
    @InjectMocks
    SofiGlShenmaRepositoryImpl repository;
    @Mock
    private SofiGlShenmaCustomerMapper sofiGlShenmaCustomerMapper;
    @Mock
    private SofiGlShenmaModelConverter sofiGlShenmaModelConverter;

    @Mock
    SofiGlShenmaHisRepository sofiGlShenmaHisRepository;

    @Test
    void selectSofiGlShenmaByReference() {
        String reference = "1111";
        List<SofiGlShenmaPO> poList = new ArrayList<>();
        SofiGlShenmaPO po = new SofiGlShenmaPO();
        po.setCcy("MX");
        poList.add(po);
        when(sofiGlShenmaCustomerMapper.queryByReference(reference)).thenReturn(poList);
        List<SofiGlShenmaDO> dotargetList = poList.stream().map(sofiGlShenmaModelConverter::convert).collect(Collectors.toList());
        List<SofiGlShenmaDO> doList = repository.selectSofiGlShenmaByReference(reference);
        assertEquals(dotargetList,doList);
    }

    @Test
    void updateShenmaInterfaceByReference() {
        SofiGlShenmaDO sofiGlShenmaDO = new SofiGlShenmaDO();
        sofiGlShenmaDO.setLinkReference("1");
        sofiGlShenmaDO.setObjectVersionNumber(1L);
        sofiGlShenmaDO.setProcessDay("2025-08-12");
        sofiGlShenmaDO.setVoucherGroup("group");
        SofiGlShenmaDO update = new SofiGlShenmaDO();
        repository.updateShenmaInterfaceByReference(sofiGlShenmaDO,update);
    }
    @Test
    void batchInsertNull() {
        List<SofiGlShenmaDO> shenmaList = new ArrayList<>();
        repository.batchInsert(shenmaList);
    }
    @Test
    void tesBatchInsertt() {
        SofiGlShenmaDO sofiGlShenmaDO = new SofiGlShenmaDO();
        sofiGlShenmaDO.setLinkReference("1");
        sofiGlShenmaDO.setObjectVersionNumber(1L);
        sofiGlShenmaDO.setProcessDay("2025-08-12");
        sofiGlShenmaDO.setVoucherGroup("group");
        sofiGlShenmaDO.setFileName("file");
        List<SofiGlShenmaDO> shenmaList = new ArrayList<>();
        shenmaList.add(sofiGlShenmaDO);
        List<SofiGlShenmaPO> existingRecords = new ArrayList<>();
        SofiGlShenmaPO sofiGlShenmaPO = new SofiGlShenmaPO();
        sofiGlShenmaPO.setCcy("ccy");
        existingRecords.add(sofiGlShenmaPO);
        repository.batchInsert(shenmaList);
    }
    @Test
    void selectPendingShenmaRecords() {
        String processDay = "2025";
        List<SofiGlShenmaPO> poList =new ArrayList<>();
        when(sofiGlShenmaCustomerMapper.queryPendingShenmaRecords(ProcessStatusEnum.NEW.getCode(), processDay)).thenReturn(poList);
        repository.selectPendingShenmaRecords(ProcessStatusEnum.NEW,processDay);
    }

    @Test
    void selectCargosAndAbonosDO() {
        String reference = "2025";
        CargosAndAbonosDO cargosAndAbonosDO = new CargosAndAbonosDO();
        cargosAndAbonosDO.setAbonos(new BigDecimal(1));
        cargosAndAbonosDO.setCargos(new BigDecimal(1));
        when(sofiGlShenmaCustomerMapper.selectCargosAndAbonosDO(reference)).thenReturn(cargosAndAbonosDO);
        CargosAndAbonosDO result = repository.selectCargosAndAbonosDO(reference);
        assertEquals(cargosAndAbonosDO.getAbonos(),result.getAbonos());
    }

    @Test
    void querySofiGlShenmaByGroupIdAndProcessDay() {
        Long groupId = 1L;
        String processDay = "2025";
        List<SofiGlShenmaPO> sofiGlShenmaPOS = new ArrayList<>();
        when(sofiGlShenmaCustomerMapper.querySofiGlShenmaByGroupIdAndProcessDay(groupId, processDay)).thenReturn(sofiGlShenmaPOS);
        repository.querySofiGlShenmaByGroupIdAndProcessDay(groupId,processDay);
    }

    @Test
    void querySofiGlShenmaByGroupIdAndProcessDayNotNull() {
        Long groupId = 1L;
        String processDay = "2025";
        List<SofiGlShenmaPO> sofiGlShenmaPOS = new ArrayList<>();
        SofiGlShenmaPO po = new SofiGlShenmaPO();
        po.setCcy("mx");
        sofiGlShenmaPOS.add(po);
        when(sofiGlShenmaCustomerMapper.querySofiGlShenmaByGroupIdAndProcessDay(groupId, processDay)).thenReturn(sofiGlShenmaPOS);
        repository.querySofiGlShenmaByGroupIdAndProcessDay(groupId,processDay);
    }
    @Test
    void updateByProcessDayAndGroupIdAndStatus() {
        String processDay = "2025";
        Long groupId = 1L;
        String processStatus =  ProcessStatusEnum.FAILED.name();
        SofiGlShenmaDO update = new SofiGlShenmaDO();
        repository.updateByProcessDayAndGroupIdAndStatus(processDay,groupId,processStatus,update);
    }

    @Test
    void selectShenMaSummary() {
        repository.selectShenMaSummary("2025");
    }
    @Test
    void selectShenMaSumByVoucherGroup() {
        repository.selectShenMaSumByVoucherGroup("2025");
    }
    @Test
    void queryByPolizaIdsAndProcessDay() {
        List<String> externalReferenceList = new ArrayList<>();
        String processDay= "2025";
        repository.queryByPolizaIdsAndProcessDay(externalReferenceList,processDay);
    }
    @Test
    void queryByPolizaIdsAndProcessDayNotNull() {
        List<String> externalReferenceList = new ArrayList<>();
        externalReferenceList.add("sss");
        String processDay= "2025";
        List<SofiGlShenmaPO> sofiGlShenmaPOS = new ArrayList<>();
        when(sofiGlShenmaCustomerMapper.queryByPolizaIdsAndProcessDay(externalReferenceList, processDay)).thenReturn(sofiGlShenmaPOS);

        repository.queryByPolizaIdsAndProcessDay(externalReferenceList,processDay);
    }
    @Test
    void queryByPolizaIdsAndProcessDayNotNull2() {
        List<String> externalReferenceList = new ArrayList<>();
        externalReferenceList.add("sss");
        String processDay= "2025";
        List<SofiGlShenmaPO> sofiGlShenmaPOS = new ArrayList<>();
        SofiGlShenmaPO sofiGlShenmaPO = new SofiGlShenmaPO();
        sofiGlShenmaPO.setCcy("ccc");
        sofiGlShenmaPOS.add(sofiGlShenmaPO);
        when(sofiGlShenmaCustomerMapper.queryByPolizaIdsAndProcessDay(externalReferenceList, processDay)).thenReturn(sofiGlShenmaPOS);

        repository.queryByPolizaIdsAndProcessDay(externalReferenceList,processDay);
    }

    @Test
    void findByIndexFieldsIsNull() {
        String processDay= "2025";
        String systemCode = "shenma";
        String linkReference = "linek";
        List<SofiGlShenmaPO> sofiGlShenmaPOS = new ArrayList<>();
        SofiGlShenmaPO sofiGlShenmaPO = new SofiGlShenmaPO();
        sofiGlShenmaPOS.add(sofiGlShenmaPO);
        when(sofiGlShenmaCustomerMapper.findByIndexFields(processDay, linkReference)).thenReturn(sofiGlShenmaPOS);

        repository.findByIndexFields(processDay,linkReference);
    }
    @Test
    void findByIndexFieldsIsNotNull() {
        String processDay= "2025";
        String systemCode = "shenma";
        String linkReference = "linek";
        List<SofiGlShenmaPO> sofiGlShenmaPOS = new ArrayList<>();
        SofiGlShenmaPO sofiGlShenmaPO = new SofiGlShenmaPO();
        sofiGlShenmaPO.setProcessDay(processDay);
        sofiGlShenmaPOS.add(sofiGlShenmaPO);
        when(sofiGlShenmaCustomerMapper.findByIndexFields(processDay, linkReference)).thenReturn(sofiGlShenmaPOS);

        repository.findByIndexFields(processDay,linkReference);
    }
    @Test
    void deleteByIndexFields() {
        String processDay= "2025";
        String systemCode = "shenma";
        String linkReference = "linek";
        List<SofiGlShenmaPO> sofiGlShenmaPOS = new ArrayList<>();
        SofiGlShenmaPO sofiGlShenmaPO = new SofiGlShenmaPO();
        sofiGlShenmaPO.setProcessDay(processDay);
        sofiGlShenmaPOS.add(sofiGlShenmaPO);
        when(sofiGlShenmaCustomerMapper.deleteByIndexFields(processDay, linkReference)).thenReturn(1);
        repository.deleteByIndexFields(processDay,linkReference);
    }
}