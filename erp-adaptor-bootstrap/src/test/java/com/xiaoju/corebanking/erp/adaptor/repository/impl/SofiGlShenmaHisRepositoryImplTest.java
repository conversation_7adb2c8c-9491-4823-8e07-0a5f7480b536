package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlShenmaPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlShenmaHisPOMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 类描述
 * @author: didi
 **/
@ExtendWith(MockitoExtension.class)
public class SofiGlShenmaHisRepositoryImplTest {
    @InjectMocks
    SofiGlShenmaHisRepositoryImpl sofiGlShenmaHisRepository;
    @Mock
    SofiGlShenmaHisPOMapper sofiGlShenmaHisPOMapper;
    @Test
    void test(){
        List<SofiGlShenmaPO> sofiGlShenmaPOList = new ArrayList<>();
        SofiGlShenmaPO sofiGlShenmaPO = new SofiGlShenmaPO();
        sofiGlShenmaPO.setCcy("mxn");
        sofiGlShenmaPOList.add(sofiGlShenmaPO);
        sofiGlShenmaHisRepository.backupShenmaHis(sofiGlShenmaPOList);
    }
    @Test
    void testNull(){
        List<SofiGlShenmaPO> sofiGlShenmaPOList = new ArrayList<>();

        sofiGlShenmaHisRepository.backupShenmaHis(sofiGlShenmaPOList);
    }
    @Test
    void testException(){
        List<SofiGlShenmaPO> sofiGlShenmaPOList = new ArrayList<>();
        SofiGlShenmaPO sofiGlShenmaPO = new SofiGlShenmaPO();
        sofiGlShenmaPO.setProcessDay(null);
        sofiGlShenmaPO.setCcy("mxn");
        sofiGlShenmaPO.setEnteredDebitAmount(null);
        sofiGlShenmaPOList.add(sofiGlShenmaPO);
        sofiGlShenmaHisRepository.backupShenmaHis(sofiGlShenmaPOList);
    }
}
