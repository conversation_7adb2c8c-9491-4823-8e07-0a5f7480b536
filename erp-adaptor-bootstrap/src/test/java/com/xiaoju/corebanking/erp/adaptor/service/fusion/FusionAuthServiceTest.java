package com.xiaoju.corebanking.erp.adaptor.service.fusion;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.security.Signature;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
* <p> FusionAuthService Tester. </p>
* <p> 2025-06-07 21:04:14.080 </p>
*
* <AUTHOR>
* @version 0.0.1-SNAPSHOT
*/
@ExtendWith(MockitoExtension.class)
public class FusionAuthServiceTest {

    @InjectMocks
    FusionAuthService fusionAuthService;

    @Mock
    private ResourceLoader resourceLoader;

    @Mock
    private Resource certResource;

    @Mock
    private Resource keyResource;

    @Mock
    private X509Certificate certificate;

    @Mock
    private CertificateFactory certificateFactory;


    @BeforeEach
    void setUp() throws NoSuchFieldException, IllegalAccessException, IOException {
        Field fusionSoapUrlField = FusionAuthService.class.getDeclaredField("certificatePath");
        fusionSoapUrlField.setAccessible(true);
        fusionSoapUrlField.set(fusionAuthService, "http://test.soap.url");

        Field fusionUsernameField = FusionAuthService.class.getDeclaredField("privateKeyPath");
        fusionUsernameField.setAccessible(true);
        fusionUsernameField.set(fusionAuthService, "classpath:cert/pub.pem");

        Field fusionUsernameField2 = FusionAuthService.class.getDeclaredField("expirationMinutes");
        fusionUsernameField2.setAccessible(true);
        fusionUsernameField2.set(fusionAuthService, 30);

        Field fusionUsernameField3 = FusionAuthService.class.getDeclaredField("issuer");
        fusionUsernameField3.setAccessible(true);
        fusionUsernameField3.set(fusionAuthService, "www.didiglobal.com");

        lenient().when(resourceLoader.getResource(anyString())).thenReturn(certResource);
        lenient().when(certResource.getInputStream()).thenReturn(new ByteArrayInputStream(new byte[]{1, 2, 3}));

        lenient().when(keyResource.getInputStream()).thenReturn(new ByteArrayInputStream(new byte[]{4, 5, 6}));
    }



    /**
    *
    * Method: generateJwtToken(username)
    */
    @Test
    public void generateJwtTokenTest() throws Exception {
        String username = "testUser";
        CertificateFactory mockCertFactory = mock(CertificateFactory.class);
        X509Certificate mockCert = mock(X509Certificate.class);

        lenient().when(mockCertFactory.generateCertificate(any(InputStream.class))).thenReturn(mockCert);
        lenient().when(mockCert.getEncoded()).thenReturn("MOCK_DER_ENCODED_CERT".getBytes());

        Signature mockSignature = mock(Signature.class);

        lenient().when(mockSignature.sign()).thenReturn("FAKE_SIGNATURE".getBytes());

        try (MockedStatic<CertificateFactory> mockedStatic1 = Mockito.mockStatic(CertificateFactory.class)) {

            try (MockedStatic<Signature> mockedStatic2 = mockStatic(Signature.class)) {
                mockedStatic1.when(() -> CertificateFactory.getInstance("X.509")).thenReturn(mockCertFactory);
                mockedStatic2.when(() -> Signature.getInstance("SHA256withRSA")).thenReturn(mockSignature);

                java.lang.reflect.Method method = FusionAuthService.class.getDeclaredMethod("calculateX5t");
                method.setAccessible(true);
                try {
                    fusionAuthService.generateJwtToken(username);
                } catch (RuntimeException e) {

                }
            }
        }
    }


}
