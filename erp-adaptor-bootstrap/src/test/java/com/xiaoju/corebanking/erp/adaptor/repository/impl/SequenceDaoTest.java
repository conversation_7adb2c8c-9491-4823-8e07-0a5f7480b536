package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SequenceMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SequenceDaoTest {
    @InjectMocks
    SequenceDao sequenceDao;

    @Mock
    SequenceMapper sequenceMapper;

    @Test
    void getCurrentValue() {
        Long target = 1L;
        when(sequenceMapper.getCurrentValue("test")).thenReturn(target);
        Long result = sequenceDao.getCurrentValue("test");
        assertEquals(target, result);
    }

    @Test
    void getCurrentValueWithLock() {
        Long target = 1L;
        when(sequenceMapper.getCurrentValueWithLock("test")).thenReturn(target);
        Long result = sequenceDao.getCurrentValueWithLock("test");
        assertEquals(target, result);
    }

    @Test
    void atomicIncrement() {
        String name = "test";
        long expectedValue = 1L;
        int increment = 1;
        int target = 2;
        when(sequenceMapper.atomicIncrement(name, expectedValue, increment)).thenReturn(target);
        int result = sequenceDao.atomicIncrement(name, expectedValue, increment);
        assertEquals(target, result);

    }

    @Test
    void createSequence() {
        String name = "test";
        long startValue = 1L;
        int batchSize = 1;
        int target = 1;
        when(sequenceMapper.createSequence(name, startValue, batchSize)).thenReturn(target);
        int result = sequenceDao.createSequence(name, startValue, batchSize);
        assertEquals(target, result);
    }

    @Test
    void updateValue() {
        String name = "test";
        long startValue = 1L;
        int target = 1;
        when(sequenceMapper.updateValue(name, startValue)).thenReturn(target);
        int result = sequenceDao.updateValue(name, startValue);
        assertEquals(target, result);
    }

    @Test
    void getBatchSize() {
        int target = 1;
        when(sequenceMapper.getBatchSize("test")).thenReturn(target);
        int result = sequenceDao.getBatchSize("test");
        assertEquals(target, result);
    }

    @Test
    void updateBatchSize() {
        String name = "test";
        int batchSize = 1;
        int target = 1;
        when(sequenceMapper.updateBatchSize(name, batchSize)).thenReturn(target);
        int result = sequenceDao.updateBatchSize(name,batchSize);
        assertEquals(target, result);

    }
}