package com.xiaoju.corebanking.erp.adaptor.service.transaction.impl;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;
import com.xiaoju.corebanking.erp.adaptor.service.header.SofiGlInterfaceHeaderService;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlShenMaService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SofiShenMaTransactionServiceImplTest {
    @InjectMocks
    SofiShenMaTransactionServiceImpl sofiShenMaTransactionService;
    @Mock
    private SofiGlShenMaService sofiGlShenMaService;
    @Mock
    private SofiGlInterfaceHeaderService sofiGlInterfaceHeaderService;
    @Test
    void updateShenMaInterfaceAndHeader() {
        SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO = new SofiGlInterfaceHeaderDO();
        SofiGlShenmaDO sofiGlShenmaDO = new SofiGlShenmaDO();
        sofiShenMaTransactionService.updateShenMaInterfaceAndHeader(sofiGlInterfaceHeaderDO,sofiGlShenmaDO,ProcessStatusEnum.PROCESSING);
    }
}