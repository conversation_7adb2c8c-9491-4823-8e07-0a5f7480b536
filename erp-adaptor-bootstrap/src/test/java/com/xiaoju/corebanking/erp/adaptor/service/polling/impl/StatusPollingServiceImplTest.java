package com.xiaoju.corebanking.erp.adaptor.service.polling.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.enums.RequestStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.enums.SourceSysEnum;
import com.xiaoju.corebanking.erp.adaptor.common.utils.ErpUtil;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceCommonRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceCommonDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;
import com.xiaoju.corebanking.erp.adaptor.repository.impl.SofiGlInterfaceCommonRepositoryImpl;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePO;
import com.xiaoju.corebanking.erp.adaptor.service.fusion.FusionAuthService;
import com.xiaoju.corebanking.erp.adaptor.service.header.SofiGlInterfaceHeaderService;
import com.xiaoju.corebanking.erp.adaptor.service.inter.SofiGlInterfaceService;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlInterfaceCommonService;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlShenMaService;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.impl.SofiGlInterfaceCommonServiceImpl;
import com.xiaoju.corebanking.erp.adaptor.service.transaction.SofiGlInterfaceTransactionService;
import com.xiaoju.corebanking.erp.adaptor.service.twotab.TwoTabValueService;
import com.xiaoju.digitalbank.ddd.po.ExecResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.wildfly.common.Assert.assertTrue;

/**
 * <p> StatusPollingServiceImpl Tester. </p>
 * <p> 2025-06-07 21:04:14.049 </p>
 *
 * <AUTHOR>
 * @version 0.0.1-SNAPSHOT
 */
@ExtendWith(MockitoExtension.class)
public class StatusPollingServiceImplTest {

    @Mock
    private SofiGlInterfaceHeaderService sofiGlInterfaceHeaderService;

    @Mock
    private SofiGlInterfaceService sofiGlInterfaceService;

    @Mock
    private SofiGlInterfaceTransactionService sofiGlInterfaceTransactionService;

    @Mock
    private RestTemplate restTemplate;

    @Mock
    private FusionAuthService fusionAuthService;

    @Mock
    private ErpUtil erpUtil;

    @InjectMocks
    private StatusPollingServiceImpl statusPollingService;

    @Mock
    private TwoTabValueService twoTabValueService;

    @Mock
    private SofiGlInterfaceCommonService sofiGlInterfaceCommonService;

    @Mock
    SofiGlShenMaService sofiGlShenMaService;

    @Mock
    SofiGlInterfaceCommonRepository sofiGlInterfaceCommonRepository;


    @BeforeEach
    public void setUp() {
        ReflectionTestUtils.setField(statusPollingService, "statusApiPath", "http://test.api/status");
        ReflectionTestUtils.setField(statusPollingService, "journalImportApiPath", "http://test.api/journal-import");
        ReflectionTestUtils.setField(statusPollingService, "issuer", "www.test.com");

        lenient().when(twoTabValueService.selectSimpleByFormCode(CommonConstant.COMPANY_LEDGER_MAPPINGS)).thenReturn(Arrays.asList(new CuxTwoTabValuePO() {{
            setId(1L);
            setValue5("121");
            setValue7("121");
        }}));
    }

    /**
     * Method: pollLoadRequestStatus()
     */
    @Test
    public void pollLoadRequestStatusTest() throws Exception {
        when(sofiGlInterfaceHeaderService.queryHeaderData(any(), anyString())).thenReturn(Collections.emptyList());

        ExecResult result = statusPollingService.pollLoadRequestStatus(SourceSysEnum.SAFI.getCode(), "20250521");

        assertNotNull(result);
    }

    @Test
    public void pollLoadRequestStatusTest1() throws Exception {
        when(sofiGlInterfaceHeaderService.queryHeaderData(any(), anyString())).thenReturn(Arrays.asList(new SofiGlInterfaceHeaderDO() {{
            setExternalReference("1001");
            setProcessStatus(ProcessStatusEnum.LOADED);
            setProcessDay("20250521");
            setGroupId(101L);
        }}));

        when(sofiGlInterfaceService.queryByPolizaIdsAndProcessDay(any(), anyString())).thenReturn(Arrays.asList(new SofiGlInterfaceDO() {{
            setPolizaId(1001L);
            setProcessStatus(ProcessStatusEnum.LOADED);
            setLoadRequestId(2001L);
            setProcessDay("20250521");
            setGroupId(101L);
        }}));


        JSONObject responseJson = new JSONObject();
        JSONArray items = new JSONArray();
        JSONObject item = new JSONObject();
        item.put("RequestStatus", RequestStatusEnum.SUCCEEDED.getCode());
        items.add(item);
        responseJson.put("items", items);

        ResponseEntity<String> responseEntity = new ResponseEntity<>(responseJson.toJSONString(), HttpStatus.OK);
        lenient().when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
                .thenReturn(responseEntity);

        JSONObject responseJson1 = new JSONObject();
        responseJson1.put("ReqstId", 1);
        ResponseEntity<String> responseEntity1 = new ResponseEntity<>(responseJson1.toJSONString(), HttpStatus.OK);

        lenient().when(restTemplate.exchange(anyString(), eq(HttpMethod.POST), any(HttpEntity.class), eq(String.class)))
                .thenReturn(responseEntity1);

        statusPollingService.pollLoadRequestStatus(SourceSysEnum.SAFI.getCode(),"20250521");
    }

    @Test
    public void callJournalImportProcessTest1()  {

        when(sofiGlInterfaceHeaderService.queryHeaderData(any(), anyString())).thenReturn(Arrays.asList(new SofiGlInterfaceHeaderDO() {{
            setExternalReference("1001");
            setProcessStatus(ProcessStatusEnum.LOADED);
            setProcessDay("20250521");
            setGroupId(101L);
        }}));

        when(sofiGlInterfaceService.queryByPolizaIdsAndProcessDay(any(), anyString())).thenReturn(Arrays.asList(new SofiGlInterfaceDO() {{
            setPolizaId(1001L);
            setProcessStatus(ProcessStatusEnum.LOADED);
            setLoadRequestId(2001L);
            setProcessDay("20250521");
            setGroupId(101L);
        }}));

        JSONObject responseJson2 = new JSONObject();
        JSONArray items2 = new JSONArray();
        JSONObject item2 = new JSONObject();
        item2.put("RequestStatus", RequestStatusEnum.FAILED.getCode());
        items2.add(item2);
        responseJson2.put("items", items2);

        ResponseEntity<String> responseEntity2 = new ResponseEntity<>(responseJson2.toJSONString(), HttpStatus.OK);
        lenient().when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
                .thenReturn(responseEntity2);

        JSONObject responseJson3 = new JSONObject();
        responseJson3.put("ReqstId", 1);
        ResponseEntity<String> responseEntity3 = new ResponseEntity<>(responseJson3.toJSONString(), HttpStatus.OK);

        lenient().when(restTemplate.exchange(anyString(), eq(HttpMethod.POST), any(HttpEntity.class), eq(String.class)))
                .thenReturn(responseEntity3);


        ExecResult result3 = statusPollingService.pollLoadRequestStatus(SourceSysEnum.SAFI.getCode(),"20250521");

        assertNotNull(result3);
    }

    /**
     * Method: callJournalImportProcess(interfaceDO)
     */
    @Test
    public void callJournalImportProcessTest() throws Exception {
        lenient().when(erpUtil.getDateDirFromInput(anyString())).thenReturn("20250521");

        List<SofiGlInterfaceHeaderDO> headerList = new ArrayList<>();
        SofiGlInterfaceHeaderDO header = new SofiGlInterfaceHeaderDO();
        header.setExternalReference("1001");
        header.setProcessStatus(ProcessStatusEnum.LOADED);
        headerList.add(header);

        lenient().when(sofiGlInterfaceHeaderService.queryHeaderData(any(), anyString())).thenReturn(headerList);

        List<SofiGlInterfaceDO> interfaceList = new ArrayList<>();
        SofiGlInterfaceDO interfaceDO = new SofiGlInterfaceDO();
        interfaceDO.setPolizaId(1001L);
        interfaceDO.setGroupId(101L);
        interfaceDO.setLoadRequestId(2001L);
        interfaceList.add(interfaceDO);

        lenient().when(sofiGlInterfaceService.queryByPolizaId(anyList())).thenReturn(interfaceList);

        JSONObject responseJson = new JSONObject();
        JSONArray items = new JSONArray();
        JSONObject item = new JSONObject();
        item.put("RequestStatus", RequestStatusEnum.SUCCEEDED.getCode());
        items.add(item);
        responseJson.put("items", items);

        ResponseEntity<String> responseEntity = new ResponseEntity<>(responseJson.toJSONString(), HttpStatus.OK);
        lenient().when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
                .thenReturn(responseEntity);

        JSONObject importResponse = new JSONObject();
        importResponse.put("ReqstId", "3001");
        ResponseEntity<String> importResponseEntity = new ResponseEntity<>(importResponse.toJSONString(), HttpStatus.OK);
        lenient().when(restTemplate.exchange(eq("http://test.api/journal-import"), eq(HttpMethod.POST), any(HttpEntity.class), eq(String.class)))
                .thenReturn(importResponseEntity);

        lenient().when(fusionAuthService.generateJwtToken(anyString())).thenReturn("test-token");

        ExecResult result = statusPollingService.pollLoadRequestStatus(SourceSysEnum.SAFI.getCode(),"20250521");

        assertNotNull(result);
    }

    @Test
    public void pollLoadRequestStatusTest2() throws Exception {
        List<SofiGlInterfaceHeaderDO> headerList = new ArrayList<>();
        SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO = new SofiGlInterfaceHeaderDO();
        sofiGlInterfaceHeaderDO.setExternalReference("1001");
        sofiGlInterfaceHeaderDO.setProcessDay("2025");
        sofiGlInterfaceHeaderDO.setProcessStatus(ProcessStatusEnum.LOADED);
        headerList.add(sofiGlInterfaceHeaderDO);
        when(sofiGlInterfaceHeaderService.queryHeaderData(any(), anyString())).thenReturn(headerList);

        List<SofiGlInterfaceDO> allInterfaces = new ArrayList<>();
        SofiGlInterfaceDO sofiGlInterfaceDO = new SofiGlInterfaceDO();
        sofiGlInterfaceDO.setPolizaId(1001L);
        sofiGlInterfaceDO.setGroupId(1L);
        allInterfaces.add(sofiGlInterfaceDO);
        when(sofiGlInterfaceService.queryByPolizaIdsAndProcessDay(getPolizaIds(headerList), sofiGlInterfaceHeaderDO.getProcessDay())).thenReturn(allInterfaces);

        ExecResult result = statusPollingService.pollLoadRequestStatus(SourceSysEnum.SAFI.getCode(),"20250521");

        assertNotNull(result);
    }

    @Test
    public void pollLoadRequestStatusTest3(){
        List<SofiGlInterfaceHeaderDO> headerList = new ArrayList<>();
        SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO = new SofiGlInterfaceHeaderDO();
        sofiGlInterfaceHeaderDO.setExternalReference("1001");
        sofiGlInterfaceHeaderDO.setProcessDay("2025");
        sofiGlInterfaceHeaderDO.setProcessStatus(ProcessStatusEnum.LOADED);
        headerList.add(sofiGlInterfaceHeaderDO);
        when(sofiGlInterfaceHeaderService.queryHeaderData(any(), anyString())).thenReturn(headerList);

        List<SofiGlInterfaceDO> allInterfaces = new ArrayList<>();
        SofiGlInterfaceDO sofiGlInterfaceDO = new SofiGlInterfaceDO();
        sofiGlInterfaceDO.setPolizaId(1001L);
        sofiGlInterfaceDO.setGroupId(1L);
        sofiGlInterfaceDO.setLoadRequestId(1L);
        allInterfaces.add(sofiGlInterfaceDO);
        when(sofiGlInterfaceService.queryByPolizaIdsAndProcessDay(getPolizaIds(headerList), sofiGlInterfaceHeaderDO.getProcessDay())).thenReturn(allInterfaces);

        ExecResult result = statusPollingService.pollLoadRequestStatus(SourceSysEnum.SAFI.getCode(),"20250521");

        assertNotNull(result);
    }

    private List<String> getPolizaIds(List<SofiGlInterfaceHeaderDO> headerList) {
        return headerList.stream()
                .map(SofiGlInterfaceHeaderDO::getExternalReference)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * Method: checkRequestStatus(loadRequestId)
     */
    @Test
    public void checkRequestStatusTest() throws Exception {
        when(fusionAuthService.generateJwtToken(anyString())).thenReturn("test-token");
        java.lang.reflect.Method method = StatusPollingServiceImpl.class.getDeclaredMethod("createHeaders");
        method.setAccessible(true);
        Object result = method.invoke(statusPollingService);
        assertNotNull(result);
        assertTrue(result instanceof org.springframework.http.HttpHeaders);
    }

    @Test
    public void testGetRequestStatus_Success() throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {
        Long loadRequestId = 2001L;

        JSONObject responseJson = new JSONObject();
        JSONArray items = new JSONArray();
        JSONObject item = new JSONObject();
        item.put("RequestStatus", RequestStatusEnum.SUCCEEDED.getCode());
        items.add(item);
        responseJson.put("items", items);

        ResponseEntity<String> responseEntity = new ResponseEntity<>(responseJson.toJSONString(), HttpStatus.OK);
        when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
                .thenReturn(responseEntity);

        when(fusionAuthService.generateJwtToken(anyString())).thenReturn("test-token");

        java.lang.reflect.Method method = StatusPollingServiceImpl.class.getDeclaredMethod("getRequestStatus", Long.class);
        method.setAccessible(true);
        Object result = method.invoke(statusPollingService, loadRequestId);

        assertNotNull(result);
        assertEquals(RequestStatusEnum.SUCCEEDED.getCode(), result);

    }

    @Test
    public void testGetRequestStatus_Error() throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {
        Long loadRequestId = 2001L;

        when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
                .thenThrow(new RuntimeException("API调用异常"));

        when(fusionAuthService.generateJwtToken(anyString())).thenReturn("test-token");

        java.lang.reflect.Method method = StatusPollingServiceImpl.class.getDeclaredMethod("getRequestStatus", Long.class);
        method.setAccessible(true);
        Object result = method.invoke(statusPollingService, loadRequestId);

        assertNull(result);
    }

    @Test
    public void testCallJournalImportProcess_Success() {
        SofiGlInterfaceDO interfaceDO = new SofiGlInterfaceDO();
        interfaceDO.setPolizaId(1001L);
        interfaceDO.setGroupId(101L);
        interfaceDO.setLedgerId(201L);
        interfaceDO.setJournalSourceName("COMPANY_LEDGER_MAPPINGS");

        JSONObject responseJson = new JSONObject();
        responseJson.put("ReqstId", "3001");

        ResponseEntity<String> responseEntity = new ResponseEntity<>(responseJson.toJSONString(), HttpStatus.OK);
        lenient().when(restTemplate.exchange(eq("http://test.api/journal-import"), eq(HttpMethod.POST), any(HttpEntity.class), eq(String.class)))
                .thenReturn(responseEntity);

        lenient().when(fusionAuthService.generateJwtToken(anyString())).thenReturn("test-token");

        Long result = statusPollingService.callJournalImportProcess(interfaceDO);

    }

    @Test
    public void testCallJournalImportProcess_Error() {
        SofiGlInterfaceDO interfaceDO = new SofiGlInterfaceDO();
        interfaceDO.setPolizaId(1001L);
        interfaceDO.setGroupId(101L);
        interfaceDO.setLedgerId(201L);
        interfaceDO.setJournalSourceName("TEST_SOURCE");

        lenient().when(restTemplate.exchange(eq("http://test.api/journal-import"), eq(HttpMethod.POST), any(HttpEntity.class), eq(String.class)))
                .thenThrow(new RuntimeException("API调用异常"));

        lenient().when(fusionAuthService.generateJwtToken(anyString())).thenReturn("test-token");

        Long result = statusPollingService.callJournalImportProcess(interfaceDO);
        assertNull(result);

    }

    @Test
    public void testCheckRequestStatus() {
        Long loadRequestId = 2001L;

        JSONObject responseJson = new JSONObject();
        JSONArray items = new JSONArray();
        JSONObject item = new JSONObject();
        item.put("RequestStatus", RequestStatusEnum.SUCCEEDED.getCode());
        items.add(item);
        responseJson.put("items", items);

        ResponseEntity<String> responseEntity = new ResponseEntity<>(responseJson.toJSONString(), HttpStatus.OK);
        when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
                .thenReturn(responseEntity);

        when(fusionAuthService.generateJwtToken(anyString())).thenReturn("test-token");

        boolean result = statusPollingService.checkRequestStatus(loadRequestId);

        assertTrue(result);
    }
    @Test
    void pollLoadRequestStatusAsyncTest() {
        statusPollingService.pollLoadRequestStatusAsync();
    }

    @Test
    void pollLoadRequestStatusAsyncTestNotNull() {
        List<SofiGlInterfaceHeaderDO> headerList = new ArrayList<>();
        SofiGlInterfaceHeaderDO headerDO = new SofiGlInterfaceHeaderDO();
        headerDO.setGroupId(1L);
        headerDO.setExternalReference("111");
        headerDO.setProcessStatus(ProcessStatusEnum.LOADED);
        headerDO.setProcessDay("2025");
        headerList.add(headerDO);
        SofiGlInterfaceHeaderDO queryDO = new SofiGlInterfaceHeaderDO();
        queryDO.setProcessStatus(ProcessStatusEnum.LOADED);
        when(sofiGlInterfaceHeaderService.queryHeaderData(queryDO, SourceSysEnum.SAFI.getCode())).thenReturn(headerList);

        List<SofiGlInterfaceDO> allInterfaces = new ArrayList<>();
        SofiGlInterfaceDO sofiGlInterfaceDO = new SofiGlInterfaceDO();
        sofiGlInterfaceDO.setProcessDay("2025");
        sofiGlInterfaceDO.setGroupId(1L);
        allInterfaces.add(sofiGlInterfaceDO);
        List<String> list = Arrays.asList("111");
        when(sofiGlInterfaceService.queryByPolizaIdsAndProcessDay(list, "2025")).thenReturn(allInterfaces);

        statusPollingService.pollLoadRequestStatusAsync();
    }
    @Test
    public void pollLoadRequestStatusShenmaTest3() throws Exception {
        List<SofiGlInterfaceHeaderDO> headerList = new ArrayList<>();
        SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO = new SofiGlInterfaceHeaderDO();
        sofiGlInterfaceHeaderDO.setExternalReference("1001");
        sofiGlInterfaceHeaderDO.setProcessDay("2025");
        sofiGlInterfaceHeaderDO.setProcessStatus(ProcessStatusEnum.LOADED);
        headerList.add(sofiGlInterfaceHeaderDO);
        when(sofiGlInterfaceHeaderService.queryHeaderData(any(), anyString())).thenReturn(headerList);

        List<SofiGlInterfaceDO> allInterfaces = new ArrayList<>();
        SofiGlInterfaceDO sofiGlInterfaceDO = new SofiGlInterfaceDO();
        sofiGlInterfaceDO.setPolizaId(1001L);
        sofiGlInterfaceDO.setGroupId(1L);
        sofiGlInterfaceDO.setLoadRequestId(1L);
        allInterfaces.add(sofiGlInterfaceDO);
        List< SofiGlInterfaceCommonDO > allCommonInterfaces = new ArrayList<>();
        when( sofiGlInterfaceCommonService.queryByReference5AndProcessDay(anyList(), anyString())).thenReturn(allCommonInterfaces);
        ExecResult result = statusPollingService.pollLoadRequestStatus(SourceSysEnum.SHEN_MA.getCode(),"20250521");
    }
    @Test
    public void pollLoadRequestStatusShenmaTest4(){
        List<SofiGlInterfaceHeaderDO> headerList = new ArrayList<>();
        SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO = new SofiGlInterfaceHeaderDO();
        sofiGlInterfaceHeaderDO.setExternalReference("1001");
        sofiGlInterfaceHeaderDO.setProcessDay("2025");
        sofiGlInterfaceHeaderDO.setProcessStatus(ProcessStatusEnum.LOADED);
        headerList.add(sofiGlInterfaceHeaderDO);
        when(sofiGlInterfaceHeaderService.queryHeaderData(any(), anyString())).thenReturn(headerList);

        List<SofiGlInterfaceDO> allInterfaces = new ArrayList<>();
        SofiGlInterfaceDO sofiGlInterfaceDO = new SofiGlInterfaceDO();
        sofiGlInterfaceDO.setPolizaId(1001L);
        sofiGlInterfaceDO.setGroupId(1L);
        sofiGlInterfaceDO.setProcessDay("20250521");
        sofiGlInterfaceDO.setLoadRequestId(1L);
        allInterfaces.add(sofiGlInterfaceDO);
        List< SofiGlInterfaceCommonDO > allCommonInterfaces = new ArrayList<>();
        SofiGlInterfaceCommonDO commonDO = new SofiGlInterfaceCommonDO();
        commonDO.setGroupId(1L);
        allCommonInterfaces.add(commonDO);
        when( sofiGlInterfaceCommonService.queryByReference5AndProcessDay(anyList(), anyString())).thenReturn(allCommonInterfaces);
        List< SofiGlShenmaDO > allShenmaInterfaces = new ArrayList<>();
        SofiGlShenmaDO sofiGlShenmaDO = new SofiGlShenmaDO();
        sofiGlShenmaDO.setGroupId(1L);
        allShenmaInterfaces.add(sofiGlShenmaDO);
        when(sofiGlShenMaService.queryByPolizaIdsAndProcessDay(anyList(), anyString())).thenReturn(allShenmaInterfaces);
        ExecResult result = statusPollingService.pollLoadRequestStatus(SourceSysEnum.SHEN_MA.getCode(),"20250521");

        commonDO.setLoadRequestId(1L);
        ExecResult result2 = statusPollingService.pollLoadRequestStatus(SourceSysEnum.SHEN_MA.getCode(),"20250521");

    }
    @Test
    void testcallJournalImportProcessForCommon(){
        SofiGlInterfaceCommonDO commonDO = new SofiGlInterfaceCommonDO();
        commonDO.setReference5("1");
        commonDO.setLedgerId(1L);
        commonDO.setGroupId(1L);
        commonDO.setSegment1("1");
        List<CuxTwoTabValuePO> companyMappings = new ArrayList<>();
        CuxTwoTabValuePO po = new CuxTwoTabValuePO();
        po.setValue5("1");
        po.setValue7("1");
        companyMappings.add(po);
        when(twoTabValueService.selectSimpleByFormCode(CommonConstant.COMPANY_LEDGER_MAPPINGS)).thenReturn(companyMappings);
        statusPollingService.callJournalImportProcessForCommon(commonDO);

    }
}
