package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.repository.converter.SofiGlAcctHistConverter;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlAcctHistDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SofiGlAcctHistCustomerMapper;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlAcctHistPOMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SofiGlAcctHistRepositoryImplTest {
    @InjectMocks
    SofiGlAcctHistRepositoryImpl sofiGlAcctHistRepository;

    @Mock
    private SofiGlAcctHistPOMapper sofiGlAcctHistPOMapper;

    @Mock
    private SofiGlAcctHistConverter sofiGlAcctHistConverter;

    @Mock
    private SofiGlAcctHistCustomerMapper sofiGlAcctHistCustomerMapper;

    @Test
    void syncSofiGlAcctHistData() {
        List<SofiGlAcctHistDO> sofiGlAcctHistList = new ArrayList<>();
        sofiGlAcctHistRepository.syncSofiGlAcctHistData(sofiGlAcctHistList);
    }
    @Test
    void syncSofiGlAcctHistDataNotNull() {
        List<SofiGlAcctHistDO> sofiGlAcctHistList = new ArrayList<>();
        SofiGlAcctHistDO sofiGlAcctHistDO = new SofiGlAcctHistDO();
        sofiGlAcctHistDO.setObjectVersionNumber(1L);
        sofiGlAcctHistDO.setCreationDate(new Date());
        sofiGlAcctHistDO.setCreatedBy(CommonConstant.SYSTEM_USER);
        sofiGlAcctHistDO.setLastModifyDate(new Date());
        sofiGlAcctHistDO.setLastModifiedBy(CommonConstant.SYSTEM_USER);
        sofiGlAcctHistList.add(sofiGlAcctHistDO);

        when(sofiGlAcctHistPOMapper.insertSelective(any())).thenReturn(1);
        sofiGlAcctHistRepository.syncSofiGlAcctHistData(sofiGlAcctHistList);
    }

    @Test
    void selectAcctHisSummary() {
        String processDay = "2025-08-11";
        sofiGlAcctHistRepository.selectAcctHisSummary(processDay);
    }
}