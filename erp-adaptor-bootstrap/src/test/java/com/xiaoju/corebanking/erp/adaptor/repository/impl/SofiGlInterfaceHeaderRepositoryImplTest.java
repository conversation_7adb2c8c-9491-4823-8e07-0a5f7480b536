package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.enums.SourceSysEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.converter.SofiGlInterfaceHeaderModelConverter;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderQueryDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHeaderPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SofiGlInterfaceHeaderCustomerMapper;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.mockito.Mockito.when;

/**
* <p> SofiGlInterfaceHeaderRepositoryImpl Tester. </p>
* <p> 2025-06-06 11:40:17.959 </p>
*
* <AUTHOR>
* @version 0.0.1-SNAPSHOT
*/
@ExtendWith(MockitoExtension.class)
public class SofiGlInterfaceHeaderRepositoryImplTest {
    @InjectMocks
    SofiGlInterfaceHeaderRepositoryImpl sofiGlInterfaceHeaderRepository;
    @Mock
    private SofiGlInterfaceHeaderCustomerMapper sofiGlInterfaceHeaderCustomerMapper;

    @Mock
    private SofiGlInterfaceHeaderModelConverter sofiGlInterfaceHeaderModelConverter;

    /**
    *
    * Method: queryList(sofiGlInterfaceHeaderQueryDO)
    */
    @Test
    public void queryListTest() throws Exception {
        SofiGlInterfaceHeaderQueryDO queryDO = new SofiGlInterfaceHeaderQueryDO();
        queryDO.setPageNum(1);
        queryDO.setPageSize(100);
        sofiGlInterfaceHeaderRepository.queryList(queryDO);
    }

    /**
    *
    * Method: updateInterfaceHeader(sofiGlInterfaceHeaderDO, update)
    */
    @Test
    public void updateInterfaceHeaderTest() throws Exception {
        SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO = new SofiGlInterfaceHeaderDO();
        SofiGlInterfaceHeaderDO update = new SofiGlInterfaceHeaderDO();
        Assert.assertThrows(IllegalArgumentException.class,()->sofiGlInterfaceHeaderRepository.updateInterfaceHeader(sofiGlInterfaceHeaderDO,update,SourceSysEnum.SAFI.getDesc()));
    }
    @Test
    public void updateInterfaceHeaderNotTest() throws Exception {
        SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO = new SofiGlInterfaceHeaderDO();
        sofiGlInterfaceHeaderDO.setExternalReference("1");
        sofiGlInterfaceHeaderDO.setProcessStatus(ProcessStatusEnum.PROCESSING);
        sofiGlInterfaceHeaderDO.setObjectVersionNumber(1L);
        SofiGlInterfaceHeaderDO update = new SofiGlInterfaceHeaderDO();
        sofiGlInterfaceHeaderRepository.updateInterfaceHeader(sofiGlInterfaceHeaderDO,update, SourceSysEnum.SAFI.getCode());
    }

    /**
    *
    * Method: insertSelective(sfiGlInterfaceHeaderDO)
    */
    @Test
    public void insertSelectiveTest() throws Exception {
        SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO = new SofiGlInterfaceHeaderDO();
        sofiGlInterfaceHeaderDO.setExternalReference("1");
        sofiGlInterfaceHeaderDO.setProcessStatus(ProcessStatusEnum.PROCESSING);
        sofiGlInterfaceHeaderDO.setObjectVersionNumber(1L);
        sofiGlInterfaceHeaderDO.setProcessDay("1");
        sofiGlInterfaceHeaderDO.setBatchId("1");
        sofiGlInterfaceHeaderDO.setJournalCount(1L);
        sofiGlInterfaceHeaderDO.setProcessMessage("1");
        sofiGlInterfaceHeaderDO.setCreatedBy("1");
        sofiGlInterfaceHeaderDO.setCreationDate(new Date());
        sofiGlInterfaceHeaderDO.setLastModifyDate(new Date());
        sofiGlInterfaceHeaderDO.setLastModifiedBy("1");
        sofiGlInterfaceHeaderDO.setGroupId(1L);
        sofiGlInterfaceHeaderRepository.insertSelective(sofiGlInterfaceHeaderDO);
    }

    /**
    *
    * Method: batchInsertInterfaceHeader(headerList)
    */
    @Test
    public void batchInsertInterfaceHeaderTest() throws Exception {
        List<SofiGlInterfaceHeaderDO> headerList = new ArrayList<>();
        sofiGlInterfaceHeaderRepository.batchInsertInterfaceHeader(headerList);
    }
    @Test
    public void batchInsertTest(){
        SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO = new SofiGlInterfaceHeaderDO();
        sofiGlInterfaceHeaderDO.setExternalReference("1");
        sofiGlInterfaceHeaderDO.setProcessStatus(ProcessStatusEnum.PROCESSING);
        sofiGlInterfaceHeaderDO.setObjectVersionNumber(1L);
        sofiGlInterfaceHeaderDO.setProcessDay("1");
        sofiGlInterfaceHeaderDO.setBatchId("1");
        sofiGlInterfaceHeaderDO.setJournalCount(1L);
        sofiGlInterfaceHeaderDO.setProcessMessage("1");
        sofiGlInterfaceHeaderDO.setCreatedBy("1");
        sofiGlInterfaceHeaderDO.setCreationDate(new Date());
        sofiGlInterfaceHeaderDO.setLastModifyDate(new Date());
        sofiGlInterfaceHeaderDO.setLastModifiedBy("1");
        sofiGlInterfaceHeaderDO.setGroupId(1L);
        List<SofiGlInterfaceHeaderDO> headerList = new ArrayList<>();
        headerList.add(sofiGlInterfaceHeaderDO);
        SofiGlInterfaceHeaderPO existingRecord = new SofiGlInterfaceHeaderPO();
        existingRecord.setExternalReference("1");
        when(sofiGlInterfaceHeaderCustomerMapper.findByPolizaIds(Arrays.asList(sofiGlInterfaceHeaderDO.getExternalReference()))).thenReturn(Arrays.asList(existingRecord));
        sofiGlInterfaceHeaderRepository.batchInsertInterfaceHeader(headerList);

    }

    /**
    *
    * Method: queryData(sofiGlInterfaceHeaderDO)
    */
    @Test
    public void queryDataTest() throws Exception {
        SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO = new SofiGlInterfaceHeaderDO();
        sofiGlInterfaceHeaderDO.setExternalReference("1");
        sofiGlInterfaceHeaderDO.setProcessStatus(ProcessStatusEnum.PROCESSING);
        sofiGlInterfaceHeaderDO.setObjectVersionNumber(1L);
        sofiGlInterfaceHeaderDO.setProcessDay("1");
        sofiGlInterfaceHeaderDO.setBatchId("1");
        sofiGlInterfaceHeaderDO.setJournalCount(1L);
        sofiGlInterfaceHeaderDO.setProcessMessage("1");
        sofiGlInterfaceHeaderDO.setCreatedBy("1");
        sofiGlInterfaceHeaderDO.setCreationDate(new Date());
        sofiGlInterfaceHeaderDO.setLastModifyDate(new Date());
        sofiGlInterfaceHeaderDO.setLastModifiedBy("1");
        sofiGlInterfaceHeaderDO.setGroupId(1L);
        sofiGlInterfaceHeaderRepository.queryData(sofiGlInterfaceHeaderDO,"safi");
    }
    @Test
    void testbBatchInsert() {
        SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO = new SofiGlInterfaceHeaderDO();
        sofiGlInterfaceHeaderDO.setExternalReference("1");
        sofiGlInterfaceHeaderDO.setProcessStatus(ProcessStatusEnum.PROCESSING);
        sofiGlInterfaceHeaderDO.setObjectVersionNumber(1L);
        sofiGlInterfaceHeaderDO.setProcessDay("1");
        sofiGlInterfaceHeaderDO.setBatchId("1");
        sofiGlInterfaceHeaderDO.setJournalCount(1L);
        sofiGlInterfaceHeaderDO.setProcessMessage("1");
        sofiGlInterfaceHeaderDO.setCreatedBy("1");
        sofiGlInterfaceHeaderDO.setCreationDate(new Date());
        sofiGlInterfaceHeaderDO.setLastModifyDate(new Date());
        sofiGlInterfaceHeaderDO.setLastModifiedBy("1");
        sofiGlInterfaceHeaderDO.setGroupId(1L);
        List<SofiGlInterfaceHeaderDO> headerList = new ArrayList<>();
        headerList.add(sofiGlInterfaceHeaderDO);
        sofiGlInterfaceHeaderRepository.batchInsert(headerList);
    }
    @Test
    void testSelectGroupByProcessDay() {
        sofiGlInterfaceHeaderRepository.selectGroupByProcessDay("","");
    }
    @Test
    void testUpdateInterfaceHeaderByGroupId() {
        sofiGlInterfaceHeaderRepository.updateInterfaceHeaderByGroupId("","",1L,ProcessStatusEnum.PROCESSING,ProcessStatusEnum.VALIDATED);
    }
    @Test
    public void queryDataTestSafi() throws Exception {
        SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO = new SofiGlInterfaceHeaderDO();
        sofiGlInterfaceHeaderDO.setExternalReference("1");
        sofiGlInterfaceHeaderDO.setProcessStatus(ProcessStatusEnum.PROCESSING);
        sofiGlInterfaceHeaderDO.setObjectVersionNumber(1L);
        sofiGlInterfaceHeaderDO.setProcessDay("1");
        sofiGlInterfaceHeaderDO.setBatchId("1");
        sofiGlInterfaceHeaderDO.setJournalCount(1L);
        sofiGlInterfaceHeaderDO.setProcessMessage("1");
        sofiGlInterfaceHeaderDO.setCreatedBy("1");
        sofiGlInterfaceHeaderDO.setCreationDate(new Date());
        sofiGlInterfaceHeaderDO.setLastModifyDate(new Date());
        sofiGlInterfaceHeaderDO.setLastModifiedBy("1");
        sofiGlInterfaceHeaderDO.setGroupId(1L);
        sofiGlInterfaceHeaderRepository.queryData(sofiGlInterfaceHeaderDO,SourceSysEnum.SAFI.getCode());
    }
}
