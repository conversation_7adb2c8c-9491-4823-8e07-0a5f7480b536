package com.xiaoju.corebanking.erp.adaptor.service.header.impl;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.enums.SourceSysEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceHeaderRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderQueryDO;
import com.xiaoju.corebanking.erp.adaptor.service.header.impl.SofiGlInterfaceHeaderServiceImpl;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * <p> SofiGlInterfaceHeaderServiceImpl Tester. </p>
 * <p> 2025-06-07 21:04:14.057 </p>
 *
 * <AUTHOR>
 * @version 0.0.1-SNAPSHOT
 */
@ExtendWith(MockitoExtension.class)
public class SofiGlInterfaceHeaderServiceImplTest {

    @InjectMocks
    private SofiGlInterfaceHeaderServiceImpl sofiGlInterfaceHeaderService;

    @Mock
    private SofiGlInterfaceHeaderRepository sofiGlInterfaceHeaderRepository;

    /**
     * Method: queryInterfaceHeaderList(sofiGlInterfaceHeaderQueryDO)
     */
    @Test
    public void queryInterfaceHeaderListTest() throws Exception {
        SofiGlInterfaceHeaderQueryDO queryDO = new SofiGlInterfaceHeaderQueryDO();
        List<SofiGlInterfaceHeaderDO> expectedList = new ArrayList<>();
        expectedList.add(new SofiGlInterfaceHeaderDO());

        when(sofiGlInterfaceHeaderRepository.queryList(queryDO)).thenReturn(expectedList);

        List<SofiGlInterfaceHeaderDO> result = sofiGlInterfaceHeaderService.queryInterfaceHeaderList(queryDO);

        assertEquals(expectedList, result);
    }

    /**
     * Method: queryHeaderData(sofiGlInterfaceHeaderDO)
     */
    @Test
    public void queryHeaderDataTest() throws Exception {
        SofiGlInterfaceHeaderDO headerDO = new SofiGlInterfaceHeaderDO();
        List<SofiGlInterfaceHeaderDO> expectedData = new ArrayList<>();
        expectedData.add(new SofiGlInterfaceHeaderDO());

        when(sofiGlInterfaceHeaderRepository.queryData(headerDO,"safi")).thenReturn(expectedData);

        List<SofiGlInterfaceHeaderDO> result = sofiGlInterfaceHeaderService.queryHeaderData(headerDO,"safi");

        assertEquals(expectedData, result);
    }

    @Test
    void findGroupIdByProcessDayTest(){
        sofiGlInterfaceHeaderService.findGroupIdByProcessDay("2025-09-09", SourceSysEnum.SAFI.getCode());
    }
    @Test
    void updateInterfaceHeaderByGroupIdTest(){
        String systemCode = SourceSysEnum.SAFI.getCode();
        String processDay = "2025-09-09";
        Long groupId = 1L;
        ProcessStatusEnum oldStatus = ProcessStatusEnum.NEW;
        ProcessStatusEnum newStatus = ProcessStatusEnum.FAILED;
        sofiGlInterfaceHeaderService.updateInterfaceHeaderByGroupId(systemCode, processDay,groupId,oldStatus,newStatus);
    }

    @Test
    public void updateInterfaceHeaderSystemcodeTest() throws Exception {
        SofiGlInterfaceHeaderDO headerDO = new SofiGlInterfaceHeaderDO();
        SofiGlInterfaceHeaderDO updateDO = new SofiGlInterfaceHeaderDO();

        sofiGlInterfaceHeaderService.updateInterfaceHeader(headerDO, updateDO,SourceSysEnum.SAFI.getDesc());
    }
}
