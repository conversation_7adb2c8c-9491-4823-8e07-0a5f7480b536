package com.xiaoju.corebanking.erp.adaptor.service.file;

import com.xiaoju.corebanking.erp.adaptor.common.domain.FusionUcmResponse;
import com.xiaoju.corebanking.erp.adaptor.common.domain.SubmitESSJobDTO;
import com.xiaoju.corebanking.erp.adaptor.common.domain.SubmitESSJobResponse;
import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.utils.CSVToZipUtil;
import com.xiaoju.corebanking.erp.adaptor.common.utils.FileUtil;
import com.xiaoju.corebanking.erp.adaptor.common.utils.ZipToBase64Util;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceCommonRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceCommonDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;
import com.xiaoju.corebanking.erp.adaptor.service.file.impl.FusionFileProcessServiceImpl;
import com.xiaoju.corebanking.erp.adaptor.service.fusion.FunsionFileImportService;
import com.xiaoju.corebanking.erp.adaptor.service.fusion.FunsionUCMService;
import com.xiaoju.corebanking.erp.adaptor.service.header.SofiGlInterfaceHeaderService;
import com.xiaoju.corebanking.erp.adaptor.service.inter.SofiGlInterfaceService;
import com.xiaoju.corebanking.erp.adaptor.service.shenma.SofiGlShenMaService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * <p> FusionFileProcessServiceImpl Tester. </p>
 * <p> 2025-06-07 21:04:14.055 </p>
 *
 * <AUTHOR>
 * @version 0.0.1-SNAPSHOT
 */
@ExtendWith(MockitoExtension.class)
public class FusionFileProcessServiceImplTest {

    @InjectMocks
    private FusionFileProcessServiceImpl fusionFileProcessService;

    @Mock
    private SofiGlInterfaceRepository sofiGlInterfaceRepository;
    @Mock
    private CSVToZipUtil csvToZipUtil;
    @Mock
    private ZipToBase64Util zipToBase64Util;
    @Mock
    private FileUtil fileUtil;
    @Mock
    private FunsionUCMService funsionUCMService;

    @Mock
    private SofiGlInterfaceHeaderService sofiGlInterfaceHeaderService;

    @Mock
    private SofiGlInterfaceService sofiGlInterfaceService;

    @Mock
    private FunsionFileImportService funsionFileImportService;

    @Mock
    private SofiGlInterfaceCommonRepository sofiGlInterfaceCommonRepository;

    @Mock
    private SofiGlShenMaService sofiGlShenMaService;

    private List<Long> groupIds;
    private String token;
    private SofiGlInterfaceDO sofiGlInterfaceDO;
    private List<SofiGlInterfaceDO> sofiGlInterfaceDOList;
    private FusionUcmResponse fusionUcmResponse;
    private SubmitESSJobResponse submitESSJobResponse;


    @BeforeEach
    void setUp() {
        groupIds = Arrays.asList(123L, 456L);
        token = "test-token";

        sofiGlInterfaceDO = new SofiGlInterfaceDO();
        sofiGlInterfaceDO.setGroupId(123L);
        sofiGlInterfaceDO.setLedgerId(123L);
        sofiGlInterfaceDO.setFecha("2023-05-15");
        sofiGlInterfaceDO.setFechaActual("2023-05-15");
        sofiGlInterfaceDO.setJournalCategory("category");
        sofiGlInterfaceDO.setJournalSource("source");
        sofiGlInterfaceDO.setCurrencyCode("USD");
        sofiGlInterfaceDO.setSegment1("seg1");
        sofiGlInterfaceDO.setSegment2("seg2");
        sofiGlInterfaceDO.setSegment3("seg3");
        sofiGlInterfaceDO.setSegment4("seg4");
        sofiGlInterfaceDO.setSegment5("seg5");
        sofiGlInterfaceDO.setSegment6("seg6");
        sofiGlInterfaceDO.setSegment7("seg7");
        sofiGlInterfaceDO.setSegment8("seg8");
        sofiGlInterfaceDO.setSegment9("seg9");
        sofiGlInterfaceDO.setSegment10("seg10");
        sofiGlInterfaceDO.setCargos(BigDecimal.ONE);
        sofiGlInterfaceDO.setAbonos(BigDecimal.ONE);
        sofiGlInterfaceDO.setReferencia("ref123");
        sofiGlInterfaceDO.setPolizaId(123L);
        sofiGlInterfaceDO.setDetallePolizaId(123L);
        sofiGlInterfaceDO.setDescripcion("Test description");
        sofiGlInterfaceDO.setSourceSys("SYS");
        sofiGlInterfaceDO.setNumTransaccion(123L);
        sofiGlInterfaceDO.setFolioUuid("folio123");
        sofiGlInterfaceDO.setRfc("rfc123");

        sofiGlInterfaceDOList = Arrays.asList(sofiGlInterfaceDO);

        fusionUcmResponse = new FusionUcmResponse();
        fusionUcmResponse.setDocumentId("1001");

        submitESSJobResponse = new SubmitESSJobResponse();
        submitESSJobResponse.setReqstId("2001");
    }

    /**
     * Method: processCSVFile(groupIds, token)
     */
    @Test
    public void processCSVFileTest() throws Exception {
        when(sofiGlInterfaceRepository.queryGLInterfaceData(any(SofiGlInterfaceDO.class))).thenReturn(sofiGlInterfaceDOList);
        FusionUcmResponse fusionUcmResponse1 = new FusionUcmResponse();
        fusionUcmResponse1.setDocumentId("1002");
        fusionUcmResponse1.setDocumentId("123");
        fusionUcmResponse.setDocumentId("2342");

        when(zipToBase64Util.zipToBase64(any())).thenReturn("base64");

        when(sofiGlInterfaceHeaderService.queryInterfaceHeaderList(any())).thenReturn(Arrays.asList(new SofiGlInterfaceHeaderDO() {{
            setId(1L);
        }}));

        doNothing().when(sofiGlInterfaceHeaderService).updateInterfaceHeader(any(), any(),anyString());

        when(sofiGlInterfaceService.querySofiGlInterface(any())).thenReturn(sofiGlInterfaceDOList);

        doReturn(fusionUcmResponse1)
                .doReturn(fusionUcmResponse)
                .when(funsionUCMService).uploadFileToUCM(anyString(), anyString(), anyString());

        when(funsionFileImportService.importFile(any(SubmitESSJobDTO.class), anyString())).thenReturn(submitESSJobResponse);

        fusionFileProcessService.processCSVFile(groupIds, token);
    }

    /**
     * Method: updateUploadStatus(groupId, oldStatus, processStatusEnum, documentId, loadRequestId)
     */
    @Test
    public void updateUploadStatusTest() throws Exception {

        when(sofiGlInterfaceRepository.queryGLInterfaceData(any(SofiGlInterfaceDO.class))).thenReturn(sofiGlInterfaceDOList);
        FusionUcmResponse fusionUcmResponse1 = new FusionUcmResponse();
        fusionUcmResponse1.setDocumentId("1002");
        fusionUcmResponse1.setDocumentId("123");
        fusionUcmResponse.setDocumentId("2342");

        when(zipToBase64Util.zipToBase64(any())).thenReturn("base64");

        when(sofiGlInterfaceHeaderService.queryInterfaceHeaderList(any())).thenReturn(Arrays.asList(new SofiGlInterfaceHeaderDO() {{
            setId(1L);
        }}));

        doNothing().when(sofiGlInterfaceHeaderService).updateInterfaceHeader(any(), any(),anyString());

        when(sofiGlInterfaceService.querySofiGlInterface(any())).thenReturn(sofiGlInterfaceDOList);

        doReturn(fusionUcmResponse1)
                .doReturn(fusionUcmResponse)
                .when(funsionUCMService).uploadFileToUCM(anyString(), anyString(), anyString());

        when(funsionFileImportService.importFile(any(SubmitESSJobDTO.class), anyString())).thenReturn(null);

        fusionFileProcessService.processCSVFile(groupIds, token);
    }

    @Test
    public void updateUploadStatusTest2() throws Exception {

        when(sofiGlInterfaceRepository.queryGLInterfaceData(any(SofiGlInterfaceDO.class))).thenReturn(sofiGlInterfaceDOList);
        FusionUcmResponse fusionUcmResponse1 = new FusionUcmResponse();
        fusionUcmResponse1.setDocumentId("1002");
        fusionUcmResponse1.setDocumentId("123");
        fusionUcmResponse.setDocumentId("2342");

        when(zipToBase64Util.zipToBase64(any())).thenReturn("base64");

        when(sofiGlInterfaceHeaderService.queryInterfaceHeaderList(any())).thenReturn(Arrays.asList(new SofiGlInterfaceHeaderDO() {{
            setId(1L);
            setProcessStatus(ProcessStatusEnum.PROCESSING);
        }}));

        doNothing().when(sofiGlInterfaceHeaderService).updateInterfaceHeader(any(), any(),anyString());

        when(sofiGlInterfaceService.querySofiGlInterface(any())).thenReturn(sofiGlInterfaceDOList);

        doReturn(null)
                .doReturn(fusionUcmResponse)
                .when(funsionUCMService).uploadFileToUCM(anyString(), anyString(), anyString());

        when(funsionFileImportService.importFile(any(SubmitESSJobDTO.class), anyString())).thenReturn(null);

        fusionFileProcessService.processCSVFile(groupIds, token);


        sofiGlInterfaceDO.setDescripcion("Test descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest descriptionTest description");
        sofiGlInterfaceDOList = Arrays.asList(sofiGlInterfaceDO);

        fusionFileProcessService.processCSVFile(groupIds, token);

    }
    @Test
    public void processCommonCSVFileTest() throws Exception {
        String systemCode ="shenma";
        String processDay = "2025-08-12";
        Long groupId = 123L;
        List<Long> groupIds = Arrays.asList(groupId);
        List<SofiGlInterfaceCommonDO> sofiGlInterfaceCommonDOS = new ArrayList<>();
        SofiGlInterfaceCommonDO commonDO = new SofiGlInterfaceCommonDO();
        commonDO.setSystemCode("test");
        commonDO.setProcessDay("test");
        commonDO.setAccountingDate(new Date());
        commonDO.setPeriodName("test");
        commonDO.setLedgerId(1L);
        commonDO.setLedgerName("test");
        commonDO.setCurrencyCode("test");
        commonDO.setJournalCategory("test");
        commonDO.setJournalSource("test");
        commonDO.setJournalSourceName("test");
        commonDO.setReference1("test");
        commonDO.setReference2("test");
        commonDO.setReference3("test");
        commonDO.setReference4("test");
        commonDO.setReference5("test");
        commonDO.setReference6("test");
        commonDO.setReference7("test");
        commonDO.setReference8("test");
        commonDO.setReference9("test");
        commonDO.setReference10("test");
        commonDO.setReference21("test");
        commonDO.setReference22("test");
        commonDO.setReference23("test");
        commonDO.setReference24("test");
        commonDO.setReference25("test");
        commonDO.setReference26("test");
        commonDO.setReference27("test");
        commonDO.setReference28("test");
        commonDO.setReference29("test");
        commonDO.setReference30("test");
        commonDO.setSegment1("test");
        commonDO.setSegment2("test");
        commonDO.setSegment3("test");
        commonDO.setSegment4("test");
        commonDO.setSegment5("test");
        commonDO.setSegment6("test");
        commonDO.setSegment7("test");
        commonDO.setSegment8("test");
        commonDO.setSegment9("test");
        commonDO.setSegment10("test");
        commonDO.setEnteredDr(new BigDecimal(0));
        commonDO.setEnteredCr(new BigDecimal(0));
        commonDO.setAccountedDr(new BigDecimal(0));
        commonDO.setAccountedCr(new BigDecimal(0));
        commonDO.setGroupId(123L);
        commonDO.setCurrencyConversionDate(new Date());
        commonDO.setCurrencyConversionRate(new BigDecimal(0));
        commonDO.setCurrencyConversionType("test");
        commonDO.setAttributeCategory("test");
        commonDO.setHeaderAttribute1("test");
        commonDO.setHeaderAttribute2("test");
        commonDO.setHeaderAttribute3("test");
        commonDO.setHeaderAttribute4("test");
        commonDO.setHeaderAttribute5("test");
        commonDO.setHeaderAttribute6("test");
        commonDO.setHeaderAttribute7("test");
        commonDO.setHeaderAttribute8("test");
        commonDO.setHeaderAttribute9("test");
        commonDO.setHeaderAttribute10("test");
        commonDO.setHeaderAttribute11("test");
        commonDO.setHeaderAttribute12("test");
        commonDO.setHeaderAttribute13("test");
        commonDO.setHeaderAttribute14("test");
        commonDO.setHeaderAttribute15("test");
        commonDO.setAttributeCategory3("test");
        commonDO.setLineAttribute1("test");
        commonDO.setLineAttribute2("test");
        commonDO.setLineAttribute3("test");
        commonDO.setLineAttribute4("test");
        commonDO.setLineAttribute5("test");
        commonDO.setLineAttribute6("test");
        commonDO.setLineAttribute7("test");
        commonDO.setLineAttribute8("test");
        commonDO.setLineAttribute9("test");
        commonDO.setLineAttribute10("test");
        commonDO.setLineAttribute11("test");
        commonDO.setLineAttribute12("test");
        commonDO.setLineAttribute13("test");
        commonDO.setLineAttribute14("test");
        commonDO.setLineAttribute15("test");
        commonDO.setLineAttribute16("test");
        commonDO.setLineAttribute17("test");
        commonDO.setLineAttribute18("test");
        commonDO.setLineAttribute19("test");
        commonDO.setLineAttribute20("test");
        commonDO.setProcessStatus(ProcessStatusEnum.PROCESSING);
        commonDO.setProcessMessage("test");
        commonDO.setJeHeaderId(1L);
        commonDO.setJournalName("test");
        commonDO.setJeLineNum(1L);
        commonDO.setDocumentId(1L);
        commonDO.setLoadRequestId(1L);
        commonDO.setImportRequestId(1L);
        commonDO.setObjectVersionNumber(1L);
        commonDO.setCreationDate(new Date());
        commonDO.setCreatedBy("test");
        commonDO.setLastModifyDate(new Date());
        commonDO.setLastModifiedBy("test");
        sofiGlInterfaceCommonDOS.add(commonDO);
        when(sofiGlInterfaceCommonRepository.querySofiGlInterfaceCommonDOList(systemCode,processDay,groupId)).thenReturn(sofiGlInterfaceCommonDOS);

        FusionUcmResponse fusionUcmResponse1 = new FusionUcmResponse();
        fusionUcmResponse1.setDocumentId("1002");
        fusionUcmResponse1.setDocumentId("123");
        fusionUcmResponse.setDocumentId("2342");

        when(zipToBase64Util.zipToBase64(any())).thenReturn("base64");

//        when(sofiGlInterfaceHeaderService.queryInterfaceHeaderList(any())).thenReturn(Arrays.asList(new SofiGlInterfaceHeaderDO() {{
//            setId(1L);
//            setProcessStatus(ProcessStatusEnum.PROCESSING);
//        }}));
//
//        doNothing().when(sofiGlInterfaceHeaderService).updateInterfaceHeader(any(), any());
//
//        when(sofiGlInterfaceService.querySofiGlInterface(any())).thenReturn(sofiGlInterfaceDOList);

        doReturn(fusionUcmResponse1)
                .doReturn(fusionUcmResponse)
                .when(funsionUCMService).uploadFileToUCM(anyString(), anyString(), anyString());

        when(funsionFileImportService.importFile(any(SubmitESSJobDTO.class), anyString())).thenReturn(submitESSJobResponse);
        SofiGlShenmaDO update = new SofiGlShenmaDO();
        update.setProcessStatus(ProcessStatusEnum.UPLOADED);
        doNothing().when(sofiGlShenMaService).updateByProcessDayAndGroupIdAndStatus(processDay,groupId,ProcessStatusEnum.MAPPED.getCode(),update);
        SofiGlInterfaceCommonDO commonDO1  = new SofiGlInterfaceCommonDO();
        commonDO1.setProcessStatus(ProcessStatusEnum.UPLOADED);
        commonDO1.setDocumentId(123L);
        when(sofiGlInterfaceCommonRepository.updateSofiGlInterfaceCommon(systemCode,processDay,ProcessStatusEnum.MAPPED.getCode(),groupId,commonDO1)).thenReturn(1);

        fusionFileProcessService.processCSVFileToCommon(systemCode,processDay,groupIds, token);
    }

}
