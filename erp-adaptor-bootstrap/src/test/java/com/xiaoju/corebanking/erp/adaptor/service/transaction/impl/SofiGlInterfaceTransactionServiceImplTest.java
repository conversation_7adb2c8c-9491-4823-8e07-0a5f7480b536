package com.xiaoju.corebanking.erp.adaptor.service.transaction.impl;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.service.header.SofiGlInterfaceHeaderService;
import com.xiaoju.corebanking.erp.adaptor.service.inter.SofiGlInterfaceService;
import com.xiaoju.corebanking.erp.adaptor.service.transaction.impl.SofiGlInterfaceTransactionServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
* <p> SofiGlInterfaceTransactionServiceImpl Tester. </p>
* <p> 2025-06-07 21:04:14.075 </p>
*
* <AUTHOR>
* @version 0.0.1-SNAPSHOT
*/
@ExtendWith(MockitoExtension.class)
public class SofiGlInterfaceTransactionServiceImplTest {

    @InjectMocks
    private SofiGlInterfaceTransactionServiceImpl sofiGlInterfaceTransactionService;

    @Mock
    private SofiGlInterfaceHeaderService sofiGlInterfaceHeaderService;

    @Mock
    private SofiGlInterfaceService sofiGlInterfaceService;

    @Test
    void updateInterfaceAndInterfaceHeaderByPolizaIdTest(){
        SofiGlInterfaceHeaderDO headerDO = new SofiGlInterfaceHeaderDO();
        headerDO.setGroupId(123L);
        headerDO.setProcessStatus(ProcessStatusEnum.PROCESSING);
        headerDO.setProcessMessage("aaaa");
        SofiGlInterfaceDO interfaceDO = new SofiGlInterfaceDO();
        interfaceDO.setGroupId(123L);
        interfaceDO.setProcessStatus(ProcessStatusEnum.PROCESSING);
        interfaceDO.setLedgerId(123L);
        interfaceDO.setLedgerName("测试账本");
        interfaceDO.setSegment1("segment1");
        interfaceDO.setJournalSource("source");
        interfaceDO.setJournalSourceName("sourceName");
        interfaceDO.setJournalCategory("category");
        interfaceDO.setCurrencyCode("CNY");
        interfaceDO.setSegment2("segment2");
        interfaceDO.setSegment3("segment3");
        interfaceDO.setSegment4("segment4");
        interfaceDO.setSegment5("segment5");
        interfaceDO.setSegment6("segment6");
        interfaceDO.setSegment7("segment7");
        interfaceDO.setSegment8("segment8");
        interfaceDO.setSegment9("segment9");
        interfaceDO.setSegment10("segment10");
        interfaceDO.setProcessMessage("处理中");

        ProcessStatusEnum newStatus = ProcessStatusEnum.MAP_FAILED;
        sofiGlInterfaceTransactionService.updateInterfaceAndInterfaceHeaderByPolizaId(headerDO,interfaceDO,newStatus);
    }
    @Test
    public void updateInterfaceAndInterfaceHeaderTest() throws Exception {
        SofiGlInterfaceHeaderDO headerDO = new SofiGlInterfaceHeaderDO();
        headerDO.setGroupId(123L);
        headerDO.setProcessStatus(ProcessStatusEnum.PROCESSING);

        SofiGlInterfaceDO interfaceDO = new SofiGlInterfaceDO();
        interfaceDO.setGroupId(123L);
        interfaceDO.setProcessStatus(ProcessStatusEnum.PROCESSING);
        interfaceDO.setLedgerId(123L);
        interfaceDO.setLedgerName("测试账本");
        interfaceDO.setSegment1("segment1");
        interfaceDO.setJournalSource("source");
        interfaceDO.setJournalSourceName("sourceName");
        interfaceDO.setJournalCategory("category");
        interfaceDO.setCurrencyCode("CNY");
        interfaceDO.setSegment2("segment2");
        interfaceDO.setSegment3("segment3");
        interfaceDO.setSegment4("segment4");
        interfaceDO.setSegment5("segment5");
        interfaceDO.setSegment6("segment6");
        interfaceDO.setSegment7("segment7");
        interfaceDO.setSegment8("segment8");
        interfaceDO.setSegment9("segment9");
        interfaceDO.setSegment10("segment10");
        interfaceDO.setProcessMessage("处理中");

        ProcessStatusEnum newStatus = ProcessStatusEnum.VALIDATED;

        sofiGlInterfaceTransactionService.updateInterfaceAndInterfaceHeader(headerDO, interfaceDO, newStatus);
    }

}
