package com.xiaoju.corebanking.erp.adaptor.repository.converter;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceCommonDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceCommonPO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 类描述
 * @author: didi
 **/
@ExtendWith(MockitoExtension.class)
public class SofiGlInterfaceCommonModelConverterTest {
    @InjectMocks
    private SofiGlInterfaceCommonModelConverter converter;
    @Test
    public void testConverterPO(){
        SofiGlInterfaceCommonDO commonDO = new SofiGlInterfaceCommonDO();
        commonDO.setSystemCode("test");
        commonDO.setProcessDay("test");
        commonDO.setAccountingDate(new Date());
        commonDO.setPeriodName("test");
        commonDO.setLedgerId(1L);
        commonDO.setLedgerName("test");
        commonDO.setCurrencyCode("test");
        commonDO.setJournalCategory("test");
        commonDO.setJournalSource("test");
        commonDO.setJournalSourceName("test");
        commonDO.setReference1("test");
        commonDO.setReference2("test");
        commonDO.setReference3("test");
        commonDO.setReference4("test");
        commonDO.setReference5("test");
        commonDO.setReference6("test");
        commonDO.setReference7("test");
        commonDO.setReference8("test");
        commonDO.setReference9("test");
        commonDO.setReference10("test");
        commonDO.setReference21("test");
        commonDO.setReference22("test");
        commonDO.setReference23("test");
        commonDO.setReference24("test");
        commonDO.setReference25("test");
        commonDO.setReference26("test");
        commonDO.setReference27("test");
        commonDO.setReference28("test");
        commonDO.setReference29("test");
        commonDO.setReference30("test");
        commonDO.setSegment1("test");
        commonDO.setSegment2("test");
        commonDO.setSegment3("test");
        commonDO.setSegment4("test");
        commonDO.setSegment5("test");
        commonDO.setSegment6("test");
        commonDO.setSegment7("test");
        commonDO.setSegment8("test");
        commonDO.setSegment9("test");
        commonDO.setSegment10("test");
        commonDO.setEnteredDr(new BigDecimal(0));
        commonDO.setEnteredCr(new BigDecimal(0));
        commonDO.setAccountedDr(new BigDecimal(0));
        commonDO.setAccountedCr(new BigDecimal(0));
        commonDO.setGroupId(1L);
        commonDO.setCurrencyConversionDate(new Date());
        commonDO.setCurrencyConversionRate(new BigDecimal(0));
        commonDO.setCurrencyConversionType("test");
        commonDO.setAttributeCategory("test");
        commonDO.setHeaderAttribute1("test");
        commonDO.setHeaderAttribute2("test");
        commonDO.setHeaderAttribute3("test");
        commonDO.setHeaderAttribute4("test");
        commonDO.setHeaderAttribute5("test");
        commonDO.setHeaderAttribute6("test");
        commonDO.setHeaderAttribute7("test");
        commonDO.setHeaderAttribute8("test");
        commonDO.setHeaderAttribute9("test");
        commonDO.setHeaderAttribute10("test");
        commonDO.setHeaderAttribute11("test");
        commonDO.setHeaderAttribute12("test");
        commonDO.setHeaderAttribute13("test");
        commonDO.setHeaderAttribute14("test");
        commonDO.setHeaderAttribute15("test");
        commonDO.setAttributeCategory3("test");
        commonDO.setLineAttribute1("test");
        commonDO.setLineAttribute2("test");
        commonDO.setLineAttribute3("test");
        commonDO.setLineAttribute4("test");
        commonDO.setLineAttribute5("test");
        commonDO.setLineAttribute6("test");
        commonDO.setLineAttribute7("test");
        commonDO.setLineAttribute8("test");
        commonDO.setLineAttribute9("test");
        commonDO.setLineAttribute10("test");
        commonDO.setLineAttribute11("test");
        commonDO.setLineAttribute12("test");
        commonDO.setLineAttribute13("test");
        commonDO.setLineAttribute14("test");
        commonDO.setLineAttribute15("test");
        commonDO.setLineAttribute16("test");
        commonDO.setLineAttribute17("test");
        commonDO.setLineAttribute18("test");
        commonDO.setLineAttribute19("test");
        commonDO.setLineAttribute20("test");
        commonDO.setProcessStatus(ProcessStatusEnum.PROCESSING);
        commonDO.setProcessMessage("test");
        commonDO.setJeHeaderId(1L);
        commonDO.setJournalName("test");
        commonDO.setJeLineNum(1L);
        commonDO.setDocumentId(1L);
        commonDO.setLoadRequestId(1L);
        commonDO.setImportRequestId(1L);
        commonDO.setObjectVersionNumber(1L);
        commonDO.setCreationDate(new Date());
        commonDO.setCreatedBy("test");
        commonDO.setLastModifyDate(new Date());
        commonDO.setLastModifiedBy("test");
        converter.convert(commonDO);
    }

    @Test
    public void testConverterDO(){
        SofiGlInterfaceCommonPO commonPO = new SofiGlInterfaceCommonPO();
        commonPO.setSystemCode("test");
        commonPO.setProcessDay("test");
        commonPO.setAccountingDate(new Date());
        commonPO.setPeriodName("test");
        commonPO.setLedgerId(1L);
        commonPO.setLedgerName("test");
        commonPO.setCurrencyCode("test");
        commonPO.setJournalCategory("test");
        commonPO.setJournalSource("test");
        commonPO.setJournalSourceName("test");
        commonPO.setReference1("test");
        commonPO.setReference2("test");
        commonPO.setReference3("test");
        commonPO.setReference4("test");
        commonPO.setReference5("test");
        commonPO.setReference6("test");
        commonPO.setReference7("test");
        commonPO.setReference8("test");
        commonPO.setReference9("test");
        commonPO.setReference10("test");
        commonPO.setReference21("test");
        commonPO.setReference22("test");
        commonPO.setReference23("test");
        commonPO.setReference24("test");
        commonPO.setReference25("test");
        commonPO.setReference26("test");
        commonPO.setReference27("test");
        commonPO.setReference28("test");
        commonPO.setReference29("test");
        commonPO.setReference30("test");
        commonPO.setSegment1("test");
        commonPO.setSegment2("test");
        commonPO.setSegment3("test");
        commonPO.setSegment4("test");
        commonPO.setSegment5("test");
        commonPO.setSegment6("test");
        commonPO.setSegment7("test");
        commonPO.setSegment8("test");
        commonPO.setSegment9("test");
        commonPO.setSegment10("test");
        commonPO.setEnteredDr(new BigDecimal(0));
        commonPO.setEnteredCr(new BigDecimal(0));
        commonPO.setAccountedDr(new BigDecimal(0));
        commonPO.setAccountedCr(new BigDecimal(0));
        commonPO.setGroupId(1L);
        commonPO.setCurrencyConversionDate(new Date());
        commonPO.setCurrencyConversionRate(new BigDecimal(0));
        commonPO.setCurrencyConversionType("test");
        commonPO.setAttributeCategory("test");
        commonPO.setHeaderAttribute1("test");
        commonPO.setHeaderAttribute2("test");
        commonPO.setHeaderAttribute3("test");
        commonPO.setHeaderAttribute4("test");
        commonPO.setHeaderAttribute5("test");
        commonPO.setHeaderAttribute6("test");
        commonPO.setHeaderAttribute7("test");
        commonPO.setHeaderAttribute8("test");
        commonPO.setHeaderAttribute9("test");
        commonPO.setHeaderAttribute10("test");
        commonPO.setHeaderAttribute11("test");
        commonPO.setHeaderAttribute12("test");
        commonPO.setHeaderAttribute13("test");
        commonPO.setHeaderAttribute14("test");
        commonPO.setHeaderAttribute15("test");
        commonPO.setAttributeCategory3("test");
        commonPO.setLineAttribute1("test");
        commonPO.setLineAttribute2("test");
        commonPO.setLineAttribute3("test");
        commonPO.setLineAttribute4("test");
        commonPO.setLineAttribute5("test");
        commonPO.setLineAttribute6("test");
        commonPO.setLineAttribute7("test");
        commonPO.setLineAttribute8("test");
        commonPO.setLineAttribute9("test");
        commonPO.setLineAttribute10("test");
        commonPO.setLineAttribute11("test");
        commonPO.setLineAttribute12("test");
        commonPO.setLineAttribute13("test");
        commonPO.setLineAttribute14("test");
        commonPO.setLineAttribute15("test");
        commonPO.setLineAttribute16("test");
        commonPO.setLineAttribute17("test");
        commonPO.setLineAttribute18("test");
        commonPO.setLineAttribute19("test");
        commonPO.setLineAttribute20("test");
        commonPO.setProcessStatus(ProcessStatusEnum.PROCESSING.getCode());
        commonPO.setProcessMessage("test");
        commonPO.setJeHeaderId(1L);
        commonPO.setJournalName("test");
        commonPO.setJeLineNum(1L);
        commonPO.setDocumentId(1L);
        commonPO.setLoadRequestId(1L);
        commonPO.setImportRequestId(1L);
        commonPO.setObjectVersionNumber(1L);
        commonPO.setCreationDate(new Date());
        commonPO.setCreatedBy("test");
        commonPO.setLastModifyDate(new Date());
        commonPO.setLastModifiedBy("test");
        converter.convert(commonPO);
    }
    @Test
    public void testCopyIgnoreNullValue(){
        SofiGlInterfaceCommonDO commonDO = new SofiGlInterfaceCommonDO();
        commonDO.setSystemCode("test");
        commonDO.setProcessDay("test");
        commonDO.setAccountingDate(new Date());
        commonDO.setPeriodName("test");
        commonDO.setLedgerId(1L);
        commonDO.setLedgerName("test");
        commonDO.setCurrencyCode("test");
        commonDO.setJournalCategory("test");
        commonDO.setJournalSource("test");
        commonDO.setJournalSourceName("test");
        commonDO.setReference1("test");
        commonDO.setReference2("test");
        commonDO.setReference3("test");
        commonDO.setReference4("test");
        commonDO.setReference5("test");
        commonDO.setReference6("test");
        commonDO.setReference7("test");
        commonDO.setReference8("test");
        commonDO.setReference9("test");
        commonDO.setReference10("test");
        commonDO.setReference21("test");
        commonDO.setReference22("test");
        commonDO.setReference23("test");
        commonDO.setReference24("test");
        commonDO.setReference25("test");
        commonDO.setReference26("test");
        commonDO.setReference27("test");
        commonDO.setReference28("test");
        commonDO.setReference29("test");
        commonDO.setReference30("test");
        commonDO.setSegment1("test");
        commonDO.setSegment2("test");
        commonDO.setSegment3("test");
        commonDO.setSegment4("test");
        commonDO.setSegment5("test");
        commonDO.setSegment6("test");
        commonDO.setSegment7("test");
        commonDO.setSegment8("test");
        commonDO.setSegment9("test");
        commonDO.setSegment10("test");
        commonDO.setEnteredDr(new BigDecimal(0));
        commonDO.setEnteredCr(new BigDecimal(0));
        commonDO.setAccountedDr(new BigDecimal(0));
        commonDO.setAccountedCr(new BigDecimal(0));
        commonDO.setGroupId(1L);
        commonDO.setCurrencyConversionDate(new Date());
        commonDO.setCurrencyConversionRate(new BigDecimal(0));
        commonDO.setCurrencyConversionType("test");
        commonDO.setAttributeCategory("test");
        commonDO.setHeaderAttribute1("test");
        commonDO.setHeaderAttribute2("test");
        commonDO.setHeaderAttribute3("test");
        commonDO.setHeaderAttribute4("test");
        commonDO.setHeaderAttribute5("test");
        commonDO.setHeaderAttribute6("test");
        commonDO.setHeaderAttribute7("test");
        commonDO.setHeaderAttribute8("test");
        commonDO.setHeaderAttribute9("test");
        commonDO.setHeaderAttribute10("test");
        commonDO.setHeaderAttribute11("test");
        commonDO.setHeaderAttribute12("test");
        commonDO.setHeaderAttribute13("test");
        commonDO.setHeaderAttribute14("test");
        commonDO.setHeaderAttribute15("test");
        commonDO.setAttributeCategory3("test");
        commonDO.setLineAttribute1("test");
        commonDO.setLineAttribute2("test");
        commonDO.setLineAttribute3("test");
        commonDO.setLineAttribute4("test");
        commonDO.setLineAttribute5("test");
        commonDO.setLineAttribute6("test");
        commonDO.setLineAttribute7("test");
        commonDO.setLineAttribute8("test");
        commonDO.setLineAttribute9("test");
        commonDO.setLineAttribute10("test");
        commonDO.setLineAttribute11("test");
        commonDO.setLineAttribute12("test");
        commonDO.setLineAttribute13("test");
        commonDO.setLineAttribute14("test");
        commonDO.setLineAttribute15("test");
        commonDO.setLineAttribute16("test");
        commonDO.setLineAttribute17("test");
        commonDO.setLineAttribute18("test");
        commonDO.setLineAttribute19("test");
        commonDO.setLineAttribute20("test");
        commonDO.setProcessStatus(ProcessStatusEnum.PROCESSING);
        commonDO.setProcessMessage("test");
        commonDO.setJeHeaderId(1L);
        commonDO.setJournalName("test");
        commonDO.setJeLineNum(1L);
        commonDO.setDocumentId(1L);
        commonDO.setLoadRequestId(1L);
        commonDO.setImportRequestId(1L);
        commonDO.setObjectVersionNumber(1L);
        commonDO.setCreationDate(new Date());
        commonDO.setCreatedBy("test");
        commonDO.setLastModifyDate(new Date());
        commonDO.setLastModifiedBy("test");

        SofiGlInterfaceCommonDO commonDO2 = new SofiGlInterfaceCommonDO();
        commonDO2.setSystemCode("test");
        commonDO2.setProcessDay("test");
        commonDO2.setAccountingDate(new Date());
        commonDO2.setPeriodName("test");
        commonDO2.setLedgerId(1L);
        commonDO2.setLedgerName("test");
        commonDO2.setCurrencyCode("test");
        commonDO2.setJournalCategory("test");
        commonDO2.setJournalSource("test");
        commonDO2.setJournalSourceName("test");
        commonDO2.setReference1("test");
        commonDO2.setReference2("test");
        commonDO2.setReference3("test");
        commonDO2.setReference4("test");
        commonDO2.setReference5("test");
        commonDO2.setReference6("test");
        commonDO2.setReference7("test");
        commonDO2.setReference8("test");
        commonDO2.setReference9("test");
        commonDO2.setReference10("test");
        commonDO2.setReference21("test");
        commonDO2.setReference22("test");
        commonDO2.setReference23("test");
        commonDO2.setReference24("test");
        commonDO2.setReference25("test");
        commonDO2.setReference26("test");
        commonDO2.setReference27("test");
        commonDO2.setReference28("test");
        commonDO2.setReference29("test");
        commonDO2.setReference30("test");
        commonDO2.setSegment1("test");
        commonDO2.setSegment2("test");
        commonDO2.setSegment3("test");
        commonDO2.setSegment4("test");
        commonDO2.setSegment5("test");
        commonDO2.setSegment6("test");
        commonDO2.setSegment7("test");
        commonDO2.setSegment8("test");
        commonDO2.setSegment9("test");
        commonDO2.setSegment10("test");
        commonDO2.setEnteredDr(new BigDecimal(0));
        commonDO2.setEnteredCr(new BigDecimal(0));
        commonDO2.setAccountedDr(new BigDecimal(0));
        commonDO2.setAccountedCr(new BigDecimal(0));
        commonDO2.setGroupId(1L);
        commonDO2.setCurrencyConversionDate(new Date());
        commonDO2.setCurrencyConversionRate(new BigDecimal(0));
        commonDO2.setCurrencyConversionType("test");
        commonDO2.setAttributeCategory("test");
        commonDO2.setHeaderAttribute1("test");
        commonDO2.setHeaderAttribute2("test");
        commonDO2.setHeaderAttribute3("test");
        commonDO2.setHeaderAttribute4("test");
        commonDO2.setHeaderAttribute5("test");
        commonDO2.setHeaderAttribute6("test");
        commonDO2.setHeaderAttribute7("test");
        commonDO2.setHeaderAttribute8("test");
        commonDO2.setHeaderAttribute9("test");
        commonDO2.setHeaderAttribute10("test");
        commonDO2.setHeaderAttribute11("test");
        commonDO2.setHeaderAttribute12("test");
        commonDO2.setHeaderAttribute13("test");
        commonDO2.setHeaderAttribute14("test");
        commonDO2.setHeaderAttribute15("test");
        commonDO2.setAttributeCategory3("test");
        commonDO2.setLineAttribute1("test");
        commonDO2.setLineAttribute2("test");
        commonDO2.setLineAttribute3("test");
        commonDO2.setLineAttribute4("test");
        commonDO2.setLineAttribute5("test");
        commonDO2.setLineAttribute6("test");
        commonDO2.setLineAttribute7("test");
        commonDO2.setLineAttribute8("test");
        commonDO2.setLineAttribute9("test");
        commonDO2.setLineAttribute10("test");
        commonDO2.setLineAttribute11("test");
        commonDO2.setLineAttribute12("test");
        commonDO2.setLineAttribute13("test");
        commonDO2.setLineAttribute14("test");
        commonDO2.setLineAttribute15("test");
        commonDO2.setLineAttribute16("test");
        commonDO2.setLineAttribute17("test");
        commonDO2.setLineAttribute18("test");
        commonDO2.setLineAttribute19("test");
        commonDO2.setLineAttribute20("test");
        commonDO2.setProcessStatus(ProcessStatusEnum.PROCESSING);
        commonDO2.setProcessMessage("test");
        commonDO2.setJeHeaderId(1L);
        commonDO2.setJournalName("test");
        commonDO2.setJeLineNum(1L);
        commonDO2.setDocumentId(1L);
        commonDO2.setLoadRequestId(1L);
        commonDO2.setImportRequestId(1L);
        commonDO2.setObjectVersionNumber(1L);
        commonDO2.setCreationDate(new Date());
        commonDO2.setCreatedBy("test");
        commonDO2.setLastModifyDate(new Date());
        commonDO2.setLastModifiedBy("test");
        converter.copyIgnoreNullValue(commonDO,commonDO2);
    }
}
