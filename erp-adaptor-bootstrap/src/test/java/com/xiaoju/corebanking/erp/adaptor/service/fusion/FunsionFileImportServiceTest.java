package com.xiaoju.corebanking.erp.adaptor.service.fusion;

import com.alibaba.fastjson.JSONObject;
import com.xiaoju.corebanking.erp.adaptor.common.domain.SubmitESSJobDTO;
import com.xiaoju.corebanking.erp.adaptor.common.domain.SubmitESSJobResponse;
import com.xiaoju.corebanking.erp.adaptor.service.inter.SofiGlInterfaceService;
import com.xiaoju.godson.http.entity.HttpClientConnection;
import com.xiaoju.godson.http.entity.HttpClientResponse;
import com.xiaoju.godson.http.util.HttpClientUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * <p> FunsionFileImportService Tester. </p>
 * <p> 2025-06-07 21:04:14.074 </p>
 *
 * <AUTHOR>
 * @version 0.0.1-SNAPSHOT
 */
@ExtendWith(MockitoExtension.class)
public class FunsionFileImportServiceTest {

    @InjectMocks
    FunsionFileImportService funsionFileImportService;

    @Mock
    private FusionAuthService fusionAuthService;
    @Mock
    RestTemplate restTemplate;

    @Mock
    private SofiGlInterfaceService sofiGlInterfaceService;

    private MockedStatic<HttpClientUtil> mockedHttpClientUtil;

    private HttpClientConnection mockHttpClientConnection;
    private HttpClientResponse mockHttpClientResponse;

    @BeforeEach
    void setUp() throws NoSuchFieldException, IllegalAccessException {
        java.lang.reflect.Field fusionSoapUrlField = FunsionFileImportService.class.getDeclaredField("commonUrl");
        fusionSoapUrlField.setAccessible(true);
        fusionSoapUrlField.set(funsionFileImportService, "http://test.soap.url");

        java.lang.reflect.Field fusionUsernameField = FunsionFileImportService.class.getDeclaredField("username");
        fusionUsernameField.setAccessible(true);
        fusionUsernameField.set(funsionFileImportService, "username");

        mockHttpClientConnection = mock(HttpClientConnection.class);
        mockHttpClientResponse = mock(HttpClientResponse.class);

        try {
            if (mockedHttpClientUtil != null) {
                mockedHttpClientUtil.close();
            }
            mockedHttpClientUtil = mockStatic(HttpClientUtil.class);
        } catch (Exception e) {
            mockedHttpClientUtil = null;
        }
    }

    /**
     * Method: importFile(submitESSJobDTO, token)
     */
    @Test
    public void importFileTest() throws Exception {
        SubmitESSJobDTO submitESSJobDTO = new SubmitESSJobDTO();
        submitESSJobDTO.setESSParameters("testParam");
        String token = "testToken";

        when(HttpClientUtil.post(anyString())).thenReturn(mockHttpClientConnection);

        when(mockHttpClientConnection.addHeader(anyString(), anyString())).thenReturn(mockHttpClientConnection);
        when(mockHttpClientConnection.bodyJson(anyString())).thenReturn(mockHttpClientConnection);
        when(mockHttpClientConnection.execute()).thenReturn(mockHttpClientResponse);

        when(mockHttpClientResponse.isSuccess()).thenReturn(true);

        SubmitESSJobResponse expectedResponse = new SubmitESSJobResponse();
        expectedResponse.setFileName("test123");

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("requestId", "test123");
        when(mockHttpClientResponse.getString()).thenReturn(jsonObject.toJSONString());

        SubmitESSJobResponse result = funsionFileImportService.importFile(submitESSJobDTO, token);

        assertNotNull(result);

        when(mockHttpClientConnection.execute()).thenThrow(new IOException());
        try {
           funsionFileImportService.importFile(submitESSJobDTO, token);
        } catch (Exception e) {

        }
    }

    @AfterEach
    void tearDown() {
        if (mockedHttpClientUtil != null) {
            try {
                mockedHttpClientUtil.close();
            } catch (Exception ignored) {
            }
            mockedHttpClientUtil = null;
        }
    }
}
