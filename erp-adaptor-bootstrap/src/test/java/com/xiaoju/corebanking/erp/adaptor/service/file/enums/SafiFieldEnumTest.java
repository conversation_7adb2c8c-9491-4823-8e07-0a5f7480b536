package com.xiaoju.corebanking.erp.adaptor.service.file.enums;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static com.xiaoju.corebanking.erp.adaptor.service.file.enums.SafiFieldEnum.DETALLE_POLIZA_ID;

/**
 * @Description: 类描述
 * @author: zhangcc
 * @date: 2025/6/17$
 **/
@ExtendWith(MockitoExtension.class)
public class SafiFieldEnumTest {
    @Test
    public void test(){
        final SafiFieldEnum centroCostoId = SafiFieldEnum.CENTRO_COSTO_ID;
        SofiGlInterfaceDO entity = new SofiGlInterfaceDO();
        String fieldName ="";
        String value="";
        SafiFieldEnum.setField(entity,fieldName,value);
    }
    @Test
    public void testNull(){
        final SafiFieldEnum centroCostoId = SafiFieldEnum.CENTRO_COSTO_ID;
        SofiGlInterfaceDO entity = new SofiGlInterfaceDO();
        String fieldName ="";
        String value="";
        SafiFieldEnum.setField(null,null,value);
    }
    @Test
    public void testNotNull(){

        SofiGlInterfaceDO entity = new SofiGlInterfaceDO();
        String fieldName =SafiFieldEnum.CENTRO_COSTO_ID.name();
        String value="CENTRO_COSTO_ID";
        SafiFieldEnum.setField(entity,fieldName,value);
    }
    @Test
    public void testString(){
        SofiGlInterfaceDO entity = new SofiGlInterfaceDO();
        String value="111";
        SafiFieldEnum.setField(entity,SafiFieldEnum.DETALLE_POLIZA_ID.name(),value);
        SafiFieldEnum.setField(entity,SafiFieldEnum.SOURCE_SYS.name(),value);
        SafiFieldEnum.setField(entity,SafiFieldEnum.FECHA.name(),value);
        SafiFieldEnum.setField(entity,SafiFieldEnum.CUENTA_COMPLETA.name(),value);
        SafiFieldEnum.setField(entity,SafiFieldEnum.DESCRIPCION.name(),value);
        SafiFieldEnum.setField(entity,SafiFieldEnum.PROCEDIMIENTO_CONT.name(),value);
        SafiFieldEnum.setField(entity,SafiFieldEnum.TIPO_INSTRUMENTO_ID.name(),value);
        SafiFieldEnum.setField(entity,SafiFieldEnum.RFC.name(),value);
        SafiFieldEnum.setField(entity,SafiFieldEnum.FOLIO_UUID.name(),value);
        SafiFieldEnum.setField(entity,SafiFieldEnum.FECHA_ACTUAL.name(),value);
        SafiFieldEnum.setField(entity,SafiFieldEnum.DIRECCION_IP.name(),value);
        SafiFieldEnum.setField(entity,SafiFieldEnum.PROGRAMA_ID.name(),value);
        SafiFieldEnum.setField(entity,SafiFieldEnum.POLIZA_ID.name(),value);
        SafiFieldEnum.setField(entity,SafiFieldEnum.EMPRESA_ID.name(),value);
        SafiFieldEnum.setField(entity,SafiFieldEnum.CENTRO_COSTO_ID.name(),value);
        SafiFieldEnum.setField(entity,SafiFieldEnum.INSTRUMENTO.name(),value);
        SafiFieldEnum.setField(entity,SafiFieldEnum.MONEDA_ID.name(),value);
        SafiFieldEnum.setField(entity,SafiFieldEnum.USUARIO.name(),value);
        SafiFieldEnum.setField(entity,SafiFieldEnum.SUCURSAL.name(),value);
        SafiFieldEnum.setField(entity,SafiFieldEnum.NUM_TRANSACCION.name(),value);
    }

    @Test
    public void testBigdecimal(){
        SofiGlInterfaceDO entity = new SofiGlInterfaceDO();
        String value="111";
        SafiFieldEnum.setField(entity,SafiFieldEnum.CARGOS.name(),value);
        SafiFieldEnum.setField(entity,SafiFieldEnum.ABONOS.name(),value);
        SafiFieldEnum.setField(entity,SafiFieldEnum.REFERENCIA.name(),value);
        SafiFieldEnum.setField(entity,SafiFieldEnum.CURRENCY_RATE.name(),value);
    }

}
