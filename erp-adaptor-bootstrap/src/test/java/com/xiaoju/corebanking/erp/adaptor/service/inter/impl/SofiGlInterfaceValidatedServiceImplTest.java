package com.xiaoju.corebanking.erp.adaptor.service.inter.impl;

import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.errorno.ErpAdaptorErrorNo;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.CargosAndAbonosDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValueExtraPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

/**
 * <p> SofiGlInterfaceValidatedServiceImpl Tester. </p>
 * <p> 2025-06-07 21:04:14.076 </p>
 *
 * <AUTHOR>
 * @version 0.0.1-SNAPSHOT
 */
@ExtendWith(MockitoExtension.class)
public class SofiGlInterfaceValidatedServiceImplTest {

    @InjectMocks
    private SofiGlInterfaceValidatedServiceImpl sofiGlInterfaceValidatedService;

    @Mock
    private SofiGlInterfaceServiceImpl sofiGlInterfaceService;


    private SofiGlInterfaceDO validSofiGlInterfaceDO;
    private SofiGlInterfaceDO invalidSofiGlInterfaceDO;
    private Map<String, Map<String, CuxTwoTabValueExtraPO>> cache;

    @BeforeEach
    void setUp() {
        validSofiGlInterfaceDO = new SofiGlInterfaceDO();
        validSofiGlInterfaceDO.setSourceSys("1");
        validSofiGlInterfaceDO.setEmpresaId(1L);
        validSofiGlInterfaceDO.setPolizaId(1L);
        validSofiGlInterfaceDO.setFecha("2023-01-01");
        validSofiGlInterfaceDO.setCentroCostoId(123456L);
        validSofiGlInterfaceDO.setCuentaCompleta("123456");
        validSofiGlInterfaceDO.setInstrumento(1L);
        validSofiGlInterfaceDO.setMonedaId(1L);
        validSofiGlInterfaceDO.setCargos(new BigDecimal(100));
        validSofiGlInterfaceDO.setAbonos(new BigDecimal(100));
        validSofiGlInterfaceDO.setReferencia("REF123");
        validSofiGlInterfaceDO.setFechaActual("2023-01-01");
        validSofiGlInterfaceDO.setSucursal(1L);
        validSofiGlInterfaceDO.setTipoInstrumentoId("1");

        invalidSofiGlInterfaceDO = new SofiGlInterfaceDO();
        invalidSofiGlInterfaceDO.setId(0L);
        invalidSofiGlInterfaceDO.setFileId(0L);
        invalidSofiGlInterfaceDO.setProcessDay("");
        invalidSofiGlInterfaceDO.setFileName("");
        invalidSofiGlInterfaceDO.setBatchId("");
        invalidSofiGlInterfaceDO.setDetallePolizaId(0L);
        invalidSofiGlInterfaceDO.setSourceSys("1");
        invalidSofiGlInterfaceDO.setEmpresaId(0L);
        invalidSofiGlInterfaceDO.setPolizaId(0L);
        invalidSofiGlInterfaceDO.setFecha("2023-01-01");
        invalidSofiGlInterfaceDO.setCentroCostoId(0L);
        invalidSofiGlInterfaceDO.setCuentaCompleta("123456");
        invalidSofiGlInterfaceDO.setInstrumento(0L);
        invalidSofiGlInterfaceDO.setMonedaId(1L);
        invalidSofiGlInterfaceDO.setCargos(new BigDecimal("100"));
        invalidSofiGlInterfaceDO.setAbonos(new BigDecimal("100"));
        invalidSofiGlInterfaceDO.setDescripcion("");
        invalidSofiGlInterfaceDO.setReferencia("");
        invalidSofiGlInterfaceDO.setProcedimientoCont("");
        invalidSofiGlInterfaceDO.setTipoInstrumentoId("1");
        invalidSofiGlInterfaceDO.setRfc("");
        invalidSofiGlInterfaceDO.setTotalFactura(new BigDecimal("0"));
        invalidSofiGlInterfaceDO.setFolioUuid("");
        invalidSofiGlInterfaceDO.setUsuario(0L);
        invalidSofiGlInterfaceDO.setFechaActual("2023-01-01");
        invalidSofiGlInterfaceDO.setDireccionIp("");
        invalidSofiGlInterfaceDO.setProgramaId("");
        invalidSofiGlInterfaceDO.setSucursal(0L);
        invalidSofiGlInterfaceDO.setNumTransaccion(0L);
        invalidSofiGlInterfaceDO.setLedgerId(0L);
        invalidSofiGlInterfaceDO.setLedgerName("");
        invalidSofiGlInterfaceDO.setCurrencyCode("");
        invalidSofiGlInterfaceDO.setJournalCategory("");
        invalidSofiGlInterfaceDO.setJournalSource("1");
        invalidSofiGlInterfaceDO.setJournalSourceName("");
        invalidSofiGlInterfaceDO.setSegment1("");
        invalidSofiGlInterfaceDO.setSegment2("");
        invalidSofiGlInterfaceDO.setSegment3("");
        invalidSofiGlInterfaceDO.setSegment4("");
        invalidSofiGlInterfaceDO.setSegment5("");
        invalidSofiGlInterfaceDO.setSegment6("");
        invalidSofiGlInterfaceDO.setSegment7("");
        invalidSofiGlInterfaceDO.setSegment8("");
        invalidSofiGlInterfaceDO.setSegment9("");
        invalidSofiGlInterfaceDO.setSegment10("");
        invalidSofiGlInterfaceDO.setGroupId(0L);
        invalidSofiGlInterfaceDO.setCurrencyConversionDate(new Date());
        invalidSofiGlInterfaceDO.setCurrencyConversionRate(new BigDecimal("0"));
        invalidSofiGlInterfaceDO.setUserCurrencyConversionType("");
        invalidSofiGlInterfaceDO.setProcessStatus(ProcessStatusEnum.NEW);
        invalidSofiGlInterfaceDO.setProcessMessage("");
        invalidSofiGlInterfaceDO.setJeHeaderId(0L);
        invalidSofiGlInterfaceDO.setJournalName("");
        invalidSofiGlInterfaceDO.setJeLineNum(0L);
        invalidSofiGlInterfaceDO.setDocumentId(0L);
        invalidSofiGlInterfaceDO.setLoadRequestId(0L);
        invalidSofiGlInterfaceDO.setImportRequestId(0L);
        invalidSofiGlInterfaceDO.setObjectVersionNumber(0L);
        invalidSofiGlInterfaceDO.setCreationDate(new Date());
        invalidSofiGlInterfaceDO.setCreatedBy("");
        invalidSofiGlInterfaceDO.setLastModifyDate(new Date());
        invalidSofiGlInterfaceDO.setLastModifiedBy("");
        cache = new HashMap<>();
        Map<String, CuxTwoTabValueExtraPO> categoryMappings = new HashMap<>();
        CuxTwoTabValueExtraPO mapping = new CuxTwoTabValueExtraPO();
        mapping.setValue1("1");
        categoryMappings.put("1",mapping);
        mapping.setValue1("2");
        categoryMappings.put("2",mapping);
        mapping.setValue1("3");
        categoryMappings.put("3",mapping);
        mapping.setValue1("4");
        categoryMappings.put("4",mapping);
        mapping.setValue1("5");
        categoryMappings.put("5",mapping);
        cache.put(CommonConstant.COMPANY_LEDGER_MAPPINGS, categoryMappings);
        cache.put(CommonConstant.CATEGORY_MAPPINGS, categoryMappings);
        cache.put(CommonConstant.CURRENCY_MAPPINGS, categoryMappings);
        cache.put(CommonConstant.PRODUCT_MAPPINGS, categoryMappings);
        cache.put(CommonConstant.GL_JOURNAL_SOURCE_MAPPINGS, categoryMappings);


    }

    /**
     * Method: validated(sofiGlInterfaceDO, cache)
     */
    @Test
    public void validatedTest() throws Exception {
        Map<Long, List<SofiGlInterfaceDO>> map = new HashMap<>();
        CargosAndAbonosDO validCargosAndAbonos = new CargosAndAbonosDO();
        validCargosAndAbonos.setCargos(new BigDecimal(100));
        validCargosAndAbonos.setAbonos(new BigDecimal(100));
//        when(sofiGlInterfaceService.queryCargosAndAbonosDO(anyLong())).thenReturn(validCargosAndAbonos);
        List<SofiGlInterfaceDO> dos = new ArrayList<>();
        SofiGlInterfaceDO invalidLengthDO = new SofiGlInterfaceDO();
        invalidLengthDO.setSourceSys("1");
        invalidLengthDO.setCuentaCompleta("1234567890123");
        invalidLengthDO.setCargos(BigDecimal.ONE);
        invalidLengthDO.setAbonos(BigDecimal.ONE);
        invalidLengthDO.setPolizaId(1L);
        invalidLengthDO.setEmpresaId(2L);
        dos.add(invalidLengthDO);
        map.put(invalidLengthDO.getPolizaId(),dos);
        String result = sofiGlInterfaceValidatedService.validated(validSofiGlInterfaceDO, map);
        assertEquals(ErpAdaptorErrorNo.IMP_ERR_ERROR.getErrorMsg(), result);
    }

    /**
     * Method: validatedCargosAndAbonos(sofiGlInterfaceDO)
     */
    @Test
    public void validatedCargosAndAbonosTest() throws Exception {
        Map<Long, List<SofiGlInterfaceDO>> map = new HashMap<>();
        CargosAndAbonosDO validCargosAndAbonos = new CargosAndAbonosDO();
        validCargosAndAbonos.setCargos(new BigDecimal(100));
        validCargosAndAbonos.setAbonos(new BigDecimal(100));
        List<SofiGlInterfaceDO> dos = new ArrayList<>();
        SofiGlInterfaceDO invalidLengthDO = new SofiGlInterfaceDO();
        invalidLengthDO.setSourceSys("1");
        invalidLengthDO.setCuentaCompleta("1234567890123");
        invalidLengthDO.setCargos(BigDecimal.ONE);
        invalidLengthDO.setAbonos(BigDecimal.ONE);
        invalidLengthDO.setPolizaId(1L);
        invalidLengthDO.setEmpresaId(2L);
        invalidLengthDO.setAbonos(new BigDecimal(1));
        invalidSofiGlInterfaceDO.setCargos(new BigDecimal(1));
        invalidSofiGlInterfaceDO.setPolizaId(1L);
        dos.add(invalidLengthDO);
        map.put(invalidLengthDO.getPolizaId(),dos);
        String result = sofiGlInterfaceValidatedService.validated(invalidSofiGlInterfaceDO, map);
        String errorMsg = ErpAdaptorErrorNo.IMP_ERR_ERROR.getErrorMsg();

        assertTrue(result.contains(errorMsg));
    }

    @Test
    void testValidated_WithInvalidSourceSys() {
        Map<Long, List<SofiGlInterfaceDO>> map = new HashMap<>();

        CargosAndAbonosDO validCargosAndAbonos = new CargosAndAbonosDO();
        validCargosAndAbonos.setCargos(new BigDecimal(100));
        validCargosAndAbonos.setAbonos(new BigDecimal(100));
//        when(sofiGlInterfaceService.queryCargosAndAbonosDO(anyLong())).thenReturn(validCargosAndAbonos);
        SofiGlInterfaceDO invalidSourceSysDO = new SofiGlInterfaceDO();
        invalidSourceSysDO.setSourceSys("1");
        invalidSourceSysDO.setCuentaCompleta("111111");
        invalidSourceSysDO.setPolizaId(1L);
        invalidSourceSysDO.setEmpresaId(2L);
        invalidSourceSysDO.setAbonos(new BigDecimal(1));
        invalidSourceSysDO.setCargos(new BigDecimal(1));
        List<SofiGlInterfaceDO> dos = new ArrayList<>();
        dos.add(invalidSourceSysDO);
        map.put(invalidSourceSysDO.getPolizaId(),dos);
        String result = sofiGlInterfaceValidatedService.validated(invalidSourceSysDO, map);
    }

    @Test
    void testValidated_WithInvalidSourceSys1() {
        Map<Long, List<SofiGlInterfaceDO>> map = new HashMap<>();

        CargosAndAbonosDO validCargosAndAbonos = new CargosAndAbonosDO();
        validCargosAndAbonos.setCargos(new BigDecimal(100));
        validCargosAndAbonos.setAbonos(new BigDecimal(100));
        SofiGlInterfaceDO invalidSourceSysDO = new SofiGlInterfaceDO();
        invalidSourceSysDO.setSourceSys("1");
        invalidSourceSysDO.setCuentaCompleta("111111");
        invalidSourceSysDO.setPolizaId(1L);
        invalidSourceSysDO.setEmpresaId(2L);
        List<SofiGlInterfaceDO> dos = new ArrayList<>();
        SofiGlInterfaceDO invalidLengthDO = new SofiGlInterfaceDO();
        invalidLengthDO.setSourceSys("1");
        invalidLengthDO.setCuentaCompleta("1234567890123");
        invalidLengthDO.setCargos(BigDecimal.ONE);
        invalidLengthDO.setAbonos(BigDecimal.ONE);
        invalidLengthDO.setPolizaId(1L);
        invalidLengthDO.setEmpresaId(2L);
        dos.add(invalidLengthDO);
        map.put(invalidLengthDO.getPolizaId(),dos);
        sofiGlInterfaceValidatedService.validated(invalidSourceSysDO,map);

        invalidSourceSysDO.setInstrumento(0L);
        sofiGlInterfaceValidatedService.validated(invalidSourceSysDO,map);

        invalidSourceSysDO.setCuentaCompleta(null);
        try {
            sofiGlInterfaceValidatedService.validated(invalidSourceSysDO,map);
        } catch (Exception e) {

        }

    }

    @Test
    void testValidated_WithInvalidCentroCostoIdLength() {
        Map<Long, List<SofiGlInterfaceDO>> map = new HashMap<>();

        CargosAndAbonosDO validCargosAndAbonos = new CargosAndAbonosDO();
        validCargosAndAbonos.setCargos(new BigDecimal(100));
        validCargosAndAbonos.setAbonos(new BigDecimal(100));
        SofiGlInterfaceDO invalidLengthDO = new SofiGlInterfaceDO();
        invalidLengthDO.setSourceSys("1");
        invalidLengthDO.setCuentaCompleta("1234567890123");
        invalidLengthDO.setCentroCostoId(1234567890123L);
        invalidLengthDO.setPolizaId(1L);
        invalidLengthDO.setEmpresaId(2L);
        invalidLengthDO.setCargos(new BigDecimal(1));
        invalidLengthDO.setAbonos(new BigDecimal(1));
        List<SofiGlInterfaceDO> dos = new ArrayList<>();

        dos.add(invalidLengthDO);
        map.put(invalidLengthDO.getPolizaId(),dos);
        String result = sofiGlInterfaceValidatedService.validated(invalidLengthDO,map);
    }

    @Test
    void testValidated_WithInvalidCuentaCompletaLength() {
        Map<Long, List<SofiGlInterfaceDO>> map = new HashMap<>();
        List<SofiGlInterfaceDO> dos = new ArrayList<>();
        SofiGlInterfaceDO invalidLengthDO = new SofiGlInterfaceDO();
        invalidLengthDO.setSourceSys("1");
        invalidLengthDO.setCuentaCompleta("1234567890123");
        invalidLengthDO.setCargos(BigDecimal.ONE);
        invalidLengthDO.setAbonos(BigDecimal.ONE);
        invalidLengthDO.setPolizaId(1L);
        invalidLengthDO.setEmpresaId(2L);
        CargosAndAbonosDO validCargosAndAbonos = new CargosAndAbonosDO();
        validCargosAndAbonos.setCargos(new BigDecimal(100));
        validCargosAndAbonos.setAbonos(new BigDecimal(100));
        dos.add(invalidLengthDO);
        map.put(invalidLengthDO.getPolizaId(),dos);
//        when(sofiGlInterfaceService.queryCargosAndAbonosDO(anyLong())).thenReturn(validCargosAndAbonos);
        String result = sofiGlInterfaceValidatedService.validated(invalidLengthDO,map);
    }

    @Test
    void testValidatedCargosAndAbonos_WithEqualValues() {
        CargosAndAbonosDO validCargosAndAbonos = new CargosAndAbonosDO();
        validCargosAndAbonos.setCargos(new BigDecimal(100));
        validCargosAndAbonos.setAbonos(new BigDecimal(100));
        when(sofiGlInterfaceService.queryCargosAndAbonosDO(anyLong())).thenReturn(validCargosAndAbonos);
        SofiGlInterfaceDO validDO = new SofiGlInterfaceDO();
        validDO.setPolizaId(1L);

        String result = sofiGlInterfaceValidatedService.validatedCargosAndAbonos(validDO);
        assertEquals(null, result);
    }

    @Test
    void testValidatedCargosAndAbonos_WithUnequalValues() {
        CargosAndAbonosDO validCargosAndAbonos = new CargosAndAbonosDO();
        validCargosAndAbonos.setCargos(new BigDecimal(100));
        validCargosAndAbonos.setAbonos(new BigDecimal(100));
        when(sofiGlInterfaceService.queryCargosAndAbonosDO(anyLong())).thenReturn(validCargosAndAbonos);
        SofiGlInterfaceDO invalidDO = new SofiGlInterfaceDO();
        invalidDO.setPolizaId(2L);

        String result = sofiGlInterfaceValidatedService.validatedCargosAndAbonos(invalidDO);
    }
    @Test
    public void mappedResultTest1(){
        SofiGlInterfaceDO invalidDO = new SofiGlInterfaceDO();
        invalidDO.setPolizaId(2L);
        invalidDO.setEmpresaId(1L);
        invalidDO.setSourceSys("1");
        invalidDO.setMonedaId(1L);
        invalidDO.setTipoInstrumentoId("s");
        invalidDO.setJournalSource(CommonConstant.SAFI_JOURNAL_SOURCE);
//        Map<String, Map<String, CuxTwoTabValueExtraPO>> cache
        Map<String,CuxTwoTabValueExtraPO> map = new HashMap<>();
        CuxTwoTabValueExtraPO po = new CuxTwoTabValueExtraPO();
        po.setValue3("3");
        po.setValue4("4");
        po.setValue5("5");
        map.put(invalidDO.getEmpresaId()+"",po);
        map.put(invalidDO.getTipoInstrumentoId(),po);
        map.put(invalidDO.getJournalSource(),po);
        cache.put(CommonConstant.COMPANY_LEDGER_MAPPINGS,map);
        cache.put(CommonConstant.PRODUCT_MAPPINGS,map);
        cache.put(CommonConstant.GL_JOURNAL_SOURCE_MAPPINGS,map);

        sofiGlInterfaceValidatedService.mappedResult(invalidDO,cache);
    }

}
