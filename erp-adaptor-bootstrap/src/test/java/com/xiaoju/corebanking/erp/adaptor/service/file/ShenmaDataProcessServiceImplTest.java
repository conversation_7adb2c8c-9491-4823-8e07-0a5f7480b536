package com.xiaoju.corebanking.erp.adaptor.service.file;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceHeaderRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlShenmaRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.VoucherGroupData;
import com.xiaoju.corebanking.erp.adaptor.service.file.impl.ShenmaDataProcessServiceImpl;
import com.xiaoju.corebanking.erp.adaptor.service.file.impl.VoucherGroupSyncServiceImpl;
import com.xiaoju.corebanking.erp.adaptor.service.twotab.SequenceService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ShenmaDataProcessServiceImplTest {
    @InjectMocks
    ShenmaDataProcessServiceImpl shenmaDataProcessService;
    @Mock
    private SofiGlShenmaRepository sofiGlShenmaRepository;

    @Mock
    private SofiGlInterfaceHeaderRepository sofiGlInterfaceHeaderRepository;

    @Mock
    private SequenceService sequenceService;

    @Test
    void createShenmaHeaderRecordsNull() {
        String processDay = "";
        shenmaDataProcessService.createShenmaHeaderRecords(processDay);
    }

    @Test
    void createShenmaHeaderRecords() {
        String processDay = "2025-08-21";
        shenmaDataProcessService.createShenmaHeaderRecords(processDay);
    }

    @Test
    void createShenmaHeaderRecordsNotNull() {
        String processDay = "2025-08-21";
        List<SofiGlShenmaDO> shenmaRecords = new ArrayList<>();
        SofiGlShenmaDO sofiGlShenmaDO = new SofiGlShenmaDO();
        sofiGlShenmaDO.setVoucherGroup("test");
        sofiGlShenmaDO.setLinkReference("test");
        shenmaRecords.add(sofiGlShenmaDO);
        when(sofiGlShenmaRepository.selectPendingShenmaRecords(ProcessStatusEnum.NEW, processDay)).thenReturn(shenmaRecords);
        shenmaDataProcessService.createShenmaHeaderRecords(processDay);
    }

    @Test
    void batchInsertShenmaData() {
        String processDay = "";
        String fileName = "";
        String csvData = "";
        shenmaDataProcessService.batchInsertShenmaData(processDay, fileName, csvData);
    }

    @Test
    void batchInsertShenmaDataNotNull() {
        String processDay = "2025-08-12";
        String fileName = "testFile/sofi_journal_shenma.csv";
        String csvData = "test,test,test";
        shenmaDataProcessService.batchInsertShenmaData(processDay, fileName, csvData);
    }

    @Test
    void batchInsertShenmaDataNull() {
        String processDay = "2025-08-12";
        String fileName = "testFile/sofi_journal_shenma.csv";
        String csvData = "111";
        shenmaDataProcessService.batchInsertShenmaData(processDay, fileName, csvData);
    }

    @Test
    void testSetShenmaFieldValue() {
        ShenmaDataProcessServiceImpl service = new ShenmaDataProcessServiceImpl();
        ReflectionTestUtils.invokeMethod(service, "setShenmaFieldValue", new SofiGlShenmaDO(), "", "");
    }

    @Test
    void testCreateShenmaRecord() {
        ShenmaDataProcessServiceImpl service = new ShenmaDataProcessServiceImpl();
        String[] headers = new String[]{"aa"};
        String[] values = new String[]{"aa"};
        String processDay = "2025";
        String fileName = "file";
        ReflectionTestUtils.invokeMethod(service, "createShenmaRecord", headers,values,processDay,fileName);
    }


}