package com.xiaoju.corebanking.erp.adaptor.repository.converter;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHeaderPO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Calendar;
import java.util.GregorianCalendar;

import static org.junit.Assert.assertEquals;

/**
* <p> SofiGlInterfaceHeaderModelConverter Tester. </p>
* <p> 2025-06-06 11:40:17.936 </p>
*
* <AUTHOR>
* @version 0.0.1-SNAPSHOT
*/
@ExtendWith(MockitoExtension.class)
public class SofiGlInterfaceHeaderModelConverterTest {

    @InjectMocks
    SofiGlInterfaceHeaderModelConverter converter;
    /**
    *
    * Method: convert(sofiGlInterfacePO)
    */
    @Test
    public void convertTest() throws Exception {
        SofiGlInterfaceHeaderDO sofiGlInterfaceDO = new SofiGlInterfaceHeaderDO();
        sofiGlInterfaceDO.setId(1L);
        sofiGlInterfaceDO.setProcessDay("1");
        sofiGlInterfaceDO.setBatchId("1");
        sofiGlInterfaceDO.setExternalReference("1");
        sofiGlInterfaceDO.setJournalCount(1L);
        sofiGlInterfaceDO.setProcessStatus(ProcessStatusEnum.PROCESSING);
        sofiGlInterfaceDO.setProcessMessage("1");
        sofiGlInterfaceDO.setObjectVersionNumber(1L);
        sofiGlInterfaceDO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setCreatedBy("1");
        sofiGlInterfaceDO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setLastModifiedBy("1");
        sofiGlInterfaceDO.setGroupId(1L);

        SofiGlInterfaceHeaderPO sofiGlInterfacePO = new SofiGlInterfaceHeaderPO();
        sofiGlInterfacePO.setId(1L);
        sofiGlInterfacePO.setProcessDay("1");
        sofiGlInterfacePO.setBatchId("1");
        sofiGlInterfacePO.setExternalReference("1");
        sofiGlInterfacePO.setJournalCount(1L);
        sofiGlInterfacePO.setProcessStatus(ProcessStatusEnum.PROCESSING.getCode());
        sofiGlInterfacePO.setProcessMessage("1");
        sofiGlInterfacePO.setObjectVersionNumber(1L);
        sofiGlInterfacePO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfacePO.setCreatedBy("1");
        sofiGlInterfacePO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfacePO.setLastModifiedBy("1");
        sofiGlInterfacePO.setGroupId(1L);
        SofiGlInterfaceHeaderDO result = converter.convert(sofiGlInterfacePO);
        assertEquals(result,sofiGlInterfaceDO);
    }

    /**
    *
    * Method: convert(sofiGlInterfaceDO)
    */
    @Test
    public void convertSofiGlInterfaceDOTest() throws Exception {
        SofiGlInterfaceHeaderDO sofiGlInterfaceDO = new SofiGlInterfaceHeaderDO();
        sofiGlInterfaceDO.setProcessDay("1");
        sofiGlInterfaceDO.setBatchId("1");
        sofiGlInterfaceDO.setExternalReference("1");
        sofiGlInterfaceDO.setJournalCount(1L);
        sofiGlInterfaceDO.setProcessStatus(ProcessStatusEnum.PROCESSING);
        sofiGlInterfaceDO.setProcessMessage("1");
        sofiGlInterfaceDO.setObjectVersionNumber(1L);
        sofiGlInterfaceDO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setCreatedBy("1");
        sofiGlInterfaceDO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setLastModifiedBy("1");
        sofiGlInterfaceDO.setGroupId(1L);

        SofiGlInterfaceHeaderPO sofiGlInterfacePO = new SofiGlInterfaceHeaderPO();
        sofiGlInterfacePO.setProcessDay("1");
        sofiGlInterfacePO.setBatchId("1");
        sofiGlInterfacePO.setExternalReference("1");
        sofiGlInterfacePO.setJournalCount(1L);
        sofiGlInterfacePO.setProcessStatus(ProcessStatusEnum.PROCESSING.getCode());
        sofiGlInterfacePO.setProcessMessage("1");
        sofiGlInterfacePO.setObjectVersionNumber(1L);
        sofiGlInterfacePO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfacePO.setCreatedBy("1");
        sofiGlInterfacePO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfacePO.setLastModifiedBy("1");
        sofiGlInterfacePO.setGroupId(1L);
        SofiGlInterfaceHeaderPO result = converter.convert(sofiGlInterfaceDO);
        assertEquals(result,sofiGlInterfacePO);
    }

    /**
    *
    * Method: copyIgnoreNullValue(source, target)
    */
    @Test
    public void copyIgnoreNullValueTest() throws Exception {
        SofiGlInterfaceHeaderDO source = new SofiGlInterfaceHeaderDO();
        source.setId(1L);
        source.setProcessDay("1");
        source.setBatchId("1");
        source.setExternalReference("1");
        source.setJournalCount(1L);
        source.setProcessStatus(ProcessStatusEnum.PROCESSING);
        source.setProcessMessage("1");
        source.setObjectVersionNumber(1L);
        source.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        source.setCreatedBy("1");
        source.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        source.setLastModifiedBy("1");
        source.setGroupId(1L);

        SofiGlInterfaceHeaderDO target = new SofiGlInterfaceHeaderDO();
        target.setId(1L);
        target.setProcessDay("1");
        target.setBatchId("1");
        target.setExternalReference("1");
        target.setJournalCount(1L);
        target.setProcessStatus(ProcessStatusEnum.PROCESSING);
        target.setProcessMessage("1");
        target.setObjectVersionNumber(1L);
        target.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        target.setCreatedBy("1");
        target.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        target.setLastModifiedBy("1");
        target.setGroupId(1L);
        converter.copyIgnoreNullValue(source,target);
    }


}
