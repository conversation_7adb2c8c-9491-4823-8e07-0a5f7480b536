package com.xiaoju.corebanking.erp.adaptor.service.file;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlFileControlRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceHeaderRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.impl.SofiGlFileControlRepositoryImpl;
import com.xiaoju.corebanking.erp.adaptor.service.file.impl.DataProcessServiceImpl;
import com.xiaoju.corebanking.erp.adaptor.service.twotab.SequenceService;
import com.xiaoju.digitalbank.ddd.po.ExecResult;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * <p> DataProcessServiceImpl Tester. </p>
 * <p> 2025-06-07 21:04:14.058 </p>
 *
 * <AUTHOR>
 * @version 0.0.1-SNAPSHOT
 */
@ExtendWith(MockitoExtension.class)
public class DataProcessServiceImplTest {

    @InjectMocks
    private DataProcessServiceImpl dataProcessService;

    @Mock
    private S3FileOperationService s3FileOperationService;

    @Mock
    private SofiGlInterfaceRepository sofiGlInterfaceRepository;

    @Mock
    private SofiGlFileControlRepository sofiGlFileControlRepository;


    @Mock
    private SofiGlInterfaceHeaderRepository sofiGlInterfaceHeaderRepository;

    @Mock
    private SequenceService sequenceService;

    /**
     * Method: processSubFile(subFile, expectedCount, processDate, sofiGlInterfaceRepository, sofiGlFileControlRepository)
     */
    @Test
    public void processSubFileTest() throws Exception {
        String subFile = "test.csv";
        int expectedCount = 10;
        String processDate = "20230101";
        String expectedMd5 = "abcdef1234567890";

        when(s3FileOperationService.checkFileExists(anyString())).thenReturn(Boolean.FALSE);

        boolean result = dataProcessService.processSubFile(anyString(),
                subFile, expectedCount, processDate, sofiGlInterfaceRepository, sofiGlFileControlRepository
        );

        assertFalse(result);

        when(s3FileOperationService.checkFileExists(anyString())).thenThrow(RuntimeException.class);
        result = dataProcessService.processSubFile(anyString(),
                subFile, expectedCount, processDate, sofiGlInterfaceRepository, sofiGlFileControlRepository
        );
        assertFalse(result);

    }

    /**
     * Method: processFileContent(subFile, processDate, expectedCount, sofiGlInterfaceRepository, sofiGlFileControlRepository)
     */
    @Test
    public void processFileContentTest() throws Exception {
        String subFile = "test.csv";
        int expectedCount = 10;
        String processDate = "20230101";

        lenient().when(s3FileOperationService.checkFileExists(subFile)).thenReturn(true);
        String subFileChecksum = subFile.replace(".csv", ".md5");
        lenient().when(s3FileOperationService.checkFileExists(subFileChecksum)).thenReturn(false);

        boolean result = dataProcessService.processSubFile(anyString(),
                subFile, expectedCount, processDate, sofiGlInterfaceRepository, sofiGlFileControlRepository
        );

        assertFalse(result);
    }

    /**
     * Method: createInterfaceRecord(headers, values, processDay, subFile, batchId)
     */
    @Test
    public void createInterfaceRecordTest() throws Exception {
        String subFile = "test.csv";
        int expectedCount = 10;
        String processDate = "20230101";

        doReturn(true)
                .doReturn(true)
                .when(s3FileOperationService).checkFileExists(anyString());

        boolean result = dataProcessService.processSubFile(anyString(),
                subFile, expectedCount, processDate, sofiGlInterfaceRepository, sofiGlFileControlRepository
        );

        assertFalse(result);
    }

    /**
     * Method: processAndVerifyFile(subFile, processDate, expectedMd5, expectedCount, sofiGlInterfaceRepository, sofiGlFileControlRepository)
     */
    @Test
    public void processAndVerifyFileTest() throws Exception {
        String subFile = "test.csv";
        int expectedCount = 10;
        String processDate = "20230101";

        doReturn(true)
                .doReturn(true)
                .when(s3FileOperationService).checkFileExists(anyString());

        lenient().when(s3FileOperationService.readFileFirstLine(anyString())).thenReturn(null);

        boolean result = dataProcessService.processSubFile(anyString(),
                subFile, expectedCount, processDate, sofiGlInterfaceRepository, sofiGlFileControlRepository
        );

        assertFalse(result);
    }

    /**
     * Method: createControlStatus(actualCount, filePath, processDate, success, message, sofiGlFileControlRepository)
     */
    @Test
    public void createControlStatusTest() throws Exception {
        String subFile = "test.csv";
        int expectedCount = 10;
        String processDate = "20230101";
        String expectedMd5 = "abcdef1234567890";

        doReturn(true)
                .doReturn(true)
                .when(s3FileOperationService).checkFileExists(anyString());

        lenient().when(s3FileOperationService.readFileFirstLine(anyString())).thenReturn(expectedMd5);

        lenient().when(dataProcessService.processAndVerifyFile("12312",  "12312", expectedMd5, 123,  sofiGlInterfaceRepository, sofiGlFileControlRepository)).thenReturn(false);

        boolean result = dataProcessService.processSubFile(anyString(),
                subFile, expectedCount, processDate, sofiGlInterfaceRepository, sofiGlFileControlRepository
        );

        assertFalse(result);
    }

    /**
     * Method: setFieldValue(sofiGlInterfaceDO, fieldName, value)
     */
    @Test
    public void setFieldValueTest() throws Exception {
        String subFile = "test.csv";
        int expectedCount = 5;
        String processDate = "20230101";

        doReturn(true)
                .doReturn(true)
                .when(s3FileOperationService).checkFileExists(anyString());

        when(s3FileOperationService.readFileFirstLine(anyString())).thenReturn("abcdef1234567890");
        when(s3FileOperationService.verifyMd5(anyString(), anyString())).thenReturn(Boolean.TRUE);

        String ss = "Settlement Order No.,Business Member ID,Business Member Name,Transaction Time,Settlement Time,Transaction Type,Business Payment Order No.,Transaction Activity,Local currency,Type,Amount,Services Fee,Tax Fee,Settlement Amount,Settlement Status\n" +
                "72057594040760244,11001,STP,2024-04-09 23:26:07,2024-04-11 01:00:00,跨行提现,680_202024041516192730677123456876,202024041516192730677123456876,MXN,SPEI,0.50,0.18,0.13,-0.50,WAITING_RECONCILING\n" +
                "72057594040760244,11001,STP,2024-04-10 07:20:50,2024-04-11 01:00:00,跨行充值,tsf_302_2404104cf711419dc84fc55e,102024041016203842321141700888,MXN,SPEI,1.00,0.26,0.14,1.00,WAITING_RECONCILING\n" +
                "72057594040760244,11001,STP,2024-04-10 07:21:10,2024-04-11 01:00:00,跨行充值,tsf_302_2404104cf711419dc84fc44w,102024041016203842321141700777,MXN,SPEI,3.00,0.58,0.19,3.00,WAITING_RECONCILING\n" +
                "72057594040760244,11001,STP,2024-04-09 23:26:07,2024-04-11 01:00:00,跨行提现,680_202024041016192730677376190155,202024041016192730677376190155,MXN,SPEI,0.50,0.18,0.13,-0.50,WAITING_RECONCILING\n" +
                "72057594040760244,11001,STP,2024-04-09 23:25:27,2024-04-11 01:00:00,支付退款,680_202024041516192730677123456132,202024041516192730677123456132,MXN,SPEI,0.50,0.18,0.13,-0.50,WAITING_RECONCILING";
        InputStream inputStream = new ByteArrayInputStream(ss.getBytes(), 0, ss.length());
        lenient().when(s3FileOperationService.download(anyString())).thenReturn(inputStream);

        boolean result = dataProcessService.processSubFile(anyString(),
                subFile, expectedCount, processDate, sofiGlInterfaceRepository, sofiGlFileControlRepository
        );

        assertTrue(result);

        lenient().when(s3FileOperationService.download(anyString())).thenThrow(RuntimeException.class);
        result = dataProcessService.processSubFile(anyString(),
                subFile, expectedCount, processDate, sofiGlInterfaceRepository, sofiGlFileControlRepository
        );
        assertFalse(result);
    }

    @Test
    public void setFieldValueTest1() throws Exception {
        String subFile = "test.csv";
        int expectedCount = 10;
        String processDate = "20230101";

        doReturn(true)
                .doReturn(true)
                .when(s3FileOperationService).checkFileExists(anyString());

        when(s3FileOperationService.readFileFirstLine(anyString())).thenReturn("abcdef1234567890");
        when(s3FileOperationService.verifyMd5(anyString(), anyString())).thenReturn(Boolean.TRUE);

        String ss = "Settlement Order No.,Business Member ID,Business Member Name,Transaction Time,Settlement Time,Transaction Type,Business Payment Order No.,Transaction Activity,Local currency,Type,Amount,Services Fee,Tax Fee,Settlement Amount,Settlement Status\n" +
                "72057594040760244,11001,STP,2024-04-09 23:26:07,2024-04-11 01:00:00,跨行提现,680_202024041516192730677123456876,202024041516192730677123456876,MXN,SPEI,0.50,0.18,0.13,-0.50,WAITING_RECONCILING\n" +
                "72057594040760244,11001,STP,2024-04-10 07:20:50,2024-04-11 01:00:00,跨行充值,tsf_302_2404104cf711419dc84fc55e,102024041016203842321141700888,MXN,SPEI,1.00,0.26,0.14,1.00,WAITING_RECONCILING\n" +
                "72057594040760244,11001,STP,2024-04-10 07:21:10,2024-04-11 01:00:00,跨行充值,tsf_302_2404104cf711419dc84fc44w,102024041016203842321141700777,MXN,SPEI,3.00,0.58,0.19,3.00,WAITING_RECONCILING\n" +
                "72057594040760244,11001,STP,2024-04-09 23:26:07,2024-04-11 01:00:00,跨行提现,680_202024041016192730677376190155,202024041016192730677376190155,MXN,SPEI,0.50,0.18,0.13,-0.50,WAITING_RECONCILING\n" +
                "72057594040760244,11001,STP,2024-04-09 23:25:27,2024-04-11 01:00:00,支付退款,680_202024041516192730677123456132,202024041516192730677123456132,MXN,SPEI,0.50,0.18,0.13,-0.50,WAITING_RECONCILING";
        InputStream inputStream = new ByteArrayInputStream(ss.getBytes(), 0, ss.length());
        lenient().when(s3FileOperationService.download(anyString())).thenReturn(inputStream);

        boolean result = dataProcessService.processSubFile(anyString(),
                subFile, expectedCount, processDate, sofiGlInterfaceRepository, sofiGlFileControlRepository
        );

        assertFalse(result);

        when(s3FileOperationService.verifyMd5(anyString(), anyString())).thenThrow(RuntimeException.class);
        result = dataProcessService.processSubFile(anyString(),
                subFile, expectedCount, processDate, sofiGlInterfaceRepository, sofiGlFileControlRepository
        );
        assertFalse(result);
    }

    @Test
    public void setFieldValueTest2() throws Exception {
        String subFile = "test.csv";
        int expectedCount = 5;
        String processDate = "20230101";

        doReturn(true)
                .doReturn(true)
                .when(s3FileOperationService).checkFileExists(anyString());

        when(s3FileOperationService.readFileFirstLine(anyString())).thenReturn("abcdef1234567890");
        when(s3FileOperationService.verifyMd5(anyString(), anyString())).thenReturn(Boolean.TRUE);

        String ss = "Settlement Order No.,Business Member ID,Business Member Name,Transaction Time,Settlement Time,Transaction Type,Business Payment Order No.,Transaction Activity,Local currency,Type,Amount,Services Fee,Tax Fee,Settlement Amount,Settlement Status\n";
        InputStream inputStream = new ByteArrayInputStream(ss.getBytes(), 0, ss.length());
        lenient().when(s3FileOperationService.download(anyString())).thenReturn(inputStream);

        boolean result = dataProcessService.processSubFile(anyString(),
                subFile, expectedCount, processDate, sofiGlInterfaceRepository, sofiGlFileControlRepository
        );

        assertFalse(result);
    }

    @Test
    public void testCreateInterfaceHeaderRecords_Success() {
        String processDay = "20230101";
        List<SofiGlInterfaceDO> interfaceRecords = new ArrayList<>();

        SofiGlInterfaceDO record1 = new SofiGlInterfaceDO();
        record1.setPolizaId(1L);
        record1.setBatchId("BATCH001");

        SofiGlInterfaceDO record2 = new SofiGlInterfaceDO();
        record2.setPolizaId(1L);
        record2.setBatchId("BATCH001");

        SofiGlInterfaceDO record3 = new SofiGlInterfaceDO();
        record3.setPolizaId(2L);
        record3.setBatchId("BATCH002");

        interfaceRecords.add(record1);
        interfaceRecords.add(record2);
        interfaceRecords.add(record3);
        List<Map<String, Object>> currentPageRecords = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put("batch_id","batch_id");
        map.put("poliza_id","poliza_id");
        map.put("record_count",123);

        currentPageRecords.add(map);
        when(sofiGlInterfaceRepository.selectGroupByPolizaId(processDay, ProcessStatusEnum.NEW.getCode())).thenReturn(currentPageRecords);
//
//        doNothing().when(sofiGlInterfaceHeaderRepository).batchInsertInterfaceHeader(any());

        ExecResult result = dataProcessService.createInterfaceHeaderRecords(processDay, sofiGlInterfaceRepository, sofiGlInterfaceHeaderRepository);


    }

    @Test
    public void testCreateInterfaceHeaderRecords_EmptyProcessDay() {
        ExecResult result = dataProcessService.createInterfaceHeaderRecords("", sofiGlInterfaceRepository, sofiGlInterfaceHeaderRepository);

        assertFalse(result.isSuccess());
    }

    @Test
    public void testCreateInterfaceHeaderRecords_NullProcessDay() {
        ExecResult result = dataProcessService.createInterfaceHeaderRecords(null, sofiGlInterfaceRepository, sofiGlInterfaceHeaderRepository);

        assertFalse(result.isSuccess());
    }

    @Test
    public void testCreateInterfaceHeaderRecords_NoRecordsFound() {
        String processDay = "20230101";
        List<SofiGlInterfaceDO> emptyList = new ArrayList<>();

//        when(sofiGlInterfaceRepository.selectByProcessDayAndStatus(processDay, ProcessStatusEnum.NEW.getCode()))
//                .thenReturn(emptyList);

        ExecResult result = dataProcessService.createInterfaceHeaderRecords(processDay, sofiGlInterfaceRepository, sofiGlInterfaceHeaderRepository);

    }

    @Test
    public void testCreateInterfaceHeaderRecords_ExceptionThrown() {
        String processDay = "20230101";

        ExecResult result = dataProcessService.createInterfaceHeaderRecords(processDay, sofiGlInterfaceRepository, sofiGlInterfaceHeaderRepository);

    }

    @Test
    public void testBatchInsertHeaderRecords_Success() {
        List<SofiGlInterfaceHeaderDO> headerRecords = new ArrayList<>();
        SofiGlInterfaceHeaderDO header1 = new SofiGlInterfaceHeaderDO();
        header1.setExternalReference("1");
        header1.setBatchId("BATCH001");
        headerRecords.add(header1);
        doNothing().when(sofiGlInterfaceHeaderRepository).batchInsertInterfaceHeader(headerRecords);

        dataProcessService.batchInsertHeaderRecords(headerRecords, sofiGlInterfaceHeaderRepository);

    }

    @Test
    public void testBatchInsertHeaderRecords_EmptyList() {
        List<SofiGlInterfaceHeaderDO> emptyList = new ArrayList<>();
        dataProcessService.batchInsertHeaderRecords(emptyList, sofiGlInterfaceHeaderRepository);

        verify(sofiGlInterfaceHeaderRepository, never()).batchInsertInterfaceHeader(any());
    }
    @Test
    void testCreateControlStatus() {
        int actualCount =1;
        String filePath=null;
        String processDate="2025";
        boolean success=true;
        String message ="tt";
        SofiGlFileControlRepository sofiGlFileControlRepository = new SofiGlFileControlRepositoryImpl();
        dataProcessService.createControlStatus(actualCount,filePath,processDate,success,message,sofiGlFileControlRepository);
    }
    @Test
    void testSetFieldValue() {
        dataProcessService.setFieldValue(null,null,null);
    }
    @Test
    void testbatchInsertInterface() {
        dataProcessService.batchInsertInterface(null,null);
    }

}
