package com.xiaoju.corebanking.erp.adaptor.repository.converter;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfacePO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.GregorianCalendar;

import static org.junit.Assert.assertEquals;

/**
* <p> SofiGlInterfaceModelConverter Tester. </p>
* <p> 2025-06-06 11:40:17.965 </p>
*
* <AUTHOR>
* @version 0.0.1-SNAPSHOT
*/
@ExtendWith(MockitoExtension.class)
public class SofiGlInterfaceModelConverterTest {

    @InjectMocks
    SofiGlInterfaceModelConverter converter;
    /**
    *
    * Method: convert(sofiGlInterfacePO)
    */
    @Test
    public void convertTest() throws Exception {
        //init
        SofiGlInterfaceDO sofiGlInterfaceDO = new SofiGlInterfaceDO();
        sofiGlInterfaceDO.setId(1L);
        sofiGlInterfaceDO.setFileId(1L);
        sofiGlInterfaceDO.setProcessDay("2025");
        sofiGlInterfaceDO.setFileName("filename");
        sofiGlInterfaceDO.setBatchId("1");
        sofiGlInterfaceDO.setDetallePolizaId(1L);
        sofiGlInterfaceDO.setSourceSys("safi");
        sofiGlInterfaceDO.setEmpresaId(1L);
        sofiGlInterfaceDO.setPolizaId(1L);
        sofiGlInterfaceDO.setFecha("2025-06");
        sofiGlInterfaceDO.setCentroCostoId(1L);
        sofiGlInterfaceDO.setCuentaCompleta("1");
        sofiGlInterfaceDO.setInstrumento(1L);
        sofiGlInterfaceDO.setMonedaId(1L);
        sofiGlInterfaceDO.setCargos(new BigDecimal(1));
        sofiGlInterfaceDO.setAbonos(new BigDecimal(1));
        sofiGlInterfaceDO.setDescripcion("1");
        sofiGlInterfaceDO.setReferencia("1");
        sofiGlInterfaceDO.setProcedimientoCont("1");
        sofiGlInterfaceDO.setTipoInstrumentoId("1");
        sofiGlInterfaceDO.setRfc("1");
        sofiGlInterfaceDO.setTotalFactura(new BigDecimal(1));
        sofiGlInterfaceDO.setFolioUuid("1");
        sofiGlInterfaceDO.setUsuario(1L);
        sofiGlInterfaceDO.setFechaActual("1");
        sofiGlInterfaceDO.setDireccionIp("1");
        sofiGlInterfaceDO.setProgramaId("1");
        sofiGlInterfaceDO.setSucursal(1L);
        sofiGlInterfaceDO.setNumTransaccion(1L);
        sofiGlInterfaceDO.setLedgerId(1L);
        sofiGlInterfaceDO.setLedgerName("1");
        sofiGlInterfaceDO.setCurrencyCode("1");
        sofiGlInterfaceDO.setJournalCategory("1");
        sofiGlInterfaceDO.setJournalSource("1");
        sofiGlInterfaceDO.setSegment1("1");
        sofiGlInterfaceDO.setSegment2("1");
        sofiGlInterfaceDO.setSegment3("1");
        sofiGlInterfaceDO.setSegment4("1");
        sofiGlInterfaceDO.setSegment5("1");
        sofiGlInterfaceDO.setSegment6("1");
        sofiGlInterfaceDO.setSegment7("1");
        sofiGlInterfaceDO.setSegment8("1");
        sofiGlInterfaceDO.setSegment9("1");
        sofiGlInterfaceDO.setSegment10("1");
        sofiGlInterfaceDO.setGroupId(1L);
        sofiGlInterfaceDO.setCurrencyConversionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setCurrencyConversionRate(new BigDecimal(1));
        sofiGlInterfaceDO.setUserCurrencyConversionType("1");
        sofiGlInterfaceDO.setProcessStatus(ProcessStatusEnum.NEW);
        sofiGlInterfaceDO.setProcessMessage("1");
        sofiGlInterfaceDO.setJeHeaderId(1L);
        sofiGlInterfaceDO.setJournalName("1");
        sofiGlInterfaceDO.setJeLineNum(1L);
        sofiGlInterfaceDO.setDocumentId(1L);
        sofiGlInterfaceDO.setLoadRequestId(1L);
        sofiGlInterfaceDO.setImportRequestId(1L);
        sofiGlInterfaceDO.setObjectVersionNumber(1L);
        sofiGlInterfaceDO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setCreatedBy("1");
        sofiGlInterfaceDO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setLastModifiedBy("1");
        sofiGlInterfaceDO.setJournalSourceName("1");


        SofiGlInterfacePO sofiGlInterfacePO = new SofiGlInterfacePO();
        sofiGlInterfacePO.setId(1L);
        sofiGlInterfacePO.setFileId(1L);
        sofiGlInterfacePO.setProcessDay("2025");
        sofiGlInterfacePO.setFileName("filename");
        sofiGlInterfacePO.setBatchId("1");
        sofiGlInterfacePO.setDetallePolizaId(1L);
        sofiGlInterfacePO.setSourceSys("safi");
        sofiGlInterfacePO.setEmpresaId(1L);
        sofiGlInterfacePO.setPolizaId(1L);
        sofiGlInterfacePO.setFecha("2025-06");
        sofiGlInterfacePO.setCentroCostoId(1L);
        sofiGlInterfacePO.setCuentaCompleta("1");
        sofiGlInterfacePO.setInstrumento(1L);
        sofiGlInterfacePO.setMonedaId(1L);
        sofiGlInterfacePO.setCargos(new BigDecimal(1));
        sofiGlInterfacePO.setAbonos(new BigDecimal(1));
        sofiGlInterfacePO.setDescripcion("1");
        sofiGlInterfacePO.setReferencia("1");
        sofiGlInterfacePO.setProcedimientoCont("1");
        sofiGlInterfacePO.setTipoInstrumentoId("1");
        sofiGlInterfacePO.setRfc("1");
        sofiGlInterfacePO.setTotalFactura(new BigDecimal(1));
        sofiGlInterfacePO.setFolioUuid("1");
        sofiGlInterfacePO.setUsuario(1L);
        sofiGlInterfacePO.setFechaActual("1");
        sofiGlInterfacePO.setDireccionIp("1");
        sofiGlInterfacePO.setProgramaId("1");
        sofiGlInterfacePO.setSucursal(1L);
        sofiGlInterfacePO.setNumTransaccion(1L);
        sofiGlInterfacePO.setLedgerId(1L);
        sofiGlInterfacePO.setLedgerName("1");
        sofiGlInterfacePO.setCurrencyCode("1");
        sofiGlInterfacePO.setJournalCategory("1");
        sofiGlInterfacePO.setJournalSource("1");
        sofiGlInterfacePO.setSegment1("1");
        sofiGlInterfacePO.setSegment2("1");
        sofiGlInterfacePO.setSegment3("1");
        sofiGlInterfacePO.setSegment4("1");
        sofiGlInterfacePO.setSegment5("1");
        sofiGlInterfacePO.setSegment6("1");
        sofiGlInterfacePO.setSegment7("1");
        sofiGlInterfacePO.setSegment8("1");
        sofiGlInterfacePO.setSegment9("1");
        sofiGlInterfacePO.setSegment10("1");
        sofiGlInterfacePO.setGroupId(1L);
        sofiGlInterfacePO.setCurrencyConversionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfacePO.setCurrencyConversionRate(new BigDecimal(1));
        sofiGlInterfacePO.setUserCurrencyConversionType("1");
        sofiGlInterfacePO.setProcessStatus(ProcessStatusEnum.NEW.getCode());
        sofiGlInterfacePO.setProcessMessage("1");
        sofiGlInterfacePO.setJeHeaderId(1L);
        sofiGlInterfacePO.setJournalName("1");
        sofiGlInterfacePO.setJeLineNum(1L);
        sofiGlInterfacePO.setDocumentId(1L);
        sofiGlInterfacePO.setLoadRequestId(1L);
        sofiGlInterfacePO.setImportRequestId(1L);
        sofiGlInterfacePO.setObjectVersionNumber(1L);
        sofiGlInterfacePO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfacePO.setCreatedBy("1");
        sofiGlInterfacePO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfacePO.setLastModifiedBy("1");
        sofiGlInterfacePO.setJournalSourceName("1");

        SofiGlInterfaceDO result = converter.convert(sofiGlInterfacePO);
        assertEquals(result,sofiGlInterfaceDO);
    }

    /**
    *
    * Method: convert(sofiGlInterfaceDO)
    */
    @Test
    public void convertSofiGlInterfaceDOTest() throws Exception {
        //init
        SofiGlInterfaceDO sofiGlInterfaceDO = new SofiGlInterfaceDO();
        sofiGlInterfaceDO.setId(1L);
        sofiGlInterfaceDO.setFileId(1L);
        sofiGlInterfaceDO.setProcessDay("2025");
        sofiGlInterfaceDO.setFileName("filename");
        sofiGlInterfaceDO.setBatchId("1");
        sofiGlInterfaceDO.setDetallePolizaId(1L);
        sofiGlInterfaceDO.setSourceSys("safi");
        sofiGlInterfaceDO.setEmpresaId(1L);
        sofiGlInterfaceDO.setPolizaId(1L);
        sofiGlInterfaceDO.setFecha("2025-06");
        sofiGlInterfaceDO.setCentroCostoId(1L);
        sofiGlInterfaceDO.setCuentaCompleta("1");
        sofiGlInterfaceDO.setInstrumento(1L);
        sofiGlInterfaceDO.setMonedaId(1L);
        sofiGlInterfaceDO.setCargos(new BigDecimal(1));
        sofiGlInterfaceDO.setAbonos(new BigDecimal(1));
        sofiGlInterfaceDO.setDescripcion("1");
        sofiGlInterfaceDO.setReferencia("1");
        sofiGlInterfaceDO.setProcedimientoCont("1");
        sofiGlInterfaceDO.setTipoInstrumentoId("1");
        sofiGlInterfaceDO.setRfc("1");
        sofiGlInterfaceDO.setTotalFactura(new BigDecimal(1));
        sofiGlInterfaceDO.setFolioUuid("1");
        sofiGlInterfaceDO.setUsuario(1L);
        sofiGlInterfaceDO.setFechaActual("1");
        sofiGlInterfaceDO.setDireccionIp("1");
        sofiGlInterfaceDO.setProgramaId("1");
        sofiGlInterfaceDO.setSucursal(1L);
        sofiGlInterfaceDO.setNumTransaccion(1L);
        sofiGlInterfaceDO.setLedgerId(1L);
        sofiGlInterfaceDO.setLedgerName("1");
        sofiGlInterfaceDO.setCurrencyCode("1");
        sofiGlInterfaceDO.setJournalCategory("1");
        sofiGlInterfaceDO.setJournalSource("1");
        sofiGlInterfaceDO.setSegment1("1");
        sofiGlInterfaceDO.setSegment2("1");
        sofiGlInterfaceDO.setSegment3("1");
        sofiGlInterfaceDO.setSegment4("1");
        sofiGlInterfaceDO.setSegment5("1");
        sofiGlInterfaceDO.setSegment6("1");
        sofiGlInterfaceDO.setSegment7("1");
        sofiGlInterfaceDO.setSegment8("1");
        sofiGlInterfaceDO.setSegment9("1");
        sofiGlInterfaceDO.setSegment10("1");
        sofiGlInterfaceDO.setGroupId(1L);
        sofiGlInterfaceDO.setCurrencyConversionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setCurrencyConversionRate(new BigDecimal(1));
        sofiGlInterfaceDO.setUserCurrencyConversionType("1");
        sofiGlInterfaceDO.setProcessStatus(ProcessStatusEnum.NEW);
        sofiGlInterfaceDO.setProcessMessage("1");
        sofiGlInterfaceDO.setJeHeaderId(1L);
        sofiGlInterfaceDO.setJournalName("1");
        sofiGlInterfaceDO.setJeLineNum(1L);
        sofiGlInterfaceDO.setDocumentId(1L);
        sofiGlInterfaceDO.setLoadRequestId(1L);
        sofiGlInterfaceDO.setImportRequestId(1L);
        sofiGlInterfaceDO.setObjectVersionNumber(1L);
        sofiGlInterfaceDO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setCreatedBy("1");
        sofiGlInterfaceDO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setLastModifiedBy("1");
        sofiGlInterfaceDO.setJournalSourceName("1");


        SofiGlInterfacePO sofiGlInterfacePO = new SofiGlInterfacePO();
        sofiGlInterfacePO.setFileId(1L);
        sofiGlInterfacePO.setProcessDay("2025");
        sofiGlInterfacePO.setFileName("filename");
        sofiGlInterfacePO.setBatchId("1");
        sofiGlInterfacePO.setDetallePolizaId(1L);
        sofiGlInterfacePO.setSourceSys("safi");
        sofiGlInterfacePO.setEmpresaId(1L);
        sofiGlInterfacePO.setPolizaId(1L);
        sofiGlInterfacePO.setFecha("2025-06");
        sofiGlInterfacePO.setCentroCostoId(1L);
        sofiGlInterfacePO.setCuentaCompleta("1");
        sofiGlInterfacePO.setInstrumento(1L);
        sofiGlInterfacePO.setMonedaId(1L);
        sofiGlInterfacePO.setCargos(new BigDecimal(1));
        sofiGlInterfacePO.setAbonos(new BigDecimal(1));
        sofiGlInterfacePO.setDescripcion("1");
        sofiGlInterfacePO.setReferencia("1");
        sofiGlInterfacePO.setProcedimientoCont("1");
        sofiGlInterfacePO.setTipoInstrumentoId("1");
        sofiGlInterfacePO.setRfc("1");
        sofiGlInterfacePO.setTotalFactura(new BigDecimal(1));
        sofiGlInterfacePO.setFolioUuid("1");
        sofiGlInterfacePO.setUsuario(1L);
        sofiGlInterfacePO.setFechaActual("1");
        sofiGlInterfacePO.setDireccionIp("1");
        sofiGlInterfacePO.setProgramaId("1");
        sofiGlInterfacePO.setSucursal(1L);
        sofiGlInterfacePO.setNumTransaccion(1L);
        sofiGlInterfacePO.setLedgerId(1L);
        sofiGlInterfacePO.setLedgerName("1");
        sofiGlInterfacePO.setCurrencyCode("1");
        sofiGlInterfacePO.setJournalCategory("1");
        sofiGlInterfacePO.setJournalSource("1");
        sofiGlInterfacePO.setSegment1("1");
        sofiGlInterfacePO.setSegment2("1");
        sofiGlInterfacePO.setSegment3("1");
        sofiGlInterfacePO.setSegment4("1");
        sofiGlInterfacePO.setSegment5("1");
        sofiGlInterfacePO.setSegment6("1");
        sofiGlInterfacePO.setSegment7("1");
        sofiGlInterfacePO.setSegment8("1");
        sofiGlInterfacePO.setSegment9("1");
        sofiGlInterfacePO.setSegment10("1");
        sofiGlInterfacePO.setGroupId(1L);
        sofiGlInterfacePO.setCurrencyConversionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfacePO.setCurrencyConversionRate(new BigDecimal(1));
        sofiGlInterfacePO.setUserCurrencyConversionType("1");
        sofiGlInterfacePO.setProcessStatus(ProcessStatusEnum.NEW.getCode());
        sofiGlInterfacePO.setProcessMessage("1");
        sofiGlInterfacePO.setJeHeaderId(1L);
        sofiGlInterfacePO.setJournalName("1");
        sofiGlInterfacePO.setJeLineNum(1L);
        sofiGlInterfacePO.setDocumentId(1L);
        sofiGlInterfacePO.setLoadRequestId(1L);
        sofiGlInterfacePO.setImportRequestId(1L);
        sofiGlInterfacePO.setObjectVersionNumber(1L);
        sofiGlInterfacePO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfacePO.setCreatedBy("1");
        sofiGlInterfacePO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfacePO.setLastModifiedBy("1");
        sofiGlInterfacePO.setJournalSourceName("1");

        SofiGlInterfacePO result = converter.convert(sofiGlInterfaceDO);
        assertEquals(result,sofiGlInterfacePO);
    }

    /**
    *
    * Method: copyIgnoreNullValue(source, target)
    */
    @Test
    public void copyIgnoreNullValueTest() throws Exception {
        SofiGlInterfaceDO source = new SofiGlInterfaceDO();
        source.setId(1L);
        source.setFileId(1L);
        source.setProcessDay("2025");
        source.setFileName("filename");
        source.setBatchId("1");
        source.setDetallePolizaId(1L);
        source.setSourceSys("safi");
        source.setEmpresaId(1L);
        source.setPolizaId(1L);
        source.setFecha("2025-06");
        source.setCentroCostoId(1L);
        source.setCuentaCompleta("1");
        source.setInstrumento(1L);
        source.setMonedaId(1L);
        source.setCargos(new BigDecimal(1));
        source.setAbonos(new BigDecimal(1));
        source.setDescripcion("1");
        source.setReferencia("1");
        source.setProcedimientoCont("1");
        source.setTipoInstrumentoId("1");
        source.setRfc("1");
        source.setTotalFactura(new BigDecimal(1));
        source.setFolioUuid("1");
        source.setUsuario(1L);
        source.setFechaActual("1");
        source.setDireccionIp("1");
        source.setProgramaId("1");
        source.setSucursal(1L);
        source.setNumTransaccion(1L);
        source.setLedgerId(1L);
        source.setLedgerName("1");
        source.setCurrencyCode("1");
        source.setJournalCategory("1");
        source.setJournalSource("1");
        source.setSegment1("1");
        source.setSegment2("1");
        source.setSegment3("1");
        source.setSegment4("1");
        source.setSegment5("1");
        source.setSegment6("1");
        source.setSegment7("1");
        source.setSegment8("1");
        source.setSegment9("1");
        source.setSegment10("1");
        source.setGroupId(1L);
        source.setCurrencyConversionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        source.setCurrencyConversionRate(new BigDecimal(1));
        source.setUserCurrencyConversionType("1");
        source.setProcessStatus(ProcessStatusEnum.NEW);
        source.setProcessMessage("1");
        source.setJeHeaderId(1L);
        source.setJournalName("1");
        source.setJeLineNum(1L);
        source.setDocumentId(1L);
        source.setLoadRequestId(1L);
        source.setImportRequestId(1L);
        source.setObjectVersionNumber(1L);
        source.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        source.setCreatedBy("1");
        source.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        source.setLastModifiedBy("1");
        source.setJournalSourceName("1");


        SofiGlInterfaceDO target = new SofiGlInterfaceDO();
        target.setId(1L);
        target.setFileId(1L);
        target.setProcessDay("2025");
        target.setFileName("filename");
        target.setBatchId("1");
        target.setDetallePolizaId(1L);
        target.setSourceSys("safi");
        target.setEmpresaId(1L);
        target.setPolizaId(1L);
        target.setFecha("2025-06");
        target.setCentroCostoId(1L);
        target.setCuentaCompleta("1");
        target.setInstrumento(1L);
        target.setMonedaId(1L);
        target.setCargos(new BigDecimal(1));
        target.setAbonos(new BigDecimal(1));
        target.setDescripcion("1");
        target.setReferencia("1");
        target.setProcedimientoCont("1");
        target.setTipoInstrumentoId("1");
        target.setRfc("1");
        target.setTotalFactura(new BigDecimal(1));
        target.setFolioUuid("1");
        target.setUsuario(1L);
        target.setFechaActual("1");
        target.setDireccionIp("1");
        target.setProgramaId("1");
        target.setSucursal(1L);
        target.setNumTransaccion(1L);
        target.setLedgerId(1L);
        target.setLedgerName("1");
        target.setCurrencyCode("1");
        target.setJournalCategory("1");
        target.setJournalSource("1");
        target.setSegment1("1");
        target.setSegment2("1");
        target.setSegment3("1");
        target.setSegment4("1");
        target.setSegment5("1");
        target.setSegment6("1");
        target.setSegment7("1");
        target.setSegment8("1");
        target.setSegment9("1");
        target.setSegment10("1");
        target.setGroupId(1L);
        target.setCurrencyConversionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        target.setCurrencyConversionRate(new BigDecimal(1));
        target.setUserCurrencyConversionType("1");
        target.setProcessStatus(ProcessStatusEnum.NEW);
        target.setProcessMessage("1");
        target.setJeHeaderId(1L);
        target.setJournalName("1");
        target.setJeLineNum(1L);
        target.setDocumentId(1L);
        target.setLoadRequestId(1L);
        target.setImportRequestId(1L);
        target.setObjectVersionNumber(1L);
        target.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        target.setCreatedBy("1");
        target.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        target.setLastModifiedBy("1");
        target.setJournalSourceName("1");
        converter.copyIgnoreNullValue(source,target);
    }
}
