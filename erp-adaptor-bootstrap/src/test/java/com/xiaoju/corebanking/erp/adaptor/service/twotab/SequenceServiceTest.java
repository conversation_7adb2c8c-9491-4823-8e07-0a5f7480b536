package com.xiaoju.corebanking.erp.adaptor.service.twotab;

import com.xiaoju.corebanking.erp.adaptor.common.exception.AccengException;
import com.xiaoju.corebanking.erp.adaptor.repository.impl.SequenceDao;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.Assert.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SequenceServiceTest {
    @InjectMocks
    SequenceService sequenceService;
    @Mock
    SequenceDao sequenceDao;

    @Test
    void nextval() {
        sequenceService.nextval("test");
    }
    @Test
    void nextvalIsNull() {
        Long current = null;
        when(sequenceDao.getCurrentValueWithLock("test")).thenReturn(current);
        assertThrows(AccengException.class, () ->sequenceService.nextval("test"));
    }

    @Test
    void createSequence() {
        when(sequenceDao.getCurrentValue("test11")).thenReturn(null);
        sequenceService.createSequence("test11",1L);
    }

    @Test
    void updateBatchSize() {
        sequenceService.updateBatchSize("test",100);
    }
}