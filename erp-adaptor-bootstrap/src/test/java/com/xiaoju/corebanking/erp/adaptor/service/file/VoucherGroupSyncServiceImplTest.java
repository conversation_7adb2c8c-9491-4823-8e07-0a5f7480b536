package com.xiaoju.corebanking.erp.adaptor.service.file;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.CuxTwoTabValueDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.VoucherGroupData;
import com.xiaoju.corebanking.erp.adaptor.service.file.impl.VoucherGroupSyncServiceImpl;
import com.xiaoju.corebanking.erp.adaptor.service.twotab.TwoTabValueService;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@ExtendWith(MockitoExtension.class)
class VoucherGroupSyncServiceImplTest {
    @InjectMocks
    VoucherGroupSyncServiceImpl voucherGroupSyncService;
    @Mock
    TwoTabValueService twoTabValueService;

    @Test
    void processVoucherGroupMapping() {
        String filePath = "../../../../testFile";
        String processDay= "2025-08-15";
        try{
            voucherGroupSyncService.processVoucherGroupMapping(filePath,processDay);
        }catch (Exception e){

        }
    }

    @Test
    void testParseRecord() throws IOException {
        VoucherGroupSyncServiceImpl service = new VoucherGroupSyncServiceImpl();
        byte[] data = {72, 101, 108, 108, 111,111};
        InputStream inputStream = new ByteArrayInputStream(data);
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
        CSVRecord record = new CSVParser(reader, CSVFormat.DEFAULT).getRecords().get(0);
        ReflectionTestUtils.invokeMethod(service,"parseRecord",record, 1,"2025");
    }
    @Test
    void testBuildCuxTwoTabValueDO() {
        VoucherGroupSyncServiceImpl service = new VoucherGroupSyncServiceImpl();
        VoucherGroupData.Builder data = VoucherGroupData.builder();
        VoucherGroupData voucherGroupData = data.build();
        ReflectionTestUtils.invokeMethod(service,"buildCuxTwoTabValueDO",voucherGroupData);
    }
    @Test
    void testTrimField() {
        VoucherGroupSyncServiceImpl service = new VoucherGroupSyncServiceImpl();
        VoucherGroupData.Builder data = VoucherGroupData.builder();
        VoucherGroupData voucherGroupData = data.build();
        ReflectionTestUtils.invokeMethod(service,"trimField","");
    }

}