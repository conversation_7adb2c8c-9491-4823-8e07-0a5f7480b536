package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.converter.SofiGlInterfaceModelConverter;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfacePO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfaceHisPOMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

/**
* <p> SofiGlInterfaceHisRepositoryImpl Tester. </p>
* <p> 2025-06-06 11:40:17.962 </p>
*
* <AUTHOR>
* @version 0.0.1-SNAPSHOT
*/
@ExtendWith(MockitoExtension.class)
public class SofiGlInterfaceHisRepositoryImplTest {
    @InjectMocks
    SofiGlInterfaceHisRepositoryImpl sofiGlInterfaceHisRepository;
    @Mock
    private SofiGlInterfaceHisPOMapper sofiGlInterfaceHisPOMapper;

    @Mock
    private SofiGlInterfaceModelConverter sofiGlInterfaceModelConverter;
    /**
    *
    * Method: backupInterfaceHis(sofiGlInterfaceDOList)
    */
    @Test
    public void backupInterfaceHisTest() throws Exception {
        List<SofiGlInterfacePO> sofiGlInterfaceDOList = new ArrayList<>();
        SofiGlInterfacePO sofiGlInterfacePO = new SofiGlInterfacePO();
        sofiGlInterfacePO.setId(1L);
        sofiGlInterfacePO.setFileId(1L);
        sofiGlInterfacePO.setProcessDay("2025");
        sofiGlInterfacePO.setFileName("filename");
        sofiGlInterfacePO.setBatchId("1");
        sofiGlInterfacePO.setDetallePolizaId(1L);
        sofiGlInterfacePO.setSourceSys("safi");
        sofiGlInterfacePO.setEmpresaId(1L);
        sofiGlInterfacePO.setPolizaId(1L);
        sofiGlInterfacePO.setFecha("2025-06");
        sofiGlInterfacePO.setCentroCostoId(1L);
        sofiGlInterfacePO.setCuentaCompleta("1");
        sofiGlInterfacePO.setInstrumento(1L);
        sofiGlInterfacePO.setMonedaId(1L);
        sofiGlInterfacePO.setCargos(new BigDecimal(1));
        sofiGlInterfacePO.setAbonos(new BigDecimal(1));
        sofiGlInterfacePO.setDescripcion("1");
        sofiGlInterfacePO.setReferencia("1");
        sofiGlInterfacePO.setProcedimientoCont("1");
        sofiGlInterfacePO.setTipoInstrumentoId("1");
        sofiGlInterfacePO.setRfc("1");
        sofiGlInterfacePO.setTotalFactura(new BigDecimal(1));
        sofiGlInterfacePO.setFolioUuid("1");
        sofiGlInterfacePO.setUsuario(1L);
        sofiGlInterfacePO.setFechaActual("1");
        sofiGlInterfacePO.setDireccionIp("1");
        sofiGlInterfacePO.setProgramaId("1");
        sofiGlInterfacePO.setSucursal(1L);
        sofiGlInterfacePO.setNumTransaccion(1L);
        sofiGlInterfacePO.setLedgerId(1L);
        sofiGlInterfacePO.setLedgerName("1");
        sofiGlInterfacePO.setCurrencyCode("1");
        sofiGlInterfacePO.setJournalCategory("1");
        sofiGlInterfacePO.setJournalSource("1");
        sofiGlInterfacePO.setSegment1("1");
        sofiGlInterfacePO.setSegment2("1");
        sofiGlInterfacePO.setSegment3("1");
        sofiGlInterfacePO.setSegment4("1");
        sofiGlInterfacePO.setSegment5("1");
        sofiGlInterfacePO.setSegment6("1");
        sofiGlInterfacePO.setSegment7("1");
        sofiGlInterfacePO.setSegment8("1");
        sofiGlInterfacePO.setSegment9("1");
        sofiGlInterfacePO.setSegment10("1");
        sofiGlInterfacePO.setGroupId(1L);
        sofiGlInterfacePO.setCurrencyConversionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfacePO.setCurrencyConversionRate(new BigDecimal(1));
        sofiGlInterfacePO.setUserCurrencyConversionType("1");
        sofiGlInterfacePO.setProcessStatus(ProcessStatusEnum.NEW.getCode());
        sofiGlInterfacePO.setProcessMessage("1");
        sofiGlInterfacePO.setJeHeaderId(1L);
        sofiGlInterfacePO.setJournalName("1");
        sofiGlInterfacePO.setJeLineNum(1L);
        sofiGlInterfacePO.setDocumentId(1L);
        sofiGlInterfacePO.setLoadRequestId(1L);
        sofiGlInterfacePO.setImportRequestId(1L);
        sofiGlInterfacePO.setObjectVersionNumber(1L);
        sofiGlInterfacePO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfacePO.setCreatedBy("1");
        sofiGlInterfacePO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfacePO.setLastModifiedBy("1");
        sofiGlInterfacePO.setJournalSourceName("1");
        sofiGlInterfaceDOList.add(sofiGlInterfacePO);
        SofiGlInterfaceDO sofiGlInterfaceDO = new SofiGlInterfaceDO();
        sofiGlInterfaceDO.setId(1L);
        sofiGlInterfaceDO.setFileId(1L);
        sofiGlInterfaceDO.setProcessDay("2025");
        sofiGlInterfaceDO.setFileName("filename");
        sofiGlInterfaceDO.setBatchId("1");
        sofiGlInterfaceDO.setDetallePolizaId(1L);
        sofiGlInterfaceDO.setSourceSys("safi");
        sofiGlInterfaceDO.setEmpresaId(1L);
        sofiGlInterfaceDO.setPolizaId(1L);
        sofiGlInterfaceDO.setFecha("2025-06");
        sofiGlInterfaceDO.setCentroCostoId(1L);
        sofiGlInterfaceDO.setCuentaCompleta("1");
        sofiGlInterfaceDO.setInstrumento(1L);
        sofiGlInterfaceDO.setMonedaId(1L);
        sofiGlInterfaceDO.setCargos(new BigDecimal(1));
        sofiGlInterfaceDO.setAbonos(new BigDecimal(1));
        sofiGlInterfaceDO.setDescripcion("1");
        sofiGlInterfaceDO.setReferencia("1");
        sofiGlInterfaceDO.setProcedimientoCont("1");
        sofiGlInterfaceDO.setTipoInstrumentoId("1");
        sofiGlInterfaceDO.setRfc("1");
        sofiGlInterfaceDO.setTotalFactura(new BigDecimal(1));
        sofiGlInterfaceDO.setFolioUuid("1");
        sofiGlInterfaceDO.setUsuario(1L);
        sofiGlInterfaceDO.setFechaActual("1");
        sofiGlInterfaceDO.setDireccionIp("1");
        sofiGlInterfaceDO.setProgramaId("1");
        sofiGlInterfaceDO.setSucursal(1L);
        sofiGlInterfaceDO.setNumTransaccion(1L);
        sofiGlInterfaceDO.setLedgerId(1L);
        sofiGlInterfaceDO.setLedgerName("1");
        sofiGlInterfaceDO.setCurrencyCode("1");
        sofiGlInterfaceDO.setJournalCategory("1");
        sofiGlInterfaceDO.setJournalSource("1");
        sofiGlInterfaceDO.setSegment1("1");
        sofiGlInterfaceDO.setSegment2("1");
        sofiGlInterfaceDO.setSegment3("1");
        sofiGlInterfaceDO.setSegment4("1");
        sofiGlInterfaceDO.setSegment5("1");
        sofiGlInterfaceDO.setSegment6("1");
        sofiGlInterfaceDO.setSegment7("1");
        sofiGlInterfaceDO.setSegment8("1");
        sofiGlInterfaceDO.setSegment9("1");
        sofiGlInterfaceDO.setSegment10("1");
        sofiGlInterfaceDO.setGroupId(1L);
        sofiGlInterfaceDO.setCurrencyConversionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setCurrencyConversionRate(new BigDecimal(1));
        sofiGlInterfaceDO.setUserCurrencyConversionType("1");
        sofiGlInterfaceDO.setProcessStatus(ProcessStatusEnum.NEW);
        sofiGlInterfaceDO.setProcessMessage("1");
        sofiGlInterfaceDO.setJeHeaderId(1L);
        sofiGlInterfaceDO.setJournalName("1");
        sofiGlInterfaceDO.setJeLineNum(1L);
        sofiGlInterfaceDO.setDocumentId(1L);
        sofiGlInterfaceDO.setLoadRequestId(1L);
        sofiGlInterfaceDO.setImportRequestId(1L);
        sofiGlInterfaceDO.setObjectVersionNumber(1L);
        sofiGlInterfaceDO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setCreatedBy("1");
        sofiGlInterfaceDO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setLastModifiedBy("1");
        sofiGlInterfaceDO.setJournalSourceName("1");
        when(sofiGlInterfaceModelConverter.convert(sofiGlInterfacePO)).thenReturn(sofiGlInterfaceDO);
        sofiGlInterfaceHisRepository.backupInterfaceHis(sofiGlInterfaceDOList);
    }
    @Test
    public void backupInterfaceHisTest2() throws Exception {
        List<SofiGlInterfacePO> sofiGlInterfaceDOList = new ArrayList<>();
        sofiGlInterfaceHisRepository.backupInterfaceHis(sofiGlInterfaceDOList);
    }

    @Test
    public void backupInterfaceHisTes3() throws Exception {
        doThrow(new RuntimeException("DB Error"))
                .when(sofiGlInterfaceHisPOMapper)
                .insertSelective(any());
        List<SofiGlInterfacePO> sofiGlInterfaceDOList = new ArrayList<>();
        SofiGlInterfacePO sofiGlInterfacePO = new SofiGlInterfacePO();
        sofiGlInterfacePO.setId(1L);
        sofiGlInterfacePO.setFileId(1L);
        sofiGlInterfacePO.setProcessDay("2025");
        sofiGlInterfacePO.setFileName("filename");
        sofiGlInterfacePO.setBatchId("1");
        sofiGlInterfacePO.setDetallePolizaId(1L);
        sofiGlInterfacePO.setSourceSys("safi");
        sofiGlInterfacePO.setEmpresaId(1L);
        sofiGlInterfacePO.setPolizaId(1L);
        sofiGlInterfacePO.setFecha("2025-06");
        sofiGlInterfacePO.setCentroCostoId(1L);
        sofiGlInterfacePO.setCuentaCompleta("1");
        sofiGlInterfacePO.setInstrumento(1L);
        sofiGlInterfacePO.setMonedaId(1L);
        sofiGlInterfacePO.setCargos(new BigDecimal(1));
        sofiGlInterfacePO.setAbonos(new BigDecimal(1));
        sofiGlInterfacePO.setDescripcion("1");
        sofiGlInterfacePO.setReferencia("1");
        sofiGlInterfacePO.setProcedimientoCont("1");
        sofiGlInterfacePO.setTipoInstrumentoId("1");
        sofiGlInterfacePO.setRfc("1");
        sofiGlInterfacePO.setTotalFactura(new BigDecimal(1));
        sofiGlInterfacePO.setFolioUuid("1");
        sofiGlInterfacePO.setUsuario(1L);
        sofiGlInterfacePO.setFechaActual("1");
        sofiGlInterfacePO.setDireccionIp("1");
        sofiGlInterfacePO.setProgramaId("1");
        sofiGlInterfacePO.setSucursal(1L);
        sofiGlInterfacePO.setNumTransaccion(1L);
        sofiGlInterfacePO.setLedgerId(1L);
        sofiGlInterfacePO.setLedgerName("1");
        sofiGlInterfacePO.setCurrencyCode("1");
        sofiGlInterfacePO.setJournalCategory("1");
        sofiGlInterfacePO.setJournalSource("1");
        sofiGlInterfacePO.setSegment1("1");
        sofiGlInterfacePO.setSegment2("1");
        sofiGlInterfacePO.setSegment3("1");
        sofiGlInterfacePO.setSegment4("1");
        sofiGlInterfacePO.setSegment5("1");
        sofiGlInterfacePO.setSegment6("1");
        sofiGlInterfacePO.setSegment7("1");
        sofiGlInterfacePO.setSegment8("1");
        sofiGlInterfacePO.setSegment9("1");
        sofiGlInterfacePO.setSegment10("1");
        sofiGlInterfacePO.setGroupId(1L);
        sofiGlInterfacePO.setCurrencyConversionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfacePO.setCurrencyConversionRate(new BigDecimal(1));
        sofiGlInterfacePO.setUserCurrencyConversionType("1");
        sofiGlInterfacePO.setProcessStatus(ProcessStatusEnum.NEW.getCode());
        sofiGlInterfacePO.setProcessMessage("1");
        sofiGlInterfacePO.setJeHeaderId(1L);
        sofiGlInterfacePO.setJournalName("1");
        sofiGlInterfacePO.setJeLineNum(1L);
        sofiGlInterfacePO.setDocumentId(1L);
        sofiGlInterfacePO.setLoadRequestId(1L);
        sofiGlInterfacePO.setImportRequestId(1L);
        sofiGlInterfacePO.setObjectVersionNumber(1L);
        sofiGlInterfacePO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfacePO.setCreatedBy("1");
        sofiGlInterfacePO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfacePO.setLastModifiedBy("1");
        sofiGlInterfacePO.setJournalSourceName("1");
        sofiGlInterfaceDOList.add(sofiGlInterfacePO);
        SofiGlInterfaceDO sofiGlInterfaceDO = new SofiGlInterfaceDO();
        sofiGlInterfaceDO.setId(1L);
        sofiGlInterfaceDO.setFileId(1L);
        sofiGlInterfaceDO.setProcessDay("2025");
        sofiGlInterfaceDO.setFileName("filename");
        sofiGlInterfaceDO.setBatchId("1");
        sofiGlInterfaceDO.setDetallePolizaId(1L);
        sofiGlInterfaceDO.setSourceSys("safi");
        sofiGlInterfaceDO.setEmpresaId(1L);
        sofiGlInterfaceDO.setPolizaId(1L);
        sofiGlInterfaceDO.setFecha("2025-06");
        sofiGlInterfaceDO.setCentroCostoId(1L);
        sofiGlInterfaceDO.setCuentaCompleta("1");
        sofiGlInterfaceDO.setInstrumento(1L);
        sofiGlInterfaceDO.setMonedaId(1L);
        sofiGlInterfaceDO.setCargos(new BigDecimal(1));
        sofiGlInterfaceDO.setAbonos(new BigDecimal(1));
        sofiGlInterfaceDO.setDescripcion("1");
        sofiGlInterfaceDO.setReferencia("1");
        sofiGlInterfaceDO.setProcedimientoCont("1");
        sofiGlInterfaceDO.setTipoInstrumentoId("1");
        sofiGlInterfaceDO.setRfc("1");
        sofiGlInterfaceDO.setTotalFactura(new BigDecimal(1));
        sofiGlInterfaceDO.setFolioUuid("1");
        sofiGlInterfaceDO.setUsuario(1L);
        sofiGlInterfaceDO.setFechaActual("1");
        sofiGlInterfaceDO.setDireccionIp("1");
        sofiGlInterfaceDO.setProgramaId("1");
        sofiGlInterfaceDO.setSucursal(1L);
        sofiGlInterfaceDO.setNumTransaccion(1L);
        sofiGlInterfaceDO.setLedgerId(1L);
        sofiGlInterfaceDO.setLedgerName("1");
        sofiGlInterfaceDO.setCurrencyCode("1");
        sofiGlInterfaceDO.setJournalCategory("1");
        sofiGlInterfaceDO.setJournalSource("1");
        sofiGlInterfaceDO.setSegment1("1");
        sofiGlInterfaceDO.setSegment2("1");
        sofiGlInterfaceDO.setSegment3("1");
        sofiGlInterfaceDO.setSegment4("1");
        sofiGlInterfaceDO.setSegment5("1");
        sofiGlInterfaceDO.setSegment6("1");
        sofiGlInterfaceDO.setSegment7("1");
        sofiGlInterfaceDO.setSegment8("1");
        sofiGlInterfaceDO.setSegment9("1");
        sofiGlInterfaceDO.setSegment10("1");
        sofiGlInterfaceDO.setGroupId(1L);
        sofiGlInterfaceDO.setCurrencyConversionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setCurrencyConversionRate(new BigDecimal(1));
        sofiGlInterfaceDO.setUserCurrencyConversionType("1");
        sofiGlInterfaceDO.setProcessStatus(ProcessStatusEnum.NEW);
        sofiGlInterfaceDO.setProcessMessage("1");
        sofiGlInterfaceDO.setJeHeaderId(1L);
        sofiGlInterfaceDO.setJournalName("1");
        sofiGlInterfaceDO.setJeLineNum(1L);
        sofiGlInterfaceDO.setDocumentId(1L);
        sofiGlInterfaceDO.setLoadRequestId(1L);
        sofiGlInterfaceDO.setImportRequestId(1L);
        sofiGlInterfaceDO.setObjectVersionNumber(1L);
        sofiGlInterfaceDO.setCreationDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setCreatedBy("1");
        sofiGlInterfaceDO.setLastModifyDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        sofiGlInterfaceDO.setLastModifiedBy("1");
        sofiGlInterfaceDO.setJournalSourceName("1");
        when(sofiGlInterfaceModelConverter.convert(sofiGlInterfacePO)).thenReturn(sofiGlInterfaceDO);
        try {
            sofiGlInterfaceHisRepository.backupInterfaceHis(sofiGlInterfaceDOList);
        } catch (Exception e) {

        }
    }

}
