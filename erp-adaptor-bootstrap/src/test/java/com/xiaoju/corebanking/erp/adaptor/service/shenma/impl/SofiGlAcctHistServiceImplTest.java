package com.xiaoju.corebanking.erp.adaptor.service.shenma.impl;

import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlAcctHistRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SofiGlAcctHistServiceImplTest {
    @InjectMocks
    SofiGlAcctHistServiceImpl sofiGlAcctHistService;
    @Mock
    private SofiGlAcctHistRepository sofiGlAcctHistRepository;
    @Test
    void queryAcctHisSummary() {
        String processDay = "2025-08-13";
        sofiGlAcctHistService.queryAcctHisSummary(processDay);
    }
}