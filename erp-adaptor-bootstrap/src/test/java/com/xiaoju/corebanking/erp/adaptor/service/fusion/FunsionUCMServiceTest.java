package com.xiaoju.corebanking.erp.adaptor.service.fusion;

import com.xiaoju.corebanking.erp.adaptor.common.domain.FusionUcmResponse;
import com.xiaoju.corebanking.erp.adaptor.service.inter.SofiGlInterfaceService;
import com.xiaoju.godson.http.entity.HttpClientConnection;
import com.xiaoju.godson.http.entity.HttpClientResponse;
import com.xiaoju.godson.http.util.HttpClientUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * <p> FunsionUCMService Tester. </p>
 * <p> 2025-06-07 21:04:14.051 </p>
 *
 * <AUTHOR>
 * @version 0.0.1-SNAPSHOT
 */
@ExtendWith(MockitoExtension.class)
public class FunsionUCMServiceTest {

    @InjectMocks
    private FunsionUCMService funsionUCMService;

    @Mock
    private FusionAuthService fusionAuthService;
    @Mock
    RestTemplate restTemplate;

    private final String USERNAME = "testUser";
    private final String COMMON_URL = "http://test.api.com";
    private final String TOKEN = "test-token";
    private final String DOCUMENT_CONTENT = "test-document-content";
    private final String FILE_NAME = "test-file.txt";

    private HttpClientConnection mockHttpClientConnection;
    private HttpClientResponse mockHttpClientResponse;


    @BeforeEach
    void setUp() throws NoSuchFieldException, IllegalAccessException {
        java.lang.reflect.Field fusionSoapUrlField = FunsionUCMService.class.getDeclaredField("commonUrl");
        fusionSoapUrlField.setAccessible(true);
        fusionSoapUrlField.set(funsionUCMService, "http://test.soap.url");

        java.lang.reflect.Field fusionUsernameField = FunsionUCMService.class.getDeclaredField("username");
        fusionUsernameField.setAccessible(true);
        fusionUsernameField.set(funsionUCMService, "username");

        mockHttpClientConnection = mock(HttpClientConnection.class);
        mockHttpClientResponse = mock(HttpClientResponse.class);
        mockStatic(HttpClientUtil.class);
    }

    @Mock
    private SofiGlInterfaceService sofiGlInterfaceService;


    /**
     * Method: uploadFileToUCM(documentContent, fileName, token)
     */
    @Test
    public void uploadFileToUCMTest() throws Exception {
        String responseJson = "{\"Content\":{\"documentId\":\"DOC123\",\"contentId\":\"CONT456\"}}";

        when(HttpClientUtil.post(anyString())).thenReturn(mockHttpClientConnection);

        when(mockHttpClientConnection.addHeader(anyString(), anyString())).thenReturn(mockHttpClientConnection);
        when(mockHttpClientConnection.bodyJson(anyString())).thenReturn(mockHttpClientConnection);
        when(mockHttpClientConnection.execute()).thenReturn(mockHttpClientResponse);

        when(mockHttpClientResponse.isSuccess()).thenReturn(true);
        when(mockHttpClientResponse.getString()).thenReturn(responseJson);

        FusionUcmResponse result = funsionUCMService.uploadFileToUCM(DOCUMENT_CONTENT, FILE_NAME, TOKEN);

        assertNotNull(result);

        when(mockHttpClientConnection.execute()).thenThrow(new IOException());
        try {
            funsionUCMService.uploadFileToUCM(DOCUMENT_CONTENT, FILE_NAME, TOKEN);
        } catch (Exception e) {

        }
    }


}
