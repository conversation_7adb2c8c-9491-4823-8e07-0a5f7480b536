package com.xiaoju.corebanking.erp.adaptor.service.shenma.impl;

import com.github.pagehelper.PageInfo;
import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.CoaRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlShenmaRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.ValidationRequestDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.ValidationResultDO;
import com.xiaoju.corebanking.erp.adaptor.service.fusion.CoaValidationService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SofiGlShenMaServiceImplTest {
    @InjectMocks
    SofiGlShenMaServiceImpl sofiGlShenMaService;
    @Mock
    private SofiGlShenmaRepository sofiGlShenmaRepository;
    @Mock
    private CoaRepository coaRepository;
    @Mock
    private CoaValidationService coaValidationService;
    @Test
    void updateShenmaInterfaceByReference() {
       SofiGlShenmaDO sofiGlShenmaDO = new SofiGlShenmaDO();
        SofiGlShenmaDO update = new SofiGlShenmaDO();
        sofiGlShenMaService.updateShenmaInterfaceByReference(sofiGlShenmaDO, update);
    }


    @Test
    void querySofiGlShenmaByGroupIdAndProcessDay() {
        Long groupId = 1L;
        String processDay =  "2025-08-18";
        sofiGlShenMaService.querySofiGlShenmaByGroupIdAndProcessDay(groupId, processDay);
    }

    @Test
    void validateCoa() {
        ValidationRequestDO validationRequestDO = new ValidationRequestDO();
        validationRequestDO.setLedgerName("test");
        List<ValidationRequestDO> list = new ArrayList<>();
        list.add(validationRequestDO);
        PageInfo<ValidationRequestDO> pageInfo = new PageInfo<>(list);
        when(coaRepository.pagingSelectShenMaCoaList("2025-08-18", 1, 5000)).thenReturn(pageInfo);
        List< ValidationResultDO > validationResultDOS = new ArrayList<>();
        ValidationResultDO validationResultDO = new ValidationResultDO();
        validationResultDO.setStatus("Invalid");
        validationResultDOS.add(validationResultDO);
        when(coaValidationService.validateCoa(pageInfo.getList())).thenReturn(validationResultDOS);
        sofiGlShenMaService.validateCoa("2025-08-18");
    }

    @Test
    void updateByProcessDayAndGroupIdAndStatus() {
        String processDay =  "2025-08-18";
        Long groupId = 1L;
        String processStatus = ProcessStatusEnum.FAILED.name();
        SofiGlShenmaDO update = new SofiGlShenmaDO();
        sofiGlShenMaService.updateByProcessDayAndGroupIdAndStatus(processDay, groupId, processStatus,update);
    }

    @Test
    void queryShenMaSummary() {
        String processDay = "2025-08-18";
        sofiGlShenMaService.queryShenMaSummary(processDay);
    }
    @Test
    void queryShenMaSumByVoucherGroup() {
        String processDay = "2025-08-18";
        sofiGlShenMaService.queryShenMaSumByVoucherGroup(processDay);
    }
    @Test
    void queryByPolizaIdsAndProcessDay() {
        String processDay = "2025-08-18";
        List<String> externalReferenceList = new ArrayList<>();
        sofiGlShenMaService.queryByPolizaIdsAndProcessDay(externalReferenceList,processDay);
    }

}