package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.xiaoju.corebanking.erp.adaptor.repository.TwoTabHeaderRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.CuxTwoTabValueDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabHeadersPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValueExtraPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.CuxTwoTabValuesExtraMapper;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.CuxTwoTabValuePOMapper;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
* <p> TwoTabValueRepositoryImpl Tester. </p>
* <p> 2025-06-06 11:40:17.952 </p>
*
* <AUTHOR>
* @version 0.0.1-SNAPSHOT
*/
@ExtendWith(MockitoExtension.class)
public class TwoTabValueRepositoryImplTest {
    @InjectMocks
    private TwoTabValueRepositoryImpl twoTabValueRepository;

    @Mock
    CuxTwoTabValuePOMapper cuxTwoTabValuePOMapper;

    @Mock
    CuxTwoTabValuesExtraMapper cuxTwoTabValuesExtraMapper;

    @Mock
    TwoTabHeaderRepository twoTabHeaderRepository;
    /**
    *
    * Method: insertValue(value)
    */
    @Test
    public void insertValueTest() throws Exception {
        CuxTwoTabValueDO cuxTwoTabValueDO = new CuxTwoTabValueDO();
        cuxTwoTabValueDO.setFormCode("1");
        Assert.assertThrows(RuntimeException.class,()->twoTabValueRepository.insertValue(cuxTwoTabValueDO));
    }
    @Test
    public void insertValueTest2() throws Exception {
        CuxTwoTabValueDO cuxTwoTabValueDO = new CuxTwoTabValueDO();
        cuxTwoTabValueDO.setValue1(null);
        cuxTwoTabValueDO.setValue2(null);
        cuxTwoTabValueDO.setValue3(null);
        cuxTwoTabValueDO.setValue4(null);
        cuxTwoTabValueDO.setValue5(null);
        cuxTwoTabValueDO.setValue6(null);
        cuxTwoTabValueDO.setValue7(null);
        cuxTwoTabValueDO.setValue8(null);
        cuxTwoTabValueDO.setValue9(null);
        cuxTwoTabValueDO.setValue10(null);
        cuxTwoTabValueDO.setValue11(null);
        cuxTwoTabValueDO.setValue12(null);
        cuxTwoTabValueDO.setValue13(null);
        cuxTwoTabValueDO.setValue14(null);
        cuxTwoTabValueDO.setValue15(null);
        cuxTwoTabValueDO.setValue16(null);
        cuxTwoTabValueDO.setValue17(null);
        cuxTwoTabValueDO.setValue18(null);
        cuxTwoTabValueDO.setValue19(null);
        cuxTwoTabValueDO.setValue20(null);
        cuxTwoTabValueDO.setValue21(null);
        cuxTwoTabValueDO.setValue22(null);
        cuxTwoTabValueDO.setValue23(null);
        cuxTwoTabValueDO.setValue24(null);
        cuxTwoTabValueDO.setValue25(null);
        cuxTwoTabValueDO.setValue26(null);
        cuxTwoTabValueDO.setValue27(null);
        cuxTwoTabValueDO.setValue28(null);
        cuxTwoTabValueDO.setValue29(null);
        cuxTwoTabValueDO.setValue30(null);
        cuxTwoTabValueDO.setValue31(null);
        cuxTwoTabValueDO.setValue32(null);
        cuxTwoTabValueDO.setValue33(null);
        cuxTwoTabValueDO.setValue34(null);
        cuxTwoTabValueDO.setValue35(null);
        cuxTwoTabValueDO.setValue36(null);
        cuxTwoTabValueDO.setValue37(null);
        cuxTwoTabValueDO.setValue38(null);
        cuxTwoTabValueDO.setValue39(null);
        cuxTwoTabValueDO.setValue40(null);
        cuxTwoTabValueDO.setValue41(null);
        cuxTwoTabValueDO.setValue42(null);
        cuxTwoTabValueDO.setValue43(null);
        cuxTwoTabValueDO.setValue44(null);
        cuxTwoTabValueDO.setValue45(null);
        cuxTwoTabValueDO.setValue46(null);
        cuxTwoTabValueDO.setValue47(null);
        cuxTwoTabValueDO.setValue48(null);
        cuxTwoTabValueDO.setValue49(null);
        cuxTwoTabValueDO.setValue50(null);


        cuxTwoTabValueDO.setFormCode("1");
        CuxTwoTabHeadersPO headersPo = new CuxTwoTabHeadersPO();
        headersPo.setFormCode("1");
        headersPo.setId(1L);
        headersPo.setDateTo(null);
        headersPo.setDateFrom(null);
        when(twoTabHeaderRepository.selectByFormCode(cuxTwoTabValueDO.getFormCode())).thenReturn(headersPo);

        twoTabValueRepository.insertValue(cuxTwoTabValueDO);
    }

    /**
    *
    * Method: updateByExample(value)
    */
    @Test
    public void updateByExampleTest() throws Exception {
        CuxTwoTabValueDO twoTabValueDO = new CuxTwoTabValueDO();
        twoTabValueRepository.updateByExample(twoTabValueDO);
    }

    /**
    *
    * Method: insertOrUpdate(list)
    */
    @Test
    public void insertOrUpdateTest() throws Exception {
        List<CuxTwoTabValueDO> list = new ArrayList<>();
        twoTabValueRepository.insertOrUpdate(list);
    }
    @Test
    public void insertOrUpdateTest2() throws Exception {
        List<CuxTwoTabValueDO> list = new ArrayList<>();
        CuxTwoTabValueDO cuxTwoTabValueDO = new CuxTwoTabValueDO();
        cuxTwoTabValueDO.setFormCode("1");
        list.add(cuxTwoTabValueDO);
        Assert.assertThrows(RuntimeException.class,()->twoTabValueRepository.insertOrUpdate(list));
    }
    @Test
    public void insertOrUpdateTest3() throws Exception {
        List<CuxTwoTabValueDO> list = new ArrayList<>();
        CuxTwoTabValueDO cuxTwoTabValueDO = new CuxTwoTabValueDO();
        cuxTwoTabValueDO.setFormCode("1");
        list.add(cuxTwoTabValueDO);
        CuxTwoTabHeadersPO po = new CuxTwoTabHeadersPO();
        po.setId(1L);
        po.setUniqueFields("2,4,2,4");
        when(twoTabHeaderRepository.selectByFormCode("1")).thenReturn(po);
        twoTabValueRepository.insertOrUpdate(list);
    }
    @Test
    public void insertOrUpdateTest4() throws Exception {
        List<CuxTwoTabValueDO> list = new ArrayList<>();
        CuxTwoTabValueDO cuxTwoTabValueDO = new CuxTwoTabValueDO();
        cuxTwoTabValueDO.setFormCode("1");
        list.add(cuxTwoTabValueDO);
        CuxTwoTabHeadersPO po = new CuxTwoTabHeadersPO();
        po.setId(1L);
        po.setUniqueFields("2,4,2,4");
        when(twoTabHeaderRepository.selectByFormCode("1")).thenReturn(po);
        List<CuxTwoTabValuePO> existing = new ArrayList<>();
        CuxTwoTabValuePO cuxTwoTabValuePO = new CuxTwoTabValuePO();
        cuxTwoTabValuePO.setId(1L);
        cuxTwoTabValuePO.setVersion(1);
        existing.add(cuxTwoTabValuePO);
        List<String> uniqueFields = new ArrayList<>();
        uniqueFields.add("2");
        uniqueFields.add("4");
        uniqueFields.add("2");
        uniqueFields.add("4");


        when(cuxTwoTabValuesExtraMapper.findByUniqueFields(any(), anyString(),any(), any())).thenReturn(existing);
        twoTabValueRepository.insertOrUpdate(list);
    }

    /**
    *
    * Method: selectByFormCode(formCode)
    */
    @Test
    public void selectByFormCodeTest() throws Exception {
       Assert.assertThrows(RuntimeException.class,()->twoTabValueRepository.selectByFormCode("1"));
    }
    @Test
    public void selectByFormCodeTest2() throws Exception {
        CuxTwoTabHeadersPO headersPo = new CuxTwoTabHeadersPO();
        headersPo.setId(1L);
        when(twoTabHeaderRepository.selectByFormCode("1")).thenReturn(headersPo);
        twoTabValueRepository.selectByFormCode("1");
    }

    /**
    *
    * Method: selectSimpleByFormCode(formCode)
    */
    @Test
    public void selectSimpleByFormCodeTest() throws Exception {
        twoTabValueRepository.selectSimpleByFormCode("1");
    }

    @Test
    public void testSelectAllByFormCode() {
        List<CuxTwoTabValueExtraPO> list = new ArrayList<>();
        CuxTwoTabValueExtraPO po = new CuxTwoTabValueExtraPO();
        po.setValue5("vvv");
        list.add(po);
        when(cuxTwoTabValuesExtraMapper.selectAllByFormCode()).thenReturn(list);
        twoTabValueRepository.selectAllByFormCode();
    }

}
