package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.xiaoju.corebanking.erp.adaptor.repository.converter.SofiGlSubjectConverter;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlSubjectDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlSubjectPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlSubjectPOKey;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlSubjectPOMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SofiGlSubjectRepositoryImplTest {
    @InjectMocks
    SofiGlSubjectRepositoryImpl sofiGlSubjectRepository;

    @Mock
    private SofiGlSubjectPOMapper sofiGlSubjectPOMapper;

    @Mock
    private SofiGlSubjectConverter sofiGlSubjectConverter;

    @Test
    void syncSofiGlSubjectData() {
        sofiGlSubjectRepository.syncSofiGlSubjectData(null);
    }
    @Test
    void syncSofiGlSubjectDataNotNull() {
        List<SofiGlSubjectDO> sofiGlSubjectList = new ArrayList<>();
        SofiGlSubjectDO sofiGlSubjectDO = new SofiGlSubjectDO();
        sofiGlSubjectDO.setSubjectCode("gllgg");
        sofiGlSubjectDO.setSubjectSetNo("2342");
        sofiGlSubjectList.add(sofiGlSubjectDO);
        SofiGlSubjectPO existingRecord = new SofiGlSubjectPO();

        SofiGlSubjectPOKey key = new SofiGlSubjectPOKey();
        key.setSubjectCode(sofiGlSubjectDO.getSubjectCode());
        key.setSubjectSetNo(sofiGlSubjectDO.getSubjectSetNo());

        when(sofiGlSubjectPOMapper.selectByPrimaryKey(key)).thenReturn(existingRecord);

        SofiGlSubjectPO po = sofiGlSubjectConverter.toPO(sofiGlSubjectDO);

        when(sofiGlSubjectPOMapper.updateByPrimaryKeySelective(po)).thenReturn(1);
        sofiGlSubjectRepository.syncSofiGlSubjectData(sofiGlSubjectList);
    }

    @Test
    void syncSofiGlSubjectDataNotNull2() {
        List<SofiGlSubjectDO> sofiGlSubjectList = new ArrayList<>();
        SofiGlSubjectDO sofiGlSubjectDO = new SofiGlSubjectDO();
        sofiGlSubjectDO.setSubjectCode("gllgg");
        sofiGlSubjectDO.setSubjectSetNo("2342");
        sofiGlSubjectList.add(sofiGlSubjectDO);
        SofiGlSubjectPO existingRecord = new SofiGlSubjectPO();

        SofiGlSubjectPOKey key = new SofiGlSubjectPOKey();
        key.setSubjectCode(sofiGlSubjectDO.getSubjectCode());
        key.setSubjectSetNo(sofiGlSubjectDO.getSubjectSetNo());

        when(sofiGlSubjectPOMapper.selectByPrimaryKey(key)).thenReturn(null);

        SofiGlSubjectPO po = sofiGlSubjectConverter.toPO(sofiGlSubjectDO);

        when( sofiGlSubjectPOMapper.insertSelective(po)).thenReturn(1);
        sofiGlSubjectRepository.syncSofiGlSubjectData(sofiGlSubjectList);
    }
}