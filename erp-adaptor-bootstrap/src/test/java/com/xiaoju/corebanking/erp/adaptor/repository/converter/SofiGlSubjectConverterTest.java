package com.xiaoju.corebanking.erp.adaptor.repository.converter;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlSubjectDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlSubjectPO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * @Description: 类描述
 * @author: didi
 **/
@ExtendWith(MockitoExtension.class)
public class SofiGlSubjectConverterTest {
    @InjectMocks
    private SofiGlSubjectConverter converter;
    @Test
    public void testConverterPO(){
        SofiGlSubjectDO sofiDo = new SofiGlSubjectDO();
        converter.toPO(sofiDo);
    }
    @Test
    public void testConverterPOIsNull(){
        converter.toPO(null);
    }
    @Test
    public void testConverterDO(){
        SofiGlSubjectPO sofiPo = new SofiGlSubjectPO();
        converter.toDO(sofiPo);
    }
    @Test
    public void testConverterDOIsNull(){
        converter.toDO(null);
    }
}
