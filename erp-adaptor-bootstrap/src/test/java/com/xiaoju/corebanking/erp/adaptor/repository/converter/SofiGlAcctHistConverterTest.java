package com.xiaoju.corebanking.erp.adaptor.repository.converter;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlAcctHistDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlAcctHistPO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * @Description: 类描述
 * @author: didi
 **/
@ExtendWith(MockitoExtension.class)
public class SofiGlAcctHistConverterTest {
    @InjectMocks
    private SofiGlAcctHistConverter converter;
    @Test
    public void testConverterPO(){
        SofiGlAcctHistDO sofiGlAcctHistDO = new SofiGlAcctHistDO();
        converter.toPO(sofiGlAcctHistDO);
    }
    @Test
    public void testConverterPOIsNull(){
        converter.toPO(null);
    }
    @Test
    public void testConverterDO(){
        SofiGlAcctHistPO sofiGlAcctHistPO = new SofiGlAcctHistPO();
        converter.toDO(sofiGlAcctHistPO);
    }
    @Test
    public void testConverterDOIsNull(){
        converter.toDO(null);
    }
}
