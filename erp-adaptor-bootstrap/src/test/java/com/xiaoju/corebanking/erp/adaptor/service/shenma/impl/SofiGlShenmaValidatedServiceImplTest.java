package com.xiaoju.corebanking.erp.adaptor.service.shenma.impl;

import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceCommonDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValueExtraPO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
class SofiGlShenmaValidatedServiceImplTest {
    @InjectMocks
    SofiGlShenmaValidatedServiceImpl sofiGlShenmaValidatedService;
    @Test
    void validated() {
        SofiGlShenmaDO sofiGlShenmaDO = new SofiGlShenmaDO();
        sofiGlShenmaDO.setEnteredCreditAmount(new BigDecimal(1));
        sofiGlShenmaDO.setEnteredDebitAmount(new BigDecimal(11));
        sofiGlShenmaValidatedService.validated(sofiGlShenmaDO);
    }
    @Test
    void validated2() {
        SofiGlShenmaDO sofiGlShenmaDO = new SofiGlShenmaDO();

        sofiGlShenmaValidatedService.validated(sofiGlShenmaDO);
    }

    @Test
    void mappedResult() {
        Map<String, Map<String, CuxTwoTabValueExtraPO>> cache = new HashMap<>();
        SofiGlShenmaDO sofiGlShenmaDO = new SofiGlShenmaDO();
        SofiGlInterfaceCommonDO commonDO = new SofiGlInterfaceCommonDO();
        commonDO.setGroupId(1L);
        sofiGlShenmaDO.setSourceBranch("shenma");
        sofiGlShenmaDO.setVoucherGroup("vouchergroup");
        sofiGlShenmaDO.setCcy("mxn");
        sofiGlShenmaDO.setProdType("prodtype");
        sofiGlShenmaDO.setProfitCenter("profit");
        sofiGlShenmaDO.setIntercompany("intercompany");
        sofiGlShenmaDO.setGlCode("glcode");
//        Map<String, Map<String, CuxTwoTabValueExtraPO>> cache
        Map<String, CuxTwoTabValueExtraPO> map = new HashMap<>();
        CuxTwoTabValueExtraPO po = new CuxTwoTabValueExtraPO();
        po.setValue1("1");
        po.setValue2("2");
        po.setValue3("3");
        po.setValue4("4");
        po.setValue5("5");
        map.put(sofiGlShenmaDO.getSourceBranch(),po);
        map.put(sofiGlShenmaDO.getVoucherGroup(),po);
        map.put(sofiGlShenmaDO.getCcy(),po);
        map.put(sofiGlShenmaDO.getProdType(),po);
        map.put(sofiGlShenmaDO.getProfitCenter(),po);
        map.put(sofiGlShenmaDO.getIntercompany(),po);
        map.put(sofiGlShenmaDO.getGlCode(),po);
        cache.put(CommonConstant.CATEGORY_MAPPINGS, map);
        cache.put(CommonConstant.CURRENCY_MAPPINGS, map);
        cache.put(CommonConstant.COMPANY_LEDGER_MAPPINGS,map);
        cache.put(CommonConstant.PRODUCT_MAPPINGS,map);
        cache.put(CommonConstant.GL_JOURNAL_SOURCE_MAPPINGS,map);
        cache.put(CommonConstant.PROFIT_CENTER_MAPPINGS,map);
        cache.put(CommonConstant.GL_CODE_MAPPINGS,map);
        sofiGlShenmaValidatedService.mappedResult(sofiGlShenmaDO,commonDO,cache);
    }
}