package com.xiaoju.corebanking.erp.adaptor.service.file.enums;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlSubjectDO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static com.xiaoju.corebanking.erp.adaptor.service.file.enums.SofiGlSubjectEnum.*;

/**
 * @Description: 类描述
 * @author: didi
 **/
@ExtendWith(MockitoExtension.class)
public class SofiGlSubjectEnumTest {
    @Test
    void setField() {
        SofiGlSubjectDO entity = new SofiGlSubjectDO();
        String fieldName = "fileName";
        String value = null;
        SofiGlSubjectEnum.setField(entity,fieldName,value);
    }
    @Test
    void setFieldNotNull() {
        SofiGlSubjectDO entity = new SofiGlSubjectDO();
        String value = "string";
        SofiGlSubjectEnum.setField(entity,SUBJECT_CODE.name(),value);
        SofiGlSubjectEnum.setField(entity,SUBJECT_DESC.name(),value);
        SofiGlSubjectEnum.setField(entity,SUBJECT_DESC_EN.name(),value);
        SofiGlSubjectEnum.setField(entity,CONTROL_SUBJECT.name(),value);
        SofiGlSubjectEnum.setField(entity,BSPL_TYPE.name(),value);
        SofiGlSubjectEnum.setField(entity,GL_TYPE.name(),value);
        SofiGlSubjectEnum.setField(entity,SUBJECT_TYPE.name(),value);
        SofiGlSubjectEnum.setField(entity,BALANCE_WAY.name(),value);
        SofiGlSubjectEnum.setField(entity,SUBJECT_STATUS.name(),value);
        SofiGlSubjectEnum.setField(entity,SUBJECT_LEVEL.name(),value);
        SofiGlSubjectEnum.setField(entity,MANUAL_ACCOUNT.name(),value);
        SofiGlSubjectEnum.setField(entity,SPECIAL_BOOKKEEPING.name(),value);
        SofiGlSubjectEnum.setField(entity,OD_FACILITY.name(),value);
        SofiGlSubjectEnum.setField(entity,RANGE_NO.name(),value);
        SofiGlSubjectEnum.setField(entity,SUBJECT_SET_NO.name(),value);
        SofiGlSubjectEnum.setField(entity,REVALUE_RATE_TYPE.name(),value);
        SofiGlSubjectEnum.setField(entity,SYSTEM_ID.name(),value);
        SofiGlSubjectEnum.setField(entity,MEASUREMENT_ATTR.name(),value);
        SofiGlSubjectEnum.setField(entity,ITEM_SEGREGATION.name(),value);
        SofiGlSubjectEnum.setField(entity,COMPANY.name(),value);
        SofiGlSubjectEnum.setField(entity,TRAN_TIMESTAMP.name(),value);
        SofiGlSubjectEnum.setField(entity,PAY_REC.name(),value);
        SofiGlSubjectEnum.setField(entity,SUBJECT_REMARK.name(),value);




    }
}
