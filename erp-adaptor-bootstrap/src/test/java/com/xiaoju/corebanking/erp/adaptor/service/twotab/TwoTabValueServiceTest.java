package com.xiaoju.corebanking.erp.adaptor.service.twotab;

import com.xiaoju.corebanking.erp.adaptor.repository.TwoTabValueRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.CuxTwoTabValueDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValueExtraPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePO;
import com.xiaoju.corebanking.erp.adaptor.service.twotab.TwoTabValueService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * <p> TwoTabValueService Tester. </p>
 * <p> 2025-06-07 21:04:14.056 </p>
 *
 * <AUTHOR>
 * @version 0.0.1-SNAPSHOT
 */
@ExtendWith(MockitoExtension.class)
public class TwoTabValueServiceTest {

    @Mock
    private TwoTabValueRepository twoTabValueRepository;

    @InjectMocks
    private TwoTabValueService twoTabValueService;

    private CuxTwoTabValueDO testValueDO;
    private CuxTwoTabValuePO testValuePO;
    private List<CuxTwoTabValueDO> testValueDOList;
    private List<CuxTwoTabValuePO> testValuePOList;

    @BeforeEach
    public void setUp() {
        testValueDO = new CuxTwoTabValueDO();

        testValuePO = new CuxTwoTabValuePO();

        testValueDOList = new ArrayList<>();
        testValueDOList.add(testValueDO);

        testValuePOList = new ArrayList<>();
        testValuePOList.add(testValuePO);
    }


    @Test
    public void insertValueTest() throws Exception {
        when(twoTabValueRepository.insertValue(any())).thenReturn(testValuePO);

        CuxTwoTabValuePO result = twoTabValueService.insertValue(testValueDO);

        assertNotNull(result);
        assertEquals(testValuePO, result);
    }


    @Test
    public void syncValuesTest() throws Exception {

        int result = twoTabValueService.syncValues(testValueDOList);

        assertEquals(0, result);
    }

    /**
     * Method: selectByFormCode(fromCode)
     */
    @Test
    public void selectByFormCodeTest() throws Exception {
        when(twoTabValueRepository.selectByFormCode(anyString())).thenReturn(testValuePOList);

        List<CuxTwoTabValuePO> result = twoTabValueService.selectByFormCode("testFormCode");

        assertNotNull(result);
        assertEquals(testValuePOList.size(), result.size());
        assertEquals(testValuePOList, result);
    }

    /**
     * Method: selectSimpleByFormCode(fromCode)
     */
    @Test
    public void selectSimpleByFormCodeTest() throws Exception {
        when(twoTabValueRepository.selectSimpleByFormCode(anyString())).thenReturn(testValuePOList);

        List<CuxTwoTabValuePO> result = twoTabValueService.selectSimpleByFormCode("testFormCode");

        assertNotNull(result);
        assertEquals(testValuePOList.size(), result.size());
        assertEquals(testValuePOList, result);
    }

    @Test
    public void selectAllByFormCodeTest() {
        List<CuxTwoTabValueExtraPO> list = new ArrayList<>();
        CuxTwoTabValueExtraPO po = new CuxTwoTabValueExtraPO();
        po.setValue5("aaa");
        list.add(po);
        when(twoTabValueRepository.selectAllByFormCode()).thenReturn(list);
        List<CuxTwoTabValueExtraPO> result = twoTabValueService.selectAllByFormCode();
        assertNotNull(result);
    }


}
