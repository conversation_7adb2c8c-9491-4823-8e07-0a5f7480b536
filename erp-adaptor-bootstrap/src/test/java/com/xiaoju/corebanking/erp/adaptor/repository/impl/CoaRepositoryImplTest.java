package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.github.pagehelper.PageInfo;
import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.ValidationRequestDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.ValidationResultDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.CoaCustomerMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static net.sf.json.test.JSONAssert.assertEquals;
import static net.sf.json.test.JSONAssert.assertNotNull;
import static org.mockito.Mockito.when;

/**
 * @Description: 类描述
 * @author: didi
 **/
@ExtendWith(MockitoExtension.class)
public class CoaRepositoryImplTest {
    @InjectMocks
    CoaRepositoryImpl coaRepository;
    @Mock
    private CoaCustomerMapper coaCustomerMapper;

    @Test
    public void queryCoaList() throws Exception {
        String processDay = "2025-08-10";
        List<ValidationRequestDO> list = coaRepository.queryCoaList(processDay);
        assertNotNull(list);
    }

    @Test
    public void queryIdListByCoa() throws Exception {
        String processDay = "2025-08-10";
        String processStatus = ProcessStatusEnum.VALIDATED.getCode();
        ValidationResultDO coa = new ValidationResultDO();
        List<Long> list = new ArrayList<>();
        list.add(1L);
        when(coaCustomerMapper.selectIdListByCoa(processDay, processStatus, coa)).thenReturn(list);
        List<Long> longs = coaRepository.queryIdListByCoa(processDay, processStatus, coa);
        assertEquals(list, longs);
    }

    @Test
    public void pagingQueryIdListByCoa() throws Exception {
        String processDay = "2025-08-10";
        String processStatus = ProcessStatusEnum.VALIDATED.getCode();
        ValidationResultDO coa = new ValidationResultDO();
        List<Long> list = new ArrayList<>();
        Integer pageNum = 1;
        Integer pageSize = 10;
        list.add(1L);
        PageInfo<Long> target = new PageInfo<>(list);
        when(coaCustomerMapper.selectIdListByCoa(processDay, processStatus, coa)).thenReturn(list);
        PageInfo<Long> source = coaRepository.pagingQueryIdListByCoa(processDay, processStatus, coa, pageNum, pageSize);
        assertEquals(source.getList(), target.getList());
    }

    @Test
    public void updateCoaStatus() throws Exception {
        String processDay = "2025-08-10";
        String processStatus = ProcessStatusEnum.VALIDATED.getCode();
        ValidationResultDO coa = new ValidationResultDO();
        List<Long> list = new ArrayList<>();
        list.add(1L);
        when(coaCustomerMapper.updateCoaStatus(processDay, coa, processStatus, "")).thenReturn(1);
        int target = coaRepository.updateCoaStatus(processDay, coa, processStatus, "");
        assertEquals(1, target);
    }

    @Test
    public void updateCoaStatusById() throws Exception {
        String processDay = "2025-08-10";
        String processStatus = ProcessStatusEnum.VALIDATED.getCode();
        ValidationResultDO coa = new ValidationResultDO();
        List<Long> list = new ArrayList<>();
        list.add(1L);
        when(coaCustomerMapper.updateCoaStatusById(processDay, "", 1L, "", "")).thenReturn(1);
        int target = coaRepository.updateCoaStatusById(processDay, "", 1L, "", "");
        assertEquals(1, target);
    }

    @Test
    public void updateCoaStatusByIdBatch() throws Exception {
        String processDay = "2025-08-10";
        String processStatus = ProcessStatusEnum.VALIDATED.getCode();
        ValidationResultDO coa = new ValidationResultDO();
        List<Long> list = new ArrayList<>();
        list.add(1L);
        int target = coaRepository.updateCoaStatusByIdBatch(processDay, coa, processStatus, "");
        assertEquals(0, target);
    }

    @Test
    public void updateSafiHeaderStatus() throws Exception {
        String processDay = "2025-08-10";
        String processStatus = ProcessStatusEnum.VALIDATED.getCode();
        ValidationResultDO coa = new ValidationResultDO();
        List<Long> list = new ArrayList<>();
        list.add(1L);
        when(coaCustomerMapper.updateSafiHeaderStatus(processDay)).thenReturn(1);
        int target = coaRepository.updateSafiHeaderStatus(processDay);
        assertEquals(1, target);
    }

    @Test
    public void updateShenmaHeaderStatus() throws Exception {
        String processDay = "2025-08-10";
        String processStatus = ProcessStatusEnum.VALIDATED.getCode();
        ValidationResultDO coa = new ValidationResultDO();
        List<Long> list = new ArrayList<>();
        list.add(1L);
        when(coaCustomerMapper.updateShenMaHeaderStatus(processDay)).thenReturn(1);
        int target = coaRepository.updateShenmaHeaderStatus(processDay);
        assertEquals(1, target);
    }

    @Test
    public void pagingSelectCoaList() throws Exception {
        String processDay = "2025-08-10";
        String processStatus = ProcessStatusEnum.VALIDATED.getCode();
        ValidationResultDO coa = new ValidationResultDO();
        List<ValidationRequestDO> list = new ArrayList<>();
        ValidationRequestDO validationRequestDO = new ValidationRequestDO();
        validationRequestDO.setLedgerName("test");
        list.add(validationRequestDO);
        PageInfo<ValidationRequestDO> target = new PageInfo<>(list);
        when(coaCustomerMapper.selectCoaList(processDay)).thenReturn(list);
        PageInfo<ValidationRequestDO> source = coaRepository.pagingSelectCoaList(processDay, 1, 10);
        assertEquals(source.getList().get(0).getLedgerName(), target.getList().get(0).getLedgerName());
    }

    @Test
    public void pagingSelectShenMaCoaList() throws Exception {
        String processDay = "2025-08-10";
        String processStatus = ProcessStatusEnum.VALIDATED.getCode();
        ValidationResultDO coa = new ValidationResultDO();
        List<ValidationRequestDO> list = new ArrayList<>();
        ValidationRequestDO validationRequestDO = new ValidationRequestDO();
        validationRequestDO.setLedgerName("test");
        list.add(validationRequestDO);
        PageInfo<ValidationRequestDO> target = new PageInfo<>(list);
        when(coaCustomerMapper.selectShenMaCoaList(processDay)).thenReturn(list);
        PageInfo<ValidationRequestDO> source = coaRepository.pagingSelectShenMaCoaList(processDay, 1, 10);
        assertEquals(source.getList().get(0).getLedgerName(), target.getList().get(0).getLedgerName());
    }

    @Test
    public void updateCommonCoaStatusByIdBatch() throws Exception {
        String processDay = "2025-08-10";
        String processStatus = ProcessStatusEnum.VALIDATED.getCode();
        ValidationResultDO coa = new ValidationResultDO();
        List<Long> list = new ArrayList<>();
        list.add(1L);
        int target = coaRepository.updateCommonCoaStatusByIdBatch(processDay, coa, processStatus, "");
        assertEquals(0, target);
    }

    @Test
    public void pagingInterfaceCommonQueryIdListByCoa() throws Exception {
        String processDay = "2025-08-10";
        String processStatus = ProcessStatusEnum.VALIDATED.getCode();
        ValidationResultDO coa = new ValidationResultDO();
        List<Long> list = new ArrayList<>();
        Integer pageNum = 1;
        Integer pageSize = 10;
        list.add(1L);
        PageInfo<Long> target = new PageInfo<>(list);
        when(coaCustomerMapper.selectInterfaceCommonIdListByCoa(processDay, processStatus, coa)).thenReturn(list);
        PageInfo<Long> source = coaRepository.pagingInterfaceCommonQueryIdListByCoa(processDay, processStatus, coa, pageNum, pageSize);
        assertEquals(source.getList(), target.getList());
    }

    @Test
    public void updateShenmaCoaStatusByIdBatch() throws Exception {
        String processDay = "2025-08-10";
        String processStatus = ProcessStatusEnum.VALIDATED.getCode();
        ValidationResultDO coa = new ValidationResultDO();
        List<Long> list = new ArrayList<>();
        list.add(1L);

        int target = coaRepository.updateShenmaCoaStatusByIdBatch(processDay, coa, processStatus, "");
        assertEquals(0, target);
    }

    @Test
    public void pagingShenmaQueryIdListByCoa() throws Exception {
        String processDay = "2025-08-10";
        String processStatus = ProcessStatusEnum.VALIDATED.getCode();
        ValidationResultDO coa = new ValidationResultDO();
        List<Long> list = new ArrayList<>();
        Integer pageNum = 1;
        Integer pageSize = 10;
        list.add(1L);
        PageInfo<Long> target = new PageInfo<>(list);
        when(coaCustomerMapper.selectShenmaIdListByCoa(processDay, processStatus, coa)).thenReturn(list);
        PageInfo<Long> source = coaRepository.pagingShenmaQueryIdListByCoa(processDay, processStatus, coa, pageNum, pageSize);
        assertEquals(source.getList(), target.getList());
    }

}
