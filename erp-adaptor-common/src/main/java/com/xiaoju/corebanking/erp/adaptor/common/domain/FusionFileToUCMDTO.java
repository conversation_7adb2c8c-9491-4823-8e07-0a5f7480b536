package com.xiaoju.corebanking.erp.adaptor.common.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Description: 类描述
 * @author: zhangcc
 * @date: 2025/5/20$
 **/
@Data
public class FusionFileToUCMDTO implements java.io.Serializable {
    @JsonProperty("OperationName")
    private String operationName;
    @JsonProperty("DocumentContent")
    private String documentContent;
    @JsonProperty("DocumentAccount")
    private String documentAccount;
    @JsonProperty("ContentType")
    private String contentType;
    @JsonProperty("FileName")
    private String fileName;
}
