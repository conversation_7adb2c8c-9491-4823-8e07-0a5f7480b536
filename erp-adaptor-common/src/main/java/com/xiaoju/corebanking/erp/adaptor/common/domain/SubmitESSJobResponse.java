package com.xiaoju.corebanking.erp.adaptor.common.domain;

import java.util.List;

/**
 * @Description: 类描述
 * @author: zhangcc
 * @date: 2025/5/26$
 **/
public class SubmitESSJobResponse implements java.io.Serializable{
    private String OperationName;

    private String DocumentId;

    private String DocumentContent;

    private String FileName;

    private String ContentType;

    private String FileType;

    private String DocumentAccount;

    private String Comments;

    private String ProcessName;

    private String LoadRequestId;

    private String JobPackageName;

    private String JobDefName;

    private String ReqstId;

    private String RequestStatus;

    private String JobName;

    private String ParameterList;

    private String NotificationCode;

    private String CallbackURL;

    private String JobOptions;

    private String StatusCode;

    private String ESSParameters;

    private List<Links> links;

    public void setOperationName(String OperationName){
        this.OperationName = OperationName;
    }
    public String getOperationName(){
        return this.OperationName;
    }
    public void setDocumentId(String DocumentId){
        this.DocumentId = DocumentId;
    }
    public String getDocumentId(){
        return this.DocumentId;
    }
    public void setDocumentContent(String DocumentContent){
        this.DocumentContent = DocumentContent;
    }
    public String getDocumentContent(){
        return this.DocumentContent;
    }
    public void setFileName(String FileName){
        this.FileName = FileName;
    }
    public String getFileName(){
        return this.FileName;
    }
    public void setContentType(String ContentType){
        this.ContentType = ContentType;
    }
    public String getContentType(){
        return this.ContentType;
    }
    public void setFileType(String FileType){
        this.FileType = FileType;
    }
    public String getFileType(){
        return this.FileType;
    }
    public void setDocumentAccount(String DocumentAccount){
        this.DocumentAccount = DocumentAccount;
    }
    public String getDocumentAccount(){
        return this.DocumentAccount;
    }
    public void setComments(String Comments){
        this.Comments = Comments;
    }
    public String getComments(){
        return this.Comments;
    }
    public void setProcessName(String ProcessName){
        this.ProcessName = ProcessName;
    }
    public String getProcessName(){
        return this.ProcessName;
    }
    public void setLoadRequestId(String LoadRequestId){
        this.LoadRequestId = LoadRequestId;
    }
    public String getLoadRequestId(){
        return this.LoadRequestId;
    }
    public void setJobPackageName(String JobPackageName){
        this.JobPackageName = JobPackageName;
    }
    public String getJobPackageName(){
        return this.JobPackageName;
    }
    public void setJobDefName(String JobDefName){
        this.JobDefName = JobDefName;
    }
    public String getJobDefName(){
        return this.JobDefName;
    }
    public void setReqstId(String ReqstId){
        this.ReqstId = ReqstId;
    }
    public String getReqstId(){
        return this.ReqstId;
    }
    public void setRequestStatus(String RequestStatus){
        this.RequestStatus = RequestStatus;
    }
    public String getRequestStatus(){
        return this.RequestStatus;
    }
    public void setJobName(String JobName){
        this.JobName = JobName;
    }
    public String getJobName(){
        return this.JobName;
    }
    public void setParameterList(String ParameterList){
        this.ParameterList = ParameterList;
    }
    public String getParameterList(){
        return this.ParameterList;
    }
    public void setNotificationCode(String NotificationCode){
        this.NotificationCode = NotificationCode;
    }
    public String getNotificationCode(){
        return this.NotificationCode;
    }
    public void setCallbackURL(String CallbackURL){
        this.CallbackURL = CallbackURL;
    }
    public String getCallbackURL(){
        return this.CallbackURL;
    }
    public void setJobOptions(String JobOptions){
        this.JobOptions = JobOptions;
    }
    public String getJobOptions(){
        return this.JobOptions;
    }
    public void setStatusCode(String StatusCode){
        this.StatusCode = StatusCode;
    }
    public String getStatusCode(){
        return this.StatusCode;
    }
    public void setESSParameters(String ESSParameters){
        this.ESSParameters = ESSParameters;
    }
    public String getESSParameters(){
        return this.ESSParameters;
    }
    public void setLinks(List<Links> links){
        this.links = links;
    }
    public List<Links> getLinks(){
        return this.links;
    }
}
