package com.xiaoju.corebanking.erp.adaptor.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 文件处理状态
 **/
@Getter
@AllArgsConstructor
public enum FusionStatusEnum {
    O("O", "打开"),
    F("F", "将来"),
    C("C", "关闭"),
    N("N", "从未打开"),
   ;

    /**
     * 编码
     */
    private final String code;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 缓存
     */
    private static final Map<String, FusionStatusEnum> CACHE_CODE = Arrays.stream(FusionStatusEnum.values()).collect(
            Collectors.toMap(FusionStatusEnum::getCode, Function.identity()));

    /**
     * 通过code获取枚举
     *
     * @param code 编码
     * @return ProcessStatusEnum
     */
    public static FusionStatusEnum getByCode(String code) {
        return CACHE_CODE.get(code);
    }

    public static boolean getCloseStatus(String code){
        List<FusionStatusEnum> list = new ArrayList<>();
        list.add(FusionStatusEnum.C);
        list.add(FusionStatusEnum.N);
        if(list.contains(getByCode(code))){
            return true;
        }
        return false;
    }

}
