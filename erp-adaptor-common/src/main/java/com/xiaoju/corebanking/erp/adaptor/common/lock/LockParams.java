package com.xiaoju.corebanking.erp.adaptor.common.lock;

import lombok.Data;

@Data
public class LockParams {

    /**
     * 锁键
     */
    private String key;

    /**
     * 锁值
     */
    private String uuid;

    /**
     * 超时时间
     */
    private int timeout;

    public LockParams(String key, String uuid, int timeout) {
        this.key = key;
        this.uuid = uuid;
        this.timeout = timeout;
    }
}
