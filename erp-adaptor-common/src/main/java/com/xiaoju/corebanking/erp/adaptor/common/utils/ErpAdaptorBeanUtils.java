package com.xiaoju.corebanking.erp.adaptor.common.utils;

import com.github.pagehelper.Page;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

public class ErpAdaptorBeanUtils {
    public static <S, T> List<T> copyList(List<? extends S> sourceList, Class<T> targetClass) {
        // 转换时带着分页信息
        if (sourceList instanceof Page) {
            Page<S> sourcePageList = (Page<S>) sourceList;
            Page pageList = new Page();
            pageList.setPageNum(sourcePageList.getPageNum());
            pageList.setPageSize(sourcePageList.getPageSize());
            pageList.setTotal(sourcePageList.getTotal());
            pageList.setPages(sourcePageList.getPages());
            // 转换内容
            List<T> targetList = new ArrayList<>();
            for (S source : sourceList) {
                T target = BeanUtils.instantiateClass(targetClass);
                BeanUtils.copyProperties(source, target);
                targetList.add(target);
            }
            pageList.addAll(targetList);
            return pageList;
        } else {
            // 仅转换内容
            List<T> targetList = new ArrayList<>();
            for (S source : sourceList) {
                T target = BeanUtils.instantiateClass(targetClass);
                BeanUtils.copyProperties(source, target);
                targetList.add(target);
            }
            return targetList;
        }

    }

    public static <S, T> List<T> copyList(List<S> sourceList, Function<S , T> converter) {
        // 转换时带着分页信息
        if (sourceList instanceof Page) {
            Page<S> sourcePageList = (Page<S>) sourceList;
            Page<T> pageList = new Page();
            pageList.setPageNum(sourcePageList.getPageNum());
            pageList.setPageSize(sourcePageList.getPageSize());
            pageList.setTotal(sourcePageList.getTotal());
            pageList.setPages(sourcePageList.getPages());
            // 转换内容
            List<T> targetList = new ArrayList<>();
            for (S source : sourceList) {
                targetList.add(converter.apply(source));
            }
            pageList.addAll(targetList);
            return pageList;
        } else {
            // 仅转换内容
            List<T> targetList = new ArrayList<>();
            for (S source : sourceList) {
                targetList.add(converter.apply(source));
            }
            return targetList;
        }
    }
}
