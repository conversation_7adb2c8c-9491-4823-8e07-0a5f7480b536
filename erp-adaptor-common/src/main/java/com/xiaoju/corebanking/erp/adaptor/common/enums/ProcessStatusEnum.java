package com.xiaoju.corebanking.erp.adaptor.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 文件处理状态
 **/
@Getter
@AllArgsConstructor
public enum ProcessStatusEnum {
    NEW("NEW", "创建数据"),
    PROCESSING("PROCESSING", "数据处理中"),
    VALIDATED("VALIDATED", "已验证"),
    VALIDATE_FAILED("VALIDATE_FAILED", "验证失败"),
    MAPPED("MAPPED", "mapping校验通过"),
    MAP_FAILED("MAP_FAILED", "mapping校验失败"),
    UPLOADED("UPLOADED", "文件上传成功"),
    UPLOAD_FAILED("UPLOAD_FAILED", "文件上传失败"),
    LOADED("LOADED", "request查询成功"),
    LOAD_FAILED("LOAD_FAILED", "request查询失败"),
    IMPORTED("IMPORTED", "调用fuction的API成功"),
    IMPORT_FAILED("IMPORT_FAILED", "调用fuction的API失败"),
    SUCCESS("S", "SUCCESS"),
    FAILED("E", "FAILED")
    ;

    /**
     * 编码
     */
    private final String code;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 缓存
     */
    private static final Map<String, ProcessStatusEnum> CACHE_CODE = Arrays.stream(ProcessStatusEnum.values()).collect(
            Collectors.toMap(ProcessStatusEnum::getCode, Function.identity()));

    /**
     * 通过code获取枚举
     *
     * @param code 编码
     * @return ProcessStatusEnum
     */
    public static ProcessStatusEnum getByCode(String code) {
        return CACHE_CODE.get(code);
    }
    /**
     * new  or mapped状态
     */
    public static List<String> getNewOrMappedFailedStatus() {
        return Arrays.asList(ProcessStatusEnum.NEW.getCode(),ProcessStatusEnum.MAP_FAILED.getCode());
    }
}
