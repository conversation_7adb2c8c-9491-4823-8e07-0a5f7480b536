package com.xiaoju.corebanking.erp.adaptor.common.utils;

import com.didichuxing.dirpc.http.HttpResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xiaoju.corebanking.erp.adaptor.common.apollo.RpcApolloConfig;
import com.xiaoju.corebanking.erp.adaptor.common.enums.RpcEnum;
import com.xiaoju.corebanking.erp.adaptor.common.exception.ErpAdaptorRpcException;
import com.xiaoju.digitalbank.common.rpc.RpcInvokeUtils;
import com.xiaoju.digitalbank.common.rpc.config.AccessConfig;
import com.xiaoju.digitalbank.common.rpc.config.HttpConfig;
import com.xiaoju.digitalbank.common.rpc.config.InvokeConfig;
import com.xiaoju.digitalbank.entity.ReturnMsg;
import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import com.xiaoju.digitalbank.errorno.MessageErrorNo;
import com.xiaoju.digitalbank.exception.BaseException;

import com.xiaoju.digitalbank.util.disf.DisfUtils;
import com.xiaoju.godson.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.util.Map;
import java.util.Objects;

@Slf4j
public class ErpRpcInvokeUtils {

    /**
     * RPC远程调用
     */
    public static <T, R> R invoke(RpcEnum rpcEnum, RpcApolloConfig rpcApolloConfig, T request,
                                  Map<String, String> customHeaderMap, TypeReference<ReturnMsg<R>> typeReference) {
        return invoke(rpcEnum, buildInvokeConfig(rpcEnum, rpcApolloConfig), request,
                customHeaderMap, typeReference);
    }

    /**
     * RPC远程调用
     */
    public static <T, R> R invoke(RpcEnum rpcEnum, InvokeConfig invokeConfig, T request,
                                  Map<String, String> customHeaderMap, TypeReference<ReturnMsg<R>> typeReference) {
        try {
            AccessConfig accessConfig = invokeConfig.getAccessConfig();
            HttpConfig httpConfig = invokeConfig.getHttpConfig();
            Map<String, String> headers = RpcInvokeUtils.generateHeaders(accessConfig);
            fillCustomHeader(headers, customHeaderMap);
            HttpResponse httpResponse = DisfUtils.post(accessConfig.getDisfName(),
                    rpcEnum.getPath(), request, httpConfig.getConnectTimeout(),
                    httpConfig.getSocketTimeout(), headers);
            // 基础校验
            if (Objects.isNull(httpResponse) || !httpResponse.isSuccessful() || httpResponse.getResult() == null) {
                throw new ErpAdaptorRpcException(CommonErrorNo.VENDOR_ERROR, "response is unsuccessful!");
            }
            // 数据解析
            ReturnMsg<R> returnMsg = JsonUtil.toObject(httpResponse.getResult(), typeReference);
            if (Objects.isNull(returnMsg)) {
                throw new ErpAdaptorRpcException(MessageErrorNo.SYSTEM_ERROR_VENDOR_ERROR, "returnMsg is NULL!");
            }
            // 结果校验
            if (!returnMsg.successful()) {
                log.error("{} returnMsg is unsuccessful", rpcEnum.getPath());
                throw new ErpAdaptorRpcException(new CommonErrorNo(returnMsg.getErrNo(), returnMsg.getErrMsg()), "returnMsg is unsuccessful!");
            }
            return returnMsg.getData();
        } catch (BaseException e) {
            log.error("{} rpc invoke has BaseException, request={}, {}", rpcEnum.getCode(), LogUtils
                    .toString(request), e.getErrorNo());
            throw new ErpAdaptorRpcException(e.getErrorNo());
        } catch (Throwable e) {
            log.error("{} rpc invoke has Throwable Exception, request={}, {}", rpcEnum.getCode(), LogUtils.toString(request), CommonErrorNo.DI_RPC_ERROR, e);
            throw new ErpAdaptorRpcException(MessageErrorNo.SYSTEM_ERROR_VENDOR_ERROR);
        }
    }

    /**
     * 构建调用配置
     */
    private static InvokeConfig buildInvokeConfig(RpcEnum rpcEnum, RpcApolloConfig rpcApolloConfig) {
        InvokeConfig invokeConfig = new InvokeConfig();
        invokeConfig.setAccessConfig(rpcApolloConfig.getAccessConfig());
        HttpConfig httpConfig = RpcHttpConfigUtils
                .getHttpConfig(rpcApolloConfig.getHttpConfig(), rpcEnum);
        invokeConfig.setHttpConfig(httpConfig);
        return invokeConfig;
    }

    /**
     * 填充自定义Header字段
     */
    private static void fillCustomHeader(Map<String, String> headerMap, Map<String, String> customHeaderMap) {
        if (MapUtils.isEmpty(customHeaderMap)) {
            return;
        }

        headerMap.putAll(customHeaderMap);
    }

}
