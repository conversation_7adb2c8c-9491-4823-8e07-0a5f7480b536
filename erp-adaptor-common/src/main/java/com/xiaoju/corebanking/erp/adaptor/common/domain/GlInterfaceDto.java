package com.xiaoju.corebanking.erp.adaptor.common.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * @Description: 类描述
 * @author: didi
 **/
@Data
public class GlInterfaceDto {
    // Required fields
    @CsvColumn(order = 1, description = "Status Code", required = true)
    private String status;
    @CsvColumn(order = 2, description = "Ledger ID", required = true)
    private Long ledgerId;
    @CsvColumn(order = 3, description = "Effective Date of Transaction", required = true)
    private String transactionDate;
    @CsvColumn(order = 4, description = "Journal Source", required = true)
    private String journalSourceName;
    @CsvColumn(order = 5, description = "Journal Category", required = true)
    private String journalCategoryName;
    @CsvColumn(order = 6, description = "Currency Code", required = true)
    private String currencyCode;
    @CsvColumn(order = 7, description = "Journal Entry Creation Date", required = true)
    private String creationDate;
    @CsvColumn(order = 8, description = "Actual Flag", required = true)
    private String actualFlag;

    // Segment fields (1-30)
    @CsvColumn(order = 9, description = "Segment1")
    private String segment1;
    @CsvColumn(order = 10, description = "Segment2")
    private String segment2;
    @CsvColumn(order = 11, description = "Segment3")
    private String segment3;
    @CsvColumn(order = 12, description = "Segment4")
    private String segment4;
    @CsvColumn(order = 13, description = "Segment5")
    private String segment5;
    @CsvColumn(order = 14, description = "Segment6")
    private String segment6;
    @CsvColumn(order = 15, description = "Segment7")
    private String segment7;
    @CsvColumn(order = 16, description = "Segment8")
    private String segment8;
    @CsvColumn(order = 17, description = "Segment9")
    private String segment9;
    @CsvColumn(order = 18, description = "Segment10")
    private String segment10;
    @CsvColumn(order = 19, description = "Segment11")
    private String segment11;
    @CsvColumn(order = 20, description = "Segment12")
    private String segment12;
    @CsvColumn(order = 21, description = "Segment13")
    private String segment13;
    @CsvColumn(order = 22, description = "Segment14")
    private String segment14;
    @CsvColumn(order = 23, description = "Segment15")
    private String segment15;
    @CsvColumn(order = 24, description = "Segment16")
    private String segment16;
    @CsvColumn(order = 25, description = "Segment17")
    private String segment17;
    @CsvColumn(order = 26, description = "Segment18")
    private String segment18;
    @CsvColumn(order = 27, description = "Segment19")
    private String segment19;
    @CsvColumn(order = 28, description = "Segment20")
    private String segment20;
    @CsvColumn(order = 29, description = "Segment21")
    private String segment21;
    @CsvColumn(order = 30, description = "Segment22")
    private String segment22;
    @CsvColumn(order = 31, description = "Segment23")
    private String segment23;
    @CsvColumn(order = 32, description = "Segment24")
    private String segment24;
    @CsvColumn(order = 33, description = "Segment25")
    private String segment25;
    @CsvColumn(order = 34, description = "Segment26")
    private String segment26;
    @CsvColumn(order = 35, description = "Segment27")
    private String segment27;
    @CsvColumn(order = 36, description = "Segment28")
    private String segment28;
    @CsvColumn(order = 37, description = "Segment29")
    private String segment29;
    @CsvColumn(order = 38, description = "Segment30")
    private String segment30;

    // Amount fields
    @CsvColumn(order = 39, description = "Entered Debit Amount")
    private BigDecimal enteredDr;
    @CsvColumn(order = 40, description = "Entered Credit Amount")
    private BigDecimal enteredCr;
    @CsvColumn(order = 41, description = "Converted Debit Amount")
    private BigDecimal accountedDr;
    @CsvColumn(order = 42, description = "Converted Credit Amount")
    private BigDecimal accountedCr;

    // Reference fields
    @CsvColumn(order = 43, description = "REFERENCE1 (Batch Name)")
    private String reference1;
    @CsvColumn(order = 44, description = "REFERENCE2 (Batch Description)")
    private String reference2;
    @CsvColumn(order = 45, description = "REFERENCE3")
    private String reference3;
    @CsvColumn(order = 46, description = "REFERENCE4 (Journal Entry Name)")
    private String reference4;
    @CsvColumn(order = 47, description = "REFERENCE5 (Journal Entry Description)")
    private String reference5;
    @CsvColumn(order = 48, description = "REFERENCE6 (Journal Entry Reference)")
    private String reference6;
    @CsvColumn(order = 49, description = "REFERENCE7 (Journal Entry Reversal flag)")
    private String reference7;
    @CsvColumn(order = 50, description = "REFERENCE8 (Journal Entry Reversal Period)")
    private String reference8;
    @CsvColumn(order = 51, description = "REFERENCE9 (Journal Reversal Method)")
    private String reference9;
    @CsvColumn(order = 52, description = "REFERENCE10 (Journal Entry Line Description)")
    private String reference10;
    @CsvColumn(order = 53, description = "Reference column 1")
    private String reference21;
    @CsvColumn(order = 54, description = "Reference column 2")
    private String reference22;
    @CsvColumn(order = 55, description = "Reference column 3")
    private String reference23;
    @CsvColumn(order = 56, description = "Reference column 4")
    private String reference24;
    @CsvColumn(order = 57, description = "Reference column 5")
    private String reference25;
    @CsvColumn(order = 58, description = "Reference column 6")
    private String reference26;
    @CsvColumn(order = 59, description = "Reference column 7")
    private String reference27;
    @CsvColumn(order = 60, description = "Reference column 8")
    private String reference28;
    @CsvColumn(order = 61, description = "Reference column 9")
    private String reference29;
    @CsvColumn(order = 62, description = "Reference column 10")
    private String reference30;

    // Additional fields
    @CsvColumn(order = 63, description = "Statistical Amount")
    private BigDecimal statAmount;
    @CsvColumn(order = 64, description = "Currency Conversion Type")
    private String currencyConversionType;
    @CsvColumn(order = 65, description = "Currency Conversion Date")
    private LocalDate currencyConversionDate;
    @CsvColumn(order = 66, description = "Currency Conversion Rate")
    private BigDecimal currencyConversionRate;
    @CsvColumn(order = 67, description = "Interface Group Identifier")
    private Long groupId;

    // Attribute fields
    @CsvColumn(order = 68, description = "Context field for Journal Entry Line DFF")
    private String attributeCategory;
    @CsvColumn(order = 69, description = "Attribute1 for Journal Entry Line DFF")
    private String attribute1;
    @CsvColumn(order = 70, description = "Attribute2 for Journal Entry Line DFF")
    private String attribute2;
    @CsvColumn(order = 71, description = "Attribute3 for Journal Entry Line DFF")
    private String attribute3;
    @CsvColumn(order = 72, description = "Attribute4 for Journal Entry Line DFF")
    private String attribute4;
    @CsvColumn(order = 73, description = "Attribute5 for Journal Entry Line DFF")
    private String attribute5;
    @CsvColumn(order = 74, description = "Attribute6 for Journal Entry Line DFF")
    private String attribute6;
    @CsvColumn(order = 75, description = "Attribute7 for Journal Entry Line DFF")
    private String attribute7;
    @CsvColumn(order = 76, description = "Attribute8 for Journal Entry Line DFF")
    private String attribute8;
    @CsvColumn(order = 77, description = "Attribute9 for Journal Entry Line DFF")
    private String attribute9;
    @CsvColumn(order = 78, description = "Attribute10 for Journal Entry Line DFF")
    private String attribute10;
    @CsvColumn(order = 79, description = "Attribute11 for Captured Information DFF")
    private String attribute11;
    @CsvColumn(order = 80, description = "Attribute12 for Captured Information DFF")
    private String attribute12;
    @CsvColumn(order = 81, description = "Attribute13 for Captured Information DFF")
    private String attribute13;
    @CsvColumn(order = 82, description = "Attribute14 for Captured Information DFF")
    private String attribute14;
    @CsvColumn(order = 83, description = "Attribute15 for Captured Information DFF")
    private String attribute15;
    @CsvColumn(order = 84, description = "Attribute16 for Captured Information DFF")
    private String attribute16;
    @CsvColumn(order = 85, description = "Attribute17 for Captured Information DFF")
    private String attribute17;
    @CsvColumn(order = 86, description = "Attribute18 for Captured Information DFF")
    private String attribute18;
    @CsvColumn(order = 87, description = "Attribute19 for Captured Information DFF")
    private String attribute19;
    @CsvColumn(order = 88, description = "Attribute20 for Captured Information DFF")
    private String attribute20;

    // Additional context and reference fields
    @CsvColumn(order = 89, description = "Context field for Captured Information DFF")
    private String attributeCategory3;
    @CsvColumn(order = 90, description = "Average Journal Flag")
    private String averageJournalFlag;
    @CsvColumn(order = 91, description = "Clearing Company")
    private String clearingCompany;
    @CsvColumn(order = 92, description = "Ledger Name")
    private String ledgerName;
    @CsvColumn(order = 93, description = "Encumbrance Type ID")
    private Long encumbranceTypeId;
    @CsvColumn(order = 94, description = "Reconciliation Reference")
    private String jgzzReconRef;
    @CsvColumn(order = 95, description = "Period Name")
    private String periodName;
    @CsvColumn(order = 96, description = "REFERENCE 18")
    private String reference18;
    @CsvColumn(order = 97, description = "REFERENCE 19")
    private String reference19;
    @CsvColumn(order = 98, description = "REFERENCE 20")
    private String reference20;

    // Attribute Date fields (1-10)
    @CsvColumn(order = 99, description = "Attribute Date 1")
    private LocalDate attributeDate1;
    @CsvColumn(order = 100, description = "Attribute Date 2")
    private LocalDate attributeDate2;
    @CsvColumn(order = 101, description = "Attribute Date 3")
    private LocalDate attributeDate3;
    @CsvColumn(order = 102, description = "Attribute Date 4")
    private LocalDate attributeDate4;
    @CsvColumn(order = 103, description = "Attribute Date 5")
    private LocalDate attributeDate5;
    @CsvColumn(order = 104, description = "Attribute Date 6")
    private LocalDate attributeDate6;
    @CsvColumn(order = 105, description = "Attribute Date 7")
    private LocalDate attributeDate7;
    @CsvColumn(order = 106, description = "Attribute Date 8")
    private LocalDate attributeDate8;
    @CsvColumn(order = 107, description = "Attribute Date 9")
    private LocalDate attributeDate9;
    @CsvColumn(order = 108, description = "Attribute Date 10")
    private LocalDate attributeDate10;

    // Attribute Number fields (1-10)
    @CsvColumn(order = 109, description = "Attribute Number 1")
    private BigDecimal attributeNumber1;
    @CsvColumn(order = 110, description = "Attribute Number 2")
    private BigDecimal attributeNumber2;
    @CsvColumn(order = 111, description = "Attribute Number 3")
    private BigDecimal attributeNumber3;
    @CsvColumn(order = 112, description = "Attribute Number 4")
    private BigDecimal attributeNumber4;
    @CsvColumn(order = 113, description = "Attribute Number 5")
    private BigDecimal attributeNumber5;
    @CsvColumn(order = 114, description = "Attribute Number 6")
    private BigDecimal attributeNumber6;
    @CsvColumn(order = 115, description = "Attribute Number 7")
    private BigDecimal attributeNumber7;
    @CsvColumn(order = 116, description = "Attribute Number 8")
    private BigDecimal attributeNumber8;
    @CsvColumn(order = 117, description = "Attribute Number 9")
    private BigDecimal attributeNumber9;
    @CsvColumn(order = 118, description = "Attribute Number 10")
    private BigDecimal attributeNumber10;

    // Global Attribute fields
    @CsvColumn(order = 119, description = "Global Attribute Category")
    private String globalAttributeCategory;
    @CsvColumn(order = 120, description = "Global Attribute 1")
    private String globalAttribute1;
    @CsvColumn(order = 121, description = "Global Attribute 2")
    private String globalAttribute2;
    @CsvColumn(order = 122, description = "Global Attribute 3")
    private String globalAttribute3;
    @CsvColumn(order = 123, description = "Global Attribute 4")
    private String globalAttribute4;
    @CsvColumn(order = 124, description = "Global Attribute 5")
    private String globalAttribute5;
    @CsvColumn(order = 125, description = "Global Attribute 6")
    private String globalAttribute6;
    @CsvColumn(order = 126, description = "Global Attribute 7")
    private String globalAttribute7;
    @CsvColumn(order = 127, description = "Global Attribute 8")
    private String globalAttribute8;
    @CsvColumn(order = 128, description = "Global Attribute 9")
    private String globalAttribute9;
    @CsvColumn(order = 129, description = "Global Attribute 10")
    private String globalAttribute10;
    @CsvColumn(order = 130, description = "Global Attribute 11")
    private String globalAttribute11;
    @CsvColumn(order = 131, description = "Global Attribute 12")
    private String globalAttribute12;
    @CsvColumn(order = 132, description = "Global Attribute 13")
    private String globalAttribute13;
    @CsvColumn(order = 133, description = "Global Attribute 14")
    private String globalAttribute14;
    @CsvColumn(order = 134, description = "Global Attribute 15")
    private String globalAttribute15;
    @CsvColumn(order = 135, description = "Global Attribute 16")
    private String globalAttribute16;
    @CsvColumn(order = 136, description = "Global Attribute 17")
    private String globalAttribute17;
    @CsvColumn(order = 137, description = "Global Attribute 18")
    private String globalAttribute18;
    @CsvColumn(order = 138, description = "Global Attribute 19")
    private String globalAttribute19;
    @CsvColumn(order = 139, description = "Global Attribute 20")
    private String globalAttribute20;

    // Global Attribute Date fields (1-5)
    @CsvColumn(order = 140, description = "Global Attribute Date 1")
    private LocalDate globalAttributeDate1;
    @CsvColumn(order = 141, description = "Global Attribute Date 2")
    private LocalDate globalAttributeDate2;
    @CsvColumn(order = 142, description = "Global Attribute Date 3")
    private LocalDate globalAttributeDate3;
    @CsvColumn(order = 143, description = "Global Attribute Date 4")
    private LocalDate globalAttributeDate4;
    @CsvColumn(order = 144, description = "Global Attribute Date 5")
    private LocalDate globalAttributeDate5;

    // Global Attribute Number fields (1-5)
    @CsvColumn(order = 145, description = "Global Attribute Number 1")
    private BigDecimal globalAttributeNumber1;
    @CsvColumn(order = 146, description = "Global Attribute Number 2")
    private BigDecimal globalAttributeNumber2;
    @CsvColumn(order = 147, description = "Global Attribute Number 3")
    private BigDecimal globalAttributeNumber3;
    @CsvColumn(order = 148, description = "Global Attribute Number 4")
    private BigDecimal globalAttributeNumber4;
    @CsvColumn(order = 149, description = "Global Attribute Number 5")
    private BigDecimal globalAttributeNumber5;
}
