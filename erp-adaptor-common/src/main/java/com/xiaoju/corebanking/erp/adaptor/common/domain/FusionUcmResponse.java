package com.xiaoju.corebanking.erp.adaptor.common.domain;

import lombok.Data;

import java.util.List;

/**
 * @Description: 类描述
 * @author: zhangcc
 * @date: 2025/5/20$
 **/

public class FusionUcmResponse implements java.io.Serializable{
    private String OperationName;

    private String DocumentId;

    private String DocumentContent;

    private String FileName;

    private String ContentType;

    private String FileType;

    private String DocumentAccount;

    private String Comments;

    private String ProcessName;

    private String LoadRequestId;

    private String JobPackageName;

    private String JobDefName;

    private String ReqstId;

    private String RequestStatus;

    private String JobName;

    private String ParameterList;

    private String NotificationCode;

    private String CallbackURL;

    private String JobOptions;

    private String StatusCode;

    private String ESSParameters;

    private List<Links> links;

    public String getOperationName() {
        return OperationName;
    }

    public void setOperationName(String operationName) {
        OperationName = operationName;
    }

    public String getDocumentId() {
        return DocumentId;
    }

    public void setDocumentId(String documentId) {
        DocumentId = documentId;
    }

    public String getDocumentContent() {
        return DocumentContent;
    }

    public void setDocumentContent(String documentContent) {
        DocumentContent = documentContent;
    }

    public String getFileName() {
        return FileName;
    }

    public void setFileName(String fileName) {
        FileName = fileName;
    }

    public String getContentType() {
        return ContentType;
    }

    public void setContentType(String contentType) {
        ContentType = contentType;
    }

    public String getFileType() {
        return FileType;
    }

    public void setFileType(String fileType) {
        FileType = fileType;
    }

    public String getDocumentAccount() {
        return DocumentAccount;
    }

    public void setDocumentAccount(String documentAccount) {
        DocumentAccount = documentAccount;
    }

    public String getComments() {
        return Comments;
    }

    public void setComments(String comments) {
        Comments = comments;
    }

    public String getProcessName() {
        return ProcessName;
    }

    public void setProcessName(String processName) {
        ProcessName = processName;
    }

    public String getLoadRequestId() {
        return LoadRequestId;
    }

    public void setLoadRequestId(String loadRequestId) {
        LoadRequestId = loadRequestId;
    }

    public String getJobPackageName() {
        return JobPackageName;
    }

    public void setJobPackageName(String jobPackageName) {
        JobPackageName = jobPackageName;
    }

    public String getJobDefName() {
        return JobDefName;
    }

    public void setJobDefName(String jobDefName) {
        JobDefName = jobDefName;
    }

    public String getReqstId() {
        return ReqstId;
    }

    public void setReqstId(String reqstId) {
        ReqstId = reqstId;
    }

    public String getRequestStatus() {
        return RequestStatus;
    }

    public void setRequestStatus(String requestStatus) {
        RequestStatus = requestStatus;
    }

    public String getJobName() {
        return JobName;
    }

    public void setJobName(String jobName) {
        JobName = jobName;
    }

    public String getParameterList() {
        return ParameterList;
    }

    public void setParameterList(String parameterList) {
        ParameterList = parameterList;
    }

    public String getNotificationCode() {
        return NotificationCode;
    }

    public void setNotificationCode(String notificationCode) {
        NotificationCode = notificationCode;
    }

    public String getCallbackURL() {
        return CallbackURL;
    }

    public void setCallbackURL(String callbackURL) {
        CallbackURL = callbackURL;
    }

    public String getJobOptions() {
        return JobOptions;
    }

    public void setJobOptions(String jobOptions) {
        JobOptions = jobOptions;
    }

    public String getStatusCode() {
        return StatusCode;
    }

    public void setStatusCode(String statusCode) {
        StatusCode = statusCode;
    }

    public String getESSParameters() {
        return ESSParameters;
    }

    public void setESSParameters(String ESSParameters) {
        this.ESSParameters = ESSParameters;
    }

    public List<Links> getLinks() {
        return links;
    }

    public void setLinks(List<Links> links) {
        this.links = links;
    }

    @Override
    public String toString() {
        return "FusionUcmResponse{" +
                "OperationName='" + OperationName + '\'' +
                ", DocumentId='" + DocumentId + '\'' +
                ", DocumentContent='" + DocumentContent + '\'' +
                ", FileName='" + FileName + '\'' +
                ", ContentType='" + ContentType + '\'' +
                ", FileType='" + FileType + '\'' +
                ", DocumentAccount='" + DocumentAccount + '\'' +
                ", Comments='" + Comments + '\'' +
                ", ProcessName='" + ProcessName + '\'' +
                ", LoadRequestId='" + LoadRequestId + '\'' +
                ", JobPackageName='" + JobPackageName + '\'' +
                ", JobDefName='" + JobDefName + '\'' +
                ", ReqstId='" + ReqstId + '\'' +
                ", RequestStatus='" + RequestStatus + '\'' +
                ", JobName='" + JobName + '\'' +
                ", ParameterList='" + ParameterList + '\'' +
                ", NotificationCode='" + NotificationCode + '\'' +
                ", CallbackURL='" + CallbackURL + '\'' +
                ", JobOptions='" + JobOptions + '\'' +
                ", StatusCode='" + StatusCode + '\'' +
                ", ESSParameters='" + ESSParameters + '\'' +
                ", links=" + links +
                '}';
    }
}
