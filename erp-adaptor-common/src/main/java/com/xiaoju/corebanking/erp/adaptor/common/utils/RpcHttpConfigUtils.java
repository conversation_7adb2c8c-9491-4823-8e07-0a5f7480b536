package com.xiaoju.corebanking.erp.adaptor.common.utils;

import com.xiaoju.corebanking.erp.adaptor.common.enums.RpcEnum;
import com.xiaoju.digitalbank.common.rpc.config.HttpConfig;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

@UtilityClass
public class RpcHttpConfigUtils {

    /**
     * 方法分割符
     */
    private static final String METHOD_SPLITTER = "#";

    /**
     * 取RPC方法的HTTP配置，按照方法->类的维度读取
     */
    public static HttpConfig getHttpConfig(Map<String, HttpConfig> httpConfigMap, RpcEnum rpcEnum) {
        if (httpConfigMap.containsKey(rpcEnum.getCode())) {
            return httpConfigMap.get(rpcEnum.getCode());
        }
        String clazz = StringUtils.substringBefore(rpcEnum.getCode(), METHOD_SPLITTER);
        return httpConfigMap.get(clazz);
    }
}
