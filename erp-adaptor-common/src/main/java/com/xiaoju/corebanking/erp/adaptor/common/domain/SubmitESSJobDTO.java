package com.xiaoju.corebanking.erp.adaptor.common.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Description: 类描述
 * @author: zhangcc
 * @date: 2025/5/26$
 **/
@Data
public class SubmitESSJobDTO implements java.io.Serializable{
    @JsonProperty("OperationName")
    private String OperationName;
    @JsonProperty("JobPackageName")
    private String JobPackageName;
    @JsonProperty("JobDefName")
    private String JobDefName;
    @JsonProperty("ESSParameters")
    private String ESSParameters;
}
