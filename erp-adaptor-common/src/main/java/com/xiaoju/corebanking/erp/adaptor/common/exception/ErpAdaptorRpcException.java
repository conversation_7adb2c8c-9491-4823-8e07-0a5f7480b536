package com.xiaoju.corebanking.erp.adaptor.common.exception;

import com.xiaoju.digitalbank.errorno.CommonResponse;

public class ErpAdaptorRpcException extends ErpAdaptorBusinessException {

    public ErpAdaptorRpcException(CommonResponse errorNo) {
        super(errorNo);
    }

    public ErpAdaptorRpcException(CommonResponse errorNo, Throwable e) {
        super(errorNo, e);
    }

    public ErpAdaptorRpcException(CommonResponse errorNo, Object data) {
        super(errorNo, data);
    }

    public ErpAdaptorRpcException(CommonResponse errorNo, String debugMsg, Throwable e) {
        super(errorNo, debugMsg, e);
    }

    public ErpAdaptorRpcException(CommonResponse errorNo, String debugMsg) {
        super(errorNo, debugMsg);
    }

    public ErpAdaptorRpcException(CommonResponse errorNo, String debugMsg, Object data) {
        super(errorNo, debugMsg, data);
    }
}
