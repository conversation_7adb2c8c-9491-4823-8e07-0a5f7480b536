package com.xiaoju.corebanking.erp.adaptor.common.apollo;

import com.xiaoju.corebanking.erp.adaptor.common.constant.ApolloConstants;
import com.xiaoju.digitalbank.common.rpc.config.AccessConfig;
import com.xiaoju.digitalbank.common.rpc.config.HttpConfig;
import com.xiaoju.godson.apollo.config.annotation.ApolloConfig;
import com.xiaoju.godson.apollo.config.annotation.ApolloField;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
@Data
@ApolloConfig(namespace = ApolloConstants.APOLLO_NAMESPACE,
        configName = ApolloConstants.MESSAGE_RPC_CONFIG, blockStart = true)
public class MessageApolloConfig implements RpcApolloConfig {

    @ApolloField(name = "access_config")
    private AccessConfig accessConfig;

    @ApolloField(name = "http_config")
    private Map<String, HttpConfig> httpConfig;


    @Override
    public Map<String, String> getErrorMapping() {
        return null;
    }
}
