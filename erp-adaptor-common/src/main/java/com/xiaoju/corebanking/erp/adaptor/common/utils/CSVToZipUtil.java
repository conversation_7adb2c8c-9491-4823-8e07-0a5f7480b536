package com.xiaoju.corebanking.erp.adaptor.common.utils;

import org.springframework.stereotype.Component;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * @Description: 类描述
 * @author: zhangcc
 * @date: 2025/5/22$
 **/
@Component
public class CSVToZipUtil {
    public  void zipFiles(String[] filesToZip, String zipFileName) {
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFileName))) {
            for (String filePath : filesToZip) {
                FileInputStream fis = new FileInputStream(filePath);
                ZipEntry zipEntry = new ZipEntry(Paths.get(filePath).getFileName().toString());
                zos.putNextEntry(zipEntry);

                byte[] bytes = new byte[2048];
                int length;
                while ((length = fis.read(bytes)) >= 0) {
                    zos.write(bytes, 0, length);
                }
                zos.closeEntry();
                fis.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
