package com.xiaoju.corebanking.erp.adaptor.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/21 16:16
 */
@Getter
@AllArgsConstructor
public enum RequestStatusEnum {
    SUCCEEDED("SUCCEEDED"),
    FAILED("ERROR"),
    WARNING("WARNING"),
    CANCELLED("CANCELLED"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 缓存
     */
    private static final Map<String, RequestStatusEnum> CACHE_CODE = Arrays.stream(RequestStatusEnum.values()).collect(
            Collectors.toMap(RequestStatusEnum::getCode, Function.identity()));

    /**
     * 通过code获取枚举
     *
     * @param code 编码
     * @return ProcessStatusEnum
     */
    public static RequestStatusEnum getByCode(String code) {
        return CACHE_CODE.get(code);
    }
}
