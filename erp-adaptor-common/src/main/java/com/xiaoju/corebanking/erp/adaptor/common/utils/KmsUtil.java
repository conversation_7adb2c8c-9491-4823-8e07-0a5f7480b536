package com.xiaoju.corebanking.erp.adaptor.common.utils;

import com.didi.kms.client.KmsClient;
import com.didi.kms.utils.CodecUtil;
import com.didi.kms.utils.JsonUtil;
import lombok.Data;
import com.google.gson.annotations.SerializedName;

public class KmsUtil {
    public static AkSk getAkSk() {
        String secretId = "************-********-cf93522619a1000";
        String secretVersion = "b2fb0c2c13a2ecf2";

        // 获取存入的凭证的值，此方法带有缓存
        String secretBinary = KmsClient.searchSecretValue(secretId, secretVersion, 1, null);

        // 对凭证值 base64 解码得到存入的原文
        String secretInfo = CodecUtil.base64Utf8Decode(secretBinary);
        return JsonUtil.parseObject(secretInfo, AkSk.class);
    }

    @Data
    public static class AkSk {
        @SerializedName("s3_ak")
        private String ak;

        @SerializedName("s3_sk")
        private String sk;
    }
}
