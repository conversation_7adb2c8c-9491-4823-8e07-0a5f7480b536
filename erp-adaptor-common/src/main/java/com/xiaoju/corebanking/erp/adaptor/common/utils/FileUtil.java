package com.xiaoju.corebanking.erp.adaptor.common.utils;

import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * @Description: 类描述
 * @author: zhangcc
 * @date: 2025/5/21$
 **/
@Slf4j
@Component
public class FileUtil {
    public  void delFile(String fileName){
        // 删除csv 文件
        Path path = Paths.get(fileName);
        try {
            boolean deleted = Files.deleteIfExists(path);
            if (deleted) {
                log.info("file deleted success,fileName={},", fileName);
            } else {
                log.info("file deleted fail,fileName={},", fileName);
            }
        } catch (IOException e) {
            log.error("file deleted fail,fileName={},errorNo={}", fileName, CommonErrorNo.FAIL,e);
        }
    }

}
