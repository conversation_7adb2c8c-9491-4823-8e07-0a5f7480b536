package com.xiaoju.corebanking.erp.adaptor.common.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Joiner;
import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import com.xiaoju.godson.common.utils.JsonUtil;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.regex.Pattern;

@Slf4j
@UtilityClass
public class CommonUtils {

    /**
     * 错误码分隔符
     */
    public static final String ERROR_SPLITTER = ":";

    /**
     * 字符串通用分割符
     */
    public static final String COMMON_SPLITTER = "_";

    /**
     * 数字2
     */
    public static final Integer NUMBER_TWO = 2;

    /**
     * 邮件校验正则表达式
     */
    public static final Pattern EMAIL_PATTERN = Pattern.compile("^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*\\.[a-zA-Z0-9]{2,6}$");

    /**
     * 异常吞没运行
     */
    public static void runQuietly(Runnable runnable) {
        try {
            runnable.run();
        } catch (Exception e) {
            log.error("runQuietly exception occurred", e);
        }
    }

    /**
     * 异常吞没运行
     */
    public static void runQuietly(Runnable runnable, CommonErrorNo errorNo) {
        try {
            runnable.run();
        } catch (Exception e) {
            log.error("runQuietly exception occurred, {}", errorNo, e);
        }
    }

    /**
     * 异常吞没运行
     */
    public static <T> T callQuietly(Callable<T> callable) {
        try {
            return callable.call();
        } catch (Exception e) {
            log.error("callQuietly exception occurred", e);
            return null;
        }
    }

    /**
     * 字符串转整型，异常吞没
     */
    public static Integer parseInt(String input) {
        if (StringUtils.isBlank(input)) {
            return null;
        }
        return callQuietly(() -> Integer.parseInt(input));
    }

    /**
     * 忽略NULL值进行拷贝
     */
    public static  <T> void copyIgnoreNull(Supplier<T> getter, Consumer<T> setter) {
        T value = getter.get();
        if (value != null) {
            setter.accept(value);
        }
    }

    /**
     * string类型转换为map
     */
    public static Map<String, String> stringToMap(String stringMap) {
        if (StringUtils.isBlank(stringMap)) {
            return new HashMap<>();
        }
        return JsonUtil.toObject(stringMap, new TypeReference<Map<String, String>>() {});
    }

    /**
     * map类型转换为string
     */
    public static String mapToString(Map<String, String> map) {
        if (MapUtils.isEmpty(map)) {
            return null;
        }
        return JsonUtil.toString(map);
    }

    /**
     * 融合两个map
     */
    public static Map<String, String> mergeMap(Map<String, String> map1, Map<String, String> map2) {
        Map<String, String> mergedMap = new HashMap<>();
        if (map1 != null) {
            mergedMap.putAll(map1);
        }
        if (map2 != null) {
            mergedMap.putAll(map2);
        }
        return mergedMap;
    }

    /**
     * 拼接错误码
     */
    public static String joinError(String errCode, String errorMsg) {
        return Joiner.on(ERROR_SPLITTER).skipNulls().join(errCode, errorMsg);
    }

    /**
     * 分割错误码
     */
    public static Pair<String, String> splitError(String error) {
        String[] parts = StringUtils.split(error, ERROR_SPLITTER);
        if (ArrayUtils.isEmpty(parts) || parts.length != NUMBER_TWO) {
            return null;
        }
        return Pair.of(parts[0], parts[1]);
    }


    /**
     * 判空获取
     */
    public static <T, R> R getValue( T obj, Function<T, R> getter) {
        if (obj == null) {
            return null;
        }
        return getter.apply(obj);
    }

    /**
     * 通用字符串拼接
     */
    public static String concat(String... parts) {
        return Joiner.on(COMMON_SPLITTER).skipNulls().join(parts);
    }

    public static <T> List<List<T>> splitList(List<T> list, int size) {
        List<List<T>> result = new ArrayList<>();
        if (list == null || list.isEmpty() || size <= 0) {
            return result;
        }
        int index = 0;
        while (index < list.size()) {
            int toIndex = Math.min(index + size, list.size());
            result.add(new ArrayList<>(list.subList(index, toIndex)));
            index = toIndex;
        }
        return result;
    }

}
