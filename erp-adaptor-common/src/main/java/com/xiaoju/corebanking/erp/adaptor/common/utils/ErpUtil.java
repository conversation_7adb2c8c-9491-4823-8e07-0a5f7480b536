package com.xiaoju.corebanking.erp.adaptor.common.utils;

import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2025/6/4 15:19
 */
@Slf4j
@Component
public class ErpUtil {

    // 尝试从路径中提取日期（格式为YYYYMMDD）
    Pattern pattern = Pattern.compile("(\\d{8})(?:/|$)");
    public String getDateDirFromInput(String input) {
        //nolint
        SimpleDateFormat sdf = new SimpleDateFormat(CommonConstant.YYYYMMDD);
        String today = sdf.format(new Date());

        if (input == null || input.isEmpty()) {
            log.info("未提供输入，使用当天日期: {}", today);
            return today;
        }

        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            String extractedDate = matcher.group(1);
            log.info("从路径中提取到日期: {}", extractedDate);
            return extractedDate;
        }

        log.info("无法从路径中提取日期，使用当天日期: {}", today);
        return today;
    }


    /**
     * 构建完整的文件路径
     */
    public String buildFullPath(String filePath, String dateDir) {
        String normalizedPath;
        try {
            Path basePath = Paths.get(filePath);
            Path dateDirPath = basePath.resolve(dateDir);
            normalizedPath = dateDirPath + File.separator;
        } catch (Exception e) {
            log.error("构建路径时发生错误: {}", e.getMessage());
            String base = filePath;
            if (!base.endsWith(File.separator)) {
                base += File.separator;
            }
            normalizedPath = base + dateDir + File.separator;
        }
        log.info("构建完整文件路径: {}", normalizedPath);
        return normalizedPath;
    }
}
