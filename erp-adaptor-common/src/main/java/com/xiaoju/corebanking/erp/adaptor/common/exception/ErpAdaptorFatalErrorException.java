package com.xiaoju.corebanking.erp.adaptor.common.exception;

import com.xiaoju.digitalbank.errorno.CommonResponse;

public class ErpAdaptorFatalErrorException extends ErpAdaptorBusinessException {

    public ErpAdaptorFatalErrorException(CommonResponse errorNo) {
        super(errorNo);
    }

    public ErpAdaptorFatalErrorException(CommonResponse errorNo, Throwable e) {
        super(errorNo, e);
    }

    public ErpAdaptorFatalErrorException(CommonResponse errorNo, Object data) {
        super(errorNo, data);
    }

    public ErpAdaptorFatalErrorException(CommonResponse errorNo, String debugMsg, Throwable e) {
        super(errorNo, debugMsg, e);
    }

    public ErpAdaptorFatalErrorException(CommonResponse errorNo, String debugMsg) {
        super(errorNo, debugMsg);
    }

    public ErpAdaptorFatalErrorException(CommonResponse errorNo, String debugMsg, Object data) {
        super(errorNo, debugMsg, data);
    }
}
