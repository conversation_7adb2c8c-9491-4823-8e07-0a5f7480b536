package com.xiaoju.corebanking.erp.adaptor.common.apollo;

import com.xiaoju.corebanking.erp.adaptor.common.constant.ApolloConstants;
import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.common.utils.EfKmsUtil;
import com.xiaoju.corebanking.erp.adaptor.common.utils.JsonUtil;
import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import com.xiaoju.digitalbank.exception.ErrorNoException;
import com.xiaoju.godson.apollo.config.annotation.ApolloConfig;
import com.xiaoju.godson.apollo.config.annotation.ApolloField;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * s3配置
 */
@Slf4j
@Data
@ApolloConfig(namespace = ApolloConstants.APOLLO_NAMESPACE,
        configName = ApolloConstants.AWS_CONFIG, blockStart = true)
public class AwsS3Config {

    @Resource
    private EfKmsUtil efKmsUtil;

    @ApolloField(name = "endpoint")
    private String endpoint;

    @ApolloField(name = "bucket_name")
    private String bucketName;

    @ApolloField(name = "s3_aksk")
    private S3aksk s3aksk;

    public String getS3akskSecretId() {
        Optional.ofNullable(s3aksk).orElseThrow(() -> new ErrorNoException(CommonErrorNo.NOT_FOUNDED, CommonConstant.S3AKSK_ILL));
        return s3aksk.getSecretId();
    }

    public String getS3akskSecretVersion() {
        Optional.ofNullable(s3aksk).orElseThrow(() -> new ErrorNoException(CommonErrorNo.NOT_FOUNDED, CommonConstant.S3AKSK_ILL));
        String secretVersion = StringUtils.isNoneBlank(s3aksk.getSecretVersion()) ? s3aksk.getSecretVersion() : StringUtils.EMPTY;
        return secretVersion;
    }

    public String getEndpoint() {
        Optional.ofNullable(endpoint).orElseThrow(() -> new ErrorNoException(CommonErrorNo.NOT_FOUNDED, "endpoint no config"));
        return endpoint;
    }

    public String getBucketName() {
        Optional.ofNullable(bucketName).orElseThrow(() -> new ErrorNoException(CommonErrorNo.NOT_FOUNDED, "bucketName no config"));
        return bucketName;
    }


    public String getAccessKey() {
        return getFromCache("accessKey");
    }

    public String getSecretKey() {
        return getFromCache("secretKey");
    }

    private final ConcurrentMap<String, String> S3_CACHE = new ConcurrentHashMap<>();

    /**
     * 获取值
     *
     * @param key
     * @return
     */
    public String getFromCache(String key) {
        if (S3_CACHE.isEmpty()
                || !StringUtils.equals(getS3akskSecretId(), S3_CACHE.get(CommonConstant.SECRET_ID))
                || !StringUtils.equals(getS3akskSecretVersion(), S3_CACHE.get(CommonConstant.SECRET_VERSION))) {
            loadData();
        }

        // 尝试从缓存中获取数据
        String value = S3_CACHE.get(key);
        if (StringUtils.isBlank(value)) {
            // 缓存中没有 重新加载数据
            loadData();
            // 将数据放入缓存中
            String existingValue = S3_CACHE.putIfAbsent(key, value);
            // 如果已经有其他插入了值，则使用已存在的值
            return existingValue == null ? value : existingValue;
        }
        return value;
    }

    /**
     * 数据加载
     *
     * @return
     */
    private void loadData() {
        S3_CACHE.clear();
        String secretInfoStr = efKmsUtil.geTrusteeshipInfo(getS3akskSecretId(), getS3akskSecretVersion());
        Map<String, String> objectMap = JsonUtil.decode(secretInfoStr);
        S3_CACHE.putAll(objectMap);
        S3_CACHE.put(CommonConstant.SECRET_ID, getS3akskSecretId());
        S3_CACHE.put(CommonConstant.SECRET_VERSION, getS3akskSecretVersion());
    }

    @Data
    public static class S3aksk {

        // 密钥凭证值
        private String secretId;

        // 密钥凭证版本号
        private String secretVersion;

        // 预留
        private String arn;
    }

}
