package com.xiaoju.corebanking.erp.adaptor.common.utils;

import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import com.xiaoju.digitalbank.exception.ErrorNoException;

import java.util.*;

/**
 * @Author: DAWANGJIAN
 * @Date: 2024-06-19
 * @Description:敏感数据匹配工具
 */

public class SensitiveMatchUtil {
    private static final Map<String, List<String>> CONFIG_MAP;

    static {
        CONFIG_MAP = new HashMap<>();
    }

    public static Map<String, List<String>> getConfigMap() {
        return CONFIG_MAP;
    }

    /**
     * 获取所有keys
     *
     * @return
     */
    public static Set<String> keys() {
        Set<String> keys = getConfigMap().keySet();
        return keys;
    }

    /**
     * 判断key是否存在
     *
     * @param key
     * @return
     */
    public static boolean containsKey(String key) {
        return getConfigMap().containsKey(key);
    }

    /**
     * 根据key获取value
     *
     * @param key
     * @return
     */
    public static List<String> getFields(String key) {
        List<String> stringList = getConfigMap().get(key);
        Optional.ofNullable(stringList).orElseThrow(() -> new ErrorNoException(CommonErrorNo.NOT_FOUNDED, key + " class not config"));
        return stringList;
    }

    /**
     * 判断field是否存在
     *
     * @param key
     * @return
     */
    public static boolean containsField(String key, String field) {
        List<String> fieldList = getFields(key);
        return fieldList.contains(field);
    }

}