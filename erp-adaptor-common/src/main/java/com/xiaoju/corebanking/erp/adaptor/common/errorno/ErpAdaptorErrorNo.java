package com.xiaoju.corebanking.erp.adaptor.common.errorno;

import com.xiaoju.digitalbank.errorno.CommonErrorNo;

/**
 * @Description: 类描述
 * @author: zhangcc
 * @date: 2024/11/11$
 **/
public class ErpAdaptorErrorNo {
    public static final CommonErrorNo SYSTEM_ERROR_INIT;
    public static final CommonErrorNo SYSTEM_ERROR_DEFAULT_SUCCESS;
    public static final CommonErrorNo SYSTEM_ERROR_DEFAULT_FAIL;
    public static final CommonErrorNo SYSTEM_ERROR_PARAM_ERROR;
    public static final CommonErrorNo SYSTEM_ERROR_NOT_FOUND;
    public static final CommonErrorNo SYSTEM_ERROR_SERVER_ERROR;
    public static final CommonErrorNo MESSAGE_FAILED_ERROR;
    public static final CommonErrorNo MESSAGE_NOTIFY_ERROR;


    public static final CommonErrorNo IMP_ERR_ERROR;

    public static final CommonErrorNo MQ_FAILED_ERROR;
    public static final CommonErrorNo COA_ERROR;


    public ErpAdaptorErrorNo() {
    }

    static {
        SYSTEM_ERROR_INIT = CommonErrorNo.INIT;
        SYSTEM_ERROR_DEFAULT_SUCCESS = CommonErrorNo.SUCCESS;
        SYSTEM_ERROR_DEFAULT_FAIL = CommonErrorNo.FAIL;
        SYSTEM_ERROR_PARAM_ERROR = CommonErrorNo.PARAM_ERROR;
        SYSTEM_ERROR_NOT_FOUND = CommonErrorNo.NOT_FOUNDED;
        SYSTEM_ERROR_SERVER_ERROR = CommonErrorNo.SERVER_ERROR;
        MESSAGE_FAILED_ERROR = new CommonErrorNo(203101001, "message send failed");
        MQ_FAILED_ERROR = new CommonErrorNo(203101002, "mq notify failed");
        IMP_ERR_ERROR = new CommonErrorNo(203101003, "IMP_ERR");
        MESSAGE_NOTIFY_ERROR = new CommonErrorNo(300500017, "message notify error");
        COA_ERROR = new CommonErrorNo(8,"revoke coa error");
    }
}
