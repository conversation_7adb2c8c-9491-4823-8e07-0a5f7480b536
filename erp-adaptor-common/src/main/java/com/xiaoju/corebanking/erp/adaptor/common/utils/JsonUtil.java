package com.xiaoju.corebanking.erp.adaptor.common.utils;

import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import com.xiaoju.digitalbank.exception.ErrorNoException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * json工具类
 */
@Slf4j
public class JsonUtil {

    private final static ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    // 初始化配置
    static {
        OBJECT_MAPPER.configure(JsonParser.Feature.ALLOW_COMMENTS, true);
        OBJECT_MAPPER.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        OBJECT_MAPPER.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        OBJECT_MAPPER.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        OBJECT_MAPPER.getFactory().configure(JsonFactory.Feature.INTERN_FIELD_NAMES, true);
        OBJECT_MAPPER.getFactory().configure(JsonFactory.Feature.CANONICALIZE_FIELD_NAMES, true);
    }


    /**
     * 将对象序列化成json string
     *
     * @param obj
     * @return
     */
    public static String encode(Object obj) {
        try {
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (Exception e) {
            log.warn("", e);
            throw new ErrorNoException(CommonErrorNo.PARAM_ERROR, String.format("error encoding json, obj=%s", obj));
        }
    }

    /**
     * @param jsonString
     * @param <K>
     * @param <V>
     * @return
     */
    public static <K, V> Map<K, V> decode(String jsonString) {
        try {
            return StringUtils.isEmpty(jsonString) ? Maps.newHashMap() : decode(jsonString, HashMap.class);
        } catch (Exception e) {
            log.info("", e);
            return Maps.newHashMap();
        }
    }

    /**
     * 将json string反序列化成对象
     *
     * @param json
     * @param valueType
     * @return
     */
    public static <T> T decode(String json, Class<T> valueType) {
        try {
            return OBJECT_MAPPER.readValue(json, valueType);
        } catch (Exception e) {
            log.error("", e);
            throw new ErrorNoException(CommonErrorNo.PARAM_ERROR, "解析失败!");
        }
    }

    /**
     * 将json array反序列化为对象
     *
     * @param json
     * @param typeReference
     * @return
     */
    public static <T> T decode(String json, TypeReference<T> typeReference) {
        try {
            return OBJECT_MAPPER.readValue(json, typeReference);
        } catch (Exception e) {
            log.error("", e);
            throw new ErrorNoException(CommonErrorNo.PARAM_ERROR, "解析失败!");
        }
    }

    public static String extract(Object strObj, String obj) {
        if (null == strObj || StringUtils.isEmpty(obj)) {
            return null;
        }
        try {
            HashMap<String, String> jsonMap = (HashMap<String, String>) strObj;
            return jsonMap.get(obj);
        } catch (Exception e) {
            log.warn("", e);
            throw new ErrorNoException(CommonErrorNo.PARAM_ERROR, String.format("error extract json, obj=%s", strObj));
        }
    }
}
