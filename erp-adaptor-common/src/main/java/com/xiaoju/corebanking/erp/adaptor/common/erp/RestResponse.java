package com.xiaoju.corebanking.erp.adaptor.common.erp;

import lombok.Data;

@Data
public class RestResponse<T> {

    public static final String REST_OK = "ok";

    public static final String REST_FAIL = "fail";

    public static final String NEED_LOGIN = "need-login";

    public static final String NO_AUTH = "no-auth";

    private String status;

    private String message;

    private T data;

    public static <T> RestResponse<T> ok() {
        RestResponse<T> response = new RestResponse<>();
        response.setStatus(REST_OK);
        return response;
    }

    public static <T> RestResponse<T> okWithData(T data) {
        RestResponse<T> response = new RestResponse<>();
        response.setStatus(REST_OK);
        response.setData(data);
        return response;
    }

    public static <T> RestResponse<T> okWithMsg(String message) {
        RestResponse<T> response = new RestResponse<>();
        response.setStatus(REST_OK);
        response.setMessage(message);
        return response;
    }

    public static <T> RestResponse<T> fail() {
        RestResponse<T> response = new RestResponse<>();
        response.setStatus(REST_FAIL);
        return response;
    }

    public static <T> RestResponse<T> failWithData(T data) {
        RestResponse<T> response = new RestResponse<>();
        response.setStatus(REST_FAIL);
        response.setData(data);
        return response;
    }

    public static <T> RestResponse<T> failWithMsg(String message) {
        RestResponse<T> response = new RestResponse<>();
        response.setStatus(REST_FAIL);
        response.setMessage(message);
        return response;
    }

    public static <T> RestResponse<T> failWithDataMsg(T data, String message) {
        RestResponse<T> response = new RestResponse<>();
        response.setStatus(REST_FAIL);
        response.setData(data);
        response.setMessage(message);
        return response;
    }

    public static <T> RestResponse<T> needLogin() {
        RestResponse<T> response = new RestResponse<>();
        response.setStatus(NEED_LOGIN);
        return response;
    }

    public static <T> RestResponse<T> needLogin(String message) {
        RestResponse<T> response = new RestResponse<>();
        response.setStatus(NEED_LOGIN);
        response.setMessage(message);
        return response;
    }

    public static <T> RestResponse<T> noAuth() {
        RestResponse<T> response = new RestResponse<>();
        response.setStatus(NO_AUTH);
        return response;
    }

    public static <T> RestResponse<T> noAuth(String message) {
        RestResponse<T> response = new RestResponse<>();
        response.setStatus(NO_AUTH);
        response.setMessage(message);
        return response;
    }
}
