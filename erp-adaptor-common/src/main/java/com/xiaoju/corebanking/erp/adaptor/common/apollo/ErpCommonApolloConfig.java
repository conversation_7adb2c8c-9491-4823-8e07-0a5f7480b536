package com.xiaoju.corebanking.erp.adaptor.common.apollo;

import com.xiaoju.corebanking.erp.adaptor.common.constant.ApolloConstants;
import com.xiaoju.digitalbank.common.rpc.config.AccessConfig;
import com.xiaoju.digitalbank.common.rpc.config.HttpConfig;
import com.xiaoju.godson.apollo.config.annotation.ApolloConfig;
import com.xiaoju.godson.apollo.config.annotation.ApolloField;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
@Data
@ApolloConfig(namespace = ApolloConstants.APOLLO_NAMESPACE,
        configName = ApolloConstants.ERP_COMMON_CONFIG, blockStart = true)
public class ErpCommonApolloConfig {

    @ApolloField(name = "templateId")
    private String templateId;

    @ApolloField(name = "receiver")
    private String receiver;
}
