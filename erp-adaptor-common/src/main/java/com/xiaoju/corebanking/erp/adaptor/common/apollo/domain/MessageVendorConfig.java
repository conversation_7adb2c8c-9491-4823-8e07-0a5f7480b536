package com.xiaoju.corebanking.erp.adaptor.common.apollo.domain;

import com.xiaoju.digitalbank.enums.message.MessageVendorType;
import com.xiaoju.digitalbank.enums.message.PerceptionType;
import com.xiaoju.digitalbank.enums.message.TemplateType;
import lombok.Data;

import java.util.List;

@Data
public class MessageVendorConfig {

    private MessageVendorType vendorType;

    private int weight;

    private List<TemplateType> templateTypes;

    private List<PerceptionType> perceptionTypes;
}
