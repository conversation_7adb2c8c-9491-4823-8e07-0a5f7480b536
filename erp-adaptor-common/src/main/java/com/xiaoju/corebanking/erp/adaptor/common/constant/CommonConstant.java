package com.xiaoju.corebanking.erp.adaptor.common.constant;

/**
 * 通用常量类
 */
public interface CommonConstant {
    String SECRET_ID = "secretId";

    String SECRET_VERSION = "secretVersion";

    String S3AKSK_ILL = "s3aksk illegal";

    String YYYYMMDD = "yyyyMMdd";

    String REGEX = "\\d{8}";

    String CSV_EXTENSION = ".csv";
    String MD5_EXTENSION = ".checksum";
    int BATCH_SIZE = 100;
    int GROUP_SIZE = 500;
    String ZIP_EXTENSION = ".zip";
    // fusion 相关
    String CURRENCY_MAPPINGS = "CURRENCY_MAPPINGS";
    String COMPANY_LEDGER_MAPPINGS = "COMPANY_LEDGER_MAPPINGS";
    String PRODUCT_MAPPINGS = "PRODUCT_MAPPINGS";
    String CATEGORY_MAPPINGS = "CATEGORY_MAPPINGS";
    String GL_JOURNAL_SOURCE_MAPPINGS = "GL_JOURNAL_SOURCE_MAPPINGS";
    String PROFIT_CENTER_MAPPINGS = "PROFIT_CENTER_MAPPINGS";
    String INTERCOMPANY_MAPPINGS = "INTERCOMPANY_MAPPINGS";

    String VOUCHER_GROUP_MAPPINGS = "VOUCHER_GROUP_MAPPINGS";

    String GL_CODE_MAPPINGS = "GL_CODE_MAPPINGS";

    String CARGOS_NOT_ABONOS = "CARGOS_NOT_ABONOS";

    String  CONTENTTYPE= "zip";
    String SAFI_JOURNAL_SOURCE ="Sofipo SAFI";
    String ZERO="0";
    String SEGMENT_5 ="1913";
    String INVALID = "Invalid";
    String Q ="?q=";
    String APPLICATION = "ApplicationId=101;";
    String LEDGERID = "LedgerId=";
    String NEW = "NEW";
    String ACTUAL_FLAG="A";
    String STATUSAPI="/fscmRestApi/resources/***********/accountingPeriodStatusLOV";
    String UPLOADFILETOUCMAPI="/fscmRestApi/resources/***********/erpintegrations";
    String UPLOADFILETOUCM="uploadFileToUCM";
    String DOCUMENTACCOUNT="fin$/generalLedger$/import$";
    String OPERATIONNAME_SUBMITESSJOBREQUEST="submitESSJobRequest";
    String JOBPACKAGENAME="oracle/apps/ess/financials/commonModules/shared/common/interfaceLoader";
    String JOBDEFNAME="InterfaceLoaderController";
    String N = "N";
    Integer C_LENGTH=25;

    Integer MAX_LENGTH=240;
    Long ZERO_NUM=0L;
    String CORE_BANKING = "CORE_BANKING";
    String ATTRIBUTE7="********";
    String SHEN_MA_JOURNAL_SOURCE ="Sofipo SHENMA";
    String SYSTEM_USER = "system";

    String SAFI_GROUP_ID = "SAFI_GROUP_ID";

    String INLAND_OFFSHORE = "INLAND_OFFSHORE";


    String FORM_CODE = "VOUCHER_GROUP_MAPPING";
    String ENABLED_FLAG = "Y";
    String LANG = "US";

    String VOUCHER_GROUP_CSV = "sofi_voucher_group.csv";
    String VOUCHER_GROUP_CHECK_CSV = "sofi_voucher_group.checksum";

    String SOFI_GL_ACCT_HIST_CSV = "sofi_gl_acct_hist.csv";
    String SOFI_GL_ACCT_HIST_CHECK_CSV = "sofi_gl_acct_hist.checksum";

    String SOFI_GL_SUBJECT_CSV = "sofi_gl_subject.csv";
    String SOFI_GL_SUBJECT_CHECK_CSV = "sofi_gl_subject.checksum";

    String FILE_CHECK_CSV = "sofi_journal_check.csv";
    String FILE_CHECK_MD5 = "sofi_journal_check.checksum";

    String FILE_CHECK_SHENMA_CSV = "sofi_journal_shenma.csv";
    String FILE_CHECK_SHENMA_MD5 = "sofi_journal_shenma.checksum";
    String AMAZON_FILE = "http://s3.amazon.com/file/11";

    int REQUIRED_COLUMN_COUNT = 5;
    int CSV0 = 0;
    int CSV1 = 1;
    int CSV2 = 2;
    int CSV3 = 3;
    int CSV4 = 4;

    int PAGE_SIZE = 1000;

    String SHENMA_SEGMENT2= "52403000000";



}