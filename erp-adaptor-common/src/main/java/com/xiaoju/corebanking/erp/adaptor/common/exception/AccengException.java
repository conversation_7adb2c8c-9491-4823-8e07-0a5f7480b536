package com.xiaoju.corebanking.erp.adaptor.common.exception;


import com.xiaoju.corebanking.erp.adaptor.common.enums.AccengCodeEnum;
import lombok.Getter;

import java.util.Arrays;


/*
 * @author: BaserWei
 * @since: 2022/09/16
 *
 * 注意：Controller 中返回为 AccengResult 时方可使用，否则影响http返回结构，详见 AccengExceptionHandler
 */

public class AccengException extends RuntimeException {

    @Getter
    protected String code;
    @Getter
    protected String desc;
    private transient Object[] parameters;

    public AccengException() {
        this(AccengCodeEnum.BUSINESS_ERROR.getCode(), AccengCodeEnum.BUSINESS_ERROR.getDesc(), "");
    }

    public AccengException(String message) {
        this(AccengCodeEnum.BUSINESS_ERROR.getCode(), message, AccengCodeEnum.BUSINESS_ERROR.getDesc());
    }

    public AccengException(String code, String message) {
        this(code, message, "");
    }

    public AccengException(String code, String message, String desc, Object... parameters) {
        super(message);
        this.code = code;
        this.desc = desc;
        this.parameters = parameters;
    }

    public AccengException(Throwable cause) {
        super(cause.getMessage(), cause);
        this.code = AccengCodeEnum.SYSTEM_ERROR.getCode();
        this.desc = AccengCodeEnum.SYSTEM_ERROR.getDesc();
    }

    @Override
    public String toString() {
        return "AccengException{"
                + "message=" + this.getMessage()
                + ", code=" + this.code
                + ", desc='" + this.desc
                + ", parameters=" + Arrays.toString(this.parameters)
                + "}";
    }

}
