package com.xiaoju.corebanking.erp.adaptor.common.utils;

import com.didi.kms.client.KmsClient;
import com.didi.kms.utils.CodecUtil;
import com.xiaoju.compliance.CipherUtil;
import com.xiaoju.corebanking.erp.adaptor.common.apollo.EfKmsCommonConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;

@Slf4j
@Component
public class EfKmsUtil {

    // 已加密标签前缀
    public static final String SENSITIVE_PARAM_TAG = "SENS_";

    public static final String SENSITIVE_PARAM_TAG_S = "S_";

    /**
     * aes加密
     *
     * @param plainData 原字段明文值
     * @return
     */
    public static String encrypt(String plainData) {
        if (StringUtils.isBlank(plainData)) {
            return null;
        }

        try {
            //创建加密器
            CipherUtil.KMSCipher kmsCipher = CipherUtil.newKMSCipher(EfKmsCommonConfig.getKmsAccessKey(),EfKmsCommonConfig.getKmsSecretKey());
            // 加密
            String cipherText = kmsCipher.aesEncrypt(EfKmsCommonConfig.getSensitiveSecretId(), EfKmsCommonConfig.getSensitiveSecretVersion(), plainData);
            return cipherText;
        } catch (Exception ex) {
            log.error("kms encrypt error: ",ex);
        }

        return plainData;
    }

    /**
     * aes解密
     *
     * @param encryptData 待解密的密文
     * @return
     */
    public static String decrypt(String encryptData) {
        if (StringUtils.isBlank(encryptData)) {
            return null;
        }
        try {
            // 创建 KMSCipher
            CipherUtil.KMSCipher kmsCipher = CipherUtil.newKMSCipher(EfKmsCommonConfig.getKmsAccessKey(),EfKmsCommonConfig.getKmsSecretKey());
            // 解密
            String plainTextStr = kmsCipher.aesDecrypt(encryptData);
            return plainTextStr;
        } catch (Exception ex) {
            log.error("kms decrypt error:", ex);
        }

        // 解密失败，旧值未加密过返回旧值
        return encryptData;
    }


    /**
     * 获取拖管明文
     *
     * @param secretId
     * @return
     */
    public String geTrusteeshipInfo(String secretId, String secretVersion) {
        // 请求 KMS 获取自己存入的机密信息
        // 如果版本号为null或者空，传""
        String version = StringUtils.isNotBlank(secretVersion) ? secretVersion : StringUtils.EMPTY;
        String remoteBase64 = KmsClient.searchSecretValue(secretId, version, 1, null);
        String secretInfo = CodecUtil.base64Utf8Decode(remoteBase64);
        return secretInfo;
    }


    /**
     * 解密
     *
     * @param paramsObject
     * @param <T>
     * @return
     * @throws IllegalAccessException
     */
    public <T> T paramEncrypt(T paramsObject) {

        Class<?> parameterObjectClass = paramsObject.getClass();
        String className = parameterObjectClass.getName();
        Field[] declaredFields = parameterObjectClass.getDeclaredFields();
        for (Field field : declaredFields) {
            // 匹配加解密字段
            boolean bool = SensitiveMatchUtil.containsField(className, field.getName());
            if (bool) {
                field.setAccessible(true);
                Object object = null;
                try {
                    object = field.get(paramsObject);
                } catch (IllegalAccessException e) {
                    log.error("encrypt1 fail ", e);
                }
                // 暂时只实现String类型的加密
                if (object instanceof String) {
                    String value = (String) object;
                    String encrypt = value;
                    if (StringUtils.isEmpty(encrypt)){continue;}
                    // 开始对字段加密使用kms的AES加密工具
                    try {
                        if (!value.startsWith(SENSITIVE_PARAM_TAG_S)) {
                            encrypt = encrypt(value);
                            encrypt = SENSITIVE_PARAM_TAG_S + encrypt;
                        }
                        // 修改加密后的字段值
                        field.set(paramsObject, encrypt);
                    } catch (Exception e) {
                        log.error("encrypt2 fail ", e);
                    }
                }
            }
        }
        return paramsObject;
    }

    /**
     * 解密
     *
     * @param result resultType的实例
     * @param <T>
     * @return
     * @throws IllegalAccessException
     */
    public <T> T paramDecrypt(T result) {
        // 取出resultType的类
        Class<?> resultClass = result.getClass();
        Field[] declaredFields = resultClass.getDeclaredFields();
        for (Field field : declaredFields) {
            // 匹配是否需要加解密字段
            boolean bool = SensitiveMatchUtil.containsField(resultClass.getName(), field.getName());
            if (bool) {
                field.setAccessible(true);
                Object object = null;
                try {
                    object = field.get(result);
                } catch (IllegalAccessException e) {
                    log.error("decrypt first fail ", e);
                }
                // 对String类型字段解密
                if (object instanceof String) {
                    String value = (String) object;
                    // 对注解的字段进行逐一解密
                    try {
                        //修改：没有标识则不解密(防止重复解密)
                        if (value.startsWith(SENSITIVE_PARAM_TAG)) {
                            // "SENS_" 已加密字符串 老的标志后期删除
                            value = value.substring(SENSITIVE_PARAM_TAG.length());
                            value = decrypt(value);
                        } else if (value.startsWith(SENSITIVE_PARAM_TAG_S)) {
                            // "S_" 已加密字符串
                            value = value.substring(SENSITIVE_PARAM_TAG_S.length());
                            value = decrypt(value);
                        }
                        // 修改字段值
                        field.set(result, value);
                    } catch (Exception e) {
                        log.error("decrypt second fail ", e);
                    }
                }
            }
        }
        return result;
    }

}
