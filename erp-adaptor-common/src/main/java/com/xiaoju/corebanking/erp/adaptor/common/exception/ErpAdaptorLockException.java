package com.xiaoju.corebanking.erp.adaptor.common.exception;

import com.xiaoju.digitalbank.errorno.CommonResponse;

public class ErpAdaptorLockException extends ErpAdaptorBusinessException {

    public ErpAdaptorLockException(CommonResponse errorNo) {
        super(errorNo);
    }

    public ErpAdaptorLockException(CommonResponse errorNo, Throwable e) {
        super(errorNo, e);
    }

    public ErpAdaptorLockException(CommonResponse errorNo, Object data) {
        super(errorNo, data);
    }

    public ErpAdaptorLockException(CommonResponse errorNo, String debugMsg, Throwable e) {
        super(errorNo, debugMsg, e);
    }

    public ErpAdaptorLockException(CommonResponse errorNo, String debugMsg) {
        super(errorNo, debugMsg);
    }

    public ErpAdaptorLockException(CommonResponse errorNo, String debugMsg, Object data) {
        super(errorNo, debugMsg, data);
    }
}
