package com.xiaoju.corebanking.erp.adaptor.common.enums;

public enum AccengCodeEnum {


    //业务异常+不要跳转首页
    BUSINESS_ERROR("Acceng-100", "BUSINESS_ERROR"),
    //业务异常+需要跳转首页
    BUSINESS_FORWARD_ERROR("Acceng-101", "BUSINESS_FORWARD_ERROR"),
    //非预期系统异常
    SYSTEM_ERROR("Acceng-200", "SYSTEM_ERROR"),
    //BPM流程异常
    BPM_ERROR("Acceng-300", "BPM_ERROR"),
    //SSO异常
    SSO_ERROR("Acceng-400", "SSO_ERROR");


    private String code;
    private String desc;

    private AccengCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
