package com.xiaoju.corebanking.erp.adaptor.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 文件处理状态
 **/
@Getter
@AllArgsConstructor
public enum SourceSysEnum {
    SHEN_MA("SHENMA", "SHENMA"),
    SAFI("SAFI", "SAFI"),
    ;

    /**
     * 编码
     */
    private final String code;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 缓存
     */
    private static final Map<String, SourceSysEnum> CACHE_CODE = Arrays.stream(SourceSysEnum.values()).collect(
            Collectors.toMap(SourceSysEnum::getCode, Function.identity()));

    /**
     * 通过code获取枚举
     *
     * @param code 编码
     * @return ProcessStatusEnum
     */
    public static SourceSysEnum getByCode(String code) {
        return CACHE_CODE.get(code);
    }
}
