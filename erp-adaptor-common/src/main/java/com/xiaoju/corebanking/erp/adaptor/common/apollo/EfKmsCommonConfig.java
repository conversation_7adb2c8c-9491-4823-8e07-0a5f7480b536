package com.xiaoju.corebanking.erp.adaptor.common.apollo;

import com.xiaoju.corebanking.erp.adaptor.common.constant.ApolloConstants;
import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import com.xiaoju.digitalbank.exception.ErrorNoException;
import com.xiaoju.godson.apollo.config.annotation.ApolloConfig;
import com.xiaoju.godson.apollo.config.annotation.ApolloField;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;

import java.util.Optional;

@Slf4j
@Getter
@ApolloConfig(namespace = ApolloConstants.APOLLO_NAMESPACE,
        configName = ApolloConstants.KMS_CONFIG, blockStart = true)
public class EfKmsCommonConfig implements InitializingBean {

    private static EfKmsCommonConfig singleton;

    @ApolloField(name = "sensitive_secret")
    private EfSensitiveSecret sensitiveSecret;

    @ApolloField(name = "kms_aksk")
    private KmsAksk kmsAksk;

    @Override
    public void afterPropertiesSet() throws Exception {
        singleton = this;
    }

    public static String getSensitiveSecretId() {
        return singleton.sensitiveSecret.getSecretId();
    }

    public static String getSensitiveSecretVersion() {
        String secretVersion = singleton.sensitiveSecret.getSecretVersion();
        String version = StringUtils.isNotBlank(secretVersion) ? secretVersion : StringUtils.EMPTY;
        return version;
    }

    public static String getKmsAccessKey() {
        Optional.ofNullable(singleton.kmsAksk).orElseThrow(() -> new ErrorNoException(CommonErrorNo.NOT_FOUNDED, "kmsAksk illegal"));
        return singleton.kmsAksk.getKmsAccessKey();
    }

    public static String getKmsSecretKey() {
        Optional.ofNullable(singleton.kmsAksk).orElseThrow(() -> new ErrorNoException(CommonErrorNo.NOT_FOUNDED, "kmsAksk illegal"));
        return singleton.kmsAksk.getKmsSecretKey();
    }


    @Setter
    @Getter
    public static class EfSensitiveSecret {

        // 密钥凭证值
        private String secretId;

        // 密钥凭证版本号
        private String secretVersion;

        // 预留
        private String arn;
    }

    @Setter
    @Getter
    public static class KmsAksk {

        // 连接ccessKey
        private String kmsAccessKey;

        // 密码secretKey
        private String kmsSecretKey;

    }

}