package com.xiaoju.corebanking.erp.adaptor.common.config;

import com.xiaoju.corebanking.erp.adaptor.common.exception.ErpAdaptorBusinessException;
import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import com.xiaoju.digitalbank.exception.ErrorNoException;
import com.xiaoju.digitalbank.util.env.TraceUtils;
import com.xiaoju.godson.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Configuration
public class ThreadPoolExecutorConfiguration {

    @Bean("batchPoolExecutor")
    public Executor batchPoolExecutor() {
        Executor BATCH_CREATE_MESSAGE_POOL_EXECUTOR = new ThreadPoolExecutor(50
                , 100
                , 60
                , TimeUnit.SECONDS
                , new LinkedBlockingQueue<>(100)
                , new ThreadFactory() {

            private final AtomicInteger COUNTER = new AtomicInteger(0);

            @Override
            public Thread newThread(Runnable r) {
                Thread tmp = new Thread(r);
                tmp.setName("BATCH_CREATE_MESSAGE_POOL_CORE_" + 50
                        + "_MAX_" + 100
                        + "_BLKQ_" + 100
                        + "_SEQ_" + COUNTER.incrementAndGet());
                return tmp;
            }
        }, (r, executor) -> {
            throw new ErrorNoException(CommonErrorNo.FAIL, "System Busy,Try Later!");
        });
        return new DelegateExecutor(BATCH_CREATE_MESSAGE_POOL_EXECUTOR);
    }

    static class DelegateExecutor implements Executor {
        private final Executor delegate;

        public DelegateExecutor(Executor delegate) {
            this.delegate = delegate;
        }

        @Override
        public void execute(Runnable command) {
            String contextTraceId = TraceContext.get().getTraceId();
            delegate.execute(() -> {
                try {
                    TraceUtils.runWithSpecificTraceId(command, contextTraceId);
                } catch (ErpAdaptorBusinessException e) {
                    log.error("DelegateExecutor execute an error, {}", CommonErrorNo.FAIL, e);
                }
            });
        }
    }
}
