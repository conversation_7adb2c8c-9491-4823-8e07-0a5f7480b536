package com.xiaoju.corebanking.erp.adaptor.common.exception;

import com.xiaoju.digitalbank.errorno.CommonResponse;
import com.xiaoju.digitalbank.exception.BaseException;

public class ErpAdaptorBusinessException extends BaseException {

    public ErpAdaptorBusinessException(CommonResponse errorNo) {
        super(errorNo);
    }

    public ErpAdaptorBusinessException(CommonResponse errorNo, Throwable e) {
        super(errorNo, e);
    }

    public ErpAdaptorBusinessException(CommonResponse errorNo, Object data) {
        super(errorNo);
        setData(data);
    }

    public ErpAdaptorBusinessException(CommonResponse errorNo, String debugMsg, Throwable e) {
        super(errorNo, debugMsg, e);
    }

    public ErpAdaptorBusinessException(CommonResponse errorNo, String debugMsg) {
        super(errorNo, debugMsg);
    }

    public ErpAdaptorBusinessException(CommonResponse errorNo, String debugMsg, Object data) {
        super(errorNo, debugMsg, data);
    }
}
