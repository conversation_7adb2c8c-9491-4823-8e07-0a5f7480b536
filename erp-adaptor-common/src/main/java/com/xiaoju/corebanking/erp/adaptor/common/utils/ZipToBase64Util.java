package com.xiaoju.corebanking.erp.adaptor.common.utils;

import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * @Description: 类描述
 * @author: zhangcc
 * @date: 2025/5/22$
 **/
@Slf4j
@Component
public class ZipToBase64Util {
    public  String zipToBase64(byte[] zipBytes) {
        try{
            // 将byte数组转换为Base64字符串
            return new String( Base64.encodeBase64(zipBytes), StandardCharsets.UTF_8);
        }catch (Exception e){
            log.error("zip error:{}", CommonErrorNo.FILE_ERROR,e);
        }
        return "";
    }
}

