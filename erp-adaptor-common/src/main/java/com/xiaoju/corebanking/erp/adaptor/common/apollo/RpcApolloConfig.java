package com.xiaoju.corebanking.erp.adaptor.common.apollo;

import com.xiaoju.digitalbank.common.rpc.config.AccessConfig;
import com.xiaoju.digitalbank.common.rpc.config.HttpConfig;

import java.util.Map;

public interface RpcApolloConfig {

    /**
     * 获取接入配置
     */
    AccessConfig getAccessConfig();

    /**
     * 获取HTTP配置
     */
    Map<String, HttpConfig> getHttpConfig();


    /**
     * 获取错误编码映射
     */
    Map<String, String> getErrorMapping();


}
