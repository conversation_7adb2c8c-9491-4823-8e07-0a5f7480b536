package com.xiaoju.corebanking.erp.adaptor.common.lock;

import com.xiaoju.corebanking.erp.adaptor.common.exception.ErpAdaptorLockException;
import com.xiaoju.corebanking.erp.adaptor.common.utils.LogUtils;
import com.xiaoju.digitalbank.errorno.MessageErrorNo;
import lombok.extern.slf4j.Slf4j;

import java.util.UUID;

@Slf4j
public class LockImpl implements Lock {

    private LockParams lockParams;

    private LockStorage lockStorage;

    private long lockAt = 0;

    public LockImpl(String key, LockStorage lockStorage, int expireInSecond) {
        this.lockStorage = lockStorage;
        lockParams = new LockParams(key, UUID.randomUUID().toString(), expireInSecond);
    }

    @Override
    public void lock() {
        log.info("Lock try to lock, params={}", LogUtils.toString(lockParams));
        int effected = doLock();
        if (effected != 1) {
            log.info("Lock lock fail, params={}", LogUtils.toString(lockParams));
            throw new ErpAdaptorLockException(MessageErrorNo.INTERNAL_ERROR_LOCK_ERROR);
        }
        lockAt = System.currentTimeMillis();
    }

    /**
     * 加锁
     */
    private int doLock() {
        int effected = 0;
        try {
            effected = lockStorage.updateForLock(lockParams, lockParams.getKey());
        } catch (Exception e) {
            log.error("Lock doLock error, params={}", LogUtils.toString(lockParams), e);
        }
        return effected;
    }

    @Override
    public void unlock() {
        if (lockAt == 0L) {
            log.info("Lock try to unlock but have not locked yet, params={}", LogUtils.toString(lockParams));
            return;
        }
        long lockTime = System.currentTimeMillis() - lockAt;
        log.info("Lock try to unlock, params={}, locking={}ms ", LogUtils.toString(lockParams), lockTime);
        int effected = doUnlock();  // 解锁
        if (effected != 1) {
            log.error("Lock unlock fail, params={}", LogUtils.toString(lockParams));
        }
    }

    /**
     * 解锁
     */
    private int doUnlock() {
        int effected = 0;
        try {
            effected = lockStorage.updateForUnlock(lockParams,lockParams.getKey());
        } catch (Exception e) {
            log.error("Lock unlock error, params={}", LogUtils.toString(lockParams), e);
        }
        return effected;
    }
}
