package com.xiaoju.corebanking.erp.adaptor.common.domain;

/**
 * @Description: 类描述
 * @author: didi
 **/
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface CsvColumn {
    int order();
    String description() default "";
    boolean required() default false;
}