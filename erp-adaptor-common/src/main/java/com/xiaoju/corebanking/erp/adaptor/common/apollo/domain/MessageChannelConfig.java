package com.xiaoju.corebanking.erp.adaptor.common.apollo.domain;

import com.xiaoju.digitalbank.enums.message.MessageChannelType;
import com.xiaoju.digitalbank.enums.message.MessageVendorType;
import com.xiaoju.digitalbank.enums.message.PerceptionType;
import com.xiaoju.digitalbank.enums.message.TemplateType;
import lombok.Data;

import java.util.List;

@Data
public class MessageChannelConfig {

    private MessageChannelType channelType;

    private MessageVendorType vendorType;

    private PerceptionType perceptionType;

    private TemplateType templateType;

    private List<MessageSender> messageSenderPool;

    private int weight;

    private int rateLimit;

    private String availableStartTime;

    private String availableEndTime;
}
