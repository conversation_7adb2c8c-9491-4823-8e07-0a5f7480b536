<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>erp-adaptor-common</artifactId>
    <parent>
        <groupId>com.xiaoju.corebanking</groupId>
        <artifactId>erp-adaptor</artifactId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <dependencies>

        <dependency>
            <groupId>com.xiaoju.digitalbank</groupId>
            <artifactId>digitalbank-common</artifactId>
            <version>${digitalbank.common.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.xiaoju.godson</groupId>
                    <artifactId>godson-dirpc</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.xiaoju.godson</groupId>
                    <artifactId>godson-metric</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.lmax</groupId>
            <artifactId>disruptor</artifactId>
        </dependency>

        <!-- 引入log4j2 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>
        <!-- godson 框架 https://git.xiaojukeji.com/hanjiancheng/godson-framework -->
        <dependency>
            <groupId>com.xiaoju.godson</groupId>
            <artifactId>godson-log-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaoju.godson</groupId>
            <artifactId>godson-log-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaoju.godson</groupId>
            <artifactId>godson-trace</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaoju.godson</groupId>
            <artifactId>godson-degrade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaoju.godson</groupId>
            <artifactId>godson-dirpc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xiaoju.godson</groupId>
            <artifactId>godson-crypto</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xiaoju.godson</groupId>
            <artifactId>godson-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xiaoju.godson</groupId>
            <artifactId>godson-metric</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xiaoju.apollo</groupId>
            <artifactId>sdk</artifactId>
            <version>2.9.9</version>
            <exclusions>
                <exclusion>
                    <artifactId>metric</artifactId>
                    <groupId>com.xiaoju.metric</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.xiaoju</groupId>
            <artifactId>compliance-java-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>metric</artifactId>
                    <groupId>com.xiaoju.metric</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 公共组件，如json -->
        <dependency>
            <groupId>com.xiaoju.godson</groupId>
            <artifactId>godson-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xiaoju.godson</groupId>
            <artifactId>godson-web</artifactId>
        </dependency>

        <!--  屏蔽json序列化的thrift非属性化字段   -->
        <dependency>
            <groupId>com.xiaoju.godson</groupId>
            <artifactId>godson-jackson-thrift</artifactId>
        </dependency>
        <!-- 公共组件，如json -->

        <!-- httpClient -->
        <dependency>
            <groupId>com.xiaoju.godson</groupId>
            <artifactId>godson-http</artifactId>
        </dependency>
        <!-- httpClient -->

        <!-- 911降级 -->
        <dependency>
            <groupId>com.xiaoju.onekey</groupId>
            <artifactId>degrade_sdk_java</artifactId>
        </dependency>
        <!-- 911降级 -->

        <!--统一货币sdk-->
        <dependency>
            <groupId>com.xiaoju.elvish</groupId>
            <artifactId>JavaSdk</artifactId>
        </dependency>
        <!--统一货币sdk-->

        <dependency>
            <groupId>com.xiaojukeji.copywriter-java-sdk</groupId>
            <artifactId>copywriter-java-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
        </dependency>

        <!-- KMS 加密组件 -->
        <dependency>
            <groupId>kms</groupId>
            <artifactId>didi-kms-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
        </dependency>
        <dependency>
            <artifactId>message-client</artifactId>
            <groupId>com.xiaoju.digitalbank</groupId>
        </dependency>
    </dependencies>
</project>