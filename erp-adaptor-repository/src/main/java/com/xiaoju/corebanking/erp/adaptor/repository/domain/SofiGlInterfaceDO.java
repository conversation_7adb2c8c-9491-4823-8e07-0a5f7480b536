package com.xiaoju.corebanking.erp.adaptor.repository.domain;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
@Data
public class SofiGlInterfaceDO implements Serializable {
    private Long id;

    private Long fileId;

    private String processDay;

    private String fileName;

    private String batchId;

    private Long detallePolizaId;

    private String sourceSys;

    private Long empresaId;

    private Long polizaId;

    private String fecha;

    private Long centroCostoId;

    private String cuentaCompleta;

    private Long instrumento;

    private Long monedaId;

    private BigDecimal cargos;

    private BigDecimal abonos;

    private String descripcion;

    private String referencia;

    private String procedimientoCont;

    private String tipoInstrumentoId;

    private String rfc;

    private BigDecimal totalFactura;

    private String folioUuid;

    private Long usuario;

    private String fechaActual;

    private String direccionIp;

    private String programaId;

    private Long sucursal;

    private Long numTransaccion;

    private Long ledgerId;

    private String ledgerName;

    private String currencyCode;

    private String journalCategory;

    private String journalSource;

    private String journalSourceName;

    private String segment1;

    private String segment2;

    private String segment3;

    private String segment4;

    private String segment5;

    private String segment6;

    private String segment7;

    private String segment8;

    private String segment9;

    private String segment10;

    private Long groupId;

    private Date currencyConversionDate;

    private BigDecimal currencyConversionRate;

    private String userCurrencyConversionType;

    private ProcessStatusEnum processStatus;

    private String processMessage;

    private Long jeHeaderId;

    private String journalName;

    private Long jeLineNum;

    private Long documentId;

    private Long loadRequestId;

    private Long importRequestId;

    private Long objectVersionNumber;

    private Date creationDate;

    private String createdBy;

    private Date lastModifyDate;

    private String lastModifiedBy;

    private static final long serialVersionUID = 1L;

    public void initializeDefaultValues() {
        this.setId(0L);
        this.setFileId(0L);
        this.setProcessDay("");
        this.setFileName("");
        this.setBatchId("");
        this.setDetallePolizaId(0L);
        this.setSourceSys("");
        this.setEmpresaId(0L);
        this.setPolizaId(0L);
        this.setFecha("");
        this.setCentroCostoId(0L);
        this.setCuentaCompleta("");
        this.setInstrumento(0L);
        this.setMonedaId(0L);
        this.setCargos(new BigDecimal("0"));
        this.setAbonos(new BigDecimal("0"));
        this.setDescripcion("");
        this.setReferencia("");
        this.setProcedimientoCont("");
        this.setTipoInstrumentoId("");
        this.setRfc("");
        this.setTotalFactura(new BigDecimal("0"));
        this.setFolioUuid("");
        this.setUsuario(0L);
        this.setFechaActual("");
        this.setDireccionIp("");
        this.setProgramaId("");
        this.setSucursal(0L);
        this.setNumTransaccion(0L);
        this.setLedgerId(0L);
        this.setLedgerName("");
        this.setCurrencyCode("");
        this.setJournalCategory("");
        this.setJournalSource("");
        this.setJournalSourceName("");
        this.setSegment1("");
        this.setSegment2("");
        this.setSegment3("");
        this.setSegment4("");
        this.setSegment5("");
        this.setSegment6("");
        this.setSegment7("");
        this.setSegment8("");
        this.setSegment9("");
        this.setSegment10("");
        this.setGroupId(0L);
        this.setCurrencyConversionDate(new Date());
        this.setCurrencyConversionRate(new BigDecimal("0"));
        this.setUserCurrencyConversionType("");
        this.setProcessStatus(ProcessStatusEnum.NEW);
        this.setProcessMessage("");
        this.setJeHeaderId(0L);
        this.setJournalName("");
        this.setJeLineNum(0L);
        this.setDocumentId(0L);
        this.setLoadRequestId(0L);
        this.setImportRequestId(0L);
        this.setObjectVersionNumber(0L);
        this.setCreationDate(new Date());
        this.setCreatedBy("");
        this.setLastModifyDate(new Date());
        this.setLastModifiedBy("");
    }
}