package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.xiaoju.corebanking.erp.adaptor.common.exception.ErpAdaptorBusinessException;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlShenmaHisRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlShenmaHisPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlShenmaPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlShenmaHisPOMapper;
import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 类描述
 * @author: zhangcc
 * @date: 2025/7/1$
 **/
@Repository
@Slf4j
public class SofiGlShenmaHisRepositoryImpl implements SofiGlShenmaHisRepository {

    @Resource
    private SofiGlShenmaHisPOMapper sofiGlShenmaHisPOMapper;

    @Override
    public int backupShenmaHis(List<SofiGlShenmaPO> sofiGlShenmaPOList) {
        if (CollectionUtils.isEmpty(sofiGlShenmaPOList)) {
            log.error("尝试备份null或空对象到历史表");
            return 0;
        }

        int successCount = 0;
        for (SofiGlShenmaPO sofiGlShenmaPO : sofiGlShenmaPOList) {
            try {
                log.info("备份Shenma数据到历史表: linkReference={}", sofiGlShenmaPO.getLinkReference());
                SofiGlShenmaHisPO hisPO = new SofiGlShenmaHisPO();
                BeanUtils.copyProperties(sofiGlShenmaPO, hisPO);
                sofiGlShenmaHisPOMapper.insertSelective(hisPO);
                successCount++;
            } catch (Exception e) {
                log.error("备份Shenma数据到历史表失败: {}", e.getMessage(), e);
                throw new ErpAdaptorBusinessException(CommonErrorNo.DB_SAVE_ERROR, "备份Shenma数据到历史表失败");
            }
        }
        return successCount;
    }
}
