package com.xiaoju.corebanking.erp.adaptor.repository.converter;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlFileControlDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlFileControlPO;
import org.springframework.stereotype.Component;

@Component
public class SofiGlFileControlModelConverter {

    public SofiGlFileControlPO convert(SofiGlFileControlDO sofiGlFileControlDO) {
        if (sofiGlFileControlDO == null) {
            return null;
        }

        SofiGlFileControlPO sofiGlFileControlPO = new SofiGlFileControlPO();

        if (sofiGlFileControlDO.getProcessDay() != null) {
            sofiGlFileControlPO.setProcessDay(sofiGlFileControlDO.getProcessDay());
        }

        if (sofiGlFileControlDO.getFileName() != null) {
            sofiGlFileControlPO.setFileName(sofiGlFileControlDO.getFileName());
        }

        if (sofiGlFileControlDO.getSystemCode() != null) {
            sofiGlFileControlPO.setSystemCode(sofiGlFileControlDO.getSystemCode());
        }

        if (sofiGlFileControlDO.getFileSize() != null) {
            sofiGlFileControlPO.setFileSize(sofiGlFileControlDO.getFileSize());
        }

        if (sofiGlFileControlDO.getFileCount() != null) {
            sofiGlFileControlPO.setFileCount(sofiGlFileControlDO.getFileCount());
        }

        if (sofiGlFileControlDO.getProcessStatus() != null) {
            sofiGlFileControlPO.setProcessStatus(sofiGlFileControlDO.getProcessStatus());
        }

        if (sofiGlFileControlDO.getProcessMessage() != null) {
            sofiGlFileControlPO.setProcessMessage(sofiGlFileControlDO.getProcessMessage());
        }

        if (sofiGlFileControlDO.getObjectVersionNumber() != null) {
            sofiGlFileControlPO.setObjectVersionNumber(sofiGlFileControlDO.getObjectVersionNumber()+1);
        }

        if (sofiGlFileControlDO.getCreationDate() != null) {
            sofiGlFileControlPO.setCreationDate(sofiGlFileControlDO.getCreationDate());
        }

        if (sofiGlFileControlDO.getCreatedBy() != null) {
            sofiGlFileControlPO.setCreatedBy(sofiGlFileControlDO.getCreatedBy());
        }

        if (sofiGlFileControlDO.getLastModifyDate() != null) {
            sofiGlFileControlPO.setLastModifyDate(sofiGlFileControlDO.getLastModifyDate());
        }

        if (sofiGlFileControlDO.getLastModifiedBy() != null) {
            sofiGlFileControlPO.setLastModifiedBy(sofiGlFileControlDO.getLastModifiedBy());
        }
        return sofiGlFileControlPO;
    }

    public SofiGlFileControlDO convert(SofiGlFileControlPO sofiGlFileControlPO) {
        SofiGlFileControlDO sofiGlFileControlDO = new SofiGlFileControlDO();
        sofiGlFileControlDO.setProcessDay(sofiGlFileControlPO.getProcessDay());
        sofiGlFileControlDO.setFileName(sofiGlFileControlPO.getFileName());
        sofiGlFileControlDO.setSystemCode(sofiGlFileControlPO.getSystemCode());
        sofiGlFileControlDO.setFileSize(sofiGlFileControlPO.getFileSize());
        sofiGlFileControlDO.setFileCount(sofiGlFileControlPO.getFileCount());
        sofiGlFileControlDO.setProcessStatus(sofiGlFileControlPO.getProcessStatus());
        sofiGlFileControlDO.setProcessMessage(sofiGlFileControlPO.getProcessMessage());
        sofiGlFileControlDO.setObjectVersionNumber(sofiGlFileControlPO.getObjectVersionNumber());
        sofiGlFileControlDO.setCreationDate(sofiGlFileControlPO.getCreationDate());
        sofiGlFileControlDO.setCreatedBy(sofiGlFileControlPO.getCreatedBy());
        sofiGlFileControlDO.setLastModifyDate(sofiGlFileControlPO.getLastModifyDate());
        sofiGlFileControlDO.setLastModifiedBy(sofiGlFileControlPO.getLastModifiedBy());

        return sofiGlFileControlDO;
    }
}
