package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated;

import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabLinesPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabLinesPOExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface CuxTwoTabLinesPOMapper {
    int deleteByExample(CuxTwoTabLinesPOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(CuxTwoTabLinesPO record);

    int insertSelective(CuxTwoTabLinesPO record);

    List<CuxTwoTabLinesPO> selectByExampleWithRowbounds(CuxTwoTabLinesPOExample example, RowBounds rowBounds);

    List<CuxTwoTabLinesPO> selectByExample(CuxTwoTabLinesPOExample example);

    CuxTwoTabLinesPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") CuxTwoTabLinesPO record, @Param("example") CuxTwoTabLinesPOExample example);

    int updateByExample(@Param("record") CuxTwoTabLinesPO record, @Param("example") CuxTwoTabLinesPOExample example);

    int updateByPrimaryKeySelective(CuxTwoTabLinesPO record);

    int updateByPrimaryKey(CuxTwoTabLinesPO record);
}