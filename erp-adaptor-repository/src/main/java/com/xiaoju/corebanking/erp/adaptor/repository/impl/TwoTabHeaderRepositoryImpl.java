package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabHeadersPOExample;
import com.xiaoju.corebanking.erp.adaptor.repository.TwoTabHeaderRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.CuxTwoTabHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabHeadersPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.CuxTwoTabHeadersPOMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Objects;

@Service
public class TwoTabHeaderRepositoryImpl implements TwoTabHeaderRepository {

    @Resource
    private CuxTwoTabHeadersPOMapper cuxTwoTabHeadersPOMapper;

    @Override
    public CuxTwoTabHeadersPO selectByFormCode(String formCode) {
        CuxTwoTabHeadersPOExample example = new CuxTwoTabHeadersPOExample();
        CuxTwoTabHeadersPOExample.Criteria criteria = example.createCriteria();
        criteria.andFormCodeEqualTo(formCode);
        return cuxTwoTabHeadersPOMapper.selectByExample(example).stream().findFirst().orElse(null);
    }

    @Override
    public List<CuxTwoTabHeadersPO> selectAll() {
        CuxTwoTabHeadersPOExample example = new CuxTwoTabHeadersPOExample();
        CuxTwoTabHeadersPOExample.Criteria criteria = example.createCriteria();
        //criteria.andFormCodeEqualTo(header.getFormCode());
        //criteria.andFormNameEqualTo(header.getFormCode());
        return cuxTwoTabHeadersPOMapper.selectByExample(example);
    }

    @Override
    public void updateByExample(CuxTwoTabHeaderDO header) {

    }

    @Override
    public void saveHeader(CuxTwoTabHeaderDO header) {
        if (Objects.isNull(header) || Objects.isNull(header.getFormCode())) {
            throw new RuntimeException("formCode is null");
        }
        CuxTwoTabHeadersPO headerPO = this.selectByFormCode(header.getFormCode());
        if (Objects.isNull(headerPO)) {
            this.insertByExample(header);
        } else {
            BeanUtils.copyProperties(header, headerPO);
            CuxTwoTabHeadersPOExample example = new CuxTwoTabHeadersPOExample();
            CuxTwoTabHeadersPOExample.Criteria criteria = example.createCriteria();
            criteria.andFormCodeEqualTo(header.getFormCode());
            headerPO.setLastUpdatedBy("system");
            headerPO.setLastUpdateDate(new Date());
            cuxTwoTabHeadersPOMapper.updateByExampleSelective(headerPO, example);
        }
    }


    @Override
    public CuxTwoTabHeadersPO insertByExample(CuxTwoTabHeaderDO header) {
        CuxTwoTabHeadersPO headerPO = new CuxTwoTabHeadersPO();
        BeanUtils.copyProperties(header, headerPO);
        headerPO.setEnableFlag("Y");
        headerPO.setLockedFlag("Y");
        headerPO.setCreatedBy("system");
        if (Objects.isNull(headerPO.getDateFrom())) {
            headerPO.setDateFrom(new Date());
        }
        if (Objects.isNull(headerPO.getDateTo())) {
            headerPO.setDateTo(new GregorianCalendar(9999, Calendar.DECEMBER, 31).getTime());
        }
        headerPO.setDateTo(new Date());
        headerPO.setLastUpdateLogin(0);
        headerPO.setLastUpdatedBy("system");
        headerPO.setCreationDate(new Date());
        headerPO.setLastUpdateDate(new Date());
        cuxTwoTabHeadersPOMapper.insert(headerPO);
        return headerPO;
    }
}
