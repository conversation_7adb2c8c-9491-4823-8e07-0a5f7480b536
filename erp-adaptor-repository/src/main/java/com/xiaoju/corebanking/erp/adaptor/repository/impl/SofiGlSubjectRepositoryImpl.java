package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlSubjectRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.converter.SofiGlSubjectConverter;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlSubjectDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlSubjectPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlSubjectPOKey;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlSubjectPOMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/7 17:08
 */
@Slf4j
@Repository
public class SofiGlSubjectRepositoryImpl implements SofiGlSubjectRepository {

    @Resource
    private SofiGlSubjectPOMapper sofiGlSubjectPOMapper;

    @Resource
    private SofiGlSubjectConverter sofiGlSubjectConverter;

    @Override
    public int syncSofiGlSubjectData(List<SofiGlSubjectDO> sofiGlSubjectList) {
        if (sofiGlSubjectList == null || sofiGlSubjectList.isEmpty()) {
            log.info("SofiGlSubject数据列表为空，无需同步");
            return 0;
        }

        int syncCount = 0;

        for (SofiGlSubjectDO sofiGlSubjectDO : sofiGlSubjectList) {
            try {
                // 根据subjectCode+subjectSetNo查询是否存在
                SofiGlSubjectPOKey key = new SofiGlSubjectPOKey();
                key.setSubjectCode(sofiGlSubjectDO.getSubjectCode());
                key.setSubjectSetNo(sofiGlSubjectDO.getSubjectSetNo());

                SofiGlSubjectPO existingRecord = sofiGlSubjectPOMapper.selectByPrimaryKey(key);

                SofiGlSubjectPO po = sofiGlSubjectConverter.toPO(sofiGlSubjectDO);

                if (existingRecord != null) {
                    // 存在则更新
                    int result = sofiGlSubjectPOMapper.updateByPrimaryKeySelective(po);
                    if (result > 0) {
                        syncCount++;
                        log.debug("更新SofiGlSubject记录成功: subjectCode={}, subjectSetNo={}",
                                sofiGlSubjectDO.getSubjectCode(), sofiGlSubjectDO.getSubjectSetNo());
                    }
                } else {
                    // 不存在则插入
                    int result = sofiGlSubjectPOMapper.insertSelective(po);
                    if (result > 0) {
                        syncCount++;
                        log.debug("插入SofiGlSubject记录成功: subjectCode={}, subjectSetNo={}",
                                sofiGlSubjectDO.getSubjectCode(), sofiGlSubjectDO.getSubjectSetNo());
                    }
                }

            } catch (Exception e) {
                log.error("同步SofiGlSubject数据失败，subjectCode: {}, subjectSetNo: {}",
                        sofiGlSubjectDO.getSubjectCode(), sofiGlSubjectDO.getSubjectSetNo(), e);
            }
        }
        return syncCount;
    }
}
