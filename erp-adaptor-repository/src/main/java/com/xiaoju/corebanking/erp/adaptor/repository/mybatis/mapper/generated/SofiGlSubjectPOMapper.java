package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated;

import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlSubjectPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlSubjectPOKey;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface SofiGlSubjectPOMapper {
    int deleteByPrimaryKey(SofiGlSubjectPOKey key);

    int insert(SofiGlSubjectPO record);

    int insertSelective(SofiGlSubjectPO record);

    SofiGlSubjectPO selectByPrimaryKey(SofiGlSubjectPOKey key);

    int updateByPrimaryKeySelective(SofiGlSubjectPO record);

    int updateByPrimaryKey(SofiGlSubjectPO record);
}