package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain;

import java.io.Serializable;
import java.util.Date;

public class CuxTwoTabValuePO implements Serializable {
    private Long id;

    private Long headerId;

    private String value1;

    private String value2;

    private String value3;

    private String value4;

    private String value5;

    private String value6;

    private String value7;

    private String value8;

    private String value9;

    private String value10;

    private String value11;

    private String value12;

    private String value13;

    private String value14;

    private String value15;

    private String value16;

    private String value17;

    private String value18;

    private String value19;

    private String value20;

    private String value21;

    private String value22;

    private String value23;

    private String value24;

    private String value25;

    private String value26;

    private String value27;

    private String value28;

    private String value29;

    private String value30;

    private String value31;

    private String value32;

    private String value33;

    private String value34;

    private String value35;

    private String value36;

    private String value37;

    private String value38;

    private String value39;

    private String value40;

    private String value41;

    private String value42;

    private String value43;

    private String value44;

    private String value45;

    private String value46;

    private String value47;

    private String value48;

    private String value49;

    private String value50;

    private String enabledFlag;

    private Date dateFrom;

    private Date dateTo;

    private String lang;

    private Integer version;

    private Date lastUpdateDate;

    private String lastUpdatedBy;

    private Integer lastUpdateLogin;

    private String createdBy;

    private Date creationDate;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getHeaderId() {
        return headerId;
    }

    public void setHeaderId(Long headerId) {
        this.headerId = headerId;
    }

    public String getValue1() {
        return value1;
    }

    public void setValue1(String value1) {
        this.value1 = value1 == null ? null : value1.trim();
    }

    public String getValue2() {
        return value2;
    }

    public void setValue2(String value2) {
        this.value2 = value2 == null ? null : value2.trim();
    }

    public String getValue3() {
        return value3;
    }

    public void setValue3(String value3) {
        this.value3 = value3 == null ? null : value3.trim();
    }

    public String getValue4() {
        return value4;
    }

    public void setValue4(String value4) {
        this.value4 = value4 == null ? null : value4.trim();
    }

    public String getValue5() {
        return value5;
    }

    public void setValue5(String value5) {
        this.value5 = value5 == null ? null : value5.trim();
    }

    public String getValue6() {
        return value6;
    }

    public void setValue6(String value6) {
        this.value6 = value6 == null ? null : value6.trim();
    }

    public String getValue7() {
        return value7;
    }

    public void setValue7(String value7) {
        this.value7 = value7 == null ? null : value7.trim();
    }

    public String getValue8() {
        return value8;
    }

    public void setValue8(String value8) {
        this.value8 = value8 == null ? null : value8.trim();
    }

    public String getValue9() {
        return value9;
    }

    public void setValue9(String value9) {
        this.value9 = value9 == null ? null : value9.trim();
    }

    public String getValue10() {
        return value10;
    }

    public void setValue10(String value10) {
        this.value10 = value10 == null ? null : value10.trim();
    }

    public String getValue11() {
        return value11;
    }

    public void setValue11(String value11) {
        this.value11 = value11 == null ? null : value11.trim();
    }

    public String getValue12() {
        return value12;
    }

    public void setValue12(String value12) {
        this.value12 = value12 == null ? null : value12.trim();
    }

    public String getValue13() {
        return value13;
    }

    public void setValue13(String value13) {
        this.value13 = value13 == null ? null : value13.trim();
    }

    public String getValue14() {
        return value14;
    }

    public void setValue14(String value14) {
        this.value14 = value14 == null ? null : value14.trim();
    }

    public String getValue15() {
        return value15;
    }

    public void setValue15(String value15) {
        this.value15 = value15 == null ? null : value15.trim();
    }

    public String getValue16() {
        return value16;
    }

    public void setValue16(String value16) {
        this.value16 = value16 == null ? null : value16.trim();
    }

    public String getValue17() {
        return value17;
    }

    public void setValue17(String value17) {
        this.value17 = value17 == null ? null : value17.trim();
    }

    public String getValue18() {
        return value18;
    }

    public void setValue18(String value18) {
        this.value18 = value18 == null ? null : value18.trim();
    }

    public String getValue19() {
        return value19;
    }

    public void setValue19(String value19) {
        this.value19 = value19 == null ? null : value19.trim();
    }

    public String getValue20() {
        return value20;
    }

    public void setValue20(String value20) {
        this.value20 = value20 == null ? null : value20.trim();
    }

    public String getValue21() {
        return value21;
    }

    public void setValue21(String value21) {
        this.value21 = value21 == null ? null : value21.trim();
    }

    public String getValue22() {
        return value22;
    }

    public void setValue22(String value22) {
        this.value22 = value22 == null ? null : value22.trim();
    }

    public String getValue23() {
        return value23;
    }

    public void setValue23(String value23) {
        this.value23 = value23 == null ? null : value23.trim();
    }

    public String getValue24() {
        return value24;
    }

    public void setValue24(String value24) {
        this.value24 = value24 == null ? null : value24.trim();
    }

    public String getValue25() {
        return value25;
    }

    public void setValue25(String value25) {
        this.value25 = value25 == null ? null : value25.trim();
    }

    public String getValue26() {
        return value26;
    }

    public void setValue26(String value26) {
        this.value26 = value26 == null ? null : value26.trim();
    }

    public String getValue27() {
        return value27;
    }

    public void setValue27(String value27) {
        this.value27 = value27 == null ? null : value27.trim();
    }

    public String getValue28() {
        return value28;
    }

    public void setValue28(String value28) {
        this.value28 = value28 == null ? null : value28.trim();
    }

    public String getValue29() {
        return value29;
    }

    public void setValue29(String value29) {
        this.value29 = value29 == null ? null : value29.trim();
    }

    public String getValue30() {
        return value30;
    }

    public void setValue30(String value30) {
        this.value30 = value30 == null ? null : value30.trim();
    }

    public String getValue31() {
        return value31;
    }

    public void setValue31(String value31) {
        this.value31 = value31 == null ? null : value31.trim();
    }

    public String getValue32() {
        return value32;
    }

    public void setValue32(String value32) {
        this.value32 = value32 == null ? null : value32.trim();
    }

    public String getValue33() {
        return value33;
    }

    public void setValue33(String value33) {
        this.value33 = value33 == null ? null : value33.trim();
    }

    public String getValue34() {
        return value34;
    }

    public void setValue34(String value34) {
        this.value34 = value34 == null ? null : value34.trim();
    }

    public String getValue35() {
        return value35;
    }

    public void setValue35(String value35) {
        this.value35 = value35 == null ? null : value35.trim();
    }

    public String getValue36() {
        return value36;
    }

    public void setValue36(String value36) {
        this.value36 = value36 == null ? null : value36.trim();
    }

    public String getValue37() {
        return value37;
    }

    public void setValue37(String value37) {
        this.value37 = value37 == null ? null : value37.trim();
    }

    public String getValue38() {
        return value38;
    }

    public void setValue38(String value38) {
        this.value38 = value38 == null ? null : value38.trim();
    }

    public String getValue39() {
        return value39;
    }

    public void setValue39(String value39) {
        this.value39 = value39 == null ? null : value39.trim();
    }

    public String getValue40() {
        return value40;
    }

    public void setValue40(String value40) {
        this.value40 = value40 == null ? null : value40.trim();
    }

    public String getValue41() {
        return value41;
    }

    public void setValue41(String value41) {
        this.value41 = value41 == null ? null : value41.trim();
    }

    public String getValue42() {
        return value42;
    }

    public void setValue42(String value42) {
        this.value42 = value42 == null ? null : value42.trim();
    }

    public String getValue43() {
        return value43;
    }

    public void setValue43(String value43) {
        this.value43 = value43 == null ? null : value43.trim();
    }

    public String getValue44() {
        return value44;
    }

    public void setValue44(String value44) {
        this.value44 = value44 == null ? null : value44.trim();
    }

    public String getValue45() {
        return value45;
    }

    public void setValue45(String value45) {
        this.value45 = value45 == null ? null : value45.trim();
    }

    public String getValue46() {
        return value46;
    }

    public void setValue46(String value46) {
        this.value46 = value46 == null ? null : value46.trim();
    }

    public String getValue47() {
        return value47;
    }

    public void setValue47(String value47) {
        this.value47 = value47 == null ? null : value47.trim();
    }

    public String getValue48() {
        return value48;
    }

    public void setValue48(String value48) {
        this.value48 = value48 == null ? null : value48.trim();
    }

    public String getValue49() {
        return value49;
    }

    public void setValue49(String value49) {
        this.value49 = value49 == null ? null : value49.trim();
    }

    public String getValue50() {
        return value50;
    }

    public void setValue50(String value50) {
        this.value50 = value50 == null ? null : value50.trim();
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }

    public Date getDateFrom() {
        return dateFrom;
    }

    public void setDateFrom(Date dateFrom) {
        this.dateFrom = dateFrom;
    }

    public Date getDateTo() {
        return dateTo;
    }

    public void setDateTo(Date dateTo) {
        this.dateTo = dateTo;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang == null ? null : lang.trim();
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Integer getLastUpdateLogin() {
        return lastUpdateLogin;
    }

    public void setLastUpdateLogin(Integer lastUpdateLogin) {
        this.lastUpdateLogin = lastUpdateLogin;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        CuxTwoTabValuePO other = (CuxTwoTabValuePO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getHeaderId() == null ? other.getHeaderId() == null : this.getHeaderId().equals(other.getHeaderId()))
            && (this.getValue1() == null ? other.getValue1() == null : this.getValue1().equals(other.getValue1()))
            && (this.getValue2() == null ? other.getValue2() == null : this.getValue2().equals(other.getValue2()))
            && (this.getValue3() == null ? other.getValue3() == null : this.getValue3().equals(other.getValue3()))
            && (this.getValue4() == null ? other.getValue4() == null : this.getValue4().equals(other.getValue4()))
            && (this.getValue5() == null ? other.getValue5() == null : this.getValue5().equals(other.getValue5()))
            && (this.getValue6() == null ? other.getValue6() == null : this.getValue6().equals(other.getValue6()))
            && (this.getValue7() == null ? other.getValue7() == null : this.getValue7().equals(other.getValue7()))
            && (this.getValue8() == null ? other.getValue8() == null : this.getValue8().equals(other.getValue8()))
            && (this.getValue9() == null ? other.getValue9() == null : this.getValue9().equals(other.getValue9()))
            && (this.getValue10() == null ? other.getValue10() == null : this.getValue10().equals(other.getValue10()))
            && (this.getValue11() == null ? other.getValue11() == null : this.getValue11().equals(other.getValue11()))
            && (this.getValue12() == null ? other.getValue12() == null : this.getValue12().equals(other.getValue12()))
            && (this.getValue13() == null ? other.getValue13() == null : this.getValue13().equals(other.getValue13()))
            && (this.getValue14() == null ? other.getValue14() == null : this.getValue14().equals(other.getValue14()))
            && (this.getValue15() == null ? other.getValue15() == null : this.getValue15().equals(other.getValue15()))
            && (this.getValue16() == null ? other.getValue16() == null : this.getValue16().equals(other.getValue16()))
            && (this.getValue17() == null ? other.getValue17() == null : this.getValue17().equals(other.getValue17()))
            && (this.getValue18() == null ? other.getValue18() == null : this.getValue18().equals(other.getValue18()))
            && (this.getValue19() == null ? other.getValue19() == null : this.getValue19().equals(other.getValue19()))
            && (this.getValue20() == null ? other.getValue20() == null : this.getValue20().equals(other.getValue20()))
            && (this.getValue21() == null ? other.getValue21() == null : this.getValue21().equals(other.getValue21()))
            && (this.getValue22() == null ? other.getValue22() == null : this.getValue22().equals(other.getValue22()))
            && (this.getValue23() == null ? other.getValue23() == null : this.getValue23().equals(other.getValue23()))
            && (this.getValue24() == null ? other.getValue24() == null : this.getValue24().equals(other.getValue24()))
            && (this.getValue25() == null ? other.getValue25() == null : this.getValue25().equals(other.getValue25()))
            && (this.getValue26() == null ? other.getValue26() == null : this.getValue26().equals(other.getValue26()))
            && (this.getValue27() == null ? other.getValue27() == null : this.getValue27().equals(other.getValue27()))
            && (this.getValue28() == null ? other.getValue28() == null : this.getValue28().equals(other.getValue28()))
            && (this.getValue29() == null ? other.getValue29() == null : this.getValue29().equals(other.getValue29()))
            && (this.getValue30() == null ? other.getValue30() == null : this.getValue30().equals(other.getValue30()))
            && (this.getValue31() == null ? other.getValue31() == null : this.getValue31().equals(other.getValue31()))
            && (this.getValue32() == null ? other.getValue32() == null : this.getValue32().equals(other.getValue32()))
            && (this.getValue33() == null ? other.getValue33() == null : this.getValue33().equals(other.getValue33()))
            && (this.getValue34() == null ? other.getValue34() == null : this.getValue34().equals(other.getValue34()))
            && (this.getValue35() == null ? other.getValue35() == null : this.getValue35().equals(other.getValue35()))
            && (this.getValue36() == null ? other.getValue36() == null : this.getValue36().equals(other.getValue36()))
            && (this.getValue37() == null ? other.getValue37() == null : this.getValue37().equals(other.getValue37()))
            && (this.getValue38() == null ? other.getValue38() == null : this.getValue38().equals(other.getValue38()))
            && (this.getValue39() == null ? other.getValue39() == null : this.getValue39().equals(other.getValue39()))
            && (this.getValue40() == null ? other.getValue40() == null : this.getValue40().equals(other.getValue40()))
            && (this.getValue41() == null ? other.getValue41() == null : this.getValue41().equals(other.getValue41()))
            && (this.getValue42() == null ? other.getValue42() == null : this.getValue42().equals(other.getValue42()))
            && (this.getValue43() == null ? other.getValue43() == null : this.getValue43().equals(other.getValue43()))
            && (this.getValue44() == null ? other.getValue44() == null : this.getValue44().equals(other.getValue44()))
            && (this.getValue45() == null ? other.getValue45() == null : this.getValue45().equals(other.getValue45()))
            && (this.getValue46() == null ? other.getValue46() == null : this.getValue46().equals(other.getValue46()))
            && (this.getValue47() == null ? other.getValue47() == null : this.getValue47().equals(other.getValue47()))
            && (this.getValue48() == null ? other.getValue48() == null : this.getValue48().equals(other.getValue48()))
            && (this.getValue49() == null ? other.getValue49() == null : this.getValue49().equals(other.getValue49()))
            && (this.getValue50() == null ? other.getValue50() == null : this.getValue50().equals(other.getValue50()))
            && (this.getEnabledFlag() == null ? other.getEnabledFlag() == null : this.getEnabledFlag().equals(other.getEnabledFlag()))
            && (this.getDateFrom() == null ? other.getDateFrom() == null : this.getDateFrom().equals(other.getDateFrom()))
            && (this.getDateTo() == null ? other.getDateTo() == null : this.getDateTo().equals(other.getDateTo()))
            && (this.getLang() == null ? other.getLang() == null : this.getLang().equals(other.getLang()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getLastUpdateDate() == null ? other.getLastUpdateDate() == null : this.getLastUpdateDate().equals(other.getLastUpdateDate()))
            && (this.getLastUpdatedBy() == null ? other.getLastUpdatedBy() == null : this.getLastUpdatedBy().equals(other.getLastUpdatedBy()))
            && (this.getLastUpdateLogin() == null ? other.getLastUpdateLogin() == null : this.getLastUpdateLogin().equals(other.getLastUpdateLogin()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreationDate() == null ? other.getCreationDate() == null : this.getCreationDate().equals(other.getCreationDate()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getHeaderId() == null) ? 0 : getHeaderId().hashCode());
        result = prime * result + ((getValue1() == null) ? 0 : getValue1().hashCode());
        result = prime * result + ((getValue2() == null) ? 0 : getValue2().hashCode());
        result = prime * result + ((getValue3() == null) ? 0 : getValue3().hashCode());
        result = prime * result + ((getValue4() == null) ? 0 : getValue4().hashCode());
        result = prime * result + ((getValue5() == null) ? 0 : getValue5().hashCode());
        result = prime * result + ((getValue6() == null) ? 0 : getValue6().hashCode());
        result = prime * result + ((getValue7() == null) ? 0 : getValue7().hashCode());
        result = prime * result + ((getValue8() == null) ? 0 : getValue8().hashCode());
        result = prime * result + ((getValue9() == null) ? 0 : getValue9().hashCode());
        result = prime * result + ((getValue10() == null) ? 0 : getValue10().hashCode());
        result = prime * result + ((getValue11() == null) ? 0 : getValue11().hashCode());
        result = prime * result + ((getValue12() == null) ? 0 : getValue12().hashCode());
        result = prime * result + ((getValue13() == null) ? 0 : getValue13().hashCode());
        result = prime * result + ((getValue14() == null) ? 0 : getValue14().hashCode());
        result = prime * result + ((getValue15() == null) ? 0 : getValue15().hashCode());
        result = prime * result + ((getValue16() == null) ? 0 : getValue16().hashCode());
        result = prime * result + ((getValue17() == null) ? 0 : getValue17().hashCode());
        result = prime * result + ((getValue18() == null) ? 0 : getValue18().hashCode());
        result = prime * result + ((getValue19() == null) ? 0 : getValue19().hashCode());
        result = prime * result + ((getValue20() == null) ? 0 : getValue20().hashCode());
        result = prime * result + ((getValue21() == null) ? 0 : getValue21().hashCode());
        result = prime * result + ((getValue22() == null) ? 0 : getValue22().hashCode());
        result = prime * result + ((getValue23() == null) ? 0 : getValue23().hashCode());
        result = prime * result + ((getValue24() == null) ? 0 : getValue24().hashCode());
        result = prime * result + ((getValue25() == null) ? 0 : getValue25().hashCode());
        result = prime * result + ((getValue26() == null) ? 0 : getValue26().hashCode());
        result = prime * result + ((getValue27() == null) ? 0 : getValue27().hashCode());
        result = prime * result + ((getValue28() == null) ? 0 : getValue28().hashCode());
        result = prime * result + ((getValue29() == null) ? 0 : getValue29().hashCode());
        result = prime * result + ((getValue30() == null) ? 0 : getValue30().hashCode());
        result = prime * result + ((getValue31() == null) ? 0 : getValue31().hashCode());
        result = prime * result + ((getValue32() == null) ? 0 : getValue32().hashCode());
        result = prime * result + ((getValue33() == null) ? 0 : getValue33().hashCode());
        result = prime * result + ((getValue34() == null) ? 0 : getValue34().hashCode());
        result = prime * result + ((getValue35() == null) ? 0 : getValue35().hashCode());
        result = prime * result + ((getValue36() == null) ? 0 : getValue36().hashCode());
        result = prime * result + ((getValue37() == null) ? 0 : getValue37().hashCode());
        result = prime * result + ((getValue38() == null) ? 0 : getValue38().hashCode());
        result = prime * result + ((getValue39() == null) ? 0 : getValue39().hashCode());
        result = prime * result + ((getValue40() == null) ? 0 : getValue40().hashCode());
        result = prime * result + ((getValue41() == null) ? 0 : getValue41().hashCode());
        result = prime * result + ((getValue42() == null) ? 0 : getValue42().hashCode());
        result = prime * result + ((getValue43() == null) ? 0 : getValue43().hashCode());
        result = prime * result + ((getValue44() == null) ? 0 : getValue44().hashCode());
        result = prime * result + ((getValue45() == null) ? 0 : getValue45().hashCode());
        result = prime * result + ((getValue46() == null) ? 0 : getValue46().hashCode());
        result = prime * result + ((getValue47() == null) ? 0 : getValue47().hashCode());
        result = prime * result + ((getValue48() == null) ? 0 : getValue48().hashCode());
        result = prime * result + ((getValue49() == null) ? 0 : getValue49().hashCode());
        result = prime * result + ((getValue50() == null) ? 0 : getValue50().hashCode());
        result = prime * result + ((getEnabledFlag() == null) ? 0 : getEnabledFlag().hashCode());
        result = prime * result + ((getDateFrom() == null) ? 0 : getDateFrom().hashCode());
        result = prime * result + ((getDateTo() == null) ? 0 : getDateTo().hashCode());
        result = prime * result + ((getLang() == null) ? 0 : getLang().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getLastUpdateDate() == null) ? 0 : getLastUpdateDate().hashCode());
        result = prime * result + ((getLastUpdatedBy() == null) ? 0 : getLastUpdatedBy().hashCode());
        result = prime * result + ((getLastUpdateLogin() == null) ? 0 : getLastUpdateLogin().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreationDate() == null) ? 0 : getCreationDate().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", headerId=").append(headerId);
        sb.append(", value1=").append(value1);
        sb.append(", value2=").append(value2);
        sb.append(", value3=").append(value3);
        sb.append(", value4=").append(value4);
        sb.append(", value5=").append(value5);
        sb.append(", value6=").append(value6);
        sb.append(", value7=").append(value7);
        sb.append(", value8=").append(value8);
        sb.append(", value9=").append(value9);
        sb.append(", value10=").append(value10);
        sb.append(", value11=").append(value11);
        sb.append(", value12=").append(value12);
        sb.append(", value13=").append(value13);
        sb.append(", value14=").append(value14);
        sb.append(", value15=").append(value15);
        sb.append(", value16=").append(value16);
        sb.append(", value17=").append(value17);
        sb.append(", value18=").append(value18);
        sb.append(", value19=").append(value19);
        sb.append(", value20=").append(value20);
        sb.append(", value21=").append(value21);
        sb.append(", value22=").append(value22);
        sb.append(", value23=").append(value23);
        sb.append(", value24=").append(value24);
        sb.append(", value25=").append(value25);
        sb.append(", value26=").append(value26);
        sb.append(", value27=").append(value27);
        sb.append(", value28=").append(value28);
        sb.append(", value29=").append(value29);
        sb.append(", value30=").append(value30);
        sb.append(", value31=").append(value31);
        sb.append(", value32=").append(value32);
        sb.append(", value33=").append(value33);
        sb.append(", value34=").append(value34);
        sb.append(", value35=").append(value35);
        sb.append(", value36=").append(value36);
        sb.append(", value37=").append(value37);
        sb.append(", value38=").append(value38);
        sb.append(", value39=").append(value39);
        sb.append(", value40=").append(value40);
        sb.append(", value41=").append(value41);
        sb.append(", value42=").append(value42);
        sb.append(", value43=").append(value43);
        sb.append(", value44=").append(value44);
        sb.append(", value45=").append(value45);
        sb.append(", value46=").append(value46);
        sb.append(", value47=").append(value47);
        sb.append(", value48=").append(value48);
        sb.append(", value49=").append(value49);
        sb.append(", value50=").append(value50);
        sb.append(", enabledFlag=").append(enabledFlag);
        sb.append(", dateFrom=").append(dateFrom);
        sb.append(", dateTo=").append(dateTo);
        sb.append(", lang=").append(lang);
        sb.append(", version=").append(version);
        sb.append(", lastUpdateDate=").append(lastUpdateDate);
        sb.append(", lastUpdatedBy=").append(lastUpdatedBy);
        sb.append(", lastUpdateLogin=").append(lastUpdateLogin);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", creationDate=").append(creationDate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}