package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer;

import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabHeadersPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValueExtraPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePOExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CuxTwoTabValuesExtraMapper {
    int batchInsert(@Param("list") List<CuxTwoTabValuePO> list);

    // Check if record exists based on unique fields and headerId
    List<CuxTwoTabValuePO> findByUniqueFields(@Param("headerId") Long headerId,
                                             @Param("lang") String lang,
                                             @Param("uniqueFields") List<String> uniqueFields,
                                             @Param("values") List<String> values);
    List<CuxTwoTabValuePO> selectByFormCode(@Param("formCode") String formCode);
    List<CuxTwoTabValueExtraPO> selectAllByFormCode();
}
