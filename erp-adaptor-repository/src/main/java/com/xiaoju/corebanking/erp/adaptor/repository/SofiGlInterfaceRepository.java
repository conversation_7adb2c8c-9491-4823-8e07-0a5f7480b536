package com.xiaoju.corebanking.erp.adaptor.repository;


import com.xiaoju.corebanking.erp.adaptor.repository.domain.CargosAndAbonosDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiEmailDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderQueryDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfacePO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfacePOExample;

import java.util.List;
import java.util.Map;

public interface SofiGlInterfaceRepository {

    void insertSelective(SofiGlInterfaceDO sofiGlInterfaceDO);

    List<SofiGlInterfaceDO> selectByProcessDayAndStatus(String processDay, String code);

    int countByProcessDayAndStatus(String processDay, String status);

    List<SofiGlInterfacePO> selectByPolizaIdAndDetalle(Long polizaId, Long detallePolizaId);

    void batchInsertInterface(List<SofiGlInterfaceDO> records);

    List<SofiGlInterfaceDO> selectSofiGlInterfaceByExternalReference(List<Long> externalReference);

    List<SofiGlInterfaceDO> selectSofiGlInterfaceByPolizaIdsAndProcessDay(List<String> externalReferenceList, String processDay);

    /**
     * 更新
     */
    void updateInterface(SofiGlInterfaceDO sofiGlInterfaceDO, SofiGlInterfaceDO update);

    /**
     * 更新
     */
    void updateInterfaceByPolizaId(SofiGlInterfaceDO sofiGlInterfaceDO, SofiGlInterfaceDO update);

    List<Long> queryGroupId(SofiGlInterfaceHeaderQueryDO sofiGlInterfaceHeaderQueryDO);

    List<SofiGlInterfaceDO> queryGLInterfaceData(SofiGlInterfaceDO sofiGlInterfaceDO);

    /**
     * 根据条件更新
     */
    int updateByExampleSelective(SofiGlInterfacePO record, SofiGlInterfacePOExample example);

    CargosAndAbonosDO selectCargosAndAbonosDO(Long polizaId);

    List<SofiGlInterfaceDO> selectByPolizaIdAndDay(Long polizaId, String processDay, Long groupId);

    List<SofiEmailDO> selectCountInterfaceEmailData(String processDay);

    List<Map<String, Object>> selectGroupByPolizaId(String processDay, String status);

    List<SofiGlInterfaceDO> selectSofiGlInterfaceByGroupIdAndProcessDay(Long groupId, String processDay);


}
