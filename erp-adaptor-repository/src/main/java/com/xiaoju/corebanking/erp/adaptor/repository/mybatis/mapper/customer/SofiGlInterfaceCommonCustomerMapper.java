package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer;

import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceCommonPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfaceCommonPOMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SofiGlInterfaceCommonCustomerMapper extends SofiGlInterfaceCommonPOMapper {
    int batchInsert(@Param("list") List<SofiGlInterfaceCommonPO> list);

    List<Long> selectGroupIds(@Param("processDay") String processDay);

    List<SofiGlInterfaceCommonPO> selectSofiGlInterfaceCommonDOList(@Param("systemCode") String systemCode, @Param("processDay") String processDay, @Param("groupId") Long groupId);

    int updateBySystemCodeAndProcessDayAndGroupIdAndStatus(@Param("systemCode") String systemCode, @Param("processDay") String processDay, @Param("groupId") Long groupId,@Param("processStatus") String processStatus,@Param("sofiGlInterfaceCommonPO") SofiGlInterfaceCommonPO sofiGlInterfaceCommonPO);

    /**
     * 根据reference5列表和处理日期查询数据
     */
    List<SofiGlInterfaceCommonPO> queryByReference5AndProcessDay(@Param("externalReferenceList") List<String> externalReferenceList, @Param("processDay") String processDay);

    /**
     * 根据ID和处理日期更新数据
     */
    int updateByIdAndProcessDay(@Param("id") Long id, @Param("processDay") String processDay, @Param("updatePO") SofiGlInterfaceCommonPO updatePO);

    int deleteByProcessDay(@Param("processDay") String processDay);
}