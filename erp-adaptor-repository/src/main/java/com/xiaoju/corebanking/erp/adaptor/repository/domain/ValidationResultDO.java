package com.xiaoju.corebanking.erp.adaptor.repository.domain;

import lombok.Data;

@Data
public class ValidationResultDO {
    private String segment1;
    private String segment2;
    private String segment3;
    private String segment4;
    private String segment5;
    private String segment6;
    private String segment7;
    private String segment8;
    private String segment9;
    private String segment10;
    private String ledgerName;
    private String status;
    private String ccId;
    private String error;
    private String errorCode;
    private String fromDate;


    public boolean isValid() {
        return "Valid".equals(status) && !"-1".equals(ccId);
    }
}
