package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.ValidationRequestDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.ValidationResultDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CoaCustomerMapper {

    List<ValidationRequestDO> selectCoaListNoGroup(@Param("processDay") String processDay);

    List<ValidationRequestDO> selectCoaList(@Param("processDay") String processDay);

    List<ValidationRequestDO> selectShenMaCoaList(@Param("processDay") String processDay);

    List<Long> selectIdListByCoa(@Param("processDay") String processDay,
                                 @Param("processStatus") String processStatus,
                                 @Param("coa") ValidationResultDO coa);
    List<Long> selectInterfaceCommonIdListByCoa(@Param("processDay") String processDay,
                                                @Param("processStatus") String processStatus,
                                                @Param("coa") ValidationResultDO coa);
    List<Long> selectShenmaIdListByCoa(@Param("processDay") String processDay,
                                                @Param("processStatus") String processStatus,
                                                @Param("coa") ValidationResultDO coa);
    int updateCoaStatus(@Param("processDay") String processDay,
                        @Param("coa") ValidationResultDO coa,
                        @Param("processStatus") String processStatus,
                        @Param("processMessage") String processMessage);

    int updateCoaStatusById(@Param("processDay") String processDay,
                            @Param("systemCode") String systemCode,
                            @Param("id") Long id,
                            @Param("processStatus") String processStatus,
                            @Param("processMessage") String processMessage);

    int updateInterfaceCommonCoaStatusById(@Param("processDay") String processDay,
                            @Param("systemCode") String systemCode,
                            @Param("id") Long id,
                            @Param("processStatus") String processStatus,
                            @Param("processMessage") String processMessage);

    int updateShenmaCoaStatusById(@Param("id") Long id,
                                           @Param("processStatus") String processStatus,
                                           @Param("processMessage") String processMessage);

    int updateSafiHeaderStatus(@Param("processDay") String processDay);

    int updateShenMaHeaderStatus(@Param("processDay") String processDay);

}
