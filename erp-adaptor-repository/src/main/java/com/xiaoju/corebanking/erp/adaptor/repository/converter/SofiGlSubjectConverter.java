package com.xiaoju.corebanking.erp.adaptor.repository.converter;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlSubjectDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlSubjectPO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/8/7 17:06
 */
@Component
public class SofiGlSubjectConverter {

    /**
     * DO转PO
     */
    public SofiGlSubjectPO toPO(SofiGlSubjectDO sofiGlSubjectDO) {
        if (sofiGlSubjectDO == null) {
            return null;
        }

        SofiGlSubjectPO po = new SofiGlSubjectPO();
        BeanUtils.copyProperties(sofiGlSubjectDO, po);
        return po;
    }

    /**
     * PO转DO
     */
    public SofiGlSubjectDO toDO(SofiGlSubjectPO sofiGlSubjectPO) {
        if (sofiGlSubjectPO == null) {
            return null;
        }

        SofiGlSubjectDO sofiGlSubjectDO = new SofiGlSubjectDO();
        BeanUtils.copyProperties(sofiGlSubjectPO, sofiGlSubjectDO);
        return sofiGlSubjectDO;
    }
}
