package com.xiaoju.corebanking.erp.adaptor.repository.domain;


import lombok.Data;

/**
 * 文件字段
 **/
@Data
public class FileDataDO {
    // 序号
    private String DetallePolizaID;
    // 公司 ID
    private String EmpresaID;
    // 保单 ID
    private String PolizaID;
    // 日期
    private String Fecha;
    // 成本中心ID
    private String CentroCostoID;
    // 完整账户
    private String CuentaCompleta;
    // 金融工具
    private String Instrumento;
    // 货币ID
    private String MonedaID;
    // 借方金额
    private String Cargos;
    // 贷方金额
    private String Abonos;
    // 描述
    private String Descripcion;
    // 参考
    private String Referencia;
    // 会计程序
    private String ProcedimientoCont;
    // 金融工具类型
    private String TipoInstrumentoID;
    // 注册地址
    private String RFC;
    // 总发票金额
    private String TotalFactura;
    // 发票 UUID
    private String FolioUUID;
    // 用户
    private String Usuario;
    // 实际日期
    private String FechaActual;
    // IP 地址
    private String DireccionIP;
    // 程序 ID
    private String ProgramaID;
    // 分公司
    private String Sucursal;
    // 交易编号
    private String NumTransaccion;
}