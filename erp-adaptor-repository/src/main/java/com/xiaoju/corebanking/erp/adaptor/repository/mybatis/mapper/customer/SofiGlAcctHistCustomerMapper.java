package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.SummaryResultDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlAcctHistPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlAcctHistPOMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SofiGlAcctHistCustomerMapper extends SofiGlAcctHistPOMapper {
    List<SummaryResultDO> selectAcctHisSummary(@Param("processDay") String processDay);
    int deleteByProcessDay(@Param("processDay") String processDay);
}