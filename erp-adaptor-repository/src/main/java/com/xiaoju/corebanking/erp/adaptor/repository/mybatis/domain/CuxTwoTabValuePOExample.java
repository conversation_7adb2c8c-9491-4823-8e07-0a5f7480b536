package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class CuxTwoTabValuePOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public CuxTwoTabValuePOExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andHeaderIdIsNull() {
            addCriterion("header_id is null");
            return (Criteria) this;
        }

        public Criteria andHeaderIdIsNotNull() {
            addCriterion("header_id is not null");
            return (Criteria) this;
        }

        public Criteria andHeaderIdEqualTo(Long value) {
            addCriterion("header_id =", value, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdNotEqualTo(Long value) {
            addCriterion("header_id <>", value, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdGreaterThan(Long value) {
            addCriterion("header_id >", value, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdGreaterThanOrEqualTo(Long value) {
            addCriterion("header_id >=", value, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdLessThan(Long value) {
            addCriterion("header_id <", value, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdLessThanOrEqualTo(Long value) {
            addCriterion("header_id <=", value, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdIn(List<Long> values) {
            addCriterion("header_id in", values, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdNotIn(List<Long> values) {
            addCriterion("header_id not in", values, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdBetween(Long value1, Long value2) {
            addCriterion("header_id between", value1, value2, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdNotBetween(Long value1, Long value2) {
            addCriterion("header_id not between", value1, value2, "headerId");
            return (Criteria) this;
        }

        public Criteria andValue1IsNull() {
            addCriterion("value1 is null");
            return (Criteria) this;
        }

        public Criteria andValue1IsNotNull() {
            addCriterion("value1 is not null");
            return (Criteria) this;
        }

        public Criteria andValue1EqualTo(String value) {
            addCriterion("value1 =", value, "value1");
            return (Criteria) this;
        }

        public Criteria andValue1NotEqualTo(String value) {
            addCriterion("value1 <>", value, "value1");
            return (Criteria) this;
        }

        public Criteria andValue1GreaterThan(String value) {
            addCriterion("value1 >", value, "value1");
            return (Criteria) this;
        }

        public Criteria andValue1GreaterThanOrEqualTo(String value) {
            addCriterion("value1 >=", value, "value1");
            return (Criteria) this;
        }

        public Criteria andValue1LessThan(String value) {
            addCriterion("value1 <", value, "value1");
            return (Criteria) this;
        }

        public Criteria andValue1LessThanOrEqualTo(String value) {
            addCriterion("value1 <=", value, "value1");
            return (Criteria) this;
        }

        public Criteria andValue1Like(String value) {
            addCriterion("value1 like", value, "value1");
            return (Criteria) this;
        }

        public Criteria andValue1NotLike(String value) {
            addCriterion("value1 not like", value, "value1");
            return (Criteria) this;
        }

        public Criteria andValue1In(List<String> values) {
            addCriterion("value1 in", values, "value1");
            return (Criteria) this;
        }

        public Criteria andValue1NotIn(List<String> values) {
            addCriterion("value1 not in", values, "value1");
            return (Criteria) this;
        }

        public Criteria andValue1Between(String value1, String value2) {
            addCriterion("value1 between", value1, value2, "value1");
            return (Criteria) this;
        }

        public Criteria andValue1NotBetween(String value1, String value2) {
            addCriterion("value1 not between", value1, value2, "value1");
            return (Criteria) this;
        }

        public Criteria andValue2IsNull() {
            addCriterion("value2 is null");
            return (Criteria) this;
        }

        public Criteria andValue2IsNotNull() {
            addCriterion("value2 is not null");
            return (Criteria) this;
        }

        public Criteria andValue2EqualTo(String value) {
            addCriterion("value2 =", value, "value2");
            return (Criteria) this;
        }

        public Criteria andValue2NotEqualTo(String value) {
            addCriterion("value2 <>", value, "value2");
            return (Criteria) this;
        }

        public Criteria andValue2GreaterThan(String value) {
            addCriterion("value2 >", value, "value2");
            return (Criteria) this;
        }

        public Criteria andValue2GreaterThanOrEqualTo(String value) {
            addCriterion("value2 >=", value, "value2");
            return (Criteria) this;
        }

        public Criteria andValue2LessThan(String value) {
            addCriterion("value2 <", value, "value2");
            return (Criteria) this;
        }

        public Criteria andValue2LessThanOrEqualTo(String value) {
            addCriterion("value2 <=", value, "value2");
            return (Criteria) this;
        }

        public Criteria andValue2Like(String value) {
            addCriterion("value2 like", value, "value2");
            return (Criteria) this;
        }

        public Criteria andValue2NotLike(String value) {
            addCriterion("value2 not like", value, "value2");
            return (Criteria) this;
        }

        public Criteria andValue2In(List<String> values) {
            addCriterion("value2 in", values, "value2");
            return (Criteria) this;
        }

        public Criteria andValue2NotIn(List<String> values) {
            addCriterion("value2 not in", values, "value2");
            return (Criteria) this;
        }

        public Criteria andValue2Between(String value1, String value2) {
            addCriterion("value2 between", value1, value2, "value2");
            return (Criteria) this;
        }

        public Criteria andValue2NotBetween(String value1, String value2) {
            addCriterion("value2 not between", value1, value2, "value2");
            return (Criteria) this;
        }

        public Criteria andValue3IsNull() {
            addCriterion("value3 is null");
            return (Criteria) this;
        }

        public Criteria andValue3IsNotNull() {
            addCriterion("value3 is not null");
            return (Criteria) this;
        }

        public Criteria andValue3EqualTo(String value) {
            addCriterion("value3 =", value, "value3");
            return (Criteria) this;
        }

        public Criteria andValue3NotEqualTo(String value) {
            addCriterion("value3 <>", value, "value3");
            return (Criteria) this;
        }

        public Criteria andValue3GreaterThan(String value) {
            addCriterion("value3 >", value, "value3");
            return (Criteria) this;
        }

        public Criteria andValue3GreaterThanOrEqualTo(String value) {
            addCriterion("value3 >=", value, "value3");
            return (Criteria) this;
        }

        public Criteria andValue3LessThan(String value) {
            addCriterion("value3 <", value, "value3");
            return (Criteria) this;
        }

        public Criteria andValue3LessThanOrEqualTo(String value) {
            addCriterion("value3 <=", value, "value3");
            return (Criteria) this;
        }

        public Criteria andValue3Like(String value) {
            addCriterion("value3 like", value, "value3");
            return (Criteria) this;
        }

        public Criteria andValue3NotLike(String value) {
            addCriterion("value3 not like", value, "value3");
            return (Criteria) this;
        }

        public Criteria andValue3In(List<String> values) {
            addCriterion("value3 in", values, "value3");
            return (Criteria) this;
        }

        public Criteria andValue3NotIn(List<String> values) {
            addCriterion("value3 not in", values, "value3");
            return (Criteria) this;
        }

        public Criteria andValue3Between(String value1, String value2) {
            addCriterion("value3 between", value1, value2, "value3");
            return (Criteria) this;
        }

        public Criteria andValue3NotBetween(String value1, String value2) {
            addCriterion("value3 not between", value1, value2, "value3");
            return (Criteria) this;
        }

        public Criteria andValue4IsNull() {
            addCriterion("value4 is null");
            return (Criteria) this;
        }

        public Criteria andValue4IsNotNull() {
            addCriterion("value4 is not null");
            return (Criteria) this;
        }

        public Criteria andValue4EqualTo(String value) {
            addCriterion("value4 =", value, "value4");
            return (Criteria) this;
        }

        public Criteria andValue4NotEqualTo(String value) {
            addCriterion("value4 <>", value, "value4");
            return (Criteria) this;
        }

        public Criteria andValue4GreaterThan(String value) {
            addCriterion("value4 >", value, "value4");
            return (Criteria) this;
        }

        public Criteria andValue4GreaterThanOrEqualTo(String value) {
            addCriterion("value4 >=", value, "value4");
            return (Criteria) this;
        }

        public Criteria andValue4LessThan(String value) {
            addCriterion("value4 <", value, "value4");
            return (Criteria) this;
        }

        public Criteria andValue4LessThanOrEqualTo(String value) {
            addCriterion("value4 <=", value, "value4");
            return (Criteria) this;
        }

        public Criteria andValue4Like(String value) {
            addCriterion("value4 like", value, "value4");
            return (Criteria) this;
        }

        public Criteria andValue4NotLike(String value) {
            addCriterion("value4 not like", value, "value4");
            return (Criteria) this;
        }

        public Criteria andValue4In(List<String> values) {
            addCriterion("value4 in", values, "value4");
            return (Criteria) this;
        }

        public Criteria andValue4NotIn(List<String> values) {
            addCriterion("value4 not in", values, "value4");
            return (Criteria) this;
        }

        public Criteria andValue4Between(String value1, String value2) {
            addCriterion("value4 between", value1, value2, "value4");
            return (Criteria) this;
        }

        public Criteria andValue4NotBetween(String value1, String value2) {
            addCriterion("value4 not between", value1, value2, "value4");
            return (Criteria) this;
        }

        public Criteria andValue5IsNull() {
            addCriterion("value5 is null");
            return (Criteria) this;
        }

        public Criteria andValue5IsNotNull() {
            addCriterion("value5 is not null");
            return (Criteria) this;
        }

        public Criteria andValue5EqualTo(String value) {
            addCriterion("value5 =", value, "value5");
            return (Criteria) this;
        }

        public Criteria andValue5NotEqualTo(String value) {
            addCriterion("value5 <>", value, "value5");
            return (Criteria) this;
        }

        public Criteria andValue5GreaterThan(String value) {
            addCriterion("value5 >", value, "value5");
            return (Criteria) this;
        }

        public Criteria andValue5GreaterThanOrEqualTo(String value) {
            addCriterion("value5 >=", value, "value5");
            return (Criteria) this;
        }

        public Criteria andValue5LessThan(String value) {
            addCriterion("value5 <", value, "value5");
            return (Criteria) this;
        }

        public Criteria andValue5LessThanOrEqualTo(String value) {
            addCriterion("value5 <=", value, "value5");
            return (Criteria) this;
        }

        public Criteria andValue5Like(String value) {
            addCriterion("value5 like", value, "value5");
            return (Criteria) this;
        }

        public Criteria andValue5NotLike(String value) {
            addCriterion("value5 not like", value, "value5");
            return (Criteria) this;
        }

        public Criteria andValue5In(List<String> values) {
            addCriterion("value5 in", values, "value5");
            return (Criteria) this;
        }

        public Criteria andValue5NotIn(List<String> values) {
            addCriterion("value5 not in", values, "value5");
            return (Criteria) this;
        }

        public Criteria andValue5Between(String value1, String value2) {
            addCriterion("value5 between", value1, value2, "value5");
            return (Criteria) this;
        }

        public Criteria andValue5NotBetween(String value1, String value2) {
            addCriterion("value5 not between", value1, value2, "value5");
            return (Criteria) this;
        }

        public Criteria andValue6IsNull() {
            addCriterion("value6 is null");
            return (Criteria) this;
        }

        public Criteria andValue6IsNotNull() {
            addCriterion("value6 is not null");
            return (Criteria) this;
        }

        public Criteria andValue6EqualTo(String value) {
            addCriterion("value6 =", value, "value6");
            return (Criteria) this;
        }

        public Criteria andValue6NotEqualTo(String value) {
            addCriterion("value6 <>", value, "value6");
            return (Criteria) this;
        }

        public Criteria andValue6GreaterThan(String value) {
            addCriterion("value6 >", value, "value6");
            return (Criteria) this;
        }

        public Criteria andValue6GreaterThanOrEqualTo(String value) {
            addCriterion("value6 >=", value, "value6");
            return (Criteria) this;
        }

        public Criteria andValue6LessThan(String value) {
            addCriterion("value6 <", value, "value6");
            return (Criteria) this;
        }

        public Criteria andValue6LessThanOrEqualTo(String value) {
            addCriterion("value6 <=", value, "value6");
            return (Criteria) this;
        }

        public Criteria andValue6Like(String value) {
            addCriterion("value6 like", value, "value6");
            return (Criteria) this;
        }

        public Criteria andValue6NotLike(String value) {
            addCriterion("value6 not like", value, "value6");
            return (Criteria) this;
        }

        public Criteria andValue6In(List<String> values) {
            addCriterion("value6 in", values, "value6");
            return (Criteria) this;
        }

        public Criteria andValue6NotIn(List<String> values) {
            addCriterion("value6 not in", values, "value6");
            return (Criteria) this;
        }

        public Criteria andValue6Between(String value1, String value2) {
            addCriterion("value6 between", value1, value2, "value6");
            return (Criteria) this;
        }

        public Criteria andValue6NotBetween(String value1, String value2) {
            addCriterion("value6 not between", value1, value2, "value6");
            return (Criteria) this;
        }

        public Criteria andValue7IsNull() {
            addCriterion("value7 is null");
            return (Criteria) this;
        }

        public Criteria andValue7IsNotNull() {
            addCriterion("value7 is not null");
            return (Criteria) this;
        }

        public Criteria andValue7EqualTo(String value) {
            addCriterion("value7 =", value, "value7");
            return (Criteria) this;
        }

        public Criteria andValue7NotEqualTo(String value) {
            addCriterion("value7 <>", value, "value7");
            return (Criteria) this;
        }

        public Criteria andValue7GreaterThan(String value) {
            addCriterion("value7 >", value, "value7");
            return (Criteria) this;
        }

        public Criteria andValue7GreaterThanOrEqualTo(String value) {
            addCriterion("value7 >=", value, "value7");
            return (Criteria) this;
        }

        public Criteria andValue7LessThan(String value) {
            addCriterion("value7 <", value, "value7");
            return (Criteria) this;
        }

        public Criteria andValue7LessThanOrEqualTo(String value) {
            addCriterion("value7 <=", value, "value7");
            return (Criteria) this;
        }

        public Criteria andValue7Like(String value) {
            addCriterion("value7 like", value, "value7");
            return (Criteria) this;
        }

        public Criteria andValue7NotLike(String value) {
            addCriterion("value7 not like", value, "value7");
            return (Criteria) this;
        }

        public Criteria andValue7In(List<String> values) {
            addCriterion("value7 in", values, "value7");
            return (Criteria) this;
        }

        public Criteria andValue7NotIn(List<String> values) {
            addCriterion("value7 not in", values, "value7");
            return (Criteria) this;
        }

        public Criteria andValue7Between(String value1, String value2) {
            addCriterion("value7 between", value1, value2, "value7");
            return (Criteria) this;
        }

        public Criteria andValue7NotBetween(String value1, String value2) {
            addCriterion("value7 not between", value1, value2, "value7");
            return (Criteria) this;
        }

        public Criteria andValue8IsNull() {
            addCriterion("value8 is null");
            return (Criteria) this;
        }

        public Criteria andValue8IsNotNull() {
            addCriterion("value8 is not null");
            return (Criteria) this;
        }

        public Criteria andValue8EqualTo(String value) {
            addCriterion("value8 =", value, "value8");
            return (Criteria) this;
        }

        public Criteria andValue8NotEqualTo(String value) {
            addCriterion("value8 <>", value, "value8");
            return (Criteria) this;
        }

        public Criteria andValue8GreaterThan(String value) {
            addCriterion("value8 >", value, "value8");
            return (Criteria) this;
        }

        public Criteria andValue8GreaterThanOrEqualTo(String value) {
            addCriterion("value8 >=", value, "value8");
            return (Criteria) this;
        }

        public Criteria andValue8LessThan(String value) {
            addCriterion("value8 <", value, "value8");
            return (Criteria) this;
        }

        public Criteria andValue8LessThanOrEqualTo(String value) {
            addCriterion("value8 <=", value, "value8");
            return (Criteria) this;
        }

        public Criteria andValue8Like(String value) {
            addCriterion("value8 like", value, "value8");
            return (Criteria) this;
        }

        public Criteria andValue8NotLike(String value) {
            addCriterion("value8 not like", value, "value8");
            return (Criteria) this;
        }

        public Criteria andValue8In(List<String> values) {
            addCriterion("value8 in", values, "value8");
            return (Criteria) this;
        }

        public Criteria andValue8NotIn(List<String> values) {
            addCriterion("value8 not in", values, "value8");
            return (Criteria) this;
        }

        public Criteria andValue8Between(String value1, String value2) {
            addCriterion("value8 between", value1, value2, "value8");
            return (Criteria) this;
        }

        public Criteria andValue8NotBetween(String value1, String value2) {
            addCriterion("value8 not between", value1, value2, "value8");
            return (Criteria) this;
        }

        public Criteria andValue9IsNull() {
            addCriterion("value9 is null");
            return (Criteria) this;
        }

        public Criteria andValue9IsNotNull() {
            addCriterion("value9 is not null");
            return (Criteria) this;
        }

        public Criteria andValue9EqualTo(String value) {
            addCriterion("value9 =", value, "value9");
            return (Criteria) this;
        }

        public Criteria andValue9NotEqualTo(String value) {
            addCriterion("value9 <>", value, "value9");
            return (Criteria) this;
        }

        public Criteria andValue9GreaterThan(String value) {
            addCriterion("value9 >", value, "value9");
            return (Criteria) this;
        }

        public Criteria andValue9GreaterThanOrEqualTo(String value) {
            addCriterion("value9 >=", value, "value9");
            return (Criteria) this;
        }

        public Criteria andValue9LessThan(String value) {
            addCriterion("value9 <", value, "value9");
            return (Criteria) this;
        }

        public Criteria andValue9LessThanOrEqualTo(String value) {
            addCriterion("value9 <=", value, "value9");
            return (Criteria) this;
        }

        public Criteria andValue9Like(String value) {
            addCriterion("value9 like", value, "value9");
            return (Criteria) this;
        }

        public Criteria andValue9NotLike(String value) {
            addCriterion("value9 not like", value, "value9");
            return (Criteria) this;
        }

        public Criteria andValue9In(List<String> values) {
            addCriterion("value9 in", values, "value9");
            return (Criteria) this;
        }

        public Criteria andValue9NotIn(List<String> values) {
            addCriterion("value9 not in", values, "value9");
            return (Criteria) this;
        }

        public Criteria andValue9Between(String value1, String value2) {
            addCriterion("value9 between", value1, value2, "value9");
            return (Criteria) this;
        }

        public Criteria andValue9NotBetween(String value1, String value2) {
            addCriterion("value9 not between", value1, value2, "value9");
            return (Criteria) this;
        }

        public Criteria andValue10IsNull() {
            addCriterion("value10 is null");
            return (Criteria) this;
        }

        public Criteria andValue10IsNotNull() {
            addCriterion("value10 is not null");
            return (Criteria) this;
        }

        public Criteria andValue10EqualTo(String value) {
            addCriterion("value10 =", value, "value10");
            return (Criteria) this;
        }

        public Criteria andValue10NotEqualTo(String value) {
            addCriterion("value10 <>", value, "value10");
            return (Criteria) this;
        }

        public Criteria andValue10GreaterThan(String value) {
            addCriterion("value10 >", value, "value10");
            return (Criteria) this;
        }

        public Criteria andValue10GreaterThanOrEqualTo(String value) {
            addCriterion("value10 >=", value, "value10");
            return (Criteria) this;
        }

        public Criteria andValue10LessThan(String value) {
            addCriterion("value10 <", value, "value10");
            return (Criteria) this;
        }

        public Criteria andValue10LessThanOrEqualTo(String value) {
            addCriterion("value10 <=", value, "value10");
            return (Criteria) this;
        }

        public Criteria andValue10Like(String value) {
            addCriterion("value10 like", value, "value10");
            return (Criteria) this;
        }

        public Criteria andValue10NotLike(String value) {
            addCriterion("value10 not like", value, "value10");
            return (Criteria) this;
        }

        public Criteria andValue10In(List<String> values) {
            addCriterion("value10 in", values, "value10");
            return (Criteria) this;
        }

        public Criteria andValue10NotIn(List<String> values) {
            addCriterion("value10 not in", values, "value10");
            return (Criteria) this;
        }

        public Criteria andValue10Between(String value1, String value2) {
            addCriterion("value10 between", value1, value2, "value10");
            return (Criteria) this;
        }

        public Criteria andValue10NotBetween(String value1, String value2) {
            addCriterion("value10 not between", value1, value2, "value10");
            return (Criteria) this;
        }

        public Criteria andValue11IsNull() {
            addCriterion("value11 is null");
            return (Criteria) this;
        }

        public Criteria andValue11IsNotNull() {
            addCriterion("value11 is not null");
            return (Criteria) this;
        }

        public Criteria andValue11EqualTo(String value) {
            addCriterion("value11 =", value, "value11");
            return (Criteria) this;
        }

        public Criteria andValue11NotEqualTo(String value) {
            addCriterion("value11 <>", value, "value11");
            return (Criteria) this;
        }

        public Criteria andValue11GreaterThan(String value) {
            addCriterion("value11 >", value, "value11");
            return (Criteria) this;
        }

        public Criteria andValue11GreaterThanOrEqualTo(String value) {
            addCriterion("value11 >=", value, "value11");
            return (Criteria) this;
        }

        public Criteria andValue11LessThan(String value) {
            addCriterion("value11 <", value, "value11");
            return (Criteria) this;
        }

        public Criteria andValue11LessThanOrEqualTo(String value) {
            addCriterion("value11 <=", value, "value11");
            return (Criteria) this;
        }

        public Criteria andValue11Like(String value) {
            addCriterion("value11 like", value, "value11");
            return (Criteria) this;
        }

        public Criteria andValue11NotLike(String value) {
            addCriterion("value11 not like", value, "value11");
            return (Criteria) this;
        }

        public Criteria andValue11In(List<String> values) {
            addCriterion("value11 in", values, "value11");
            return (Criteria) this;
        }

        public Criteria andValue11NotIn(List<String> values) {
            addCriterion("value11 not in", values, "value11");
            return (Criteria) this;
        }

        public Criteria andValue11Between(String value1, String value2) {
            addCriterion("value11 between", value1, value2, "value11");
            return (Criteria) this;
        }

        public Criteria andValue11NotBetween(String value1, String value2) {
            addCriterion("value11 not between", value1, value2, "value11");
            return (Criteria) this;
        }

        public Criteria andValue12IsNull() {
            addCriterion("value12 is null");
            return (Criteria) this;
        }

        public Criteria andValue12IsNotNull() {
            addCriterion("value12 is not null");
            return (Criteria) this;
        }

        public Criteria andValue12EqualTo(String value) {
            addCriterion("value12 =", value, "value12");
            return (Criteria) this;
        }

        public Criteria andValue12NotEqualTo(String value) {
            addCriterion("value12 <>", value, "value12");
            return (Criteria) this;
        }

        public Criteria andValue12GreaterThan(String value) {
            addCriterion("value12 >", value, "value12");
            return (Criteria) this;
        }

        public Criteria andValue12GreaterThanOrEqualTo(String value) {
            addCriterion("value12 >=", value, "value12");
            return (Criteria) this;
        }

        public Criteria andValue12LessThan(String value) {
            addCriterion("value12 <", value, "value12");
            return (Criteria) this;
        }

        public Criteria andValue12LessThanOrEqualTo(String value) {
            addCriterion("value12 <=", value, "value12");
            return (Criteria) this;
        }

        public Criteria andValue12Like(String value) {
            addCriterion("value12 like", value, "value12");
            return (Criteria) this;
        }

        public Criteria andValue12NotLike(String value) {
            addCriterion("value12 not like", value, "value12");
            return (Criteria) this;
        }

        public Criteria andValue12In(List<String> values) {
            addCriterion("value12 in", values, "value12");
            return (Criteria) this;
        }

        public Criteria andValue12NotIn(List<String> values) {
            addCriterion("value12 not in", values, "value12");
            return (Criteria) this;
        }

        public Criteria andValue12Between(String value1, String value2) {
            addCriterion("value12 between", value1, value2, "value12");
            return (Criteria) this;
        }

        public Criteria andValue12NotBetween(String value1, String value2) {
            addCriterion("value12 not between", value1, value2, "value12");
            return (Criteria) this;
        }

        public Criteria andValue13IsNull() {
            addCriterion("value13 is null");
            return (Criteria) this;
        }

        public Criteria andValue13IsNotNull() {
            addCriterion("value13 is not null");
            return (Criteria) this;
        }

        public Criteria andValue13EqualTo(String value) {
            addCriterion("value13 =", value, "value13");
            return (Criteria) this;
        }

        public Criteria andValue13NotEqualTo(String value) {
            addCriterion("value13 <>", value, "value13");
            return (Criteria) this;
        }

        public Criteria andValue13GreaterThan(String value) {
            addCriterion("value13 >", value, "value13");
            return (Criteria) this;
        }

        public Criteria andValue13GreaterThanOrEqualTo(String value) {
            addCriterion("value13 >=", value, "value13");
            return (Criteria) this;
        }

        public Criteria andValue13LessThan(String value) {
            addCriterion("value13 <", value, "value13");
            return (Criteria) this;
        }

        public Criteria andValue13LessThanOrEqualTo(String value) {
            addCriterion("value13 <=", value, "value13");
            return (Criteria) this;
        }

        public Criteria andValue13Like(String value) {
            addCriterion("value13 like", value, "value13");
            return (Criteria) this;
        }

        public Criteria andValue13NotLike(String value) {
            addCriterion("value13 not like", value, "value13");
            return (Criteria) this;
        }

        public Criteria andValue13In(List<String> values) {
            addCriterion("value13 in", values, "value13");
            return (Criteria) this;
        }

        public Criteria andValue13NotIn(List<String> values) {
            addCriterion("value13 not in", values, "value13");
            return (Criteria) this;
        }

        public Criteria andValue13Between(String value1, String value2) {
            addCriterion("value13 between", value1, value2, "value13");
            return (Criteria) this;
        }

        public Criteria andValue13NotBetween(String value1, String value2) {
            addCriterion("value13 not between", value1, value2, "value13");
            return (Criteria) this;
        }

        public Criteria andValue14IsNull() {
            addCriterion("value14 is null");
            return (Criteria) this;
        }

        public Criteria andValue14IsNotNull() {
            addCriterion("value14 is not null");
            return (Criteria) this;
        }

        public Criteria andValue14EqualTo(String value) {
            addCriterion("value14 =", value, "value14");
            return (Criteria) this;
        }

        public Criteria andValue14NotEqualTo(String value) {
            addCriterion("value14 <>", value, "value14");
            return (Criteria) this;
        }

        public Criteria andValue14GreaterThan(String value) {
            addCriterion("value14 >", value, "value14");
            return (Criteria) this;
        }

        public Criteria andValue14GreaterThanOrEqualTo(String value) {
            addCriterion("value14 >=", value, "value14");
            return (Criteria) this;
        }

        public Criteria andValue14LessThan(String value) {
            addCriterion("value14 <", value, "value14");
            return (Criteria) this;
        }

        public Criteria andValue14LessThanOrEqualTo(String value) {
            addCriterion("value14 <=", value, "value14");
            return (Criteria) this;
        }

        public Criteria andValue14Like(String value) {
            addCriterion("value14 like", value, "value14");
            return (Criteria) this;
        }

        public Criteria andValue14NotLike(String value) {
            addCriterion("value14 not like", value, "value14");
            return (Criteria) this;
        }

        public Criteria andValue14In(List<String> values) {
            addCriterion("value14 in", values, "value14");
            return (Criteria) this;
        }

        public Criteria andValue14NotIn(List<String> values) {
            addCriterion("value14 not in", values, "value14");
            return (Criteria) this;
        }

        public Criteria andValue14Between(String value1, String value2) {
            addCriterion("value14 between", value1, value2, "value14");
            return (Criteria) this;
        }

        public Criteria andValue14NotBetween(String value1, String value2) {
            addCriterion("value14 not between", value1, value2, "value14");
            return (Criteria) this;
        }

        public Criteria andValue15IsNull() {
            addCriterion("value15 is null");
            return (Criteria) this;
        }

        public Criteria andValue15IsNotNull() {
            addCriterion("value15 is not null");
            return (Criteria) this;
        }

        public Criteria andValue15EqualTo(String value) {
            addCriterion("value15 =", value, "value15");
            return (Criteria) this;
        }

        public Criteria andValue15NotEqualTo(String value) {
            addCriterion("value15 <>", value, "value15");
            return (Criteria) this;
        }

        public Criteria andValue15GreaterThan(String value) {
            addCriterion("value15 >", value, "value15");
            return (Criteria) this;
        }

        public Criteria andValue15GreaterThanOrEqualTo(String value) {
            addCriterion("value15 >=", value, "value15");
            return (Criteria) this;
        }

        public Criteria andValue15LessThan(String value) {
            addCriterion("value15 <", value, "value15");
            return (Criteria) this;
        }

        public Criteria andValue15LessThanOrEqualTo(String value) {
            addCriterion("value15 <=", value, "value15");
            return (Criteria) this;
        }

        public Criteria andValue15Like(String value) {
            addCriterion("value15 like", value, "value15");
            return (Criteria) this;
        }

        public Criteria andValue15NotLike(String value) {
            addCriterion("value15 not like", value, "value15");
            return (Criteria) this;
        }

        public Criteria andValue15In(List<String> values) {
            addCriterion("value15 in", values, "value15");
            return (Criteria) this;
        }

        public Criteria andValue15NotIn(List<String> values) {
            addCriterion("value15 not in", values, "value15");
            return (Criteria) this;
        }

        public Criteria andValue15Between(String value1, String value2) {
            addCriterion("value15 between", value1, value2, "value15");
            return (Criteria) this;
        }

        public Criteria andValue15NotBetween(String value1, String value2) {
            addCriterion("value15 not between", value1, value2, "value15");
            return (Criteria) this;
        }

        public Criteria andValue16IsNull() {
            addCriterion("value16 is null");
            return (Criteria) this;
        }

        public Criteria andValue16IsNotNull() {
            addCriterion("value16 is not null");
            return (Criteria) this;
        }

        public Criteria andValue16EqualTo(String value) {
            addCriterion("value16 =", value, "value16");
            return (Criteria) this;
        }

        public Criteria andValue16NotEqualTo(String value) {
            addCriterion("value16 <>", value, "value16");
            return (Criteria) this;
        }

        public Criteria andValue16GreaterThan(String value) {
            addCriterion("value16 >", value, "value16");
            return (Criteria) this;
        }

        public Criteria andValue16GreaterThanOrEqualTo(String value) {
            addCriterion("value16 >=", value, "value16");
            return (Criteria) this;
        }

        public Criteria andValue16LessThan(String value) {
            addCriterion("value16 <", value, "value16");
            return (Criteria) this;
        }

        public Criteria andValue16LessThanOrEqualTo(String value) {
            addCriterion("value16 <=", value, "value16");
            return (Criteria) this;
        }

        public Criteria andValue16Like(String value) {
            addCriterion("value16 like", value, "value16");
            return (Criteria) this;
        }

        public Criteria andValue16NotLike(String value) {
            addCriterion("value16 not like", value, "value16");
            return (Criteria) this;
        }

        public Criteria andValue16In(List<String> values) {
            addCriterion("value16 in", values, "value16");
            return (Criteria) this;
        }

        public Criteria andValue16NotIn(List<String> values) {
            addCriterion("value16 not in", values, "value16");
            return (Criteria) this;
        }

        public Criteria andValue16Between(String value1, String value2) {
            addCriterion("value16 between", value1, value2, "value16");
            return (Criteria) this;
        }

        public Criteria andValue16NotBetween(String value1, String value2) {
            addCriterion("value16 not between", value1, value2, "value16");
            return (Criteria) this;
        }

        public Criteria andValue17IsNull() {
            addCriterion("value17 is null");
            return (Criteria) this;
        }

        public Criteria andValue17IsNotNull() {
            addCriterion("value17 is not null");
            return (Criteria) this;
        }

        public Criteria andValue17EqualTo(String value) {
            addCriterion("value17 =", value, "value17");
            return (Criteria) this;
        }

        public Criteria andValue17NotEqualTo(String value) {
            addCriterion("value17 <>", value, "value17");
            return (Criteria) this;
        }

        public Criteria andValue17GreaterThan(String value) {
            addCriterion("value17 >", value, "value17");
            return (Criteria) this;
        }

        public Criteria andValue17GreaterThanOrEqualTo(String value) {
            addCriterion("value17 >=", value, "value17");
            return (Criteria) this;
        }

        public Criteria andValue17LessThan(String value) {
            addCriterion("value17 <", value, "value17");
            return (Criteria) this;
        }

        public Criteria andValue17LessThanOrEqualTo(String value) {
            addCriterion("value17 <=", value, "value17");
            return (Criteria) this;
        }

        public Criteria andValue17Like(String value) {
            addCriterion("value17 like", value, "value17");
            return (Criteria) this;
        }

        public Criteria andValue17NotLike(String value) {
            addCriterion("value17 not like", value, "value17");
            return (Criteria) this;
        }

        public Criteria andValue17In(List<String> values) {
            addCriterion("value17 in", values, "value17");
            return (Criteria) this;
        }

        public Criteria andValue17NotIn(List<String> values) {
            addCriterion("value17 not in", values, "value17");
            return (Criteria) this;
        }

        public Criteria andValue17Between(String value1, String value2) {
            addCriterion("value17 between", value1, value2, "value17");
            return (Criteria) this;
        }

        public Criteria andValue17NotBetween(String value1, String value2) {
            addCriterion("value17 not between", value1, value2, "value17");
            return (Criteria) this;
        }

        public Criteria andValue18IsNull() {
            addCriterion("value18 is null");
            return (Criteria) this;
        }

        public Criteria andValue18IsNotNull() {
            addCriterion("value18 is not null");
            return (Criteria) this;
        }

        public Criteria andValue18EqualTo(String value) {
            addCriterion("value18 =", value, "value18");
            return (Criteria) this;
        }

        public Criteria andValue18NotEqualTo(String value) {
            addCriterion("value18 <>", value, "value18");
            return (Criteria) this;
        }

        public Criteria andValue18GreaterThan(String value) {
            addCriterion("value18 >", value, "value18");
            return (Criteria) this;
        }

        public Criteria andValue18GreaterThanOrEqualTo(String value) {
            addCriterion("value18 >=", value, "value18");
            return (Criteria) this;
        }

        public Criteria andValue18LessThan(String value) {
            addCriterion("value18 <", value, "value18");
            return (Criteria) this;
        }

        public Criteria andValue18LessThanOrEqualTo(String value) {
            addCriterion("value18 <=", value, "value18");
            return (Criteria) this;
        }

        public Criteria andValue18Like(String value) {
            addCriterion("value18 like", value, "value18");
            return (Criteria) this;
        }

        public Criteria andValue18NotLike(String value) {
            addCriterion("value18 not like", value, "value18");
            return (Criteria) this;
        }

        public Criteria andValue18In(List<String> values) {
            addCriterion("value18 in", values, "value18");
            return (Criteria) this;
        }

        public Criteria andValue18NotIn(List<String> values) {
            addCriterion("value18 not in", values, "value18");
            return (Criteria) this;
        }

        public Criteria andValue18Between(String value1, String value2) {
            addCriterion("value18 between", value1, value2, "value18");
            return (Criteria) this;
        }

        public Criteria andValue18NotBetween(String value1, String value2) {
            addCriterion("value18 not between", value1, value2, "value18");
            return (Criteria) this;
        }

        public Criteria andValue19IsNull() {
            addCriterion("value19 is null");
            return (Criteria) this;
        }

        public Criteria andValue19IsNotNull() {
            addCriterion("value19 is not null");
            return (Criteria) this;
        }

        public Criteria andValue19EqualTo(String value) {
            addCriterion("value19 =", value, "value19");
            return (Criteria) this;
        }

        public Criteria andValue19NotEqualTo(String value) {
            addCriterion("value19 <>", value, "value19");
            return (Criteria) this;
        }

        public Criteria andValue19GreaterThan(String value) {
            addCriterion("value19 >", value, "value19");
            return (Criteria) this;
        }

        public Criteria andValue19GreaterThanOrEqualTo(String value) {
            addCriterion("value19 >=", value, "value19");
            return (Criteria) this;
        }

        public Criteria andValue19LessThan(String value) {
            addCriterion("value19 <", value, "value19");
            return (Criteria) this;
        }

        public Criteria andValue19LessThanOrEqualTo(String value) {
            addCriterion("value19 <=", value, "value19");
            return (Criteria) this;
        }

        public Criteria andValue19Like(String value) {
            addCriterion("value19 like", value, "value19");
            return (Criteria) this;
        }

        public Criteria andValue19NotLike(String value) {
            addCriterion("value19 not like", value, "value19");
            return (Criteria) this;
        }

        public Criteria andValue19In(List<String> values) {
            addCriterion("value19 in", values, "value19");
            return (Criteria) this;
        }

        public Criteria andValue19NotIn(List<String> values) {
            addCriterion("value19 not in", values, "value19");
            return (Criteria) this;
        }

        public Criteria andValue19Between(String value1, String value2) {
            addCriterion("value19 between", value1, value2, "value19");
            return (Criteria) this;
        }

        public Criteria andValue19NotBetween(String value1, String value2) {
            addCriterion("value19 not between", value1, value2, "value19");
            return (Criteria) this;
        }

        public Criteria andValue20IsNull() {
            addCriterion("value20 is null");
            return (Criteria) this;
        }

        public Criteria andValue20IsNotNull() {
            addCriterion("value20 is not null");
            return (Criteria) this;
        }

        public Criteria andValue20EqualTo(String value) {
            addCriterion("value20 =", value, "value20");
            return (Criteria) this;
        }

        public Criteria andValue20NotEqualTo(String value) {
            addCriterion("value20 <>", value, "value20");
            return (Criteria) this;
        }

        public Criteria andValue20GreaterThan(String value) {
            addCriterion("value20 >", value, "value20");
            return (Criteria) this;
        }

        public Criteria andValue20GreaterThanOrEqualTo(String value) {
            addCriterion("value20 >=", value, "value20");
            return (Criteria) this;
        }

        public Criteria andValue20LessThan(String value) {
            addCriterion("value20 <", value, "value20");
            return (Criteria) this;
        }

        public Criteria andValue20LessThanOrEqualTo(String value) {
            addCriterion("value20 <=", value, "value20");
            return (Criteria) this;
        }

        public Criteria andValue20Like(String value) {
            addCriterion("value20 like", value, "value20");
            return (Criteria) this;
        }

        public Criteria andValue20NotLike(String value) {
            addCriterion("value20 not like", value, "value20");
            return (Criteria) this;
        }

        public Criteria andValue20In(List<String> values) {
            addCriterion("value20 in", values, "value20");
            return (Criteria) this;
        }

        public Criteria andValue20NotIn(List<String> values) {
            addCriterion("value20 not in", values, "value20");
            return (Criteria) this;
        }

        public Criteria andValue20Between(String value1, String value2) {
            addCriterion("value20 between", value1, value2, "value20");
            return (Criteria) this;
        }

        public Criteria andValue20NotBetween(String value1, String value2) {
            addCriterion("value20 not between", value1, value2, "value20");
            return (Criteria) this;
        }

        public Criteria andValue21IsNull() {
            addCriterion("value21 is null");
            return (Criteria) this;
        }

        public Criteria andValue21IsNotNull() {
            addCriterion("value21 is not null");
            return (Criteria) this;
        }

        public Criteria andValue21EqualTo(String value) {
            addCriterion("value21 =", value, "value21");
            return (Criteria) this;
        }

        public Criteria andValue21NotEqualTo(String value) {
            addCriterion("value21 <>", value, "value21");
            return (Criteria) this;
        }

        public Criteria andValue21GreaterThan(String value) {
            addCriterion("value21 >", value, "value21");
            return (Criteria) this;
        }

        public Criteria andValue21GreaterThanOrEqualTo(String value) {
            addCriterion("value21 >=", value, "value21");
            return (Criteria) this;
        }

        public Criteria andValue21LessThan(String value) {
            addCriterion("value21 <", value, "value21");
            return (Criteria) this;
        }

        public Criteria andValue21LessThanOrEqualTo(String value) {
            addCriterion("value21 <=", value, "value21");
            return (Criteria) this;
        }

        public Criteria andValue21Like(String value) {
            addCriterion("value21 like", value, "value21");
            return (Criteria) this;
        }

        public Criteria andValue21NotLike(String value) {
            addCriterion("value21 not like", value, "value21");
            return (Criteria) this;
        }

        public Criteria andValue21In(List<String> values) {
            addCriterion("value21 in", values, "value21");
            return (Criteria) this;
        }

        public Criteria andValue21NotIn(List<String> values) {
            addCriterion("value21 not in", values, "value21");
            return (Criteria) this;
        }

        public Criteria andValue21Between(String value1, String value2) {
            addCriterion("value21 between", value1, value2, "value21");
            return (Criteria) this;
        }

        public Criteria andValue21NotBetween(String value1, String value2) {
            addCriterion("value21 not between", value1, value2, "value21");
            return (Criteria) this;
        }

        public Criteria andValue22IsNull() {
            addCriterion("value22 is null");
            return (Criteria) this;
        }

        public Criteria andValue22IsNotNull() {
            addCriterion("value22 is not null");
            return (Criteria) this;
        }

        public Criteria andValue22EqualTo(String value) {
            addCriterion("value22 =", value, "value22");
            return (Criteria) this;
        }

        public Criteria andValue22NotEqualTo(String value) {
            addCriterion("value22 <>", value, "value22");
            return (Criteria) this;
        }

        public Criteria andValue22GreaterThan(String value) {
            addCriterion("value22 >", value, "value22");
            return (Criteria) this;
        }

        public Criteria andValue22GreaterThanOrEqualTo(String value) {
            addCriterion("value22 >=", value, "value22");
            return (Criteria) this;
        }

        public Criteria andValue22LessThan(String value) {
            addCriterion("value22 <", value, "value22");
            return (Criteria) this;
        }

        public Criteria andValue22LessThanOrEqualTo(String value) {
            addCriterion("value22 <=", value, "value22");
            return (Criteria) this;
        }

        public Criteria andValue22Like(String value) {
            addCriterion("value22 like", value, "value22");
            return (Criteria) this;
        }

        public Criteria andValue22NotLike(String value) {
            addCriterion("value22 not like", value, "value22");
            return (Criteria) this;
        }

        public Criteria andValue22In(List<String> values) {
            addCriterion("value22 in", values, "value22");
            return (Criteria) this;
        }

        public Criteria andValue22NotIn(List<String> values) {
            addCriterion("value22 not in", values, "value22");
            return (Criteria) this;
        }

        public Criteria andValue22Between(String value1, String value2) {
            addCriterion("value22 between", value1, value2, "value22");
            return (Criteria) this;
        }

        public Criteria andValue22NotBetween(String value1, String value2) {
            addCriterion("value22 not between", value1, value2, "value22");
            return (Criteria) this;
        }

        public Criteria andValue23IsNull() {
            addCriterion("value23 is null");
            return (Criteria) this;
        }

        public Criteria andValue23IsNotNull() {
            addCriterion("value23 is not null");
            return (Criteria) this;
        }

        public Criteria andValue23EqualTo(String value) {
            addCriterion("value23 =", value, "value23");
            return (Criteria) this;
        }

        public Criteria andValue23NotEqualTo(String value) {
            addCriterion("value23 <>", value, "value23");
            return (Criteria) this;
        }

        public Criteria andValue23GreaterThan(String value) {
            addCriterion("value23 >", value, "value23");
            return (Criteria) this;
        }

        public Criteria andValue23GreaterThanOrEqualTo(String value) {
            addCriterion("value23 >=", value, "value23");
            return (Criteria) this;
        }

        public Criteria andValue23LessThan(String value) {
            addCriterion("value23 <", value, "value23");
            return (Criteria) this;
        }

        public Criteria andValue23LessThanOrEqualTo(String value) {
            addCriterion("value23 <=", value, "value23");
            return (Criteria) this;
        }

        public Criteria andValue23Like(String value) {
            addCriterion("value23 like", value, "value23");
            return (Criteria) this;
        }

        public Criteria andValue23NotLike(String value) {
            addCriterion("value23 not like", value, "value23");
            return (Criteria) this;
        }

        public Criteria andValue23In(List<String> values) {
            addCriterion("value23 in", values, "value23");
            return (Criteria) this;
        }

        public Criteria andValue23NotIn(List<String> values) {
            addCriterion("value23 not in", values, "value23");
            return (Criteria) this;
        }

        public Criteria andValue23Between(String value1, String value2) {
            addCriterion("value23 between", value1, value2, "value23");
            return (Criteria) this;
        }

        public Criteria andValue23NotBetween(String value1, String value2) {
            addCriterion("value23 not between", value1, value2, "value23");
            return (Criteria) this;
        }

        public Criteria andValue24IsNull() {
            addCriterion("value24 is null");
            return (Criteria) this;
        }

        public Criteria andValue24IsNotNull() {
            addCriterion("value24 is not null");
            return (Criteria) this;
        }

        public Criteria andValue24EqualTo(String value) {
            addCriterion("value24 =", value, "value24");
            return (Criteria) this;
        }

        public Criteria andValue24NotEqualTo(String value) {
            addCriterion("value24 <>", value, "value24");
            return (Criteria) this;
        }

        public Criteria andValue24GreaterThan(String value) {
            addCriterion("value24 >", value, "value24");
            return (Criteria) this;
        }

        public Criteria andValue24GreaterThanOrEqualTo(String value) {
            addCriterion("value24 >=", value, "value24");
            return (Criteria) this;
        }

        public Criteria andValue24LessThan(String value) {
            addCriterion("value24 <", value, "value24");
            return (Criteria) this;
        }

        public Criteria andValue24LessThanOrEqualTo(String value) {
            addCriterion("value24 <=", value, "value24");
            return (Criteria) this;
        }

        public Criteria andValue24Like(String value) {
            addCriterion("value24 like", value, "value24");
            return (Criteria) this;
        }

        public Criteria andValue24NotLike(String value) {
            addCriterion("value24 not like", value, "value24");
            return (Criteria) this;
        }

        public Criteria andValue24In(List<String> values) {
            addCriterion("value24 in", values, "value24");
            return (Criteria) this;
        }

        public Criteria andValue24NotIn(List<String> values) {
            addCriterion("value24 not in", values, "value24");
            return (Criteria) this;
        }

        public Criteria andValue24Between(String value1, String value2) {
            addCriterion("value24 between", value1, value2, "value24");
            return (Criteria) this;
        }

        public Criteria andValue24NotBetween(String value1, String value2) {
            addCriterion("value24 not between", value1, value2, "value24");
            return (Criteria) this;
        }

        public Criteria andValue25IsNull() {
            addCriterion("value25 is null");
            return (Criteria) this;
        }

        public Criteria andValue25IsNotNull() {
            addCriterion("value25 is not null");
            return (Criteria) this;
        }

        public Criteria andValue25EqualTo(String value) {
            addCriterion("value25 =", value, "value25");
            return (Criteria) this;
        }

        public Criteria andValue25NotEqualTo(String value) {
            addCriterion("value25 <>", value, "value25");
            return (Criteria) this;
        }

        public Criteria andValue25GreaterThan(String value) {
            addCriterion("value25 >", value, "value25");
            return (Criteria) this;
        }

        public Criteria andValue25GreaterThanOrEqualTo(String value) {
            addCriterion("value25 >=", value, "value25");
            return (Criteria) this;
        }

        public Criteria andValue25LessThan(String value) {
            addCriterion("value25 <", value, "value25");
            return (Criteria) this;
        }

        public Criteria andValue25LessThanOrEqualTo(String value) {
            addCriterion("value25 <=", value, "value25");
            return (Criteria) this;
        }

        public Criteria andValue25Like(String value) {
            addCriterion("value25 like", value, "value25");
            return (Criteria) this;
        }

        public Criteria andValue25NotLike(String value) {
            addCriterion("value25 not like", value, "value25");
            return (Criteria) this;
        }

        public Criteria andValue25In(List<String> values) {
            addCriterion("value25 in", values, "value25");
            return (Criteria) this;
        }

        public Criteria andValue25NotIn(List<String> values) {
            addCriterion("value25 not in", values, "value25");
            return (Criteria) this;
        }

        public Criteria andValue25Between(String value1, String value2) {
            addCriterion("value25 between", value1, value2, "value25");
            return (Criteria) this;
        }

        public Criteria andValue25NotBetween(String value1, String value2) {
            addCriterion("value25 not between", value1, value2, "value25");
            return (Criteria) this;
        }

        public Criteria andValue26IsNull() {
            addCriterion("value26 is null");
            return (Criteria) this;
        }

        public Criteria andValue26IsNotNull() {
            addCriterion("value26 is not null");
            return (Criteria) this;
        }

        public Criteria andValue26EqualTo(String value) {
            addCriterion("value26 =", value, "value26");
            return (Criteria) this;
        }

        public Criteria andValue26NotEqualTo(String value) {
            addCriterion("value26 <>", value, "value26");
            return (Criteria) this;
        }

        public Criteria andValue26GreaterThan(String value) {
            addCriterion("value26 >", value, "value26");
            return (Criteria) this;
        }

        public Criteria andValue26GreaterThanOrEqualTo(String value) {
            addCriterion("value26 >=", value, "value26");
            return (Criteria) this;
        }

        public Criteria andValue26LessThan(String value) {
            addCriterion("value26 <", value, "value26");
            return (Criteria) this;
        }

        public Criteria andValue26LessThanOrEqualTo(String value) {
            addCriterion("value26 <=", value, "value26");
            return (Criteria) this;
        }

        public Criteria andValue26Like(String value) {
            addCriterion("value26 like", value, "value26");
            return (Criteria) this;
        }

        public Criteria andValue26NotLike(String value) {
            addCriterion("value26 not like", value, "value26");
            return (Criteria) this;
        }

        public Criteria andValue26In(List<String> values) {
            addCriterion("value26 in", values, "value26");
            return (Criteria) this;
        }

        public Criteria andValue26NotIn(List<String> values) {
            addCriterion("value26 not in", values, "value26");
            return (Criteria) this;
        }

        public Criteria andValue26Between(String value1, String value2) {
            addCriterion("value26 between", value1, value2, "value26");
            return (Criteria) this;
        }

        public Criteria andValue26NotBetween(String value1, String value2) {
            addCriterion("value26 not between", value1, value2, "value26");
            return (Criteria) this;
        }

        public Criteria andValue27IsNull() {
            addCriterion("value27 is null");
            return (Criteria) this;
        }

        public Criteria andValue27IsNotNull() {
            addCriterion("value27 is not null");
            return (Criteria) this;
        }

        public Criteria andValue27EqualTo(String value) {
            addCriterion("value27 =", value, "value27");
            return (Criteria) this;
        }

        public Criteria andValue27NotEqualTo(String value) {
            addCriterion("value27 <>", value, "value27");
            return (Criteria) this;
        }

        public Criteria andValue27GreaterThan(String value) {
            addCriterion("value27 >", value, "value27");
            return (Criteria) this;
        }

        public Criteria andValue27GreaterThanOrEqualTo(String value) {
            addCriterion("value27 >=", value, "value27");
            return (Criteria) this;
        }

        public Criteria andValue27LessThan(String value) {
            addCriterion("value27 <", value, "value27");
            return (Criteria) this;
        }

        public Criteria andValue27LessThanOrEqualTo(String value) {
            addCriterion("value27 <=", value, "value27");
            return (Criteria) this;
        }

        public Criteria andValue27Like(String value) {
            addCriterion("value27 like", value, "value27");
            return (Criteria) this;
        }

        public Criteria andValue27NotLike(String value) {
            addCriterion("value27 not like", value, "value27");
            return (Criteria) this;
        }

        public Criteria andValue27In(List<String> values) {
            addCriterion("value27 in", values, "value27");
            return (Criteria) this;
        }

        public Criteria andValue27NotIn(List<String> values) {
            addCriterion("value27 not in", values, "value27");
            return (Criteria) this;
        }

        public Criteria andValue27Between(String value1, String value2) {
            addCriterion("value27 between", value1, value2, "value27");
            return (Criteria) this;
        }

        public Criteria andValue27NotBetween(String value1, String value2) {
            addCriterion("value27 not between", value1, value2, "value27");
            return (Criteria) this;
        }

        public Criteria andValue28IsNull() {
            addCriterion("value28 is null");
            return (Criteria) this;
        }

        public Criteria andValue28IsNotNull() {
            addCriterion("value28 is not null");
            return (Criteria) this;
        }

        public Criteria andValue28EqualTo(String value) {
            addCriterion("value28 =", value, "value28");
            return (Criteria) this;
        }

        public Criteria andValue28NotEqualTo(String value) {
            addCriterion("value28 <>", value, "value28");
            return (Criteria) this;
        }

        public Criteria andValue28GreaterThan(String value) {
            addCriterion("value28 >", value, "value28");
            return (Criteria) this;
        }

        public Criteria andValue28GreaterThanOrEqualTo(String value) {
            addCriterion("value28 >=", value, "value28");
            return (Criteria) this;
        }

        public Criteria andValue28LessThan(String value) {
            addCriterion("value28 <", value, "value28");
            return (Criteria) this;
        }

        public Criteria andValue28LessThanOrEqualTo(String value) {
            addCriterion("value28 <=", value, "value28");
            return (Criteria) this;
        }

        public Criteria andValue28Like(String value) {
            addCriterion("value28 like", value, "value28");
            return (Criteria) this;
        }

        public Criteria andValue28NotLike(String value) {
            addCriterion("value28 not like", value, "value28");
            return (Criteria) this;
        }

        public Criteria andValue28In(List<String> values) {
            addCriterion("value28 in", values, "value28");
            return (Criteria) this;
        }

        public Criteria andValue28NotIn(List<String> values) {
            addCriterion("value28 not in", values, "value28");
            return (Criteria) this;
        }

        public Criteria andValue28Between(String value1, String value2) {
            addCriterion("value28 between", value1, value2, "value28");
            return (Criteria) this;
        }

        public Criteria andValue28NotBetween(String value1, String value2) {
            addCriterion("value28 not between", value1, value2, "value28");
            return (Criteria) this;
        }

        public Criteria andValue29IsNull() {
            addCriterion("value29 is null");
            return (Criteria) this;
        }

        public Criteria andValue29IsNotNull() {
            addCriterion("value29 is not null");
            return (Criteria) this;
        }

        public Criteria andValue29EqualTo(String value) {
            addCriterion("value29 =", value, "value29");
            return (Criteria) this;
        }

        public Criteria andValue29NotEqualTo(String value) {
            addCriterion("value29 <>", value, "value29");
            return (Criteria) this;
        }

        public Criteria andValue29GreaterThan(String value) {
            addCriterion("value29 >", value, "value29");
            return (Criteria) this;
        }

        public Criteria andValue29GreaterThanOrEqualTo(String value) {
            addCriterion("value29 >=", value, "value29");
            return (Criteria) this;
        }

        public Criteria andValue29LessThan(String value) {
            addCriterion("value29 <", value, "value29");
            return (Criteria) this;
        }

        public Criteria andValue29LessThanOrEqualTo(String value) {
            addCriterion("value29 <=", value, "value29");
            return (Criteria) this;
        }

        public Criteria andValue29Like(String value) {
            addCriterion("value29 like", value, "value29");
            return (Criteria) this;
        }

        public Criteria andValue29NotLike(String value) {
            addCriterion("value29 not like", value, "value29");
            return (Criteria) this;
        }

        public Criteria andValue29In(List<String> values) {
            addCriterion("value29 in", values, "value29");
            return (Criteria) this;
        }

        public Criteria andValue29NotIn(List<String> values) {
            addCriterion("value29 not in", values, "value29");
            return (Criteria) this;
        }

        public Criteria andValue29Between(String value1, String value2) {
            addCriterion("value29 between", value1, value2, "value29");
            return (Criteria) this;
        }

        public Criteria andValue29NotBetween(String value1, String value2) {
            addCriterion("value29 not between", value1, value2, "value29");
            return (Criteria) this;
        }

        public Criteria andValue30IsNull() {
            addCriterion("value30 is null");
            return (Criteria) this;
        }

        public Criteria andValue30IsNotNull() {
            addCriterion("value30 is not null");
            return (Criteria) this;
        }

        public Criteria andValue30EqualTo(String value) {
            addCriterion("value30 =", value, "value30");
            return (Criteria) this;
        }

        public Criteria andValue30NotEqualTo(String value) {
            addCriterion("value30 <>", value, "value30");
            return (Criteria) this;
        }

        public Criteria andValue30GreaterThan(String value) {
            addCriterion("value30 >", value, "value30");
            return (Criteria) this;
        }

        public Criteria andValue30GreaterThanOrEqualTo(String value) {
            addCriterion("value30 >=", value, "value30");
            return (Criteria) this;
        }

        public Criteria andValue30LessThan(String value) {
            addCriterion("value30 <", value, "value30");
            return (Criteria) this;
        }

        public Criteria andValue30LessThanOrEqualTo(String value) {
            addCriterion("value30 <=", value, "value30");
            return (Criteria) this;
        }

        public Criteria andValue30Like(String value) {
            addCriterion("value30 like", value, "value30");
            return (Criteria) this;
        }

        public Criteria andValue30NotLike(String value) {
            addCriterion("value30 not like", value, "value30");
            return (Criteria) this;
        }

        public Criteria andValue30In(List<String> values) {
            addCriterion("value30 in", values, "value30");
            return (Criteria) this;
        }

        public Criteria andValue30NotIn(List<String> values) {
            addCriterion("value30 not in", values, "value30");
            return (Criteria) this;
        }

        public Criteria andValue30Between(String value1, String value2) {
            addCriterion("value30 between", value1, value2, "value30");
            return (Criteria) this;
        }

        public Criteria andValue30NotBetween(String value1, String value2) {
            addCriterion("value30 not between", value1, value2, "value30");
            return (Criteria) this;
        }

        public Criteria andValue31IsNull() {
            addCriterion("value31 is null");
            return (Criteria) this;
        }

        public Criteria andValue31IsNotNull() {
            addCriterion("value31 is not null");
            return (Criteria) this;
        }

        public Criteria andValue31EqualTo(String value) {
            addCriterion("value31 =", value, "value31");
            return (Criteria) this;
        }

        public Criteria andValue31NotEqualTo(String value) {
            addCriterion("value31 <>", value, "value31");
            return (Criteria) this;
        }

        public Criteria andValue31GreaterThan(String value) {
            addCriterion("value31 >", value, "value31");
            return (Criteria) this;
        }

        public Criteria andValue31GreaterThanOrEqualTo(String value) {
            addCriterion("value31 >=", value, "value31");
            return (Criteria) this;
        }

        public Criteria andValue31LessThan(String value) {
            addCriterion("value31 <", value, "value31");
            return (Criteria) this;
        }

        public Criteria andValue31LessThanOrEqualTo(String value) {
            addCriterion("value31 <=", value, "value31");
            return (Criteria) this;
        }

        public Criteria andValue31Like(String value) {
            addCriterion("value31 like", value, "value31");
            return (Criteria) this;
        }

        public Criteria andValue31NotLike(String value) {
            addCriterion("value31 not like", value, "value31");
            return (Criteria) this;
        }

        public Criteria andValue31In(List<String> values) {
            addCriterion("value31 in", values, "value31");
            return (Criteria) this;
        }

        public Criteria andValue31NotIn(List<String> values) {
            addCriterion("value31 not in", values, "value31");
            return (Criteria) this;
        }

        public Criteria andValue31Between(String value1, String value2) {
            addCriterion("value31 between", value1, value2, "value31");
            return (Criteria) this;
        }

        public Criteria andValue31NotBetween(String value1, String value2) {
            addCriterion("value31 not between", value1, value2, "value31");
            return (Criteria) this;
        }

        public Criteria andValue32IsNull() {
            addCriterion("value32 is null");
            return (Criteria) this;
        }

        public Criteria andValue32IsNotNull() {
            addCriterion("value32 is not null");
            return (Criteria) this;
        }

        public Criteria andValue32EqualTo(String value) {
            addCriterion("value32 =", value, "value32");
            return (Criteria) this;
        }

        public Criteria andValue32NotEqualTo(String value) {
            addCriterion("value32 <>", value, "value32");
            return (Criteria) this;
        }

        public Criteria andValue32GreaterThan(String value) {
            addCriterion("value32 >", value, "value32");
            return (Criteria) this;
        }

        public Criteria andValue32GreaterThanOrEqualTo(String value) {
            addCriterion("value32 >=", value, "value32");
            return (Criteria) this;
        }

        public Criteria andValue32LessThan(String value) {
            addCriterion("value32 <", value, "value32");
            return (Criteria) this;
        }

        public Criteria andValue32LessThanOrEqualTo(String value) {
            addCriterion("value32 <=", value, "value32");
            return (Criteria) this;
        }

        public Criteria andValue32Like(String value) {
            addCriterion("value32 like", value, "value32");
            return (Criteria) this;
        }

        public Criteria andValue32NotLike(String value) {
            addCriterion("value32 not like", value, "value32");
            return (Criteria) this;
        }

        public Criteria andValue32In(List<String> values) {
            addCriterion("value32 in", values, "value32");
            return (Criteria) this;
        }

        public Criteria andValue32NotIn(List<String> values) {
            addCriterion("value32 not in", values, "value32");
            return (Criteria) this;
        }

        public Criteria andValue32Between(String value1, String value2) {
            addCriterion("value32 between", value1, value2, "value32");
            return (Criteria) this;
        }

        public Criteria andValue32NotBetween(String value1, String value2) {
            addCriterion("value32 not between", value1, value2, "value32");
            return (Criteria) this;
        }

        public Criteria andValue33IsNull() {
            addCriterion("value33 is null");
            return (Criteria) this;
        }

        public Criteria andValue33IsNotNull() {
            addCriterion("value33 is not null");
            return (Criteria) this;
        }

        public Criteria andValue33EqualTo(String value) {
            addCriterion("value33 =", value, "value33");
            return (Criteria) this;
        }

        public Criteria andValue33NotEqualTo(String value) {
            addCriterion("value33 <>", value, "value33");
            return (Criteria) this;
        }

        public Criteria andValue33GreaterThan(String value) {
            addCriterion("value33 >", value, "value33");
            return (Criteria) this;
        }

        public Criteria andValue33GreaterThanOrEqualTo(String value) {
            addCriterion("value33 >=", value, "value33");
            return (Criteria) this;
        }

        public Criteria andValue33LessThan(String value) {
            addCriterion("value33 <", value, "value33");
            return (Criteria) this;
        }

        public Criteria andValue33LessThanOrEqualTo(String value) {
            addCriterion("value33 <=", value, "value33");
            return (Criteria) this;
        }

        public Criteria andValue33Like(String value) {
            addCriterion("value33 like", value, "value33");
            return (Criteria) this;
        }

        public Criteria andValue33NotLike(String value) {
            addCriterion("value33 not like", value, "value33");
            return (Criteria) this;
        }

        public Criteria andValue33In(List<String> values) {
            addCriterion("value33 in", values, "value33");
            return (Criteria) this;
        }

        public Criteria andValue33NotIn(List<String> values) {
            addCriterion("value33 not in", values, "value33");
            return (Criteria) this;
        }

        public Criteria andValue33Between(String value1, String value2) {
            addCriterion("value33 between", value1, value2, "value33");
            return (Criteria) this;
        }

        public Criteria andValue33NotBetween(String value1, String value2) {
            addCriterion("value33 not between", value1, value2, "value33");
            return (Criteria) this;
        }

        public Criteria andValue34IsNull() {
            addCriterion("value34 is null");
            return (Criteria) this;
        }

        public Criteria andValue34IsNotNull() {
            addCriterion("value34 is not null");
            return (Criteria) this;
        }

        public Criteria andValue34EqualTo(String value) {
            addCriterion("value34 =", value, "value34");
            return (Criteria) this;
        }

        public Criteria andValue34NotEqualTo(String value) {
            addCriterion("value34 <>", value, "value34");
            return (Criteria) this;
        }

        public Criteria andValue34GreaterThan(String value) {
            addCriterion("value34 >", value, "value34");
            return (Criteria) this;
        }

        public Criteria andValue34GreaterThanOrEqualTo(String value) {
            addCriterion("value34 >=", value, "value34");
            return (Criteria) this;
        }

        public Criteria andValue34LessThan(String value) {
            addCriterion("value34 <", value, "value34");
            return (Criteria) this;
        }

        public Criteria andValue34LessThanOrEqualTo(String value) {
            addCriterion("value34 <=", value, "value34");
            return (Criteria) this;
        }

        public Criteria andValue34Like(String value) {
            addCriterion("value34 like", value, "value34");
            return (Criteria) this;
        }

        public Criteria andValue34NotLike(String value) {
            addCriterion("value34 not like", value, "value34");
            return (Criteria) this;
        }

        public Criteria andValue34In(List<String> values) {
            addCriterion("value34 in", values, "value34");
            return (Criteria) this;
        }

        public Criteria andValue34NotIn(List<String> values) {
            addCriterion("value34 not in", values, "value34");
            return (Criteria) this;
        }

        public Criteria andValue34Between(String value1, String value2) {
            addCriterion("value34 between", value1, value2, "value34");
            return (Criteria) this;
        }

        public Criteria andValue34NotBetween(String value1, String value2) {
            addCriterion("value34 not between", value1, value2, "value34");
            return (Criteria) this;
        }

        public Criteria andValue35IsNull() {
            addCriterion("value35 is null");
            return (Criteria) this;
        }

        public Criteria andValue35IsNotNull() {
            addCriterion("value35 is not null");
            return (Criteria) this;
        }

        public Criteria andValue35EqualTo(String value) {
            addCriterion("value35 =", value, "value35");
            return (Criteria) this;
        }

        public Criteria andValue35NotEqualTo(String value) {
            addCriterion("value35 <>", value, "value35");
            return (Criteria) this;
        }

        public Criteria andValue35GreaterThan(String value) {
            addCriterion("value35 >", value, "value35");
            return (Criteria) this;
        }

        public Criteria andValue35GreaterThanOrEqualTo(String value) {
            addCriterion("value35 >=", value, "value35");
            return (Criteria) this;
        }

        public Criteria andValue35LessThan(String value) {
            addCriterion("value35 <", value, "value35");
            return (Criteria) this;
        }

        public Criteria andValue35LessThanOrEqualTo(String value) {
            addCriterion("value35 <=", value, "value35");
            return (Criteria) this;
        }

        public Criteria andValue35Like(String value) {
            addCriterion("value35 like", value, "value35");
            return (Criteria) this;
        }

        public Criteria andValue35NotLike(String value) {
            addCriterion("value35 not like", value, "value35");
            return (Criteria) this;
        }

        public Criteria andValue35In(List<String> values) {
            addCriterion("value35 in", values, "value35");
            return (Criteria) this;
        }

        public Criteria andValue35NotIn(List<String> values) {
            addCriterion("value35 not in", values, "value35");
            return (Criteria) this;
        }

        public Criteria andValue35Between(String value1, String value2) {
            addCriterion("value35 between", value1, value2, "value35");
            return (Criteria) this;
        }

        public Criteria andValue35NotBetween(String value1, String value2) {
            addCriterion("value35 not between", value1, value2, "value35");
            return (Criteria) this;
        }

        public Criteria andValue36IsNull() {
            addCriterion("value36 is null");
            return (Criteria) this;
        }

        public Criteria andValue36IsNotNull() {
            addCriterion("value36 is not null");
            return (Criteria) this;
        }

        public Criteria andValue36EqualTo(String value) {
            addCriterion("value36 =", value, "value36");
            return (Criteria) this;
        }

        public Criteria andValue36NotEqualTo(String value) {
            addCriterion("value36 <>", value, "value36");
            return (Criteria) this;
        }

        public Criteria andValue36GreaterThan(String value) {
            addCriterion("value36 >", value, "value36");
            return (Criteria) this;
        }

        public Criteria andValue36GreaterThanOrEqualTo(String value) {
            addCriterion("value36 >=", value, "value36");
            return (Criteria) this;
        }

        public Criteria andValue36LessThan(String value) {
            addCriterion("value36 <", value, "value36");
            return (Criteria) this;
        }

        public Criteria andValue36LessThanOrEqualTo(String value) {
            addCriterion("value36 <=", value, "value36");
            return (Criteria) this;
        }

        public Criteria andValue36Like(String value) {
            addCriterion("value36 like", value, "value36");
            return (Criteria) this;
        }

        public Criteria andValue36NotLike(String value) {
            addCriterion("value36 not like", value, "value36");
            return (Criteria) this;
        }

        public Criteria andValue36In(List<String> values) {
            addCriterion("value36 in", values, "value36");
            return (Criteria) this;
        }

        public Criteria andValue36NotIn(List<String> values) {
            addCriterion("value36 not in", values, "value36");
            return (Criteria) this;
        }

        public Criteria andValue36Between(String value1, String value2) {
            addCriterion("value36 between", value1, value2, "value36");
            return (Criteria) this;
        }

        public Criteria andValue36NotBetween(String value1, String value2) {
            addCriterion("value36 not between", value1, value2, "value36");
            return (Criteria) this;
        }

        public Criteria andValue37IsNull() {
            addCriterion("value37 is null");
            return (Criteria) this;
        }

        public Criteria andValue37IsNotNull() {
            addCriterion("value37 is not null");
            return (Criteria) this;
        }

        public Criteria andValue37EqualTo(String value) {
            addCriterion("value37 =", value, "value37");
            return (Criteria) this;
        }

        public Criteria andValue37NotEqualTo(String value) {
            addCriterion("value37 <>", value, "value37");
            return (Criteria) this;
        }

        public Criteria andValue37GreaterThan(String value) {
            addCriterion("value37 >", value, "value37");
            return (Criteria) this;
        }

        public Criteria andValue37GreaterThanOrEqualTo(String value) {
            addCriterion("value37 >=", value, "value37");
            return (Criteria) this;
        }

        public Criteria andValue37LessThan(String value) {
            addCriterion("value37 <", value, "value37");
            return (Criteria) this;
        }

        public Criteria andValue37LessThanOrEqualTo(String value) {
            addCriterion("value37 <=", value, "value37");
            return (Criteria) this;
        }

        public Criteria andValue37Like(String value) {
            addCriterion("value37 like", value, "value37");
            return (Criteria) this;
        }

        public Criteria andValue37NotLike(String value) {
            addCriterion("value37 not like", value, "value37");
            return (Criteria) this;
        }

        public Criteria andValue37In(List<String> values) {
            addCriterion("value37 in", values, "value37");
            return (Criteria) this;
        }

        public Criteria andValue37NotIn(List<String> values) {
            addCriterion("value37 not in", values, "value37");
            return (Criteria) this;
        }

        public Criteria andValue37Between(String value1, String value2) {
            addCriterion("value37 between", value1, value2, "value37");
            return (Criteria) this;
        }

        public Criteria andValue37NotBetween(String value1, String value2) {
            addCriterion("value37 not between", value1, value2, "value37");
            return (Criteria) this;
        }

        public Criteria andValue38IsNull() {
            addCriterion("value38 is null");
            return (Criteria) this;
        }

        public Criteria andValue38IsNotNull() {
            addCriterion("value38 is not null");
            return (Criteria) this;
        }

        public Criteria andValue38EqualTo(String value) {
            addCriterion("value38 =", value, "value38");
            return (Criteria) this;
        }

        public Criteria andValue38NotEqualTo(String value) {
            addCriterion("value38 <>", value, "value38");
            return (Criteria) this;
        }

        public Criteria andValue38GreaterThan(String value) {
            addCriterion("value38 >", value, "value38");
            return (Criteria) this;
        }

        public Criteria andValue38GreaterThanOrEqualTo(String value) {
            addCriterion("value38 >=", value, "value38");
            return (Criteria) this;
        }

        public Criteria andValue38LessThan(String value) {
            addCriterion("value38 <", value, "value38");
            return (Criteria) this;
        }

        public Criteria andValue38LessThanOrEqualTo(String value) {
            addCriterion("value38 <=", value, "value38");
            return (Criteria) this;
        }

        public Criteria andValue38Like(String value) {
            addCriterion("value38 like", value, "value38");
            return (Criteria) this;
        }

        public Criteria andValue38NotLike(String value) {
            addCriterion("value38 not like", value, "value38");
            return (Criteria) this;
        }

        public Criteria andValue38In(List<String> values) {
            addCriterion("value38 in", values, "value38");
            return (Criteria) this;
        }

        public Criteria andValue38NotIn(List<String> values) {
            addCriterion("value38 not in", values, "value38");
            return (Criteria) this;
        }

        public Criteria andValue38Between(String value1, String value2) {
            addCriterion("value38 between", value1, value2, "value38");
            return (Criteria) this;
        }

        public Criteria andValue38NotBetween(String value1, String value2) {
            addCriterion("value38 not between", value1, value2, "value38");
            return (Criteria) this;
        }

        public Criteria andValue39IsNull() {
            addCriterion("value39 is null");
            return (Criteria) this;
        }

        public Criteria andValue39IsNotNull() {
            addCriterion("value39 is not null");
            return (Criteria) this;
        }

        public Criteria andValue39EqualTo(String value) {
            addCriterion("value39 =", value, "value39");
            return (Criteria) this;
        }

        public Criteria andValue39NotEqualTo(String value) {
            addCriterion("value39 <>", value, "value39");
            return (Criteria) this;
        }

        public Criteria andValue39GreaterThan(String value) {
            addCriterion("value39 >", value, "value39");
            return (Criteria) this;
        }

        public Criteria andValue39GreaterThanOrEqualTo(String value) {
            addCriterion("value39 >=", value, "value39");
            return (Criteria) this;
        }

        public Criteria andValue39LessThan(String value) {
            addCriterion("value39 <", value, "value39");
            return (Criteria) this;
        }

        public Criteria andValue39LessThanOrEqualTo(String value) {
            addCriterion("value39 <=", value, "value39");
            return (Criteria) this;
        }

        public Criteria andValue39Like(String value) {
            addCriterion("value39 like", value, "value39");
            return (Criteria) this;
        }

        public Criteria andValue39NotLike(String value) {
            addCriterion("value39 not like", value, "value39");
            return (Criteria) this;
        }

        public Criteria andValue39In(List<String> values) {
            addCriterion("value39 in", values, "value39");
            return (Criteria) this;
        }

        public Criteria andValue39NotIn(List<String> values) {
            addCriterion("value39 not in", values, "value39");
            return (Criteria) this;
        }

        public Criteria andValue39Between(String value1, String value2) {
            addCriterion("value39 between", value1, value2, "value39");
            return (Criteria) this;
        }

        public Criteria andValue39NotBetween(String value1, String value2) {
            addCriterion("value39 not between", value1, value2, "value39");
            return (Criteria) this;
        }

        public Criteria andValue40IsNull() {
            addCriterion("value40 is null");
            return (Criteria) this;
        }

        public Criteria andValue40IsNotNull() {
            addCriterion("value40 is not null");
            return (Criteria) this;
        }

        public Criteria andValue40EqualTo(String value) {
            addCriterion("value40 =", value, "value40");
            return (Criteria) this;
        }

        public Criteria andValue40NotEqualTo(String value) {
            addCriterion("value40 <>", value, "value40");
            return (Criteria) this;
        }

        public Criteria andValue40GreaterThan(String value) {
            addCriterion("value40 >", value, "value40");
            return (Criteria) this;
        }

        public Criteria andValue40GreaterThanOrEqualTo(String value) {
            addCriterion("value40 >=", value, "value40");
            return (Criteria) this;
        }

        public Criteria andValue40LessThan(String value) {
            addCriterion("value40 <", value, "value40");
            return (Criteria) this;
        }

        public Criteria andValue40LessThanOrEqualTo(String value) {
            addCriterion("value40 <=", value, "value40");
            return (Criteria) this;
        }

        public Criteria andValue40Like(String value) {
            addCriterion("value40 like", value, "value40");
            return (Criteria) this;
        }

        public Criteria andValue40NotLike(String value) {
            addCriterion("value40 not like", value, "value40");
            return (Criteria) this;
        }

        public Criteria andValue40In(List<String> values) {
            addCriterion("value40 in", values, "value40");
            return (Criteria) this;
        }

        public Criteria andValue40NotIn(List<String> values) {
            addCriterion("value40 not in", values, "value40");
            return (Criteria) this;
        }

        public Criteria andValue40Between(String value1, String value2) {
            addCriterion("value40 between", value1, value2, "value40");
            return (Criteria) this;
        }

        public Criteria andValue40NotBetween(String value1, String value2) {
            addCriterion("value40 not between", value1, value2, "value40");
            return (Criteria) this;
        }

        public Criteria andValue41IsNull() {
            addCriterion("value41 is null");
            return (Criteria) this;
        }

        public Criteria andValue41IsNotNull() {
            addCriterion("value41 is not null");
            return (Criteria) this;
        }

        public Criteria andValue41EqualTo(String value) {
            addCriterion("value41 =", value, "value41");
            return (Criteria) this;
        }

        public Criteria andValue41NotEqualTo(String value) {
            addCriterion("value41 <>", value, "value41");
            return (Criteria) this;
        }

        public Criteria andValue41GreaterThan(String value) {
            addCriterion("value41 >", value, "value41");
            return (Criteria) this;
        }

        public Criteria andValue41GreaterThanOrEqualTo(String value) {
            addCriterion("value41 >=", value, "value41");
            return (Criteria) this;
        }

        public Criteria andValue41LessThan(String value) {
            addCriterion("value41 <", value, "value41");
            return (Criteria) this;
        }

        public Criteria andValue41LessThanOrEqualTo(String value) {
            addCriterion("value41 <=", value, "value41");
            return (Criteria) this;
        }

        public Criteria andValue41Like(String value) {
            addCriterion("value41 like", value, "value41");
            return (Criteria) this;
        }

        public Criteria andValue41NotLike(String value) {
            addCriterion("value41 not like", value, "value41");
            return (Criteria) this;
        }

        public Criteria andValue41In(List<String> values) {
            addCriterion("value41 in", values, "value41");
            return (Criteria) this;
        }

        public Criteria andValue41NotIn(List<String> values) {
            addCriterion("value41 not in", values, "value41");
            return (Criteria) this;
        }

        public Criteria andValue41Between(String value1, String value2) {
            addCriterion("value41 between", value1, value2, "value41");
            return (Criteria) this;
        }

        public Criteria andValue41NotBetween(String value1, String value2) {
            addCriterion("value41 not between", value1, value2, "value41");
            return (Criteria) this;
        }

        public Criteria andValue42IsNull() {
            addCriterion("value42 is null");
            return (Criteria) this;
        }

        public Criteria andValue42IsNotNull() {
            addCriterion("value42 is not null");
            return (Criteria) this;
        }

        public Criteria andValue42EqualTo(String value) {
            addCriterion("value42 =", value, "value42");
            return (Criteria) this;
        }

        public Criteria andValue42NotEqualTo(String value) {
            addCriterion("value42 <>", value, "value42");
            return (Criteria) this;
        }

        public Criteria andValue42GreaterThan(String value) {
            addCriterion("value42 >", value, "value42");
            return (Criteria) this;
        }

        public Criteria andValue42GreaterThanOrEqualTo(String value) {
            addCriterion("value42 >=", value, "value42");
            return (Criteria) this;
        }

        public Criteria andValue42LessThan(String value) {
            addCriterion("value42 <", value, "value42");
            return (Criteria) this;
        }

        public Criteria andValue42LessThanOrEqualTo(String value) {
            addCriterion("value42 <=", value, "value42");
            return (Criteria) this;
        }

        public Criteria andValue42Like(String value) {
            addCriterion("value42 like", value, "value42");
            return (Criteria) this;
        }

        public Criteria andValue42NotLike(String value) {
            addCriterion("value42 not like", value, "value42");
            return (Criteria) this;
        }

        public Criteria andValue42In(List<String> values) {
            addCriterion("value42 in", values, "value42");
            return (Criteria) this;
        }

        public Criteria andValue42NotIn(List<String> values) {
            addCriterion("value42 not in", values, "value42");
            return (Criteria) this;
        }

        public Criteria andValue42Between(String value1, String value2) {
            addCriterion("value42 between", value1, value2, "value42");
            return (Criteria) this;
        }

        public Criteria andValue42NotBetween(String value1, String value2) {
            addCriterion("value42 not between", value1, value2, "value42");
            return (Criteria) this;
        }

        public Criteria andValue43IsNull() {
            addCriterion("value43 is null");
            return (Criteria) this;
        }

        public Criteria andValue43IsNotNull() {
            addCriterion("value43 is not null");
            return (Criteria) this;
        }

        public Criteria andValue43EqualTo(String value) {
            addCriterion("value43 =", value, "value43");
            return (Criteria) this;
        }

        public Criteria andValue43NotEqualTo(String value) {
            addCriterion("value43 <>", value, "value43");
            return (Criteria) this;
        }

        public Criteria andValue43GreaterThan(String value) {
            addCriterion("value43 >", value, "value43");
            return (Criteria) this;
        }

        public Criteria andValue43GreaterThanOrEqualTo(String value) {
            addCriterion("value43 >=", value, "value43");
            return (Criteria) this;
        }

        public Criteria andValue43LessThan(String value) {
            addCriterion("value43 <", value, "value43");
            return (Criteria) this;
        }

        public Criteria andValue43LessThanOrEqualTo(String value) {
            addCriterion("value43 <=", value, "value43");
            return (Criteria) this;
        }

        public Criteria andValue43Like(String value) {
            addCriterion("value43 like", value, "value43");
            return (Criteria) this;
        }

        public Criteria andValue43NotLike(String value) {
            addCriterion("value43 not like", value, "value43");
            return (Criteria) this;
        }

        public Criteria andValue43In(List<String> values) {
            addCriterion("value43 in", values, "value43");
            return (Criteria) this;
        }

        public Criteria andValue43NotIn(List<String> values) {
            addCriterion("value43 not in", values, "value43");
            return (Criteria) this;
        }

        public Criteria andValue43Between(String value1, String value2) {
            addCriterion("value43 between", value1, value2, "value43");
            return (Criteria) this;
        }

        public Criteria andValue43NotBetween(String value1, String value2) {
            addCriterion("value43 not between", value1, value2, "value43");
            return (Criteria) this;
        }

        public Criteria andValue44IsNull() {
            addCriterion("value44 is null");
            return (Criteria) this;
        }

        public Criteria andValue44IsNotNull() {
            addCriterion("value44 is not null");
            return (Criteria) this;
        }

        public Criteria andValue44EqualTo(String value) {
            addCriterion("value44 =", value, "value44");
            return (Criteria) this;
        }

        public Criteria andValue44NotEqualTo(String value) {
            addCriterion("value44 <>", value, "value44");
            return (Criteria) this;
        }

        public Criteria andValue44GreaterThan(String value) {
            addCriterion("value44 >", value, "value44");
            return (Criteria) this;
        }

        public Criteria andValue44GreaterThanOrEqualTo(String value) {
            addCriterion("value44 >=", value, "value44");
            return (Criteria) this;
        }

        public Criteria andValue44LessThan(String value) {
            addCriterion("value44 <", value, "value44");
            return (Criteria) this;
        }

        public Criteria andValue44LessThanOrEqualTo(String value) {
            addCriterion("value44 <=", value, "value44");
            return (Criteria) this;
        }

        public Criteria andValue44Like(String value) {
            addCriterion("value44 like", value, "value44");
            return (Criteria) this;
        }

        public Criteria andValue44NotLike(String value) {
            addCriterion("value44 not like", value, "value44");
            return (Criteria) this;
        }

        public Criteria andValue44In(List<String> values) {
            addCriterion("value44 in", values, "value44");
            return (Criteria) this;
        }

        public Criteria andValue44NotIn(List<String> values) {
            addCriterion("value44 not in", values, "value44");
            return (Criteria) this;
        }

        public Criteria andValue44Between(String value1, String value2) {
            addCriterion("value44 between", value1, value2, "value44");
            return (Criteria) this;
        }

        public Criteria andValue44NotBetween(String value1, String value2) {
            addCriterion("value44 not between", value1, value2, "value44");
            return (Criteria) this;
        }

        public Criteria andValue45IsNull() {
            addCriterion("value45 is null");
            return (Criteria) this;
        }

        public Criteria andValue45IsNotNull() {
            addCriterion("value45 is not null");
            return (Criteria) this;
        }

        public Criteria andValue45EqualTo(String value) {
            addCriterion("value45 =", value, "value45");
            return (Criteria) this;
        }

        public Criteria andValue45NotEqualTo(String value) {
            addCriterion("value45 <>", value, "value45");
            return (Criteria) this;
        }

        public Criteria andValue45GreaterThan(String value) {
            addCriterion("value45 >", value, "value45");
            return (Criteria) this;
        }

        public Criteria andValue45GreaterThanOrEqualTo(String value) {
            addCriterion("value45 >=", value, "value45");
            return (Criteria) this;
        }

        public Criteria andValue45LessThan(String value) {
            addCriterion("value45 <", value, "value45");
            return (Criteria) this;
        }

        public Criteria andValue45LessThanOrEqualTo(String value) {
            addCriterion("value45 <=", value, "value45");
            return (Criteria) this;
        }

        public Criteria andValue45Like(String value) {
            addCriterion("value45 like", value, "value45");
            return (Criteria) this;
        }

        public Criteria andValue45NotLike(String value) {
            addCriterion("value45 not like", value, "value45");
            return (Criteria) this;
        }

        public Criteria andValue45In(List<String> values) {
            addCriterion("value45 in", values, "value45");
            return (Criteria) this;
        }

        public Criteria andValue45NotIn(List<String> values) {
            addCriterion("value45 not in", values, "value45");
            return (Criteria) this;
        }

        public Criteria andValue45Between(String value1, String value2) {
            addCriterion("value45 between", value1, value2, "value45");
            return (Criteria) this;
        }

        public Criteria andValue45NotBetween(String value1, String value2) {
            addCriterion("value45 not between", value1, value2, "value45");
            return (Criteria) this;
        }

        public Criteria andValue46IsNull() {
            addCriterion("value46 is null");
            return (Criteria) this;
        }

        public Criteria andValue46IsNotNull() {
            addCriterion("value46 is not null");
            return (Criteria) this;
        }

        public Criteria andValue46EqualTo(String value) {
            addCriterion("value46 =", value, "value46");
            return (Criteria) this;
        }

        public Criteria andValue46NotEqualTo(String value) {
            addCriterion("value46 <>", value, "value46");
            return (Criteria) this;
        }

        public Criteria andValue46GreaterThan(String value) {
            addCriterion("value46 >", value, "value46");
            return (Criteria) this;
        }

        public Criteria andValue46GreaterThanOrEqualTo(String value) {
            addCriterion("value46 >=", value, "value46");
            return (Criteria) this;
        }

        public Criteria andValue46LessThan(String value) {
            addCriterion("value46 <", value, "value46");
            return (Criteria) this;
        }

        public Criteria andValue46LessThanOrEqualTo(String value) {
            addCriterion("value46 <=", value, "value46");
            return (Criteria) this;
        }

        public Criteria andValue46Like(String value) {
            addCriterion("value46 like", value, "value46");
            return (Criteria) this;
        }

        public Criteria andValue46NotLike(String value) {
            addCriterion("value46 not like", value, "value46");
            return (Criteria) this;
        }

        public Criteria andValue46In(List<String> values) {
            addCriterion("value46 in", values, "value46");
            return (Criteria) this;
        }

        public Criteria andValue46NotIn(List<String> values) {
            addCriterion("value46 not in", values, "value46");
            return (Criteria) this;
        }

        public Criteria andValue46Between(String value1, String value2) {
            addCriterion("value46 between", value1, value2, "value46");
            return (Criteria) this;
        }

        public Criteria andValue46NotBetween(String value1, String value2) {
            addCriterion("value46 not between", value1, value2, "value46");
            return (Criteria) this;
        }

        public Criteria andValue47IsNull() {
            addCriterion("value47 is null");
            return (Criteria) this;
        }

        public Criteria andValue47IsNotNull() {
            addCriterion("value47 is not null");
            return (Criteria) this;
        }

        public Criteria andValue47EqualTo(String value) {
            addCriterion("value47 =", value, "value47");
            return (Criteria) this;
        }

        public Criteria andValue47NotEqualTo(String value) {
            addCriterion("value47 <>", value, "value47");
            return (Criteria) this;
        }

        public Criteria andValue47GreaterThan(String value) {
            addCriterion("value47 >", value, "value47");
            return (Criteria) this;
        }

        public Criteria andValue47GreaterThanOrEqualTo(String value) {
            addCriterion("value47 >=", value, "value47");
            return (Criteria) this;
        }

        public Criteria andValue47LessThan(String value) {
            addCriterion("value47 <", value, "value47");
            return (Criteria) this;
        }

        public Criteria andValue47LessThanOrEqualTo(String value) {
            addCriterion("value47 <=", value, "value47");
            return (Criteria) this;
        }

        public Criteria andValue47Like(String value) {
            addCriterion("value47 like", value, "value47");
            return (Criteria) this;
        }

        public Criteria andValue47NotLike(String value) {
            addCriterion("value47 not like", value, "value47");
            return (Criteria) this;
        }

        public Criteria andValue47In(List<String> values) {
            addCriterion("value47 in", values, "value47");
            return (Criteria) this;
        }

        public Criteria andValue47NotIn(List<String> values) {
            addCriterion("value47 not in", values, "value47");
            return (Criteria) this;
        }

        public Criteria andValue47Between(String value1, String value2) {
            addCriterion("value47 between", value1, value2, "value47");
            return (Criteria) this;
        }

        public Criteria andValue47NotBetween(String value1, String value2) {
            addCriterion("value47 not between", value1, value2, "value47");
            return (Criteria) this;
        }

        public Criteria andValue48IsNull() {
            addCriterion("value48 is null");
            return (Criteria) this;
        }

        public Criteria andValue48IsNotNull() {
            addCriterion("value48 is not null");
            return (Criteria) this;
        }

        public Criteria andValue48EqualTo(String value) {
            addCriterion("value48 =", value, "value48");
            return (Criteria) this;
        }

        public Criteria andValue48NotEqualTo(String value) {
            addCriterion("value48 <>", value, "value48");
            return (Criteria) this;
        }

        public Criteria andValue48GreaterThan(String value) {
            addCriterion("value48 >", value, "value48");
            return (Criteria) this;
        }

        public Criteria andValue48GreaterThanOrEqualTo(String value) {
            addCriterion("value48 >=", value, "value48");
            return (Criteria) this;
        }

        public Criteria andValue48LessThan(String value) {
            addCriterion("value48 <", value, "value48");
            return (Criteria) this;
        }

        public Criteria andValue48LessThanOrEqualTo(String value) {
            addCriterion("value48 <=", value, "value48");
            return (Criteria) this;
        }

        public Criteria andValue48Like(String value) {
            addCriterion("value48 like", value, "value48");
            return (Criteria) this;
        }

        public Criteria andValue48NotLike(String value) {
            addCriterion("value48 not like", value, "value48");
            return (Criteria) this;
        }

        public Criteria andValue48In(List<String> values) {
            addCriterion("value48 in", values, "value48");
            return (Criteria) this;
        }

        public Criteria andValue48NotIn(List<String> values) {
            addCriterion("value48 not in", values, "value48");
            return (Criteria) this;
        }

        public Criteria andValue48Between(String value1, String value2) {
            addCriterion("value48 between", value1, value2, "value48");
            return (Criteria) this;
        }

        public Criteria andValue48NotBetween(String value1, String value2) {
            addCriterion("value48 not between", value1, value2, "value48");
            return (Criteria) this;
        }

        public Criteria andValue49IsNull() {
            addCriterion("value49 is null");
            return (Criteria) this;
        }

        public Criteria andValue49IsNotNull() {
            addCriterion("value49 is not null");
            return (Criteria) this;
        }

        public Criteria andValue49EqualTo(String value) {
            addCriterion("value49 =", value, "value49");
            return (Criteria) this;
        }

        public Criteria andValue49NotEqualTo(String value) {
            addCriterion("value49 <>", value, "value49");
            return (Criteria) this;
        }

        public Criteria andValue49GreaterThan(String value) {
            addCriterion("value49 >", value, "value49");
            return (Criteria) this;
        }

        public Criteria andValue49GreaterThanOrEqualTo(String value) {
            addCriterion("value49 >=", value, "value49");
            return (Criteria) this;
        }

        public Criteria andValue49LessThan(String value) {
            addCriterion("value49 <", value, "value49");
            return (Criteria) this;
        }

        public Criteria andValue49LessThanOrEqualTo(String value) {
            addCriterion("value49 <=", value, "value49");
            return (Criteria) this;
        }

        public Criteria andValue49Like(String value) {
            addCriterion("value49 like", value, "value49");
            return (Criteria) this;
        }

        public Criteria andValue49NotLike(String value) {
            addCriterion("value49 not like", value, "value49");
            return (Criteria) this;
        }

        public Criteria andValue49In(List<String> values) {
            addCriterion("value49 in", values, "value49");
            return (Criteria) this;
        }

        public Criteria andValue49NotIn(List<String> values) {
            addCriterion("value49 not in", values, "value49");
            return (Criteria) this;
        }

        public Criteria andValue49Between(String value1, String value2) {
            addCriterion("value49 between", value1, value2, "value49");
            return (Criteria) this;
        }

        public Criteria andValue49NotBetween(String value1, String value2) {
            addCriterion("value49 not between", value1, value2, "value49");
            return (Criteria) this;
        }

        public Criteria andValue50IsNull() {
            addCriterion("value50 is null");
            return (Criteria) this;
        }

        public Criteria andValue50IsNotNull() {
            addCriterion("value50 is not null");
            return (Criteria) this;
        }

        public Criteria andValue50EqualTo(String value) {
            addCriterion("value50 =", value, "value50");
            return (Criteria) this;
        }

        public Criteria andValue50NotEqualTo(String value) {
            addCriterion("value50 <>", value, "value50");
            return (Criteria) this;
        }

        public Criteria andValue50GreaterThan(String value) {
            addCriterion("value50 >", value, "value50");
            return (Criteria) this;
        }

        public Criteria andValue50GreaterThanOrEqualTo(String value) {
            addCriterion("value50 >=", value, "value50");
            return (Criteria) this;
        }

        public Criteria andValue50LessThan(String value) {
            addCriterion("value50 <", value, "value50");
            return (Criteria) this;
        }

        public Criteria andValue50LessThanOrEqualTo(String value) {
            addCriterion("value50 <=", value, "value50");
            return (Criteria) this;
        }

        public Criteria andValue50Like(String value) {
            addCriterion("value50 like", value, "value50");
            return (Criteria) this;
        }

        public Criteria andValue50NotLike(String value) {
            addCriterion("value50 not like", value, "value50");
            return (Criteria) this;
        }

        public Criteria andValue50In(List<String> values) {
            addCriterion("value50 in", values, "value50");
            return (Criteria) this;
        }

        public Criteria andValue50NotIn(List<String> values) {
            addCriterion("value50 not in", values, "value50");
            return (Criteria) this;
        }

        public Criteria andValue50Between(String value1, String value2) {
            addCriterion("value50 between", value1, value2, "value50");
            return (Criteria) this;
        }

        public Criteria andValue50NotBetween(String value1, String value2) {
            addCriterion("value50 not between", value1, value2, "value50");
            return (Criteria) this;
        }

        public Criteria andEnabledFlagIsNull() {
            addCriterion("enabled_flag is null");
            return (Criteria) this;
        }

        public Criteria andEnabledFlagIsNotNull() {
            addCriterion("enabled_flag is not null");
            return (Criteria) this;
        }

        public Criteria andEnabledFlagEqualTo(String value) {
            addCriterion("enabled_flag =", value, "enabledFlag");
            return (Criteria) this;
        }

        public Criteria andEnabledFlagNotEqualTo(String value) {
            addCriterion("enabled_flag <>", value, "enabledFlag");
            return (Criteria) this;
        }

        public Criteria andEnabledFlagGreaterThan(String value) {
            addCriterion("enabled_flag >", value, "enabledFlag");
            return (Criteria) this;
        }

        public Criteria andEnabledFlagGreaterThanOrEqualTo(String value) {
            addCriterion("enabled_flag >=", value, "enabledFlag");
            return (Criteria) this;
        }

        public Criteria andEnabledFlagLessThan(String value) {
            addCriterion("enabled_flag <", value, "enabledFlag");
            return (Criteria) this;
        }

        public Criteria andEnabledFlagLessThanOrEqualTo(String value) {
            addCriterion("enabled_flag <=", value, "enabledFlag");
            return (Criteria) this;
        }

        public Criteria andEnabledFlagLike(String value) {
            addCriterion("enabled_flag like", value, "enabledFlag");
            return (Criteria) this;
        }

        public Criteria andEnabledFlagNotLike(String value) {
            addCriterion("enabled_flag not like", value, "enabledFlag");
            return (Criteria) this;
        }

        public Criteria andEnabledFlagIn(List<String> values) {
            addCriterion("enabled_flag in", values, "enabledFlag");
            return (Criteria) this;
        }

        public Criteria andEnabledFlagNotIn(List<String> values) {
            addCriterion("enabled_flag not in", values, "enabledFlag");
            return (Criteria) this;
        }

        public Criteria andEnabledFlagBetween(String value1, String value2) {
            addCriterion("enabled_flag between", value1, value2, "enabledFlag");
            return (Criteria) this;
        }

        public Criteria andEnabledFlagNotBetween(String value1, String value2) {
            addCriterion("enabled_flag not between", value1, value2, "enabledFlag");
            return (Criteria) this;
        }

        public Criteria andDateFromIsNull() {
            addCriterion("date_from is null");
            return (Criteria) this;
        }

        public Criteria andDateFromIsNotNull() {
            addCriterion("date_from is not null");
            return (Criteria) this;
        }

        public Criteria andDateFromEqualTo(Date value) {
            addCriterionForJDBCDate("date_from =", value, "dateFrom");
            return (Criteria) this;
        }

        public Criteria andDateFromNotEqualTo(Date value) {
            addCriterionForJDBCDate("date_from <>", value, "dateFrom");
            return (Criteria) this;
        }

        public Criteria andDateFromGreaterThan(Date value) {
            addCriterionForJDBCDate("date_from >", value, "dateFrom");
            return (Criteria) this;
        }

        public Criteria andDateFromGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("date_from >=", value, "dateFrom");
            return (Criteria) this;
        }

        public Criteria andDateFromLessThan(Date value) {
            addCriterionForJDBCDate("date_from <", value, "dateFrom");
            return (Criteria) this;
        }

        public Criteria andDateFromLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("date_from <=", value, "dateFrom");
            return (Criteria) this;
        }

        public Criteria andDateFromIn(List<Date> values) {
            addCriterionForJDBCDate("date_from in", values, "dateFrom");
            return (Criteria) this;
        }

        public Criteria andDateFromNotIn(List<Date> values) {
            addCriterionForJDBCDate("date_from not in", values, "dateFrom");
            return (Criteria) this;
        }

        public Criteria andDateFromBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("date_from between", value1, value2, "dateFrom");
            return (Criteria) this;
        }

        public Criteria andDateFromNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("date_from not between", value1, value2, "dateFrom");
            return (Criteria) this;
        }

        public Criteria andDateToIsNull() {
            addCriterion("date_to is null");
            return (Criteria) this;
        }

        public Criteria andDateToIsNotNull() {
            addCriterion("date_to is not null");
            return (Criteria) this;
        }

        public Criteria andDateToEqualTo(Date value) {
            addCriterionForJDBCDate("date_to =", value, "dateTo");
            return (Criteria) this;
        }

        public Criteria andDateToNotEqualTo(Date value) {
            addCriterionForJDBCDate("date_to <>", value, "dateTo");
            return (Criteria) this;
        }

        public Criteria andDateToGreaterThan(Date value) {
            addCriterionForJDBCDate("date_to >", value, "dateTo");
            return (Criteria) this;
        }

        public Criteria andDateToGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("date_to >=", value, "dateTo");
            return (Criteria) this;
        }

        public Criteria andDateToLessThan(Date value) {
            addCriterionForJDBCDate("date_to <", value, "dateTo");
            return (Criteria) this;
        }

        public Criteria andDateToLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("date_to <=", value, "dateTo");
            return (Criteria) this;
        }

        public Criteria andDateToIn(List<Date> values) {
            addCriterionForJDBCDate("date_to in", values, "dateTo");
            return (Criteria) this;
        }

        public Criteria andDateToNotIn(List<Date> values) {
            addCriterionForJDBCDate("date_to not in", values, "dateTo");
            return (Criteria) this;
        }

        public Criteria andDateToBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("date_to between", value1, value2, "dateTo");
            return (Criteria) this;
        }

        public Criteria andDateToNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("date_to not between", value1, value2, "dateTo");
            return (Criteria) this;
        }

        public Criteria andLangIsNull() {
            addCriterion("lang is null");
            return (Criteria) this;
        }

        public Criteria andLangIsNotNull() {
            addCriterion("lang is not null");
            return (Criteria) this;
        }

        public Criteria andLangEqualTo(String value) {
            addCriterion("lang =", value, "lang");
            return (Criteria) this;
        }

        public Criteria andLangNotEqualTo(String value) {
            addCriterion("lang <>", value, "lang");
            return (Criteria) this;
        }

        public Criteria andLangGreaterThan(String value) {
            addCriterion("lang >", value, "lang");
            return (Criteria) this;
        }

        public Criteria andLangGreaterThanOrEqualTo(String value) {
            addCriterion("lang >=", value, "lang");
            return (Criteria) this;
        }

        public Criteria andLangLessThan(String value) {
            addCriterion("lang <", value, "lang");
            return (Criteria) this;
        }

        public Criteria andLangLessThanOrEqualTo(String value) {
            addCriterion("lang <=", value, "lang");
            return (Criteria) this;
        }

        public Criteria andLangLike(String value) {
            addCriterion("lang like", value, "lang");
            return (Criteria) this;
        }

        public Criteria andLangNotLike(String value) {
            addCriterion("lang not like", value, "lang");
            return (Criteria) this;
        }

        public Criteria andLangIn(List<String> values) {
            addCriterion("lang in", values, "lang");
            return (Criteria) this;
        }

        public Criteria andLangNotIn(List<String> values) {
            addCriterion("lang not in", values, "lang");
            return (Criteria) this;
        }

        public Criteria andLangBetween(String value1, String value2) {
            addCriterion("lang between", value1, value2, "lang");
            return (Criteria) this;
        }

        public Criteria andLangNotBetween(String value1, String value2) {
            addCriterion("lang not between", value1, value2, "lang");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIsNull() {
            addCriterion("last_update_date is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIsNotNull() {
            addCriterion("last_update_date is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateEqualTo(Date value) {
            addCriterion("last_update_date =", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotEqualTo(Date value) {
            addCriterion("last_update_date <>", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateGreaterThan(Date value) {
            addCriterion("last_update_date >", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateGreaterThanOrEqualTo(Date value) {
            addCriterion("last_update_date >=", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateLessThan(Date value) {
            addCriterion("last_update_date <", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateLessThanOrEqualTo(Date value) {
            addCriterion("last_update_date <=", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIn(List<Date> values) {
            addCriterion("last_update_date in", values, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotIn(List<Date> values) {
            addCriterion("last_update_date not in", values, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateBetween(Date value1, Date value2) {
            addCriterion("last_update_date between", value1, value2, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotBetween(Date value1, Date value2) {
            addCriterion("last_update_date not between", value1, value2, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByIsNull() {
            addCriterion("last_updated_by is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByIsNotNull() {
            addCriterion("last_updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByEqualTo(String value) {
            addCriterion("last_updated_by =", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotEqualTo(String value) {
            addCriterion("last_updated_by <>", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByGreaterThan(String value) {
            addCriterion("last_updated_by >", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("last_updated_by >=", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByLessThan(String value) {
            addCriterion("last_updated_by <", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("last_updated_by <=", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByLike(String value) {
            addCriterion("last_updated_by like", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotLike(String value) {
            addCriterion("last_updated_by not like", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByIn(List<String> values) {
            addCriterion("last_updated_by in", values, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotIn(List<String> values) {
            addCriterion("last_updated_by not in", values, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByBetween(String value1, String value2) {
            addCriterion("last_updated_by between", value1, value2, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotBetween(String value1, String value2) {
            addCriterion("last_updated_by not between", value1, value2, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdateLoginIsNull() {
            addCriterion("last_update_login is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateLoginIsNotNull() {
            addCriterion("last_update_login is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateLoginEqualTo(Integer value) {
            addCriterion("last_update_login =", value, "lastUpdateLogin");
            return (Criteria) this;
        }

        public Criteria andLastUpdateLoginNotEqualTo(Integer value) {
            addCriterion("last_update_login <>", value, "lastUpdateLogin");
            return (Criteria) this;
        }

        public Criteria andLastUpdateLoginGreaterThan(Integer value) {
            addCriterion("last_update_login >", value, "lastUpdateLogin");
            return (Criteria) this;
        }

        public Criteria andLastUpdateLoginGreaterThanOrEqualTo(Integer value) {
            addCriterion("last_update_login >=", value, "lastUpdateLogin");
            return (Criteria) this;
        }

        public Criteria andLastUpdateLoginLessThan(Integer value) {
            addCriterion("last_update_login <", value, "lastUpdateLogin");
            return (Criteria) this;
        }

        public Criteria andLastUpdateLoginLessThanOrEqualTo(Integer value) {
            addCriterion("last_update_login <=", value, "lastUpdateLogin");
            return (Criteria) this;
        }

        public Criteria andLastUpdateLoginIn(List<Integer> values) {
            addCriterion("last_update_login in", values, "lastUpdateLogin");
            return (Criteria) this;
        }

        public Criteria andLastUpdateLoginNotIn(List<Integer> values) {
            addCriterion("last_update_login not in", values, "lastUpdateLogin");
            return (Criteria) this;
        }

        public Criteria andLastUpdateLoginBetween(Integer value1, Integer value2) {
            addCriterion("last_update_login between", value1, value2, "lastUpdateLogin");
            return (Criteria) this;
        }

        public Criteria andLastUpdateLoginNotBetween(Integer value1, Integer value2) {
            addCriterion("last_update_login not between", value1, value2, "lastUpdateLogin");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreationDateIsNull() {
            addCriterion("creation_date is null");
            return (Criteria) this;
        }

        public Criteria andCreationDateIsNotNull() {
            addCriterion("creation_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreationDateEqualTo(Date value) {
            addCriterion("creation_date =", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateNotEqualTo(Date value) {
            addCriterion("creation_date <>", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateGreaterThan(Date value) {
            addCriterion("creation_date >", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateGreaterThanOrEqualTo(Date value) {
            addCriterion("creation_date >=", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateLessThan(Date value) {
            addCriterion("creation_date <", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateLessThanOrEqualTo(Date value) {
            addCriterion("creation_date <=", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateIn(List<Date> values) {
            addCriterion("creation_date in", values, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateNotIn(List<Date> values) {
            addCriterion("creation_date not in", values, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateBetween(Date value1, Date value2) {
            addCriterion("creation_date between", value1, value2, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateNotBetween(Date value1, Date value2) {
            addCriterion("creation_date not between", value1, value2, "creationDate");
            return (Criteria) this;
        }

        public Criteria andValue1LikeInsensitive(String value) {
            addCriterion("upper(value1) like", value.toUpperCase(), "value1");
            return (Criteria) this;
        }

        public Criteria andValue2LikeInsensitive(String value) {
            addCriterion("upper(value2) like", value.toUpperCase(), "value2");
            return (Criteria) this;
        }

        public Criteria andValue3LikeInsensitive(String value) {
            addCriterion("upper(value3) like", value.toUpperCase(), "value3");
            return (Criteria) this;
        }

        public Criteria andValue4LikeInsensitive(String value) {
            addCriterion("upper(value4) like", value.toUpperCase(), "value4");
            return (Criteria) this;
        }

        public Criteria andValue5LikeInsensitive(String value) {
            addCriterion("upper(value5) like", value.toUpperCase(), "value5");
            return (Criteria) this;
        }

        public Criteria andValue6LikeInsensitive(String value) {
            addCriterion("upper(value6) like", value.toUpperCase(), "value6");
            return (Criteria) this;
        }

        public Criteria andValue7LikeInsensitive(String value) {
            addCriterion("upper(value7) like", value.toUpperCase(), "value7");
            return (Criteria) this;
        }

        public Criteria andValue8LikeInsensitive(String value) {
            addCriterion("upper(value8) like", value.toUpperCase(), "value8");
            return (Criteria) this;
        }

        public Criteria andValue9LikeInsensitive(String value) {
            addCriterion("upper(value9) like", value.toUpperCase(), "value9");
            return (Criteria) this;
        }

        public Criteria andValue10LikeInsensitive(String value) {
            addCriterion("upper(value10) like", value.toUpperCase(), "value10");
            return (Criteria) this;
        }

        public Criteria andValue11LikeInsensitive(String value) {
            addCriterion("upper(value11) like", value.toUpperCase(), "value11");
            return (Criteria) this;
        }

        public Criteria andValue12LikeInsensitive(String value) {
            addCriterion("upper(value12) like", value.toUpperCase(), "value12");
            return (Criteria) this;
        }

        public Criteria andValue13LikeInsensitive(String value) {
            addCriterion("upper(value13) like", value.toUpperCase(), "value13");
            return (Criteria) this;
        }

        public Criteria andValue14LikeInsensitive(String value) {
            addCriterion("upper(value14) like", value.toUpperCase(), "value14");
            return (Criteria) this;
        }

        public Criteria andValue15LikeInsensitive(String value) {
            addCriterion("upper(value15) like", value.toUpperCase(), "value15");
            return (Criteria) this;
        }

        public Criteria andValue16LikeInsensitive(String value) {
            addCriterion("upper(value16) like", value.toUpperCase(), "value16");
            return (Criteria) this;
        }

        public Criteria andValue17LikeInsensitive(String value) {
            addCriterion("upper(value17) like", value.toUpperCase(), "value17");
            return (Criteria) this;
        }

        public Criteria andValue18LikeInsensitive(String value) {
            addCriterion("upper(value18) like", value.toUpperCase(), "value18");
            return (Criteria) this;
        }

        public Criteria andValue19LikeInsensitive(String value) {
            addCriterion("upper(value19) like", value.toUpperCase(), "value19");
            return (Criteria) this;
        }

        public Criteria andValue20LikeInsensitive(String value) {
            addCriterion("upper(value20) like", value.toUpperCase(), "value20");
            return (Criteria) this;
        }

        public Criteria andValue21LikeInsensitive(String value) {
            addCriterion("upper(value21) like", value.toUpperCase(), "value21");
            return (Criteria) this;
        }

        public Criteria andValue22LikeInsensitive(String value) {
            addCriterion("upper(value22) like", value.toUpperCase(), "value22");
            return (Criteria) this;
        }

        public Criteria andValue23LikeInsensitive(String value) {
            addCriterion("upper(value23) like", value.toUpperCase(), "value23");
            return (Criteria) this;
        }

        public Criteria andValue24LikeInsensitive(String value) {
            addCriterion("upper(value24) like", value.toUpperCase(), "value24");
            return (Criteria) this;
        }

        public Criteria andValue25LikeInsensitive(String value) {
            addCriterion("upper(value25) like", value.toUpperCase(), "value25");
            return (Criteria) this;
        }

        public Criteria andValue26LikeInsensitive(String value) {
            addCriterion("upper(value26) like", value.toUpperCase(), "value26");
            return (Criteria) this;
        }

        public Criteria andValue27LikeInsensitive(String value) {
            addCriterion("upper(value27) like", value.toUpperCase(), "value27");
            return (Criteria) this;
        }

        public Criteria andValue28LikeInsensitive(String value) {
            addCriterion("upper(value28) like", value.toUpperCase(), "value28");
            return (Criteria) this;
        }

        public Criteria andValue29LikeInsensitive(String value) {
            addCriterion("upper(value29) like", value.toUpperCase(), "value29");
            return (Criteria) this;
        }

        public Criteria andValue30LikeInsensitive(String value) {
            addCriterion("upper(value30) like", value.toUpperCase(), "value30");
            return (Criteria) this;
        }

        public Criteria andValue31LikeInsensitive(String value) {
            addCriterion("upper(value31) like", value.toUpperCase(), "value31");
            return (Criteria) this;
        }

        public Criteria andValue32LikeInsensitive(String value) {
            addCriterion("upper(value32) like", value.toUpperCase(), "value32");
            return (Criteria) this;
        }

        public Criteria andValue33LikeInsensitive(String value) {
            addCriterion("upper(value33) like", value.toUpperCase(), "value33");
            return (Criteria) this;
        }

        public Criteria andValue34LikeInsensitive(String value) {
            addCriterion("upper(value34) like", value.toUpperCase(), "value34");
            return (Criteria) this;
        }

        public Criteria andValue35LikeInsensitive(String value) {
            addCriterion("upper(value35) like", value.toUpperCase(), "value35");
            return (Criteria) this;
        }

        public Criteria andValue36LikeInsensitive(String value) {
            addCriterion("upper(value36) like", value.toUpperCase(), "value36");
            return (Criteria) this;
        }

        public Criteria andValue37LikeInsensitive(String value) {
            addCriterion("upper(value37) like", value.toUpperCase(), "value37");
            return (Criteria) this;
        }

        public Criteria andValue38LikeInsensitive(String value) {
            addCriterion("upper(value38) like", value.toUpperCase(), "value38");
            return (Criteria) this;
        }

        public Criteria andValue39LikeInsensitive(String value) {
            addCriterion("upper(value39) like", value.toUpperCase(), "value39");
            return (Criteria) this;
        }

        public Criteria andValue40LikeInsensitive(String value) {
            addCriterion("upper(value40) like", value.toUpperCase(), "value40");
            return (Criteria) this;
        }

        public Criteria andValue41LikeInsensitive(String value) {
            addCriterion("upper(value41) like", value.toUpperCase(), "value41");
            return (Criteria) this;
        }

        public Criteria andValue42LikeInsensitive(String value) {
            addCriterion("upper(value42) like", value.toUpperCase(), "value42");
            return (Criteria) this;
        }

        public Criteria andValue43LikeInsensitive(String value) {
            addCriterion("upper(value43) like", value.toUpperCase(), "value43");
            return (Criteria) this;
        }

        public Criteria andValue44LikeInsensitive(String value) {
            addCriterion("upper(value44) like", value.toUpperCase(), "value44");
            return (Criteria) this;
        }

        public Criteria andValue45LikeInsensitive(String value) {
            addCriterion("upper(value45) like", value.toUpperCase(), "value45");
            return (Criteria) this;
        }

        public Criteria andValue46LikeInsensitive(String value) {
            addCriterion("upper(value46) like", value.toUpperCase(), "value46");
            return (Criteria) this;
        }

        public Criteria andValue47LikeInsensitive(String value) {
            addCriterion("upper(value47) like", value.toUpperCase(), "value47");
            return (Criteria) this;
        }

        public Criteria andValue48LikeInsensitive(String value) {
            addCriterion("upper(value48) like", value.toUpperCase(), "value48");
            return (Criteria) this;
        }

        public Criteria andValue49LikeInsensitive(String value) {
            addCriterion("upper(value49) like", value.toUpperCase(), "value49");
            return (Criteria) this;
        }

        public Criteria andValue50LikeInsensitive(String value) {
            addCriterion("upper(value50) like", value.toUpperCase(), "value50");
            return (Criteria) this;
        }

        public Criteria andEnabledFlagLikeInsensitive(String value) {
            addCriterion("upper(enabled_flag) like", value.toUpperCase(), "enabledFlag");
            return (Criteria) this;
        }

        public Criteria andLangLikeInsensitive(String value) {
            addCriterion("upper(lang) like", value.toUpperCase(), "lang");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByLikeInsensitive(String value) {
            addCriterion("upper(last_updated_by) like", value.toUpperCase(), "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLikeInsensitive(String value) {
            addCriterion("upper(created_by) like", value.toUpperCase(), "createdBy");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}