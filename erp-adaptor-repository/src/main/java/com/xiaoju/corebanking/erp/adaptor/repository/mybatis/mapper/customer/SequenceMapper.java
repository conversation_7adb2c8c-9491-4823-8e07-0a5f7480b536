package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SequenceMapper {
    Long getCurrentValue(String name);

    Long getCurrentValueWithLock(String name);

    int atomicIncrement(@Param("name") String name,
                        @Param("expectedValue") long expectedValue,
                        @Param("increment") int increment);

    int createSequence(@Param("name") String name,
                       @Param("startValue") long startValue,
                       @Param("batchSize") int batchSize);

    int updateValue(@Param("name") String name, @Param("newValue") long newValue);

    Integer getBatchSize(String name);

    int updateBatchSize(@Param("name") String name, @Param("batchSize") int batchSize);
}
