package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.CargosAndAbonosDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SummaryResultDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlShenmaPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlShenmaPOMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SofiGlShenmaCustomerMapper extends SofiGlShenmaPOMapper {
    int batchInsert(@Param("list") List<SofiGlShenmaPO> list);

    List<SofiGlShenmaPO> queryPendingShenmaRecords(@Param("processStatus") String processStatus, @Param("processDay") String processDay);

    List<SofiGlShenmaPO> queryByReference(@Param("reference") String reference);

    CargosAndAbonosDO selectCargosAndAbonosDO(@Param("reference") String reference);

    int updateReferenceSelective(@Param("processDay") String processDay, @Param("voucherGroup") String voucherGroup, @Param("linkReference") String linkReference, @Param("sofiGlShenmaPO") SofiGlShenmaPO sofiGlShenmaPO);

    int updateTradeNoSelective(@Param("tradeNo") String tradeNo, @Param("version") Long version, @Param("processStatus") String processStatus, @Param("sofiGlShenmaPO") SofiGlShenmaPO sofiGlShenmaPO);

    List<SofiGlShenmaPO> querySofiGlShenmaByGroupIdAndProcessDay(@Param("groupId") Long groupId, @Param("processDay") String processDay);

    int updateByProcessDayAndGroupIdAndStatus(@Param("processDay") String processDay, @Param("groupId") Long groupId, @Param("processStatus") String processStatus, @Param("sofiGlShenmaPO") SofiGlShenmaPO sofiGlShenmaPO);

    List<SummaryResultDO> selectShenMaSummary(@Param("processDay") String processDay);

    List<SummaryResultDO> selectShenMaSumByVoucherGroup(@Param("processDay") String processDay);

    List<SofiGlShenmaPO> queryByPolizaIdsAndProcessDay(@Param("externalReferenceList") List<String> externalReferenceList, @Param("processDay") String processDay);

    List<SofiGlShenmaPO> findByIndexFields(@Param("processDay") String processDay,
                                           @Param("linkReference") String linkReference);

    int deleteByIndexFields(@Param("processDay") String processDay,
                           @Param("linkReference") String linkReference);

    int deleteByProcessDay(@Param("processDay") String processDay);
}