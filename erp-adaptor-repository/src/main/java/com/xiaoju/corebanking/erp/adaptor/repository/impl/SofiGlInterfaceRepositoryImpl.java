package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.github.pagehelper.PageHelper;
import com.xiaoju.corebanking.erp.adaptor.common.utils.LogUtils;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.converter.SofiGlInterfaceModelConverter;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.CargosAndAbonosDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiEmailDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderQueryDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHisPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfacePO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfacePOExample;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SofiGlInterfaceCustomerMapper;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfaceHisPOMapper;
import com.xiaoju.godson.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * @Description: 类描述
 * @author: zhangcc
 * @date: 2025/5/14$
 **/
@Slf4j
@Repository
public class SofiGlInterfaceRepositoryImpl implements SofiGlInterfaceRepository {
    @Resource
    private SofiGlInterfaceModelConverter sofiGlInterfaceModelConverter;

    @Resource
    private SofiGlInterfaceCustomerMapper sofiGlInterfaceCustomerMapper;

    @Resource
    private SofiGlInterfaceHisPOMapper sofiGlInterfaceHisPOMapper;

    @Resource
    private ApplicationContext applicationContext;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertSelective(SofiGlInterfaceDO sofiGlInterfaceDO) {
        SofiGlInterfacePO convert = sofiGlInterfaceModelConverter.convert(sofiGlInterfaceDO);
        sofiGlInterfaceCustomerMapper.insertSelective(convert);
    }

    @Override
    public List<SofiGlInterfaceDO> selectByProcessDayAndStatus(String processDay, String code) {
        if (StringUtils.isEmpty(processDay) || StringUtils.isEmpty(code)) {
            return Collections.emptyList();
        }

        SofiGlInterfacePOExample example = new SofiGlInterfacePOExample();
        example.createCriteria()
                .andProcessDayEqualTo(processDay)
                .andProcessStatusEqualTo(code);

        List<SofiGlInterfacePO> poList = sofiGlInterfaceCustomerMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptyList();
        }

        return poList.stream()
                .map(sofiGlInterfaceModelConverter::convert)
                .collect(Collectors.toList());
    }

    @Override
    public List<SofiGlInterfacePO> selectByPolizaIdAndDetalle(Long polizaId, Long detallePolizaId) {
        SofiGlInterfacePOExample sofiGlInterfacePOExample = new SofiGlInterfacePOExample();
        sofiGlInterfacePOExample.createCriteria().andPolizaIdEqualTo(polizaId).andDetallePolizaIdEqualTo(detallePolizaId);
        return sofiGlInterfaceCustomerMapper.selectByExample(sofiGlInterfacePOExample);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertInterface(List<SofiGlInterfaceDO> interfaceList) {
        if (interfaceList == null || interfaceList.isEmpty()) {
            return;
        }

        // 按照polizaId + DetallePolizaID 分组，备份已存在记录
        Map<String, List<SofiGlInterfaceDO>> groupedByPolizaAndDetalle = interfaceList.stream()
                .filter(record -> record.getPolizaId() != null && record.getDetallePolizaId() != null)
                .collect(Collectors.groupingBy(
                        record -> record.getPolizaId() + "_" + record.getDetallePolizaId()
                ));
        for (Map.Entry<String, List<SofiGlInterfaceDO>> entry : groupedByPolizaAndDetalle.entrySet()) {
            String[] parts = entry.getKey().split("_");
            Long polizaId = Long.valueOf(parts[0]);
            Long detallePolizaId = Long.valueOf(parts[1]);
            if (!CollectionUtils.isEmpty(entry.getValue())) {
                SofiGlInterfaceDO firstRecord = entry.getValue().get(0);
                if (firstRecord != null) {
                    String processDay = firstRecord.getProcessDay();
                    String fileName = firstRecord.getFileName();

                    List<SofiGlInterfacePO> existingRecords = sofiGlInterfaceCustomerMapper.findByIndexFields(
                            processDay, fileName, polizaId, detallePolizaId);

                    if (!CollectionUtils.isEmpty(existingRecords)) {
                        log.info("在索引(process_day={}, file_name={}, poliza_id={}, detallePolizaId={})下发现{}条已存在记录",
                                processDay, fileName, polizaId, detallePolizaId, existingRecords.size());

                        for (SofiGlInterfacePO record : existingRecords) {
                            log.info("已存在记录详情: {}", JsonUtil.toString(record));
                            SofiGlInterfaceHisPO hisPO = new SofiGlInterfaceHisPO();

                            BeanUtils.copyProperties(sofiGlInterfaceModelConverter.convert(record), hisPO);

                            if (record.getProcessStatus() != null) {
                                hisPO.setProcessStatus(record.getProcessStatus());
                            }
                            // 备份历史表
                            sofiGlInterfaceHisPOMapper.insertSelective(hisPO);
                        }
                        log.info("备份成功{}条记录", existingRecords.size());

                        int deleteCount = sofiGlInterfaceCustomerMapper.deleteByIndexFields(
                                processDay, fileName, polizaId, detallePolizaId);

                        log.info("成功删除{}条已存在记录", deleteCount);
                    }
                }
            }
        }

        List<SofiGlInterfacePO> poList = interfaceList.stream()
                .map(sofiGlInterfaceModelConverter::convert)
                .collect(Collectors.toList());
        sofiGlInterfaceCustomerMapper.batchInsert(poList);
    }

    @Override
    public List<Long> queryGroupId(SofiGlInterfaceHeaderQueryDO sofiGlInterfaceHeaderQueryDO) {
        PageHelper.startPage(sofiGlInterfaceHeaderQueryDO.getPageNum(), sofiGlInterfaceHeaderQueryDO.getPageSize());
        return sofiGlInterfaceCustomerMapper.queryGroupIdByProcessStatus(sofiGlInterfaceHeaderQueryDO.getProcessDay());
    }

    @Override
    public List<SofiGlInterfaceDO> queryGLInterfaceData(SofiGlInterfaceDO sofiGlInterfaceDO) {
        SofiGlInterfacePO convert = sofiGlInterfaceModelConverter.convert(sofiGlInterfaceDO);
        List<SofiGlInterfacePO> sofiGlInterfacePOS = sofiGlInterfaceCustomerMapper.queryGLInterfaceData(convert);
        return sofiGlInterfacePOS.stream().map(sofiGlInterfaceModelConverter::convert).collect(Collectors.toList());
    }

    @Override
    public CargosAndAbonosDO selectCargosAndAbonosDO(Long polizaId) {
        return sofiGlInterfaceCustomerMapper.selectCargosAndAbonosDO(polizaId);
    }

    @Override
    public List<SofiGlInterfaceDO> selectSofiGlInterfaceByExternalReference(List<Long> externalReference) {
        return sofiGlInterfaceCustomerMapper.queryByPolizaId(externalReference).stream().map(sofiGlInterfaceModelConverter::convert).collect(Collectors.toList());
    }

    @Override
    public List<SofiGlInterfaceDO> selectSofiGlInterfaceByPolizaIdsAndProcessDay(List<String> externalReferenceList, String processDay) {
        if (CollectionUtils.isEmpty(externalReferenceList) || StringUtils.isEmpty(processDay)) {
            return Collections.emptyList();
        }

        List<Long> collect = externalReferenceList.stream()
                .map(Long::valueOf)
                .collect(Collectors.toList());

        SofiGlInterfacePOExample example = new SofiGlInterfacePOExample();
        example.createCriteria()
                .andPolizaIdIn(collect)
                .andProcessDayEqualTo(processDay);

        List<SofiGlInterfacePO> poList = sofiGlInterfaceCustomerMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptyList();
        }

        return poList.stream()
                .map(sofiGlInterfaceModelConverter::convert)
                .collect(Collectors.toList());
    }

    @Override
    public void updateInterface(SofiGlInterfaceDO sofiGlInterfaceDO, SofiGlInterfaceDO update) {
        Assert.notNull(sofiGlInterfaceDO.getDetallePolizaId(), "detailPolizaId can not be null!");
        update.setObjectVersionNumber(sofiGlInterfaceDO.getObjectVersionNumber() + 1);
        log.info("updateInterface sofiGlInterfaceDO={},update={}", sofiGlInterfaceDO, update);
        SofiGlInterfacePO sofiGlInterfacePO = sofiGlInterfaceModelConverter.convert(update);
        log.info("updateInterface sofiGlInterfaceDO={}", LogUtils.toString(sofiGlInterfacePO));
        int effected = sofiGlInterfaceCustomerMapper.updateDetailPolizaIdSelective(sofiGlInterfaceDO.getId(),sofiGlInterfaceDO.getPolizaId(),sofiGlInterfaceDO.getDetallePolizaId(), sofiGlInterfaceDO.getObjectVersionNumber(), sofiGlInterfaceDO.getProcessStatus().getCode(), sofiGlInterfacePO);
        log.info("updateInterface effected={},sofiGlInterfaceDO={},update={}", effected, sofiGlInterfaceDO, update);
        // 数据回填
        SofiGlInterfaceDO backup = new SofiGlInterfaceDO();
        sofiGlInterfaceModelConverter.copyIgnoreNullValue(sofiGlInterfaceDO, backup);
        sofiGlInterfaceModelConverter.copyIgnoreNullValue(update, sofiGlInterfaceDO);

    }

    @Override
    public void updateInterfaceByPolizaId(SofiGlInterfaceDO sofiGlInterfaceDO, SofiGlInterfaceDO update) {
        Assert.notNull(sofiGlInterfaceDO.getPolizaId(), "polizaId can not be null!");
        update.setObjectVersionNumber(sofiGlInterfaceDO.getObjectVersionNumber() + 1);
        log.info("updateInterfaceByPolizaId sofiGlInterfaceDO={},update={}", sofiGlInterfaceDO, update);
        SofiGlInterfacePO sofiGlInterfacePO = sofiGlInterfaceModelConverter.convert(update);
        log.info("updateInterface sofiGlInterfaceDO={}", LogUtils.toString(sofiGlInterfacePO));
        int effected = sofiGlInterfaceCustomerMapper.updatePolizaIdSelective(sofiGlInterfaceDO.getPolizaId(), sofiGlInterfacePO);
        log.info("updateInterfaceByPolizaId effected={},sofiGlInterfaceDO={},update={}", effected, sofiGlInterfaceDO, update);
        // 数据回填
        SofiGlInterfaceDO backup = new SofiGlInterfaceDO();
        sofiGlInterfaceModelConverter.copyIgnoreNullValue(sofiGlInterfaceDO, backup);
        sofiGlInterfaceModelConverter.copyIgnoreNullValue(update, sofiGlInterfaceDO);
    }

    @Override
    public int updateByExampleSelective(SofiGlInterfacePO record, SofiGlInterfacePOExample example) {
        return sofiGlInterfaceCustomerMapper.updateByExampleSelective(record, example);
    }

    @Override
    public List<SofiGlInterfaceDO> selectByPolizaIdAndDay(Long polizaId, String processDay, Long groupId) {
        if (polizaId == null || StringUtils.isEmpty(processDay)) {
            return Collections.emptyList();
        }
        SofiGlInterfacePOExample example = new SofiGlInterfacePOExample();
        example.createCriteria()
                .andPolizaIdEqualTo(polizaId)
                .andProcessDayEqualTo(processDay)
                .andGroupIdEqualTo(groupId);

        List<SofiGlInterfacePO> poList = sofiGlInterfaceCustomerMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptyList();
        }

        return poList.stream()
                .map(sofiGlInterfaceModelConverter::convert)
                .collect(Collectors.toList());
    }

    @Override
    public List<SofiEmailDO> selectCountInterfaceEmailData(String processDay) {
        return sofiGlInterfaceCustomerMapper.selectConutInterfaceEmailData(processDay);
    }

    @Override
    public List<SofiGlInterfaceDO> selectSofiGlInterfaceByGroupIdAndProcessDay(Long groupId, String processDay) {
        List<SofiGlInterfacePO> poList = sofiGlInterfaceCustomerMapper.selectSofiGlInterfaceByGroupIdAndProcessDay(groupId,processDay);
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptyList();
        }
        return poList.stream()
                .map(sofiGlInterfaceModelConverter::convert)
                .collect(Collectors.toList());
    }

    @Override
    public int countByProcessDayAndStatus(String processDay, String status) {
        if (StringUtils.isEmpty(processDay) || StringUtils.isEmpty(status)) {
            return 0;
        }

        long count = sofiGlInterfaceCustomerMapper.countByExample(processDay, status);
        log.info("countByProcessDayAndStatus: processDay={}, status={}, count={}", processDay, status, count);

        return (int) count;
    }

    @Override
    public List<Map<String, Object>> selectGroupByPolizaId(String processDay, String status) {
        return sofiGlInterfaceCustomerMapper.selectGroupByPolizaId(processDay, status);
    }
}
