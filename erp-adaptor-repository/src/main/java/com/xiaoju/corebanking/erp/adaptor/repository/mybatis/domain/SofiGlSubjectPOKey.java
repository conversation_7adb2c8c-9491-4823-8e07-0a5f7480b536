package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain;

import java.io.Serializable;

public class SofiGlSubjectPOKey implements Serializable {
    private String subjectCode;

    private String subjectSetNo;

    private static final long serialVersionUID = 1L;

    public String getSubjectCode() {
        return subjectCode;
    }

    public void setSubjectCode(String subjectCode) {
        this.subjectCode = subjectCode == null ? null : subjectCode.trim();
    }

    public String getSubjectSetNo() {
        return subjectSetNo;
    }

    public void setSubjectSetNo(String subjectSetNo) {
        this.subjectSetNo = subjectSetNo == null ? null : subjectSetNo.trim();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SofiGlSubjectPOKey other = (SofiGlSubjectPOKey) that;
        return (this.getSubjectCode() == null ? other.getSubjectCode() == null : this.getSubjectCode().equals(other.getSubjectCode()))
            && (this.getSubjectSetNo() == null ? other.getSubjectSetNo() == null : this.getSubjectSetNo().equals(other.getSubjectSetNo()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getSubjectCode() == null) ? 0 : getSubjectCode().hashCode());
        result = prime * result + ((getSubjectSetNo() == null) ? 0 : getSubjectSetNo().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", subjectCode=").append(subjectCode);
        sb.append(", subjectSetNo=").append(subjectSetNo);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}