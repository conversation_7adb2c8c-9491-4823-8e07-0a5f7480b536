package com.xiaoju.corebanking.erp.adaptor.repository.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class CuxTwoTabValueDO {

    private String formCode;

    private Long headerId;

    private Long sourceId;

    private String value1;

    private String value2;

    private String value3;

    private String value4;

    private String value5;

    private String value6;

    private String value7;

    private String value8;

    private String value9;

    private String value10;

    private String value11;

    private String value12;

    private String value13;

    private String value14;

    private String value15;

    private String value16;

    private String value17;

    private String value18;

    private String value19;

    private String value20;

    private String value21;

    private String value22;

    private String value23;

    private String value24;

    private String value25;

    private String value26;

    private String value27;

    private String value28;

    private String value29;

    private String value30;

    private String value31;

    private String value32;

    private String value33;

    private String value34;

    private String value35;

    private String value36;

    private String value37;

    private String value38;

    private String value39;

    private String value40;

    private String value41;

    private String value42;

    private String value43;

    private String value44;

    private String value45;

    private String value46;

    private String value47;

    private String value48;

    private String value49;

    private String value50;

    private String enabledFlag;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateFrom;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateTo;

    private String lang;

    private Integer version;
}
