package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated;

import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlAcctHistPO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface SofiGlAcctHistPOMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SofiGlAcctHistPO record);

    int insertSelective(SofiGlAcctHistPO record);

    SofiGlAcctHistPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SofiGlAcctHistPO record);

    int updateByPrimaryKey(SofiGlAcctHistPO record);
}