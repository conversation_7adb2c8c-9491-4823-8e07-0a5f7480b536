package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.github.pagehelper.PageHelper;
import com.xiaoju.corebanking.erp.adaptor.common.utils.LogUtils;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceCommonRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.converter.SofiGlInterfaceCommonModelConverter;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceCommonDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceCommonPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SofiGlInterfaceCommonCustomerMapper;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfaceCommonPOMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 类描述
 * @author: zhangcc
 * @date: 2025/7/1$
 **/
@Slf4j
@Repository
public class SofiGlInterfaceCommonRepositoryImpl implements SofiGlInterfaceCommonRepository {
    @Autowired
    private SofiGlInterfaceCommonPOMapper sofiGlInterfaceCommonPOMapper;

    @Autowired
    private SofiGlInterfaceCommonCustomerMapper sofiGlInterfaceCommonCustomerMapper;
    @Autowired
    private SofiGlInterfaceCommonModelConverter sofiGlInterfaceCommonModelConverter;
    @Override
    public void insertSelective(SofiGlInterfaceCommonDO sofiGlInterfaceCommonDO) {
        sofiGlInterfaceCommonPOMapper.insertSelective(sofiGlInterfaceCommonModelConverter.convert(sofiGlInterfaceCommonDO));
    }

    @Override
    public int batchInsert(List<SofiGlInterfaceCommonDO> list) {
        List<SofiGlInterfaceCommonPO> poList = list.stream()
                .map(sofiGlInterfaceCommonModelConverter::convert)
                .collect(Collectors.toList());
        return sofiGlInterfaceCommonCustomerMapper.batchInsert(poList);
    }

    @Override
    public List<Long> queryGroupIds(Integer pageNum,Integer pageSize,String processDay) {
        PageHelper.startPage(pageNum,pageSize);
        return sofiGlInterfaceCommonCustomerMapper.selectGroupIds(processDay);
    }

    @Override
    public List<SofiGlInterfaceCommonDO> querySofiGlInterfaceCommonDOList(String systemCode, String processDay, Long groupId) {
        List<SofiGlInterfaceCommonPO> poList = sofiGlInterfaceCommonCustomerMapper.selectSofiGlInterfaceCommonDOList(systemCode,processDay,groupId);
        if(CollectionUtils.isEmpty(poList)) {
            return Collections.emptyList();
        }
        return poList.stream().map(sofiGlInterfaceCommonModelConverter::convert).collect(Collectors.toList());
    }

    @Override
    public int updateSofiGlInterfaceCommon(String systemCode, String processDay, String oldStatus, Long groupId, SofiGlInterfaceCommonDO update) {
        SofiGlInterfaceCommonPO po = sofiGlInterfaceCommonModelConverter.convert(update);
        log.info("updateSofiGlInterfaceCommon po={}", LogUtils.toString(po));
        int effected = sofiGlInterfaceCommonCustomerMapper.updateBySystemCodeAndProcessDayAndGroupIdAndStatus(systemCode,processDay,groupId,oldStatus,po);
        log.info("updateSofiGlInterfaceCommon effected={},update={}", effected, update);
        return effected;
    }

    @Override
    public List<SofiGlInterfaceCommonDO> queryByReference5AndProcessDay(List<String> externalReferenceList, String processDay) {
        List<SofiGlInterfaceCommonPO> poList = sofiGlInterfaceCommonCustomerMapper.queryByReference5AndProcessDay(externalReferenceList, processDay);
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptyList();
        }
        return poList.stream().map(sofiGlInterfaceCommonModelConverter::convert).collect(Collectors.toList());
    }

    @Override
    public void updateInterfaceCommon(SofiGlInterfaceCommonDO original, SofiGlInterfaceCommonDO update) {
        SofiGlInterfaceCommonPO updatePO = sofiGlInterfaceCommonModelConverter.convert(update);
        log.info("updateInterfaceCommon updatePO={}", LogUtils.toString(updatePO));
        int effected = sofiGlInterfaceCommonCustomerMapper.updateByIdAndProcessDay(original.getId(), original.getProcessDay(), updatePO);
        log.info("updateInterfaceCommon effected={}, originalId={}, processDay={}", effected, original.getId(), original.getProcessDay());
    }
}
