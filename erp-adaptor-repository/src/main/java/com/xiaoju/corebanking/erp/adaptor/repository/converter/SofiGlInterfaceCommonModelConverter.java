package com.xiaoju.corebanking.erp.adaptor.repository.converter;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.utils.CommonUtils;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceCommonDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceCommonPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfacePO;
import org.springframework.stereotype.Component;

@Component
public class SofiGlInterfaceCommonModelConverter {

    public SofiGlInterfaceCommonDO convert(SofiGlInterfaceCommonPO sofiGlInterfaceCommonPO) {
        SofiGlInterfaceCommonDO sofiGlInterfaceCommonDO = new SofiGlInterfaceCommonDO();
        sofiGlInterfaceCommonDO.setId(sofiGlInterfaceCommonPO.getId());
        sofiGlInterfaceCommonDO.setSystemCode(sofiGlInterfaceCommonPO.getSystemCode());
        sofiGlInterfaceCommonDO.setProcessDay(sofiGlInterfaceCommonPO.getProcessDay());
        sofiGlInterfaceCommonDO.setAccountingDate(sofiGlInterfaceCommonPO.getAccountingDate());
        sofiGlInterfaceCommonDO.setPeriodName(sofiGlInterfaceCommonPO.getPeriodName());
        sofiGlInterfaceCommonDO.setLedgerId(sofiGlInterfaceCommonPO.getLedgerId());
        sofiGlInterfaceCommonDO.setLedgerName(sofiGlInterfaceCommonPO.getLedgerName());
        sofiGlInterfaceCommonDO.setCurrencyCode(sofiGlInterfaceCommonPO.getCurrencyCode());
        sofiGlInterfaceCommonDO.setJournalCategory(sofiGlInterfaceCommonPO.getJournalCategory());
        sofiGlInterfaceCommonDO.setJournalSource(sofiGlInterfaceCommonPO.getJournalSource());
        sofiGlInterfaceCommonDO.setJournalSourceName(sofiGlInterfaceCommonPO.getJournalSourceName());
        sofiGlInterfaceCommonDO.setReference1(sofiGlInterfaceCommonPO.getReference1());
        sofiGlInterfaceCommonDO.setReference2(sofiGlInterfaceCommonPO.getReference2());
        sofiGlInterfaceCommonDO.setReference3(sofiGlInterfaceCommonPO.getReference3());
        sofiGlInterfaceCommonDO.setReference4(sofiGlInterfaceCommonPO.getReference4());
        sofiGlInterfaceCommonDO.setReference5(sofiGlInterfaceCommonPO.getReference5());
        sofiGlInterfaceCommonDO.setReference6(sofiGlInterfaceCommonPO.getReference6());
        sofiGlInterfaceCommonDO.setReference7(sofiGlInterfaceCommonPO.getReference7());
        sofiGlInterfaceCommonDO.setReference8(sofiGlInterfaceCommonPO.getReference8());
        sofiGlInterfaceCommonDO.setReference9(sofiGlInterfaceCommonPO.getReference9());
        sofiGlInterfaceCommonDO.setReference10(sofiGlInterfaceCommonPO.getReference10());
        sofiGlInterfaceCommonDO.setReference21(sofiGlInterfaceCommonPO.getReference21());
        sofiGlInterfaceCommonDO.setReference22(sofiGlInterfaceCommonPO.getReference22());
        sofiGlInterfaceCommonDO.setReference23(sofiGlInterfaceCommonPO.getReference23());
        sofiGlInterfaceCommonDO.setReference24(sofiGlInterfaceCommonPO.getReference24());
        sofiGlInterfaceCommonDO.setReference25(sofiGlInterfaceCommonPO.getReference25());
        sofiGlInterfaceCommonDO.setReference26(sofiGlInterfaceCommonPO.getReference26());
        sofiGlInterfaceCommonDO.setReference27(sofiGlInterfaceCommonPO.getReference27());
        sofiGlInterfaceCommonDO.setReference28(sofiGlInterfaceCommonPO.getReference28());
        sofiGlInterfaceCommonDO.setReference29(sofiGlInterfaceCommonPO.getReference29());
        sofiGlInterfaceCommonDO.setReference30(sofiGlInterfaceCommonPO.getReference30());
        sofiGlInterfaceCommonDO.setSegment1(sofiGlInterfaceCommonPO.getSegment1());
        sofiGlInterfaceCommonDO.setSegment2(sofiGlInterfaceCommonPO.getSegment2());
        sofiGlInterfaceCommonDO.setSegment3(sofiGlInterfaceCommonPO.getSegment3());
        sofiGlInterfaceCommonDO.setSegment4(sofiGlInterfaceCommonPO.getSegment4());
        sofiGlInterfaceCommonDO.setSegment5(sofiGlInterfaceCommonPO.getSegment5());
        sofiGlInterfaceCommonDO.setSegment6(sofiGlInterfaceCommonPO.getSegment6());
        sofiGlInterfaceCommonDO.setSegment7(sofiGlInterfaceCommonPO.getSegment7());
        sofiGlInterfaceCommonDO.setSegment8(sofiGlInterfaceCommonPO.getSegment8());
        sofiGlInterfaceCommonDO.setSegment9(sofiGlInterfaceCommonPO.getSegment9());
        sofiGlInterfaceCommonDO.setSegment10(sofiGlInterfaceCommonPO.getSegment10());
        sofiGlInterfaceCommonDO.setEnteredDr(sofiGlInterfaceCommonPO.getEnteredDr());
        sofiGlInterfaceCommonDO.setEnteredCr(sofiGlInterfaceCommonPO.getEnteredCr());
        sofiGlInterfaceCommonDO.setAccountedDr(sofiGlInterfaceCommonPO.getAccountedDr());
        sofiGlInterfaceCommonDO.setAccountedCr(sofiGlInterfaceCommonPO.getAccountedCr());
        sofiGlInterfaceCommonDO.setGroupId(sofiGlInterfaceCommonPO.getGroupId());
        sofiGlInterfaceCommonDO.setCurrencyConversionDate(sofiGlInterfaceCommonPO.getCurrencyConversionDate());
        sofiGlInterfaceCommonDO.setCurrencyConversionRate(sofiGlInterfaceCommonPO.getCurrencyConversionRate());
        sofiGlInterfaceCommonDO.setCurrencyConversionType(sofiGlInterfaceCommonPO.getCurrencyConversionType());

        sofiGlInterfaceCommonDO.setHeaderAttribute1(sofiGlInterfaceCommonPO.getHeaderAttribute1());
        sofiGlInterfaceCommonDO.setHeaderAttribute2(sofiGlInterfaceCommonPO.getHeaderAttribute2());
        sofiGlInterfaceCommonDO.setHeaderAttribute3(sofiGlInterfaceCommonPO.getHeaderAttribute3());
        sofiGlInterfaceCommonDO.setHeaderAttribute4(sofiGlInterfaceCommonPO.getHeaderAttribute4());
        sofiGlInterfaceCommonDO.setHeaderAttribute5(sofiGlInterfaceCommonPO.getHeaderAttribute5());
        sofiGlInterfaceCommonDO.setHeaderAttribute6(sofiGlInterfaceCommonPO.getHeaderAttribute6());
        sofiGlInterfaceCommonDO.setHeaderAttribute7(sofiGlInterfaceCommonPO.getHeaderAttribute7());
        sofiGlInterfaceCommonDO.setHeaderAttribute8(sofiGlInterfaceCommonPO.getHeaderAttribute8());
        sofiGlInterfaceCommonDO.setHeaderAttribute9(sofiGlInterfaceCommonPO.getHeaderAttribute9());
        sofiGlInterfaceCommonDO.setHeaderAttribute10(sofiGlInterfaceCommonPO.getHeaderAttribute10());
        sofiGlInterfaceCommonDO.setHeaderAttribute11(sofiGlInterfaceCommonPO.getHeaderAttribute11());
        sofiGlInterfaceCommonDO.setHeaderAttribute12(sofiGlInterfaceCommonPO.getHeaderAttribute12());
        sofiGlInterfaceCommonDO.setHeaderAttribute13(sofiGlInterfaceCommonPO.getHeaderAttribute13());
        sofiGlInterfaceCommonDO.setHeaderAttribute14(sofiGlInterfaceCommonPO.getHeaderAttribute14());
        sofiGlInterfaceCommonDO.setHeaderAttribute15(sofiGlInterfaceCommonPO.getHeaderAttribute15());
        sofiGlInterfaceCommonDO.setAttributeCategory3(sofiGlInterfaceCommonPO.getAttributeCategory3());
        sofiGlInterfaceCommonDO.setLineAttribute1(sofiGlInterfaceCommonPO.getLineAttribute1());
        sofiGlInterfaceCommonDO.setLineAttribute2(sofiGlInterfaceCommonPO.getLineAttribute2());
        sofiGlInterfaceCommonDO.setLineAttribute3(sofiGlInterfaceCommonPO.getLineAttribute3());
        sofiGlInterfaceCommonDO.setLineAttribute4(sofiGlInterfaceCommonPO.getLineAttribute4());
        sofiGlInterfaceCommonDO.setLineAttribute5(sofiGlInterfaceCommonPO.getLineAttribute5());
        sofiGlInterfaceCommonDO.setLineAttribute6(sofiGlInterfaceCommonPO.getLineAttribute6());
        sofiGlInterfaceCommonDO.setLineAttribute7(sofiGlInterfaceCommonPO.getLineAttribute7());
        sofiGlInterfaceCommonDO.setLineAttribute8(sofiGlInterfaceCommonPO.getLineAttribute8());
        sofiGlInterfaceCommonDO.setLineAttribute9(sofiGlInterfaceCommonPO.getLineAttribute9());
        sofiGlInterfaceCommonDO.setLineAttribute10(sofiGlInterfaceCommonPO.getLineAttribute10());
        sofiGlInterfaceCommonDO.setLineAttribute11(sofiGlInterfaceCommonPO.getLineAttribute11());
        sofiGlInterfaceCommonDO.setLineAttribute12(sofiGlInterfaceCommonPO.getLineAttribute12());
        sofiGlInterfaceCommonDO.setLineAttribute13(sofiGlInterfaceCommonPO.getLineAttribute13());
        sofiGlInterfaceCommonDO.setLineAttribute14(sofiGlInterfaceCommonPO.getLineAttribute14());
        sofiGlInterfaceCommonDO.setLineAttribute15(sofiGlInterfaceCommonPO.getLineAttribute15());
        sofiGlInterfaceCommonDO.setLineAttribute16(sofiGlInterfaceCommonPO.getLineAttribute16());
        sofiGlInterfaceCommonDO.setLineAttribute17(sofiGlInterfaceCommonPO.getLineAttribute17());
        sofiGlInterfaceCommonDO.setLineAttribute18(sofiGlInterfaceCommonPO.getLineAttribute18());
        sofiGlInterfaceCommonDO.setLineAttribute19(sofiGlInterfaceCommonPO.getLineAttribute19());
        sofiGlInterfaceCommonDO.setLineAttribute20(sofiGlInterfaceCommonPO.getLineAttribute20());
        sofiGlInterfaceCommonDO.setProcessStatus(ProcessStatusEnum.getByCode(sofiGlInterfaceCommonPO.getProcessStatus()));
        sofiGlInterfaceCommonDO.setProcessMessage(sofiGlInterfaceCommonPO.getProcessMessage());
        sofiGlInterfaceCommonDO.setJeHeaderId(sofiGlInterfaceCommonPO.getJeHeaderId());
        sofiGlInterfaceCommonDO.setJournalName(sofiGlInterfaceCommonPO.getJournalName());
        sofiGlInterfaceCommonDO.setJeLineNum(sofiGlInterfaceCommonPO.getJeLineNum());
        sofiGlInterfaceCommonDO.setDocumentId(sofiGlInterfaceCommonPO.getDocumentId());
        sofiGlInterfaceCommonDO.setLoadRequestId(sofiGlInterfaceCommonPO.getLoadRequestId());
        sofiGlInterfaceCommonDO.setImportRequestId(sofiGlInterfaceCommonPO.getImportRequestId());
        sofiGlInterfaceCommonDO.setObjectVersionNumber(sofiGlInterfaceCommonPO.getObjectVersionNumber());
        sofiGlInterfaceCommonDO.setCreationDate(sofiGlInterfaceCommonPO.getCreationDate());
        sofiGlInterfaceCommonDO.setCreatedBy(sofiGlInterfaceCommonPO.getCreatedBy());
        sofiGlInterfaceCommonDO.setLastModifyDate(sofiGlInterfaceCommonPO.getLastModifyDate());
        sofiGlInterfaceCommonDO.setLastModifiedBy(sofiGlInterfaceCommonPO.getLastModifiedBy());
        sofiGlInterfaceCommonDO.setAttributeCategory(sofiGlInterfaceCommonPO.getAttributeCategory());
        return sofiGlInterfaceCommonDO;
    }

    public SofiGlInterfaceCommonPO convert(SofiGlInterfaceCommonDO sofiGlInterfaceCommonDO) {
        SofiGlInterfaceCommonPO sofiGlInterfaceCommonPO = new SofiGlInterfaceCommonPO();
        sofiGlInterfaceCommonPO.setId(sofiGlInterfaceCommonDO.getId());
        sofiGlInterfaceCommonPO.setSystemCode(sofiGlInterfaceCommonDO.getSystemCode());
        sofiGlInterfaceCommonPO.setProcessDay(sofiGlInterfaceCommonDO.getProcessDay());
        sofiGlInterfaceCommonPO.setAccountingDate(sofiGlInterfaceCommonDO.getAccountingDate());
        sofiGlInterfaceCommonPO.setPeriodName(sofiGlInterfaceCommonDO.getPeriodName());
        sofiGlInterfaceCommonPO.setLedgerId(sofiGlInterfaceCommonDO.getLedgerId());
        sofiGlInterfaceCommonPO.setLedgerName(sofiGlInterfaceCommonDO.getLedgerName());
        sofiGlInterfaceCommonPO.setCurrencyCode(sofiGlInterfaceCommonDO.getCurrencyCode());
        sofiGlInterfaceCommonPO.setJournalCategory(sofiGlInterfaceCommonDO.getJournalCategory());
        sofiGlInterfaceCommonPO.setJournalSource(sofiGlInterfaceCommonDO.getJournalSource());
        sofiGlInterfaceCommonPO.setJournalSourceName(sofiGlInterfaceCommonDO.getJournalSourceName());
        sofiGlInterfaceCommonPO.setReference1(sofiGlInterfaceCommonDO.getReference1());
        sofiGlInterfaceCommonPO.setReference2(sofiGlInterfaceCommonDO.getReference2());
        sofiGlInterfaceCommonPO.setReference3(sofiGlInterfaceCommonDO.getReference3());
        sofiGlInterfaceCommonPO.setReference4(sofiGlInterfaceCommonDO.getReference4());
        sofiGlInterfaceCommonPO.setReference5(sofiGlInterfaceCommonDO.getReference5());
        sofiGlInterfaceCommonPO.setReference6(sofiGlInterfaceCommonDO.getReference6());
        sofiGlInterfaceCommonPO.setReference7(sofiGlInterfaceCommonDO.getReference7());
        sofiGlInterfaceCommonPO.setReference8(sofiGlInterfaceCommonDO.getReference8());
        sofiGlInterfaceCommonPO.setReference9(sofiGlInterfaceCommonDO.getReference9());
        sofiGlInterfaceCommonPO.setReference10(sofiGlInterfaceCommonDO.getReference10());
        sofiGlInterfaceCommonPO.setReference21(sofiGlInterfaceCommonDO.getReference21());
        sofiGlInterfaceCommonPO.setReference22(sofiGlInterfaceCommonDO.getReference22());
        sofiGlInterfaceCommonPO.setReference23(sofiGlInterfaceCommonDO.getReference23());
        sofiGlInterfaceCommonPO.setReference24(sofiGlInterfaceCommonDO.getReference24());
        sofiGlInterfaceCommonPO.setReference25(sofiGlInterfaceCommonDO.getReference25());
        sofiGlInterfaceCommonPO.setReference26(sofiGlInterfaceCommonDO.getReference26());
        sofiGlInterfaceCommonPO.setReference27(sofiGlInterfaceCommonDO.getReference27());
        sofiGlInterfaceCommonPO.setReference28(sofiGlInterfaceCommonDO.getReference28());
        sofiGlInterfaceCommonPO.setReference29(sofiGlInterfaceCommonDO.getReference29());
        sofiGlInterfaceCommonPO.setReference30(sofiGlInterfaceCommonDO.getReference30());
        sofiGlInterfaceCommonPO.setSegment1(sofiGlInterfaceCommonDO.getSegment1());
        sofiGlInterfaceCommonPO.setSegment2(sofiGlInterfaceCommonDO.getSegment2());
        sofiGlInterfaceCommonPO.setSegment3(sofiGlInterfaceCommonDO.getSegment3());
        sofiGlInterfaceCommonPO.setSegment4(sofiGlInterfaceCommonDO.getSegment4());
        sofiGlInterfaceCommonPO.setSegment5(sofiGlInterfaceCommonDO.getSegment5());
        sofiGlInterfaceCommonPO.setSegment6(sofiGlInterfaceCommonDO.getSegment6());
        sofiGlInterfaceCommonPO.setSegment7(sofiGlInterfaceCommonDO.getSegment7());
        sofiGlInterfaceCommonPO.setSegment8(sofiGlInterfaceCommonDO.getSegment8());
        sofiGlInterfaceCommonPO.setSegment9(sofiGlInterfaceCommonDO.getSegment9());
        sofiGlInterfaceCommonPO.setSegment10(sofiGlInterfaceCommonDO.getSegment10());
        sofiGlInterfaceCommonPO.setEnteredDr(sofiGlInterfaceCommonDO.getEnteredDr());
        sofiGlInterfaceCommonPO.setEnteredCr(sofiGlInterfaceCommonDO.getEnteredCr());
        sofiGlInterfaceCommonPO.setAccountedDr(sofiGlInterfaceCommonDO.getAccountedDr());
        sofiGlInterfaceCommonPO.setAccountedCr(sofiGlInterfaceCommonDO.getAccountedCr());
        sofiGlInterfaceCommonPO.setGroupId(sofiGlInterfaceCommonDO.getGroupId());
        sofiGlInterfaceCommonPO.setCurrencyConversionDate(sofiGlInterfaceCommonDO.getCurrencyConversionDate());
        sofiGlInterfaceCommonPO.setCurrencyConversionRate(sofiGlInterfaceCommonDO.getCurrencyConversionRate());
        sofiGlInterfaceCommonPO.setCurrencyConversionType(sofiGlInterfaceCommonDO.getCurrencyConversionType());
        sofiGlInterfaceCommonPO.setHeaderAttribute1(sofiGlInterfaceCommonDO.getHeaderAttribute1());
        sofiGlInterfaceCommonPO.setHeaderAttribute2(sofiGlInterfaceCommonDO.getHeaderAttribute2());
        sofiGlInterfaceCommonPO.setHeaderAttribute3(sofiGlInterfaceCommonDO.getHeaderAttribute3());
        sofiGlInterfaceCommonPO.setHeaderAttribute4(sofiGlInterfaceCommonDO.getHeaderAttribute4());
        sofiGlInterfaceCommonPO.setHeaderAttribute5(sofiGlInterfaceCommonDO.getHeaderAttribute5());
        sofiGlInterfaceCommonPO.setHeaderAttribute6(sofiGlInterfaceCommonDO.getHeaderAttribute6());
        sofiGlInterfaceCommonPO.setHeaderAttribute7(sofiGlInterfaceCommonDO.getHeaderAttribute7());
        sofiGlInterfaceCommonPO.setHeaderAttribute8(sofiGlInterfaceCommonDO.getHeaderAttribute8());
        sofiGlInterfaceCommonPO.setHeaderAttribute9(sofiGlInterfaceCommonDO.getHeaderAttribute9());
        sofiGlInterfaceCommonPO.setHeaderAttribute10(sofiGlInterfaceCommonDO.getHeaderAttribute10());
        sofiGlInterfaceCommonPO.setHeaderAttribute11(sofiGlInterfaceCommonDO.getHeaderAttribute11());
        sofiGlInterfaceCommonPO.setHeaderAttribute12(sofiGlInterfaceCommonDO.getHeaderAttribute12());
        sofiGlInterfaceCommonPO.setHeaderAttribute13(sofiGlInterfaceCommonDO.getHeaderAttribute13());
        sofiGlInterfaceCommonPO.setHeaderAttribute14(sofiGlInterfaceCommonDO.getHeaderAttribute14());
        sofiGlInterfaceCommonPO.setHeaderAttribute15(sofiGlInterfaceCommonDO.getHeaderAttribute15());
        sofiGlInterfaceCommonPO.setAttributeCategory3(sofiGlInterfaceCommonDO.getAttributeCategory3());
        sofiGlInterfaceCommonPO.setLineAttribute1(sofiGlInterfaceCommonDO.getLineAttribute1());
        sofiGlInterfaceCommonPO.setLineAttribute2(sofiGlInterfaceCommonDO.getLineAttribute2());
        sofiGlInterfaceCommonPO.setLineAttribute3(sofiGlInterfaceCommonDO.getLineAttribute3());
        sofiGlInterfaceCommonPO.setLineAttribute4(sofiGlInterfaceCommonDO.getLineAttribute4());
        sofiGlInterfaceCommonPO.setLineAttribute5(sofiGlInterfaceCommonDO.getLineAttribute5());
        sofiGlInterfaceCommonPO.setLineAttribute6(sofiGlInterfaceCommonDO.getLineAttribute6());
        sofiGlInterfaceCommonPO.setLineAttribute7(sofiGlInterfaceCommonDO.getLineAttribute7());
        sofiGlInterfaceCommonPO.setLineAttribute8(sofiGlInterfaceCommonDO.getLineAttribute8());
        sofiGlInterfaceCommonPO.setLineAttribute9(sofiGlInterfaceCommonDO.getLineAttribute9());
        sofiGlInterfaceCommonPO.setLineAttribute10(sofiGlInterfaceCommonDO.getLineAttribute10());
        sofiGlInterfaceCommonPO.setLineAttribute11(sofiGlInterfaceCommonDO.getLineAttribute11());
        sofiGlInterfaceCommonPO.setLineAttribute12(sofiGlInterfaceCommonDO.getLineAttribute12());
        sofiGlInterfaceCommonPO.setLineAttribute13(sofiGlInterfaceCommonDO.getLineAttribute13());
        sofiGlInterfaceCommonPO.setLineAttribute14(sofiGlInterfaceCommonDO.getLineAttribute14());
        sofiGlInterfaceCommonPO.setLineAttribute15(sofiGlInterfaceCommonDO.getLineAttribute15());
        sofiGlInterfaceCommonPO.setLineAttribute16(sofiGlInterfaceCommonDO.getLineAttribute16());
        sofiGlInterfaceCommonPO.setLineAttribute17(sofiGlInterfaceCommonDO.getLineAttribute17());
        sofiGlInterfaceCommonPO.setLineAttribute18(sofiGlInterfaceCommonDO.getLineAttribute18());
        sofiGlInterfaceCommonPO.setLineAttribute19(sofiGlInterfaceCommonDO.getLineAttribute19());
        sofiGlInterfaceCommonPO.setLineAttribute20(sofiGlInterfaceCommonDO.getLineAttribute20());
        sofiGlInterfaceCommonPO.setProcessStatus(sofiGlInterfaceCommonDO.getProcessStatus().getCode());
        sofiGlInterfaceCommonPO.setProcessMessage(sofiGlInterfaceCommonDO.getProcessMessage());
        sofiGlInterfaceCommonPO.setJeHeaderId(sofiGlInterfaceCommonDO.getJeHeaderId());
        sofiGlInterfaceCommonPO.setJournalName(sofiGlInterfaceCommonDO.getJournalName());
        sofiGlInterfaceCommonPO.setJeLineNum(sofiGlInterfaceCommonDO.getJeLineNum());
        sofiGlInterfaceCommonPO.setDocumentId(sofiGlInterfaceCommonDO.getDocumentId());
        sofiGlInterfaceCommonPO.setLoadRequestId(sofiGlInterfaceCommonDO.getLoadRequestId());
        sofiGlInterfaceCommonPO.setImportRequestId(sofiGlInterfaceCommonDO.getImportRequestId());
        sofiGlInterfaceCommonPO.setObjectVersionNumber(sofiGlInterfaceCommonDO.getObjectVersionNumber());
        sofiGlInterfaceCommonPO.setCreationDate(sofiGlInterfaceCommonDO.getCreationDate());
        sofiGlInterfaceCommonPO.setCreatedBy(sofiGlInterfaceCommonDO.getCreatedBy());
        sofiGlInterfaceCommonPO.setLastModifyDate(sofiGlInterfaceCommonDO.getLastModifyDate());
        sofiGlInterfaceCommonPO.setLastModifiedBy(sofiGlInterfaceCommonDO.getLastModifiedBy());
        sofiGlInterfaceCommonPO.setAttributeCategory(sofiGlInterfaceCommonDO.getAttributeCategory());
        return sofiGlInterfaceCommonPO;
    }


    /**
     * DO -> DO
     */
    public void copyIgnoreNullValue(SofiGlInterfaceCommonDO source, SofiGlInterfaceCommonDO target) {
        CommonUtils.copyIgnoreNull(source::getId, target::setId);
        CommonUtils.copyIgnoreNull(source::getSystemCode, target::setSystemCode);
        CommonUtils.copyIgnoreNull(source::getAccountingDate, target::setAccountingDate);
        CommonUtils.copyIgnoreNull(source::getPeriodName, target::setPeriodName);
        CommonUtils.copyIgnoreNull(source::getLedgerId, target::setLedgerId);
        CommonUtils.copyIgnoreNull(source::getLedgerName, target::setLedgerName);
        CommonUtils.copyIgnoreNull(source::getCurrencyCode, target::setCurrencyCode);
        CommonUtils.copyIgnoreNull(source::getJournalCategory, target::setJournalCategory);
        CommonUtils.copyIgnoreNull(source::getJournalSource, target::setJournalSource);
        CommonUtils.copyIgnoreNull(source::getJournalSourceName, target::setJournalSourceName);
        CommonUtils.copyIgnoreNull(source::getSegment1, target::setSegment1);
        CommonUtils.copyIgnoreNull(source::getSegment2, target::setSegment2);
        CommonUtils.copyIgnoreNull(source::getSegment3, target::setSegment3);
        CommonUtils.copyIgnoreNull(source::getSegment4, target::setSegment4);
        CommonUtils.copyIgnoreNull(source::getSegment5, target::setSegment5);
        CommonUtils.copyIgnoreNull(source::getSegment6, target::setSegment6);
        CommonUtils.copyIgnoreNull(source::getSegment7, target::setSegment7);
        CommonUtils.copyIgnoreNull(source::getSegment8, target::setSegment8);
        CommonUtils.copyIgnoreNull(source::getSegment9, target::setSegment9);
        CommonUtils.copyIgnoreNull(source::getSegment10, target::setSegment10);
        CommonUtils.copyIgnoreNull(source::getGroupId, target::setGroupId);
        CommonUtils.copyIgnoreNull(source::getCurrencyConversionDate, target::setCurrencyConversionDate);
        CommonUtils.copyIgnoreNull(source::getCurrencyConversionRate, target::setCurrencyConversionRate);
        CommonUtils.copyIgnoreNull(source::getHeaderAttribute1, target::setHeaderAttribute1);
        CommonUtils.copyIgnoreNull(source::getHeaderAttribute2, target::setHeaderAttribute2);
        CommonUtils.copyIgnoreNull(source::getHeaderAttribute3, target::setHeaderAttribute3);
        CommonUtils.copyIgnoreNull(source::getHeaderAttribute4, target::setHeaderAttribute4);
        CommonUtils.copyIgnoreNull(source::getHeaderAttribute5, target::setHeaderAttribute5);
        CommonUtils.copyIgnoreNull(source::getHeaderAttribute6, target::setHeaderAttribute6);
        CommonUtils.copyIgnoreNull(source::getHeaderAttribute7, target::setHeaderAttribute7);
        CommonUtils.copyIgnoreNull(source::getHeaderAttribute8, target::setHeaderAttribute8);
        CommonUtils.copyIgnoreNull(source::getHeaderAttribute9, target::setHeaderAttribute9);
        CommonUtils.copyIgnoreNull(source::getHeaderAttribute10, target::setHeaderAttribute10);
        CommonUtils.copyIgnoreNull(source::getHeaderAttribute11, target::setHeaderAttribute11);
        CommonUtils.copyIgnoreNull(source::getHeaderAttribute12, target::setHeaderAttribute12);
        CommonUtils.copyIgnoreNull(source::getHeaderAttribute13, target::setHeaderAttribute13);
        CommonUtils.copyIgnoreNull(source::getHeaderAttribute14, target::setHeaderAttribute14);
        CommonUtils.copyIgnoreNull(source::getHeaderAttribute15, target::setHeaderAttribute15);
        CommonUtils.copyIgnoreNull(source::getLineAttribute1, target::setLineAttribute1);
        CommonUtils.copyIgnoreNull(source::getLineAttribute2, target::setLineAttribute2);
        CommonUtils.copyIgnoreNull(source::getLineAttribute3, target::setLineAttribute3);
        CommonUtils.copyIgnoreNull(source::getLineAttribute4, target::setLineAttribute4);
        CommonUtils.copyIgnoreNull(source::getLineAttribute5, target::setLineAttribute5);
        CommonUtils.copyIgnoreNull(source::getLineAttribute6, target::setLineAttribute6);
        CommonUtils.copyIgnoreNull(source::getLineAttribute7, target::setLineAttribute7);
        CommonUtils.copyIgnoreNull(source::getLineAttribute8, target::setLineAttribute8);
        CommonUtils.copyIgnoreNull(source::getLineAttribute9, target::setLineAttribute9);
        CommonUtils.copyIgnoreNull(source::getLineAttribute10, target::setLineAttribute10);
        CommonUtils.copyIgnoreNull(source::getLineAttribute11, target::setLineAttribute11);
        CommonUtils.copyIgnoreNull(source::getLineAttribute12, target::setLineAttribute12);
        CommonUtils.copyIgnoreNull(source::getLineAttribute13, target::setLineAttribute13);
        CommonUtils.copyIgnoreNull(source::getLineAttribute14, target::setLineAttribute14);
        CommonUtils.copyIgnoreNull(source::getLineAttribute15, target::setLineAttribute15);
        CommonUtils.copyIgnoreNull(source::getProcessStatus, target::setProcessStatus);
        CommonUtils.copyIgnoreNull(source::getProcessMessage, target::setProcessMessage);
        CommonUtils.copyIgnoreNull(source::getJeHeaderId, target::setJeHeaderId);
        CommonUtils.copyIgnoreNull(source::getJournalName, target::setJournalName);
        CommonUtils.copyIgnoreNull(source::getJeLineNum, target::setJeLineNum);
        CommonUtils.copyIgnoreNull(source::getDocumentId, target::setDocumentId);
        CommonUtils.copyIgnoreNull(source::getLoadRequestId, target::setLoadRequestId);
        CommonUtils.copyIgnoreNull(source::getImportRequestId, target::setImportRequestId);
        CommonUtils.copyIgnoreNull(source::getObjectVersionNumber, target::setObjectVersionNumber);
        CommonUtils.copyIgnoreNull(source::getCreationDate, target::setCreationDate);
        CommonUtils.copyIgnoreNull(source::getCreatedBy, target::setCreatedBy);
        CommonUtils.copyIgnoreNull(source::getLastModifyDate, target::setLastModifyDate);
        CommonUtils.copyIgnoreNull(source::getLastModifiedBy, target::setLastModifiedBy);
    }

}
