package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated;

import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabHeadersPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabHeadersPOExample;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface CuxTwoTabHeadersPOMapper {
    int deleteByExample(CuxTwoTabHeadersPOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(CuxTwoTabHeadersPO record);

    int insertSelective(CuxTwoTabHeadersPO record);

    List<CuxTwoTabHeadersPO> selectByExampleWithRowbounds(CuxTwoTabHeadersPOExample example, RowBounds rowBounds);

    List<CuxTwoTabHeadersPO> selectByExample(CuxTwoTabHeadersPOExample example);

    CuxTwoTabHeadersPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") CuxTwoTabHeadersPO record, @Param("example") CuxTwoTabHeadersPOExample example);

    int updateByExample(@Param("record") CuxTwoTabHeadersPO record, @Param("example") CuxTwoTabHeadersPOExample example);

    int updateByPrimaryKeySelective(CuxTwoTabHeadersPO record);

    int updateByPrimaryKey(CuxTwoTabHeadersPO record);
}