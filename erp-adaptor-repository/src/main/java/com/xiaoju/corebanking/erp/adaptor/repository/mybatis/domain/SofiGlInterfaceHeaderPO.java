package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
@Data
public class SofiGlInterfaceHeaderPO implements Serializable {
    private Long id;

    private String systemCode;

    private String processDay;

    private String batchId;

    private String externalReference;

    private Long journalCount;

    private String processStatus;

    private String processMessage;
    private Long objectVersionNumber;

    private Date creationDate;

    private String createdBy;

    private Date lastModifyDate;

    private String lastModifiedBy;

    private Long groupId;

    private static final long serialVersionUID = 1L;

}