package com.xiaoju.corebanking.erp.adaptor.repository;

import com.github.pagehelper.PageInfo;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.ValidationRequestDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.ValidationResultDO;

import java.util.List;

public interface CoaRepository {
    List<ValidationRequestDO> queryCoaList(String processDay);

    List<Long> queryIdListByCoa(String processDay, String processStatus, ValidationResultDO coa);

    PageInfo<Long> pagingQueryIdListByCoa(String processDay, String processStatus, ValidationResultDO coa, Integer pageNum, Integer pageSize);

    int updateSafiHeaderStatus(String processDay);

    int updateShenmaHeaderStatus(String processDay);

    int updateCoaStatus(String processDay, ValidationResultDO coa, String processStatus, String processMessage);

    int updateCoaStatusById(String processDay, String systemCode, Long id, String processStatus, String processMessage);

    int updateCoaStatusByIdBatch(String processDay, ValidationResultDO coa, String processStatus, String processMessage);

    PageInfo<ValidationRequestDO> pagingSelectCoaList(String processDay, Integer pageNum, Integer pageSize);

    PageInfo<ValidationRequestDO> pagingSelectShenMaCoaList(String processDay, Integer pageNum, Integer pageSize);

    int updateCommonCoaStatusByIdBatch(String processDay, ValidationResultDO coa, String processStatus, String processMessage);

    PageInfo<Long> pagingInterfaceCommonQueryIdListByCoa(String processDay, String processStatus, ValidationResultDO coa, Integer pageNum, Integer pageSize);

    int updateShenmaCoaStatusByIdBatch(String processDay, ValidationResultDO coa, String processStatus,String processMessage);

    PageInfo<Long> pagingShenmaQueryIdListByCoa(String processDay, String processStatus, ValidationResultDO coa, Integer pageNum, Integer pageSize);

}