package com.xiaoju.corebanking.erp.adaptor.repository;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceCommonDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SofiGlInterfaceCommonRepository {
    void insertSelective(SofiGlInterfaceCommonDO sofiGlInterfaceCommonDO);

    int batchInsert(List<SofiGlInterfaceCommonDO> list);

    List<Long> queryGroupIds(Integer pageNum, Integer pageSize, String processDay);

    List<SofiGlInterfaceCommonDO> querySofiGlInterfaceCommonDOList(String systemCode, String processDay, Long groupId);

    int updateSofiGlInterfaceCommon(String systemCode, String processDay, String oldStatus, Long groupId, SofiGlInterfaceCommonDO update);

    /**
     * 根据reference5列表和处理日期查询数据
     */
    List<SofiGlInterfaceCommonDO> queryByReference5AndProcessDay(@Param("externalReferenceList") List<String> externalReferenceList, @Param("processDay") String processDay);

    /**
     * 更新通用接口数据
     */
    void updateInterfaceCommon(@Param("original") SofiGlInterfaceCommonDO original, @Param("update") SofiGlInterfaceCommonDO update);
}