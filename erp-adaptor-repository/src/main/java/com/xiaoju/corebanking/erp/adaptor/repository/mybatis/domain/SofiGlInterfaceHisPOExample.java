package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SofiGlInterfaceHisPOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public SofiGlInterfaceHisPOExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andFileIdIsNull() {
            addCriterion("file_id is null");
            return (Criteria) this;
        }

        public Criteria andFileIdIsNotNull() {
            addCriterion("file_id is not null");
            return (Criteria) this;
        }

        public Criteria andFileIdEqualTo(Long value) {
            addCriterion("file_id =", value, "fileId");
            return (Criteria) this;
        }

        public Criteria andFileIdNotEqualTo(Long value) {
            addCriterion("file_id <>", value, "fileId");
            return (Criteria) this;
        }

        public Criteria andFileIdGreaterThan(Long value) {
            addCriterion("file_id >", value, "fileId");
            return (Criteria) this;
        }

        public Criteria andFileIdGreaterThanOrEqualTo(Long value) {
            addCriterion("file_id >=", value, "fileId");
            return (Criteria) this;
        }

        public Criteria andFileIdLessThan(Long value) {
            addCriterion("file_id <", value, "fileId");
            return (Criteria) this;
        }

        public Criteria andFileIdLessThanOrEqualTo(Long value) {
            addCriterion("file_id <=", value, "fileId");
            return (Criteria) this;
        }

        public Criteria andFileIdIn(List<Long> values) {
            addCriterion("file_id in", values, "fileId");
            return (Criteria) this;
        }

        public Criteria andFileIdNotIn(List<Long> values) {
            addCriterion("file_id not in", values, "fileId");
            return (Criteria) this;
        }

        public Criteria andFileIdBetween(Long value1, Long value2) {
            addCriterion("file_id between", value1, value2, "fileId");
            return (Criteria) this;
        }

        public Criteria andFileIdNotBetween(Long value1, Long value2) {
            addCriterion("file_id not between", value1, value2, "fileId");
            return (Criteria) this;
        }

        public Criteria andProcessDayIsNull() {
            addCriterion("process_day is null");
            return (Criteria) this;
        }

        public Criteria andProcessDayIsNotNull() {
            addCriterion("process_day is not null");
            return (Criteria) this;
        }

        public Criteria andProcessDayEqualTo(String value) {
            addCriterion("process_day =", value, "processDay");
            return (Criteria) this;
        }

        public Criteria andProcessDayNotEqualTo(String value) {
            addCriterion("process_day <>", value, "processDay");
            return (Criteria) this;
        }

        public Criteria andProcessDayGreaterThan(String value) {
            addCriterion("process_day >", value, "processDay");
            return (Criteria) this;
        }

        public Criteria andProcessDayGreaterThanOrEqualTo(String value) {
            addCriterion("process_day >=", value, "processDay");
            return (Criteria) this;
        }

        public Criteria andProcessDayLessThan(String value) {
            addCriterion("process_day <", value, "processDay");
            return (Criteria) this;
        }

        public Criteria andProcessDayLessThanOrEqualTo(String value) {
            addCriterion("process_day <=", value, "processDay");
            return (Criteria) this;
        }

        public Criteria andProcessDayLike(String value) {
            addCriterion("process_day like", value, "processDay");
            return (Criteria) this;
        }

        public Criteria andProcessDayNotLike(String value) {
            addCriterion("process_day not like", value, "processDay");
            return (Criteria) this;
        }

        public Criteria andProcessDayIn(List<String> values) {
            addCriterion("process_day in", values, "processDay");
            return (Criteria) this;
        }

        public Criteria andProcessDayNotIn(List<String> values) {
            addCriterion("process_day not in", values, "processDay");
            return (Criteria) this;
        }

        public Criteria andProcessDayBetween(String value1, String value2) {
            addCriterion("process_day between", value1, value2, "processDay");
            return (Criteria) this;
        }

        public Criteria andProcessDayNotBetween(String value1, String value2) {
            addCriterion("process_day not between", value1, value2, "processDay");
            return (Criteria) this;
        }

        public Criteria andFileNameIsNull() {
            addCriterion("file_name is null");
            return (Criteria) this;
        }

        public Criteria andFileNameIsNotNull() {
            addCriterion("file_name is not null");
            return (Criteria) this;
        }

        public Criteria andFileNameEqualTo(String value) {
            addCriterion("file_name =", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameNotEqualTo(String value) {
            addCriterion("file_name <>", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameGreaterThan(String value) {
            addCriterion("file_name >", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameGreaterThanOrEqualTo(String value) {
            addCriterion("file_name >=", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameLessThan(String value) {
            addCriterion("file_name <", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameLessThanOrEqualTo(String value) {
            addCriterion("file_name <=", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameLike(String value) {
            addCriterion("file_name like", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameNotLike(String value) {
            addCriterion("file_name not like", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameIn(List<String> values) {
            addCriterion("file_name in", values, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameNotIn(List<String> values) {
            addCriterion("file_name not in", values, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameBetween(String value1, String value2) {
            addCriterion("file_name between", value1, value2, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameNotBetween(String value1, String value2) {
            addCriterion("file_name not between", value1, value2, "fileName");
            return (Criteria) this;
        }

        public Criteria andBatchIdIsNull() {
            addCriterion("batch_id is null");
            return (Criteria) this;
        }

        public Criteria andBatchIdIsNotNull() {
            addCriterion("batch_id is not null");
            return (Criteria) this;
        }

        public Criteria andBatchIdEqualTo(String value) {
            addCriterion("batch_id =", value, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdNotEqualTo(String value) {
            addCriterion("batch_id <>", value, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdGreaterThan(String value) {
            addCriterion("batch_id >", value, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdGreaterThanOrEqualTo(String value) {
            addCriterion("batch_id >=", value, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdLessThan(String value) {
            addCriterion("batch_id <", value, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdLessThanOrEqualTo(String value) {
            addCriterion("batch_id <=", value, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdLike(String value) {
            addCriterion("batch_id like", value, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdNotLike(String value) {
            addCriterion("batch_id not like", value, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdIn(List<String> values) {
            addCriterion("batch_id in", values, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdNotIn(List<String> values) {
            addCriterion("batch_id not in", values, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdBetween(String value1, String value2) {
            addCriterion("batch_id between", value1, value2, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdNotBetween(String value1, String value2) {
            addCriterion("batch_id not between", value1, value2, "batchId");
            return (Criteria) this;
        }

        public Criteria andDetallePolizaIdIsNull() {
            addCriterion("detalle_poliza_id is null");
            return (Criteria) this;
        }

        public Criteria andDetallePolizaIdIsNotNull() {
            addCriterion("detalle_poliza_id is not null");
            return (Criteria) this;
        }

        public Criteria andDetallePolizaIdEqualTo(Long value) {
            addCriterion("detalle_poliza_id =", value, "detallePolizaId");
            return (Criteria) this;
        }

        public Criteria andDetallePolizaIdNotEqualTo(Long value) {
            addCriterion("detalle_poliza_id <>", value, "detallePolizaId");
            return (Criteria) this;
        }

        public Criteria andDetallePolizaIdGreaterThan(Long value) {
            addCriterion("detalle_poliza_id >", value, "detallePolizaId");
            return (Criteria) this;
        }

        public Criteria andDetallePolizaIdGreaterThanOrEqualTo(Long value) {
            addCriterion("detalle_poliza_id >=", value, "detallePolizaId");
            return (Criteria) this;
        }

        public Criteria andDetallePolizaIdLessThan(Long value) {
            addCriterion("detalle_poliza_id <", value, "detallePolizaId");
            return (Criteria) this;
        }

        public Criteria andDetallePolizaIdLessThanOrEqualTo(Long value) {
            addCriterion("detalle_poliza_id <=", value, "detallePolizaId");
            return (Criteria) this;
        }

        public Criteria andDetallePolizaIdIn(List<Long> values) {
            addCriterion("detalle_poliza_id in", values, "detallePolizaId");
            return (Criteria) this;
        }

        public Criteria andDetallePolizaIdNotIn(List<Long> values) {
            addCriterion("detalle_poliza_id not in", values, "detallePolizaId");
            return (Criteria) this;
        }

        public Criteria andDetallePolizaIdBetween(Long value1, Long value2) {
            addCriterion("detalle_poliza_id between", value1, value2, "detallePolizaId");
            return (Criteria) this;
        }

        public Criteria andDetallePolizaIdNotBetween(Long value1, Long value2) {
            addCriterion("detalle_poliza_id not between", value1, value2, "detallePolizaId");
            return (Criteria) this;
        }

        public Criteria andSourceSysIsNull() {
            addCriterion("source_sys is null");
            return (Criteria) this;
        }

        public Criteria andSourceSysIsNotNull() {
            addCriterion("source_sys is not null");
            return (Criteria) this;
        }

        public Criteria andSourceSysEqualTo(String value) {
            addCriterion("source_sys =", value, "sourceSys");
            return (Criteria) this;
        }

        public Criteria andSourceSysNotEqualTo(String value) {
            addCriterion("source_sys <>", value, "sourceSys");
            return (Criteria) this;
        }

        public Criteria andSourceSysGreaterThan(String value) {
            addCriterion("source_sys >", value, "sourceSys");
            return (Criteria) this;
        }

        public Criteria andSourceSysGreaterThanOrEqualTo(String value) {
            addCriterion("source_sys >=", value, "sourceSys");
            return (Criteria) this;
        }

        public Criteria andSourceSysLessThan(String value) {
            addCriterion("source_sys <", value, "sourceSys");
            return (Criteria) this;
        }

        public Criteria andSourceSysLessThanOrEqualTo(String value) {
            addCriterion("source_sys <=", value, "sourceSys");
            return (Criteria) this;
        }

        public Criteria andSourceSysLike(String value) {
            addCriterion("source_sys like", value, "sourceSys");
            return (Criteria) this;
        }

        public Criteria andSourceSysNotLike(String value) {
            addCriterion("source_sys not like", value, "sourceSys");
            return (Criteria) this;
        }

        public Criteria andSourceSysIn(List<String> values) {
            addCriterion("source_sys in", values, "sourceSys");
            return (Criteria) this;
        }

        public Criteria andSourceSysNotIn(List<String> values) {
            addCriterion("source_sys not in", values, "sourceSys");
            return (Criteria) this;
        }

        public Criteria andSourceSysBetween(String value1, String value2) {
            addCriterion("source_sys between", value1, value2, "sourceSys");
            return (Criteria) this;
        }

        public Criteria andSourceSysNotBetween(String value1, String value2) {
            addCriterion("source_sys not between", value1, value2, "sourceSys");
            return (Criteria) this;
        }

        public Criteria andEmpresaIdIsNull() {
            addCriterion("empresa_id is null");
            return (Criteria) this;
        }

        public Criteria andEmpresaIdIsNotNull() {
            addCriterion("empresa_id is not null");
            return (Criteria) this;
        }

        public Criteria andEmpresaIdEqualTo(Long value) {
            addCriterion("empresa_id =", value, "empresaId");
            return (Criteria) this;
        }

        public Criteria andEmpresaIdNotEqualTo(Long value) {
            addCriterion("empresa_id <>", value, "empresaId");
            return (Criteria) this;
        }

        public Criteria andEmpresaIdGreaterThan(Long value) {
            addCriterion("empresa_id >", value, "empresaId");
            return (Criteria) this;
        }

        public Criteria andEmpresaIdGreaterThanOrEqualTo(Long value) {
            addCriterion("empresa_id >=", value, "empresaId");
            return (Criteria) this;
        }

        public Criteria andEmpresaIdLessThan(Long value) {
            addCriterion("empresa_id <", value, "empresaId");
            return (Criteria) this;
        }

        public Criteria andEmpresaIdLessThanOrEqualTo(Long value) {
            addCriterion("empresa_id <=", value, "empresaId");
            return (Criteria) this;
        }

        public Criteria andEmpresaIdIn(List<Long> values) {
            addCriterion("empresa_id in", values, "empresaId");
            return (Criteria) this;
        }

        public Criteria andEmpresaIdNotIn(List<Long> values) {
            addCriterion("empresa_id not in", values, "empresaId");
            return (Criteria) this;
        }

        public Criteria andEmpresaIdBetween(Long value1, Long value2) {
            addCriterion("empresa_id between", value1, value2, "empresaId");
            return (Criteria) this;
        }

        public Criteria andEmpresaIdNotBetween(Long value1, Long value2) {
            addCriterion("empresa_id not between", value1, value2, "empresaId");
            return (Criteria) this;
        }

        public Criteria andPolizaIdIsNull() {
            addCriterion("poliza_id is null");
            return (Criteria) this;
        }

        public Criteria andPolizaIdIsNotNull() {
            addCriterion("poliza_id is not null");
            return (Criteria) this;
        }

        public Criteria andPolizaIdEqualTo(Long value) {
            addCriterion("poliza_id =", value, "polizaId");
            return (Criteria) this;
        }

        public Criteria andPolizaIdNotEqualTo(Long value) {
            addCriterion("poliza_id <>", value, "polizaId");
            return (Criteria) this;
        }

        public Criteria andPolizaIdGreaterThan(Long value) {
            addCriterion("poliza_id >", value, "polizaId");
            return (Criteria) this;
        }

        public Criteria andPolizaIdGreaterThanOrEqualTo(Long value) {
            addCriterion("poliza_id >=", value, "polizaId");
            return (Criteria) this;
        }

        public Criteria andPolizaIdLessThan(Long value) {
            addCriterion("poliza_id <", value, "polizaId");
            return (Criteria) this;
        }

        public Criteria andPolizaIdLessThanOrEqualTo(Long value) {
            addCriterion("poliza_id <=", value, "polizaId");
            return (Criteria) this;
        }

        public Criteria andPolizaIdIn(List<Long> values) {
            addCriterion("poliza_id in", values, "polizaId");
            return (Criteria) this;
        }

        public Criteria andPolizaIdNotIn(List<Long> values) {
            addCriterion("poliza_id not in", values, "polizaId");
            return (Criteria) this;
        }

        public Criteria andPolizaIdBetween(Long value1, Long value2) {
            addCriterion("poliza_id between", value1, value2, "polizaId");
            return (Criteria) this;
        }

        public Criteria andPolizaIdNotBetween(Long value1, Long value2) {
            addCriterion("poliza_id not between", value1, value2, "polizaId");
            return (Criteria) this;
        }

        public Criteria andFechaIsNull() {
            addCriterion("fecha is null");
            return (Criteria) this;
        }

        public Criteria andFechaIsNotNull() {
            addCriterion("fecha is not null");
            return (Criteria) this;
        }

        public Criteria andFechaEqualTo(String value) {
            addCriterion("fecha =", value, "fecha");
            return (Criteria) this;
        }

        public Criteria andFechaNotEqualTo(String value) {
            addCriterion("fecha <>", value, "fecha");
            return (Criteria) this;
        }

        public Criteria andFechaGreaterThan(String value) {
            addCriterion("fecha >", value, "fecha");
            return (Criteria) this;
        }

        public Criteria andFechaGreaterThanOrEqualTo(String value) {
            addCriterion("fecha >=", value, "fecha");
            return (Criteria) this;
        }

        public Criteria andFechaLessThan(String value) {
            addCriterion("fecha <", value, "fecha");
            return (Criteria) this;
        }

        public Criteria andFechaLessThanOrEqualTo(String value) {
            addCriterion("fecha <=", value, "fecha");
            return (Criteria) this;
        }

        public Criteria andFechaLike(String value) {
            addCriterion("fecha like", value, "fecha");
            return (Criteria) this;
        }

        public Criteria andFechaNotLike(String value) {
            addCriterion("fecha not like", value, "fecha");
            return (Criteria) this;
        }

        public Criteria andFechaIn(List<String> values) {
            addCriterion("fecha in", values, "fecha");
            return (Criteria) this;
        }

        public Criteria andFechaNotIn(List<String> values) {
            addCriterion("fecha not in", values, "fecha");
            return (Criteria) this;
        }

        public Criteria andFechaBetween(String value1, String value2) {
            addCriterion("fecha between", value1, value2, "fecha");
            return (Criteria) this;
        }

        public Criteria andFechaNotBetween(String value1, String value2) {
            addCriterion("fecha not between", value1, value2, "fecha");
            return (Criteria) this;
        }

        public Criteria andCentroCostoIdIsNull() {
            addCriterion("centro_costo_id is null");
            return (Criteria) this;
        }

        public Criteria andCentroCostoIdIsNotNull() {
            addCriterion("centro_costo_id is not null");
            return (Criteria) this;
        }

        public Criteria andCentroCostoIdEqualTo(Long value) {
            addCriterion("centro_costo_id =", value, "centroCostoId");
            return (Criteria) this;
        }

        public Criteria andCentroCostoIdNotEqualTo(Long value) {
            addCriterion("centro_costo_id <>", value, "centroCostoId");
            return (Criteria) this;
        }

        public Criteria andCentroCostoIdGreaterThan(Long value) {
            addCriterion("centro_costo_id >", value, "centroCostoId");
            return (Criteria) this;
        }

        public Criteria andCentroCostoIdGreaterThanOrEqualTo(Long value) {
            addCriterion("centro_costo_id >=", value, "centroCostoId");
            return (Criteria) this;
        }

        public Criteria andCentroCostoIdLessThan(Long value) {
            addCriterion("centro_costo_id <", value, "centroCostoId");
            return (Criteria) this;
        }

        public Criteria andCentroCostoIdLessThanOrEqualTo(Long value) {
            addCriterion("centro_costo_id <=", value, "centroCostoId");
            return (Criteria) this;
        }

        public Criteria andCentroCostoIdIn(List<Long> values) {
            addCriterion("centro_costo_id in", values, "centroCostoId");
            return (Criteria) this;
        }

        public Criteria andCentroCostoIdNotIn(List<Long> values) {
            addCriterion("centro_costo_id not in", values, "centroCostoId");
            return (Criteria) this;
        }

        public Criteria andCentroCostoIdBetween(Long value1, Long value2) {
            addCriterion("centro_costo_id between", value1, value2, "centroCostoId");
            return (Criteria) this;
        }

        public Criteria andCentroCostoIdNotBetween(Long value1, Long value2) {
            addCriterion("centro_costo_id not between", value1, value2, "centroCostoId");
            return (Criteria) this;
        }

        public Criteria andCuentaCompletaIsNull() {
            addCriterion("cuenta_completa is null");
            return (Criteria) this;
        }

        public Criteria andCuentaCompletaIsNotNull() {
            addCriterion("cuenta_completa is not null");
            return (Criteria) this;
        }

        public Criteria andCuentaCompletaEqualTo(String value) {
            addCriterion("cuenta_completa =", value, "cuentaCompleta");
            return (Criteria) this;
        }

        public Criteria andCuentaCompletaNotEqualTo(String value) {
            addCriterion("cuenta_completa <>", value, "cuentaCompleta");
            return (Criteria) this;
        }

        public Criteria andCuentaCompletaGreaterThan(String value) {
            addCriterion("cuenta_completa >", value, "cuentaCompleta");
            return (Criteria) this;
        }

        public Criteria andCuentaCompletaGreaterThanOrEqualTo(String value) {
            addCriterion("cuenta_completa >=", value, "cuentaCompleta");
            return (Criteria) this;
        }

        public Criteria andCuentaCompletaLessThan(String value) {
            addCriterion("cuenta_completa <", value, "cuentaCompleta");
            return (Criteria) this;
        }

        public Criteria andCuentaCompletaLessThanOrEqualTo(String value) {
            addCriterion("cuenta_completa <=", value, "cuentaCompleta");
            return (Criteria) this;
        }

        public Criteria andCuentaCompletaLike(String value) {
            addCriterion("cuenta_completa like", value, "cuentaCompleta");
            return (Criteria) this;
        }

        public Criteria andCuentaCompletaNotLike(String value) {
            addCriterion("cuenta_completa not like", value, "cuentaCompleta");
            return (Criteria) this;
        }

        public Criteria andCuentaCompletaIn(List<String> values) {
            addCriterion("cuenta_completa in", values, "cuentaCompleta");
            return (Criteria) this;
        }

        public Criteria andCuentaCompletaNotIn(List<String> values) {
            addCriterion("cuenta_completa not in", values, "cuentaCompleta");
            return (Criteria) this;
        }

        public Criteria andCuentaCompletaBetween(String value1, String value2) {
            addCriterion("cuenta_completa between", value1, value2, "cuentaCompleta");
            return (Criteria) this;
        }

        public Criteria andCuentaCompletaNotBetween(String value1, String value2) {
            addCriterion("cuenta_completa not between", value1, value2, "cuentaCompleta");
            return (Criteria) this;
        }

        public Criteria andInstrumentoIsNull() {
            addCriterion("instrumento is null");
            return (Criteria) this;
        }

        public Criteria andInstrumentoIsNotNull() {
            addCriterion("instrumento is not null");
            return (Criteria) this;
        }

        public Criteria andInstrumentoEqualTo(Long value) {
            addCriterion("instrumento =", value, "instrumento");
            return (Criteria) this;
        }

        public Criteria andInstrumentoNotEqualTo(Long value) {
            addCriterion("instrumento <>", value, "instrumento");
            return (Criteria) this;
        }

        public Criteria andInstrumentoGreaterThan(Long value) {
            addCriterion("instrumento >", value, "instrumento");
            return (Criteria) this;
        }

        public Criteria andInstrumentoGreaterThanOrEqualTo(Long value) {
            addCriterion("instrumento >=", value, "instrumento");
            return (Criteria) this;
        }

        public Criteria andInstrumentoLessThan(Long value) {
            addCriterion("instrumento <", value, "instrumento");
            return (Criteria) this;
        }

        public Criteria andInstrumentoLessThanOrEqualTo(Long value) {
            addCriterion("instrumento <=", value, "instrumento");
            return (Criteria) this;
        }

        public Criteria andInstrumentoIn(List<Long> values) {
            addCriterion("instrumento in", values, "instrumento");
            return (Criteria) this;
        }

        public Criteria andInstrumentoNotIn(List<Long> values) {
            addCriterion("instrumento not in", values, "instrumento");
            return (Criteria) this;
        }

        public Criteria andInstrumentoBetween(Long value1, Long value2) {
            addCriterion("instrumento between", value1, value2, "instrumento");
            return (Criteria) this;
        }

        public Criteria andInstrumentoNotBetween(Long value1, Long value2) {
            addCriterion("instrumento not between", value1, value2, "instrumento");
            return (Criteria) this;
        }

        public Criteria andMonedaIdIsNull() {
            addCriterion("moneda_id is null");
            return (Criteria) this;
        }

        public Criteria andMonedaIdIsNotNull() {
            addCriterion("moneda_id is not null");
            return (Criteria) this;
        }

        public Criteria andMonedaIdEqualTo(Long value) {
            addCriterion("moneda_id =", value, "monedaId");
            return (Criteria) this;
        }

        public Criteria andMonedaIdNotEqualTo(Long value) {
            addCriterion("moneda_id <>", value, "monedaId");
            return (Criteria) this;
        }

        public Criteria andMonedaIdGreaterThan(Long value) {
            addCriterion("moneda_id >", value, "monedaId");
            return (Criteria) this;
        }

        public Criteria andMonedaIdGreaterThanOrEqualTo(Long value) {
            addCriterion("moneda_id >=", value, "monedaId");
            return (Criteria) this;
        }

        public Criteria andMonedaIdLessThan(Long value) {
            addCriterion("moneda_id <", value, "monedaId");
            return (Criteria) this;
        }

        public Criteria andMonedaIdLessThanOrEqualTo(Long value) {
            addCriterion("moneda_id <=", value, "monedaId");
            return (Criteria) this;
        }

        public Criteria andMonedaIdIn(List<Long> values) {
            addCriterion("moneda_id in", values, "monedaId");
            return (Criteria) this;
        }

        public Criteria andMonedaIdNotIn(List<Long> values) {
            addCriterion("moneda_id not in", values, "monedaId");
            return (Criteria) this;
        }

        public Criteria andMonedaIdBetween(Long value1, Long value2) {
            addCriterion("moneda_id between", value1, value2, "monedaId");
            return (Criteria) this;
        }

        public Criteria andMonedaIdNotBetween(Long value1, Long value2) {
            addCriterion("moneda_id not between", value1, value2, "monedaId");
            return (Criteria) this;
        }

        public Criteria andCargosIsNull() {
            addCriterion("cargos is null");
            return (Criteria) this;
        }

        public Criteria andCargosIsNotNull() {
            addCriterion("cargos is not null");
            return (Criteria) this;
        }

        public Criteria andCargosEqualTo(BigDecimal value) {
            addCriterion("cargos =", value, "cargos");
            return (Criteria) this;
        }

        public Criteria andCargosNotEqualTo(BigDecimal value) {
            addCriterion("cargos <>", value, "cargos");
            return (Criteria) this;
        }

        public Criteria andCargosGreaterThan(BigDecimal value) {
            addCriterion("cargos >", value, "cargos");
            return (Criteria) this;
        }

        public Criteria andCargosGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("cargos >=", value, "cargos");
            return (Criteria) this;
        }

        public Criteria andCargosLessThan(BigDecimal value) {
            addCriterion("cargos <", value, "cargos");
            return (Criteria) this;
        }

        public Criteria andCargosLessThanOrEqualTo(BigDecimal value) {
            addCriterion("cargos <=", value, "cargos");
            return (Criteria) this;
        }

        public Criteria andCargosIn(List<BigDecimal> values) {
            addCriterion("cargos in", values, "cargos");
            return (Criteria) this;
        }

        public Criteria andCargosNotIn(List<BigDecimal> values) {
            addCriterion("cargos not in", values, "cargos");
            return (Criteria) this;
        }

        public Criteria andCargosBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cargos between", value1, value2, "cargos");
            return (Criteria) this;
        }

        public Criteria andCargosNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cargos not between", value1, value2, "cargos");
            return (Criteria) this;
        }

        public Criteria andAbonosIsNull() {
            addCriterion("abonos is null");
            return (Criteria) this;
        }

        public Criteria andAbonosIsNotNull() {
            addCriterion("abonos is not null");
            return (Criteria) this;
        }

        public Criteria andAbonosEqualTo(BigDecimal value) {
            addCriterion("abonos =", value, "abonos");
            return (Criteria) this;
        }

        public Criteria andAbonosNotEqualTo(BigDecimal value) {
            addCriterion("abonos <>", value, "abonos");
            return (Criteria) this;
        }

        public Criteria andAbonosGreaterThan(BigDecimal value) {
            addCriterion("abonos >", value, "abonos");
            return (Criteria) this;
        }

        public Criteria andAbonosGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("abonos >=", value, "abonos");
            return (Criteria) this;
        }

        public Criteria andAbonosLessThan(BigDecimal value) {
            addCriterion("abonos <", value, "abonos");
            return (Criteria) this;
        }

        public Criteria andAbonosLessThanOrEqualTo(BigDecimal value) {
            addCriterion("abonos <=", value, "abonos");
            return (Criteria) this;
        }

        public Criteria andAbonosIn(List<BigDecimal> values) {
            addCriterion("abonos in", values, "abonos");
            return (Criteria) this;
        }

        public Criteria andAbonosNotIn(List<BigDecimal> values) {
            addCriterion("abonos not in", values, "abonos");
            return (Criteria) this;
        }

        public Criteria andAbonosBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("abonos between", value1, value2, "abonos");
            return (Criteria) this;
        }

        public Criteria andAbonosNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("abonos not between", value1, value2, "abonos");
            return (Criteria) this;
        }

        public Criteria andDescripcionIsNull() {
            addCriterion("descripcion is null");
            return (Criteria) this;
        }

        public Criteria andDescripcionIsNotNull() {
            addCriterion("descripcion is not null");
            return (Criteria) this;
        }

        public Criteria andDescripcionEqualTo(String value) {
            addCriterion("descripcion =", value, "descripcion");
            return (Criteria) this;
        }

        public Criteria andDescripcionNotEqualTo(String value) {
            addCriterion("descripcion <>", value, "descripcion");
            return (Criteria) this;
        }

        public Criteria andDescripcionGreaterThan(String value) {
            addCriterion("descripcion >", value, "descripcion");
            return (Criteria) this;
        }

        public Criteria andDescripcionGreaterThanOrEqualTo(String value) {
            addCriterion("descripcion >=", value, "descripcion");
            return (Criteria) this;
        }

        public Criteria andDescripcionLessThan(String value) {
            addCriterion("descripcion <", value, "descripcion");
            return (Criteria) this;
        }

        public Criteria andDescripcionLessThanOrEqualTo(String value) {
            addCriterion("descripcion <=", value, "descripcion");
            return (Criteria) this;
        }

        public Criteria andDescripcionLike(String value) {
            addCriterion("descripcion like", value, "descripcion");
            return (Criteria) this;
        }

        public Criteria andDescripcionNotLike(String value) {
            addCriterion("descripcion not like", value, "descripcion");
            return (Criteria) this;
        }

        public Criteria andDescripcionIn(List<String> values) {
            addCriterion("descripcion in", values, "descripcion");
            return (Criteria) this;
        }

        public Criteria andDescripcionNotIn(List<String> values) {
            addCriterion("descripcion not in", values, "descripcion");
            return (Criteria) this;
        }

        public Criteria andDescripcionBetween(String value1, String value2) {
            addCriterion("descripcion between", value1, value2, "descripcion");
            return (Criteria) this;
        }

        public Criteria andDescripcionNotBetween(String value1, String value2) {
            addCriterion("descripcion not between", value1, value2, "descripcion");
            return (Criteria) this;
        }

        public Criteria andReferenciaIsNull() {
            addCriterion("referencia is null");
            return (Criteria) this;
        }

        public Criteria andReferenciaIsNotNull() {
            addCriterion("referencia is not null");
            return (Criteria) this;
        }

        public Criteria andReferenciaEqualTo(String value) {
            addCriterion("referencia =", value, "referencia");
            return (Criteria) this;
        }

        public Criteria andReferenciaNotEqualTo(String value) {
            addCriterion("referencia <>", value, "referencia");
            return (Criteria) this;
        }

        public Criteria andReferenciaGreaterThan(String value) {
            addCriterion("referencia >", value, "referencia");
            return (Criteria) this;
        }

        public Criteria andReferenciaGreaterThanOrEqualTo(String value) {
            addCriterion("referencia >=", value, "referencia");
            return (Criteria) this;
        }

        public Criteria andReferenciaLessThan(String value) {
            addCriterion("referencia <", value, "referencia");
            return (Criteria) this;
        }

        public Criteria andReferenciaLessThanOrEqualTo(String value) {
            addCriterion("referencia <=", value, "referencia");
            return (Criteria) this;
        }

        public Criteria andReferenciaLike(String value) {
            addCriterion("referencia like", value, "referencia");
            return (Criteria) this;
        }

        public Criteria andReferenciaNotLike(String value) {
            addCriterion("referencia not like", value, "referencia");
            return (Criteria) this;
        }

        public Criteria andReferenciaIn(List<String> values) {
            addCriterion("referencia in", values, "referencia");
            return (Criteria) this;
        }

        public Criteria andReferenciaNotIn(List<String> values) {
            addCriterion("referencia not in", values, "referencia");
            return (Criteria) this;
        }

        public Criteria andReferenciaBetween(String value1, String value2) {
            addCriterion("referencia between", value1, value2, "referencia");
            return (Criteria) this;
        }

        public Criteria andReferenciaNotBetween(String value1, String value2) {
            addCriterion("referencia not between", value1, value2, "referencia");
            return (Criteria) this;
        }

        public Criteria andProcedimientoContIsNull() {
            addCriterion("procedimiento_cont is null");
            return (Criteria) this;
        }

        public Criteria andProcedimientoContIsNotNull() {
            addCriterion("procedimiento_cont is not null");
            return (Criteria) this;
        }

        public Criteria andProcedimientoContEqualTo(String value) {
            addCriterion("procedimiento_cont =", value, "procedimientoCont");
            return (Criteria) this;
        }

        public Criteria andProcedimientoContNotEqualTo(String value) {
            addCriterion("procedimiento_cont <>", value, "procedimientoCont");
            return (Criteria) this;
        }

        public Criteria andProcedimientoContGreaterThan(String value) {
            addCriterion("procedimiento_cont >", value, "procedimientoCont");
            return (Criteria) this;
        }

        public Criteria andProcedimientoContGreaterThanOrEqualTo(String value) {
            addCriterion("procedimiento_cont >=", value, "procedimientoCont");
            return (Criteria) this;
        }

        public Criteria andProcedimientoContLessThan(String value) {
            addCriterion("procedimiento_cont <", value, "procedimientoCont");
            return (Criteria) this;
        }

        public Criteria andProcedimientoContLessThanOrEqualTo(String value) {
            addCriterion("procedimiento_cont <=", value, "procedimientoCont");
            return (Criteria) this;
        }

        public Criteria andProcedimientoContLike(String value) {
            addCriterion("procedimiento_cont like", value, "procedimientoCont");
            return (Criteria) this;
        }

        public Criteria andProcedimientoContNotLike(String value) {
            addCriterion("procedimiento_cont not like", value, "procedimientoCont");
            return (Criteria) this;
        }

        public Criteria andProcedimientoContIn(List<String> values) {
            addCriterion("procedimiento_cont in", values, "procedimientoCont");
            return (Criteria) this;
        }

        public Criteria andProcedimientoContNotIn(List<String> values) {
            addCriterion("procedimiento_cont not in", values, "procedimientoCont");
            return (Criteria) this;
        }

        public Criteria andProcedimientoContBetween(String value1, String value2) {
            addCriterion("procedimiento_cont between", value1, value2, "procedimientoCont");
            return (Criteria) this;
        }

        public Criteria andProcedimientoContNotBetween(String value1, String value2) {
            addCriterion("procedimiento_cont not between", value1, value2, "procedimientoCont");
            return (Criteria) this;
        }

        public Criteria andTipoInstrumentoIdIsNull() {
            addCriterion("tipo_instrumento_id is null");
            return (Criteria) this;
        }

        public Criteria andTipoInstrumentoIdIsNotNull() {
            addCriterion("tipo_instrumento_id is not null");
            return (Criteria) this;
        }

        public Criteria andTipoInstrumentoIdEqualTo(String value) {
            addCriterion("tipo_instrumento_id =", value, "tipoInstrumentoId");
            return (Criteria) this;
        }

        public Criteria andTipoInstrumentoIdNotEqualTo(String value) {
            addCriterion("tipo_instrumento_id <>", value, "tipoInstrumentoId");
            return (Criteria) this;
        }

        public Criteria andTipoInstrumentoIdGreaterThan(String value) {
            addCriterion("tipo_instrumento_id >", value, "tipoInstrumentoId");
            return (Criteria) this;
        }

        public Criteria andTipoInstrumentoIdGreaterThanOrEqualTo(String value) {
            addCriterion("tipo_instrumento_id >=", value, "tipoInstrumentoId");
            return (Criteria) this;
        }

        public Criteria andTipoInstrumentoIdLessThan(String value) {
            addCriterion("tipo_instrumento_id <", value, "tipoInstrumentoId");
            return (Criteria) this;
        }

        public Criteria andTipoInstrumentoIdLessThanOrEqualTo(String value) {
            addCriterion("tipo_instrumento_id <=", value, "tipoInstrumentoId");
            return (Criteria) this;
        }

        public Criteria andTipoInstrumentoIdLike(String value) {
            addCriterion("tipo_instrumento_id like", value, "tipoInstrumentoId");
            return (Criteria) this;
        }

        public Criteria andTipoInstrumentoIdNotLike(String value) {
            addCriterion("tipo_instrumento_id not like", value, "tipoInstrumentoId");
            return (Criteria) this;
        }

        public Criteria andTipoInstrumentoIdIn(List<String> values) {
            addCriterion("tipo_instrumento_id in", values, "tipoInstrumentoId");
            return (Criteria) this;
        }

        public Criteria andTipoInstrumentoIdNotIn(List<String> values) {
            addCriterion("tipo_instrumento_id not in", values, "tipoInstrumentoId");
            return (Criteria) this;
        }

        public Criteria andTipoInstrumentoIdBetween(String value1, String value2) {
            addCriterion("tipo_instrumento_id between", value1, value2, "tipoInstrumentoId");
            return (Criteria) this;
        }

        public Criteria andTipoInstrumentoIdNotBetween(String value1, String value2) {
            addCriterion("tipo_instrumento_id not between", value1, value2, "tipoInstrumentoId");
            return (Criteria) this;
        }

        public Criteria andRfcIsNull() {
            addCriterion("rfc is null");
            return (Criteria) this;
        }

        public Criteria andRfcIsNotNull() {
            addCriterion("rfc is not null");
            return (Criteria) this;
        }

        public Criteria andRfcEqualTo(String value) {
            addCriterion("rfc =", value, "rfc");
            return (Criteria) this;
        }

        public Criteria andRfcNotEqualTo(String value) {
            addCriterion("rfc <>", value, "rfc");
            return (Criteria) this;
        }

        public Criteria andRfcGreaterThan(String value) {
            addCriterion("rfc >", value, "rfc");
            return (Criteria) this;
        }

        public Criteria andRfcGreaterThanOrEqualTo(String value) {
            addCriterion("rfc >=", value, "rfc");
            return (Criteria) this;
        }

        public Criteria andRfcLessThan(String value) {
            addCriterion("rfc <", value, "rfc");
            return (Criteria) this;
        }

        public Criteria andRfcLessThanOrEqualTo(String value) {
            addCriterion("rfc <=", value, "rfc");
            return (Criteria) this;
        }

        public Criteria andRfcLike(String value) {
            addCriterion("rfc like", value, "rfc");
            return (Criteria) this;
        }

        public Criteria andRfcNotLike(String value) {
            addCriterion("rfc not like", value, "rfc");
            return (Criteria) this;
        }

        public Criteria andRfcIn(List<String> values) {
            addCriterion("rfc in", values, "rfc");
            return (Criteria) this;
        }

        public Criteria andRfcNotIn(List<String> values) {
            addCriterion("rfc not in", values, "rfc");
            return (Criteria) this;
        }

        public Criteria andRfcBetween(String value1, String value2) {
            addCriterion("rfc between", value1, value2, "rfc");
            return (Criteria) this;
        }

        public Criteria andRfcNotBetween(String value1, String value2) {
            addCriterion("rfc not between", value1, value2, "rfc");
            return (Criteria) this;
        }

        public Criteria andTotalFacturaIsNull() {
            addCriterion("total_factura is null");
            return (Criteria) this;
        }

        public Criteria andTotalFacturaIsNotNull() {
            addCriterion("total_factura is not null");
            return (Criteria) this;
        }

        public Criteria andTotalFacturaEqualTo(BigDecimal value) {
            addCriterion("total_factura =", value, "totalFactura");
            return (Criteria) this;
        }

        public Criteria andTotalFacturaNotEqualTo(BigDecimal value) {
            addCriterion("total_factura <>", value, "totalFactura");
            return (Criteria) this;
        }

        public Criteria andTotalFacturaGreaterThan(BigDecimal value) {
            addCriterion("total_factura >", value, "totalFactura");
            return (Criteria) this;
        }

        public Criteria andTotalFacturaGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_factura >=", value, "totalFactura");
            return (Criteria) this;
        }

        public Criteria andTotalFacturaLessThan(BigDecimal value) {
            addCriterion("total_factura <", value, "totalFactura");
            return (Criteria) this;
        }

        public Criteria andTotalFacturaLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_factura <=", value, "totalFactura");
            return (Criteria) this;
        }

        public Criteria andTotalFacturaIn(List<BigDecimal> values) {
            addCriterion("total_factura in", values, "totalFactura");
            return (Criteria) this;
        }

        public Criteria andTotalFacturaNotIn(List<BigDecimal> values) {
            addCriterion("total_factura not in", values, "totalFactura");
            return (Criteria) this;
        }

        public Criteria andTotalFacturaBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_factura between", value1, value2, "totalFactura");
            return (Criteria) this;
        }

        public Criteria andTotalFacturaNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_factura not between", value1, value2, "totalFactura");
            return (Criteria) this;
        }

        public Criteria andFolioUuidIsNull() {
            addCriterion("folio_uuid is null");
            return (Criteria) this;
        }

        public Criteria andFolioUuidIsNotNull() {
            addCriterion("folio_uuid is not null");
            return (Criteria) this;
        }

        public Criteria andFolioUuidEqualTo(String value) {
            addCriterion("folio_uuid =", value, "folioUuid");
            return (Criteria) this;
        }

        public Criteria andFolioUuidNotEqualTo(String value) {
            addCriterion("folio_uuid <>", value, "folioUuid");
            return (Criteria) this;
        }

        public Criteria andFolioUuidGreaterThan(String value) {
            addCriterion("folio_uuid >", value, "folioUuid");
            return (Criteria) this;
        }

        public Criteria andFolioUuidGreaterThanOrEqualTo(String value) {
            addCriterion("folio_uuid >=", value, "folioUuid");
            return (Criteria) this;
        }

        public Criteria andFolioUuidLessThan(String value) {
            addCriterion("folio_uuid <", value, "folioUuid");
            return (Criteria) this;
        }

        public Criteria andFolioUuidLessThanOrEqualTo(String value) {
            addCriterion("folio_uuid <=", value, "folioUuid");
            return (Criteria) this;
        }

        public Criteria andFolioUuidLike(String value) {
            addCriterion("folio_uuid like", value, "folioUuid");
            return (Criteria) this;
        }

        public Criteria andFolioUuidNotLike(String value) {
            addCriterion("folio_uuid not like", value, "folioUuid");
            return (Criteria) this;
        }

        public Criteria andFolioUuidIn(List<String> values) {
            addCriterion("folio_uuid in", values, "folioUuid");
            return (Criteria) this;
        }

        public Criteria andFolioUuidNotIn(List<String> values) {
            addCriterion("folio_uuid not in", values, "folioUuid");
            return (Criteria) this;
        }

        public Criteria andFolioUuidBetween(String value1, String value2) {
            addCriterion("folio_uuid between", value1, value2, "folioUuid");
            return (Criteria) this;
        }

        public Criteria andFolioUuidNotBetween(String value1, String value2) {
            addCriterion("folio_uuid not between", value1, value2, "folioUuid");
            return (Criteria) this;
        }

        public Criteria andUsuarioIsNull() {
            addCriterion("usuario is null");
            return (Criteria) this;
        }

        public Criteria andUsuarioIsNotNull() {
            addCriterion("usuario is not null");
            return (Criteria) this;
        }

        public Criteria andUsuarioEqualTo(Long value) {
            addCriterion("usuario =", value, "usuario");
            return (Criteria) this;
        }

        public Criteria andUsuarioNotEqualTo(Long value) {
            addCriterion("usuario <>", value, "usuario");
            return (Criteria) this;
        }

        public Criteria andUsuarioGreaterThan(Long value) {
            addCriterion("usuario >", value, "usuario");
            return (Criteria) this;
        }

        public Criteria andUsuarioGreaterThanOrEqualTo(Long value) {
            addCriterion("usuario >=", value, "usuario");
            return (Criteria) this;
        }

        public Criteria andUsuarioLessThan(Long value) {
            addCriterion("usuario <", value, "usuario");
            return (Criteria) this;
        }

        public Criteria andUsuarioLessThanOrEqualTo(Long value) {
            addCriterion("usuario <=", value, "usuario");
            return (Criteria) this;
        }

        public Criteria andUsuarioIn(List<Long> values) {
            addCriterion("usuario in", values, "usuario");
            return (Criteria) this;
        }

        public Criteria andUsuarioNotIn(List<Long> values) {
            addCriterion("usuario not in", values, "usuario");
            return (Criteria) this;
        }

        public Criteria andUsuarioBetween(Long value1, Long value2) {
            addCriterion("usuario between", value1, value2, "usuario");
            return (Criteria) this;
        }

        public Criteria andUsuarioNotBetween(Long value1, Long value2) {
            addCriterion("usuario not between", value1, value2, "usuario");
            return (Criteria) this;
        }

        public Criteria andFechaActualIsNull() {
            addCriterion("fecha_actual is null");
            return (Criteria) this;
        }

        public Criteria andFechaActualIsNotNull() {
            addCriterion("fecha_actual is not null");
            return (Criteria) this;
        }

        public Criteria andFechaActualEqualTo(String value) {
            addCriterion("fecha_actual =", value, "fechaActual");
            return (Criteria) this;
        }

        public Criteria andFechaActualNotEqualTo(String value) {
            addCriterion("fecha_actual <>", value, "fechaActual");
            return (Criteria) this;
        }

        public Criteria andFechaActualGreaterThan(String value) {
            addCriterion("fecha_actual >", value, "fechaActual");
            return (Criteria) this;
        }

        public Criteria andFechaActualGreaterThanOrEqualTo(String value) {
            addCriterion("fecha_actual >=", value, "fechaActual");
            return (Criteria) this;
        }

        public Criteria andFechaActualLessThan(String value) {
            addCriterion("fecha_actual <", value, "fechaActual");
            return (Criteria) this;
        }

        public Criteria andFechaActualLessThanOrEqualTo(String value) {
            addCriterion("fecha_actual <=", value, "fechaActual");
            return (Criteria) this;
        }

        public Criteria andFechaActualLike(String value) {
            addCriterion("fecha_actual like", value, "fechaActual");
            return (Criteria) this;
        }

        public Criteria andFechaActualNotLike(String value) {
            addCriterion("fecha_actual not like", value, "fechaActual");
            return (Criteria) this;
        }

        public Criteria andFechaActualIn(List<String> values) {
            addCriterion("fecha_actual in", values, "fechaActual");
            return (Criteria) this;
        }

        public Criteria andFechaActualNotIn(List<String> values) {
            addCriterion("fecha_actual not in", values, "fechaActual");
            return (Criteria) this;
        }

        public Criteria andFechaActualBetween(String value1, String value2) {
            addCriterion("fecha_actual between", value1, value2, "fechaActual");
            return (Criteria) this;
        }

        public Criteria andFechaActualNotBetween(String value1, String value2) {
            addCriterion("fecha_actual not between", value1, value2, "fechaActual");
            return (Criteria) this;
        }

        public Criteria andDireccionIpIsNull() {
            addCriterion("direccion_ip is null");
            return (Criteria) this;
        }

        public Criteria andDireccionIpIsNotNull() {
            addCriterion("direccion_ip is not null");
            return (Criteria) this;
        }

        public Criteria andDireccionIpEqualTo(String value) {
            addCriterion("direccion_ip =", value, "direccionIp");
            return (Criteria) this;
        }

        public Criteria andDireccionIpNotEqualTo(String value) {
            addCriterion("direccion_ip <>", value, "direccionIp");
            return (Criteria) this;
        }

        public Criteria andDireccionIpGreaterThan(String value) {
            addCriterion("direccion_ip >", value, "direccionIp");
            return (Criteria) this;
        }

        public Criteria andDireccionIpGreaterThanOrEqualTo(String value) {
            addCriterion("direccion_ip >=", value, "direccionIp");
            return (Criteria) this;
        }

        public Criteria andDireccionIpLessThan(String value) {
            addCriterion("direccion_ip <", value, "direccionIp");
            return (Criteria) this;
        }

        public Criteria andDireccionIpLessThanOrEqualTo(String value) {
            addCriterion("direccion_ip <=", value, "direccionIp");
            return (Criteria) this;
        }

        public Criteria andDireccionIpLike(String value) {
            addCriterion("direccion_ip like", value, "direccionIp");
            return (Criteria) this;
        }

        public Criteria andDireccionIpNotLike(String value) {
            addCriterion("direccion_ip not like", value, "direccionIp");
            return (Criteria) this;
        }

        public Criteria andDireccionIpIn(List<String> values) {
            addCriterion("direccion_ip in", values, "direccionIp");
            return (Criteria) this;
        }

        public Criteria andDireccionIpNotIn(List<String> values) {
            addCriterion("direccion_ip not in", values, "direccionIp");
            return (Criteria) this;
        }

        public Criteria andDireccionIpBetween(String value1, String value2) {
            addCriterion("direccion_ip between", value1, value2, "direccionIp");
            return (Criteria) this;
        }

        public Criteria andDireccionIpNotBetween(String value1, String value2) {
            addCriterion("direccion_ip not between", value1, value2, "direccionIp");
            return (Criteria) this;
        }

        public Criteria andProgramaIdIsNull() {
            addCriterion("programa_id is null");
            return (Criteria) this;
        }

        public Criteria andProgramaIdIsNotNull() {
            addCriterion("programa_id is not null");
            return (Criteria) this;
        }

        public Criteria andProgramaIdEqualTo(String value) {
            addCriterion("programa_id =", value, "programaId");
            return (Criteria) this;
        }

        public Criteria andProgramaIdNotEqualTo(String value) {
            addCriterion("programa_id <>", value, "programaId");
            return (Criteria) this;
        }

        public Criteria andProgramaIdGreaterThan(String value) {
            addCriterion("programa_id >", value, "programaId");
            return (Criteria) this;
        }

        public Criteria andProgramaIdGreaterThanOrEqualTo(String value) {
            addCriterion("programa_id >=", value, "programaId");
            return (Criteria) this;
        }

        public Criteria andProgramaIdLessThan(String value) {
            addCriterion("programa_id <", value, "programaId");
            return (Criteria) this;
        }

        public Criteria andProgramaIdLessThanOrEqualTo(String value) {
            addCriterion("programa_id <=", value, "programaId");
            return (Criteria) this;
        }

        public Criteria andProgramaIdLike(String value) {
            addCriterion("programa_id like", value, "programaId");
            return (Criteria) this;
        }

        public Criteria andProgramaIdNotLike(String value) {
            addCriterion("programa_id not like", value, "programaId");
            return (Criteria) this;
        }

        public Criteria andProgramaIdIn(List<String> values) {
            addCriterion("programa_id in", values, "programaId");
            return (Criteria) this;
        }

        public Criteria andProgramaIdNotIn(List<String> values) {
            addCriterion("programa_id not in", values, "programaId");
            return (Criteria) this;
        }

        public Criteria andProgramaIdBetween(String value1, String value2) {
            addCriterion("programa_id between", value1, value2, "programaId");
            return (Criteria) this;
        }

        public Criteria andProgramaIdNotBetween(String value1, String value2) {
            addCriterion("programa_id not between", value1, value2, "programaId");
            return (Criteria) this;
        }

        public Criteria andSucursalIsNull() {
            addCriterion("sucursal is null");
            return (Criteria) this;
        }

        public Criteria andSucursalIsNotNull() {
            addCriterion("sucursal is not null");
            return (Criteria) this;
        }

        public Criteria andSucursalEqualTo(Long value) {
            addCriterion("sucursal =", value, "sucursal");
            return (Criteria) this;
        }

        public Criteria andSucursalNotEqualTo(Long value) {
            addCriterion("sucursal <>", value, "sucursal");
            return (Criteria) this;
        }

        public Criteria andSucursalGreaterThan(Long value) {
            addCriterion("sucursal >", value, "sucursal");
            return (Criteria) this;
        }

        public Criteria andSucursalGreaterThanOrEqualTo(Long value) {
            addCriterion("sucursal >=", value, "sucursal");
            return (Criteria) this;
        }

        public Criteria andSucursalLessThan(Long value) {
            addCriterion("sucursal <", value, "sucursal");
            return (Criteria) this;
        }

        public Criteria andSucursalLessThanOrEqualTo(Long value) {
            addCriterion("sucursal <=", value, "sucursal");
            return (Criteria) this;
        }

        public Criteria andSucursalIn(List<Long> values) {
            addCriterion("sucursal in", values, "sucursal");
            return (Criteria) this;
        }

        public Criteria andSucursalNotIn(List<Long> values) {
            addCriterion("sucursal not in", values, "sucursal");
            return (Criteria) this;
        }

        public Criteria andSucursalBetween(Long value1, Long value2) {
            addCriterion("sucursal between", value1, value2, "sucursal");
            return (Criteria) this;
        }

        public Criteria andSucursalNotBetween(Long value1, Long value2) {
            addCriterion("sucursal not between", value1, value2, "sucursal");
            return (Criteria) this;
        }

        public Criteria andNumTransaccionIsNull() {
            addCriterion("num_transaccion is null");
            return (Criteria) this;
        }

        public Criteria andNumTransaccionIsNotNull() {
            addCriterion("num_transaccion is not null");
            return (Criteria) this;
        }

        public Criteria andNumTransaccionEqualTo(Long value) {
            addCriterion("num_transaccion =", value, "numTransaccion");
            return (Criteria) this;
        }

        public Criteria andNumTransaccionNotEqualTo(Long value) {
            addCriterion("num_transaccion <>", value, "numTransaccion");
            return (Criteria) this;
        }

        public Criteria andNumTransaccionGreaterThan(Long value) {
            addCriterion("num_transaccion >", value, "numTransaccion");
            return (Criteria) this;
        }

        public Criteria andNumTransaccionGreaterThanOrEqualTo(Long value) {
            addCriterion("num_transaccion >=", value, "numTransaccion");
            return (Criteria) this;
        }

        public Criteria andNumTransaccionLessThan(Long value) {
            addCriterion("num_transaccion <", value, "numTransaccion");
            return (Criteria) this;
        }

        public Criteria andNumTransaccionLessThanOrEqualTo(Long value) {
            addCriterion("num_transaccion <=", value, "numTransaccion");
            return (Criteria) this;
        }

        public Criteria andNumTransaccionIn(List<Long> values) {
            addCriterion("num_transaccion in", values, "numTransaccion");
            return (Criteria) this;
        }

        public Criteria andNumTransaccionNotIn(List<Long> values) {
            addCriterion("num_transaccion not in", values, "numTransaccion");
            return (Criteria) this;
        }

        public Criteria andNumTransaccionBetween(Long value1, Long value2) {
            addCriterion("num_transaccion between", value1, value2, "numTransaccion");
            return (Criteria) this;
        }

        public Criteria andNumTransaccionNotBetween(Long value1, Long value2) {
            addCriterion("num_transaccion not between", value1, value2, "numTransaccion");
            return (Criteria) this;
        }

        public Criteria andLedgerIdIsNull() {
            addCriterion("ledger_id is null");
            return (Criteria) this;
        }

        public Criteria andLedgerIdIsNotNull() {
            addCriterion("ledger_id is not null");
            return (Criteria) this;
        }

        public Criteria andLedgerIdEqualTo(Long value) {
            addCriterion("ledger_id =", value, "ledgerId");
            return (Criteria) this;
        }

        public Criteria andLedgerIdNotEqualTo(Long value) {
            addCriterion("ledger_id <>", value, "ledgerId");
            return (Criteria) this;
        }

        public Criteria andLedgerIdGreaterThan(Long value) {
            addCriterion("ledger_id >", value, "ledgerId");
            return (Criteria) this;
        }

        public Criteria andLedgerIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ledger_id >=", value, "ledgerId");
            return (Criteria) this;
        }

        public Criteria andLedgerIdLessThan(Long value) {
            addCriterion("ledger_id <", value, "ledgerId");
            return (Criteria) this;
        }

        public Criteria andLedgerIdLessThanOrEqualTo(Long value) {
            addCriterion("ledger_id <=", value, "ledgerId");
            return (Criteria) this;
        }

        public Criteria andLedgerIdIn(List<Long> values) {
            addCriterion("ledger_id in", values, "ledgerId");
            return (Criteria) this;
        }

        public Criteria andLedgerIdNotIn(List<Long> values) {
            addCriterion("ledger_id not in", values, "ledgerId");
            return (Criteria) this;
        }

        public Criteria andLedgerIdBetween(Long value1, Long value2) {
            addCriterion("ledger_id between", value1, value2, "ledgerId");
            return (Criteria) this;
        }

        public Criteria andLedgerIdNotBetween(Long value1, Long value2) {
            addCriterion("ledger_id not between", value1, value2, "ledgerId");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNull() {
            addCriterion("currency_code is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNotNull() {
            addCriterion("currency_code is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeEqualTo(String value) {
            addCriterion("currency_code =", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotEqualTo(String value) {
            addCriterion("currency_code <>", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThan(String value) {
            addCriterion("currency_code >", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("currency_code >=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThan(String value) {
            addCriterion("currency_code <", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThanOrEqualTo(String value) {
            addCriterion("currency_code <=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLike(String value) {
            addCriterion("currency_code like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotLike(String value) {
            addCriterion("currency_code not like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIn(List<String> values) {
            addCriterion("currency_code in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotIn(List<String> values) {
            addCriterion("currency_code not in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeBetween(String value1, String value2) {
            addCriterion("currency_code between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotBetween(String value1, String value2) {
            addCriterion("currency_code not between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andJournalCategoryIsNull() {
            addCriterion("journal_category is null");
            return (Criteria) this;
        }

        public Criteria andJournalCategoryIsNotNull() {
            addCriterion("journal_category is not null");
            return (Criteria) this;
        }

        public Criteria andJournalCategoryEqualTo(String value) {
            addCriterion("journal_category =", value, "journalCategory");
            return (Criteria) this;
        }

        public Criteria andJournalCategoryNotEqualTo(String value) {
            addCriterion("journal_category <>", value, "journalCategory");
            return (Criteria) this;
        }

        public Criteria andJournalCategoryGreaterThan(String value) {
            addCriterion("journal_category >", value, "journalCategory");
            return (Criteria) this;
        }

        public Criteria andJournalCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("journal_category >=", value, "journalCategory");
            return (Criteria) this;
        }

        public Criteria andJournalCategoryLessThan(String value) {
            addCriterion("journal_category <", value, "journalCategory");
            return (Criteria) this;
        }

        public Criteria andJournalCategoryLessThanOrEqualTo(String value) {
            addCriterion("journal_category <=", value, "journalCategory");
            return (Criteria) this;
        }

        public Criteria andJournalCategoryLike(String value) {
            addCriterion("journal_category like", value, "journalCategory");
            return (Criteria) this;
        }

        public Criteria andJournalCategoryNotLike(String value) {
            addCriterion("journal_category not like", value, "journalCategory");
            return (Criteria) this;
        }

        public Criteria andJournalCategoryIn(List<String> values) {
            addCriterion("journal_category in", values, "journalCategory");
            return (Criteria) this;
        }

        public Criteria andJournalCategoryNotIn(List<String> values) {
            addCriterion("journal_category not in", values, "journalCategory");
            return (Criteria) this;
        }

        public Criteria andJournalCategoryBetween(String value1, String value2) {
            addCriterion("journal_category between", value1, value2, "journalCategory");
            return (Criteria) this;
        }

        public Criteria andJournalCategoryNotBetween(String value1, String value2) {
            addCriterion("journal_category not between", value1, value2, "journalCategory");
            return (Criteria) this;
        }

        public Criteria andJournalSourceIsNull() {
            addCriterion("journal_source is null");
            return (Criteria) this;
        }

        public Criteria andJournalSourceIsNotNull() {
            addCriterion("journal_source is not null");
            return (Criteria) this;
        }

        public Criteria andJournalSourceEqualTo(String value) {
            addCriterion("journal_source =", value, "journalSource");
            return (Criteria) this;
        }

        public Criteria andJournalSourceNotEqualTo(String value) {
            addCriterion("journal_source <>", value, "journalSource");
            return (Criteria) this;
        }

        public Criteria andJournalSourceGreaterThan(String value) {
            addCriterion("journal_source >", value, "journalSource");
            return (Criteria) this;
        }

        public Criteria andJournalSourceGreaterThanOrEqualTo(String value) {
            addCriterion("journal_source >=", value, "journalSource");
            return (Criteria) this;
        }

        public Criteria andJournalSourceLessThan(String value) {
            addCriterion("journal_source <", value, "journalSource");
            return (Criteria) this;
        }

        public Criteria andJournalSourceLessThanOrEqualTo(String value) {
            addCriterion("journal_source <=", value, "journalSource");
            return (Criteria) this;
        }

        public Criteria andJournalSourceLike(String value) {
            addCriterion("journal_source like", value, "journalSource");
            return (Criteria) this;
        }

        public Criteria andJournalSourceNotLike(String value) {
            addCriterion("journal_source not like", value, "journalSource");
            return (Criteria) this;
        }

        public Criteria andJournalSourceIn(List<String> values) {
            addCriterion("journal_source in", values, "journalSource");
            return (Criteria) this;
        }

        public Criteria andJournalSourceNotIn(List<String> values) {
            addCriterion("journal_source not in", values, "journalSource");
            return (Criteria) this;
        }

        public Criteria andJournalSourceBetween(String value1, String value2) {
            addCriterion("journal_source between", value1, value2, "journalSource");
            return (Criteria) this;
        }

        public Criteria andJournalSourceNotBetween(String value1, String value2) {
            addCriterion("journal_source not between", value1, value2, "journalSource");
            return (Criteria) this;
        }

        public Criteria andSegment1IsNull() {
            addCriterion("segment1 is null");
            return (Criteria) this;
        }

        public Criteria andSegment1IsNotNull() {
            addCriterion("segment1 is not null");
            return (Criteria) this;
        }

        public Criteria andSegment1EqualTo(String value) {
            addCriterion("segment1 =", value, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1NotEqualTo(String value) {
            addCriterion("segment1 <>", value, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1GreaterThan(String value) {
            addCriterion("segment1 >", value, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1GreaterThanOrEqualTo(String value) {
            addCriterion("segment1 >=", value, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1LessThan(String value) {
            addCriterion("segment1 <", value, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1LessThanOrEqualTo(String value) {
            addCriterion("segment1 <=", value, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1Like(String value) {
            addCriterion("segment1 like", value, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1NotLike(String value) {
            addCriterion("segment1 not like", value, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1In(List<String> values) {
            addCriterion("segment1 in", values, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1NotIn(List<String> values) {
            addCriterion("segment1 not in", values, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1Between(String value1, String value2) {
            addCriterion("segment1 between", value1, value2, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1NotBetween(String value1, String value2) {
            addCriterion("segment1 not between", value1, value2, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment2IsNull() {
            addCriterion("segment2 is null");
            return (Criteria) this;
        }

        public Criteria andSegment2IsNotNull() {
            addCriterion("segment2 is not null");
            return (Criteria) this;
        }

        public Criteria andSegment2EqualTo(String value) {
            addCriterion("segment2 =", value, "segment2");
            return (Criteria) this;
        }

        public Criteria andSegment2NotEqualTo(String value) {
            addCriterion("segment2 <>", value, "segment2");
            return (Criteria) this;
        }

        public Criteria andSegment2GreaterThan(String value) {
            addCriterion("segment2 >", value, "segment2");
            return (Criteria) this;
        }

        public Criteria andSegment2GreaterThanOrEqualTo(String value) {
            addCriterion("segment2 >=", value, "segment2");
            return (Criteria) this;
        }

        public Criteria andSegment2LessThan(String value) {
            addCriterion("segment2 <", value, "segment2");
            return (Criteria) this;
        }

        public Criteria andSegment2LessThanOrEqualTo(String value) {
            addCriterion("segment2 <=", value, "segment2");
            return (Criteria) this;
        }

        public Criteria andSegment2Like(String value) {
            addCriterion("segment2 like", value, "segment2");
            return (Criteria) this;
        }

        public Criteria andSegment2NotLike(String value) {
            addCriterion("segment2 not like", value, "segment2");
            return (Criteria) this;
        }

        public Criteria andSegment2In(List<String> values) {
            addCriterion("segment2 in", values, "segment2");
            return (Criteria) this;
        }

        public Criteria andSegment2NotIn(List<String> values) {
            addCriterion("segment2 not in", values, "segment2");
            return (Criteria) this;
        }

        public Criteria andSegment2Between(String value1, String value2) {
            addCriterion("segment2 between", value1, value2, "segment2");
            return (Criteria) this;
        }

        public Criteria andSegment2NotBetween(String value1, String value2) {
            addCriterion("segment2 not between", value1, value2, "segment2");
            return (Criteria) this;
        }

        public Criteria andSegment3IsNull() {
            addCriterion("segment3 is null");
            return (Criteria) this;
        }

        public Criteria andSegment3IsNotNull() {
            addCriterion("segment3 is not null");
            return (Criteria) this;
        }

        public Criteria andSegment3EqualTo(String value) {
            addCriterion("segment3 =", value, "segment3");
            return (Criteria) this;
        }

        public Criteria andSegment3NotEqualTo(String value) {
            addCriterion("segment3 <>", value, "segment3");
            return (Criteria) this;
        }

        public Criteria andSegment3GreaterThan(String value) {
            addCriterion("segment3 >", value, "segment3");
            return (Criteria) this;
        }

        public Criteria andSegment3GreaterThanOrEqualTo(String value) {
            addCriterion("segment3 >=", value, "segment3");
            return (Criteria) this;
        }

        public Criteria andSegment3LessThan(String value) {
            addCriterion("segment3 <", value, "segment3");
            return (Criteria) this;
        }

        public Criteria andSegment3LessThanOrEqualTo(String value) {
            addCriterion("segment3 <=", value, "segment3");
            return (Criteria) this;
        }

        public Criteria andSegment3Like(String value) {
            addCriterion("segment3 like", value, "segment3");
            return (Criteria) this;
        }

        public Criteria andSegment3NotLike(String value) {
            addCriterion("segment3 not like", value, "segment3");
            return (Criteria) this;
        }

        public Criteria andSegment3In(List<String> values) {
            addCriterion("segment3 in", values, "segment3");
            return (Criteria) this;
        }

        public Criteria andSegment3NotIn(List<String> values) {
            addCriterion("segment3 not in", values, "segment3");
            return (Criteria) this;
        }

        public Criteria andSegment3Between(String value1, String value2) {
            addCriterion("segment3 between", value1, value2, "segment3");
            return (Criteria) this;
        }

        public Criteria andSegment3NotBetween(String value1, String value2) {
            addCriterion("segment3 not between", value1, value2, "segment3");
            return (Criteria) this;
        }

        public Criteria andSegment4IsNull() {
            addCriterion("segment4 is null");
            return (Criteria) this;
        }

        public Criteria andSegment4IsNotNull() {
            addCriterion("segment4 is not null");
            return (Criteria) this;
        }

        public Criteria andSegment4EqualTo(String value) {
            addCriterion("segment4 =", value, "segment4");
            return (Criteria) this;
        }

        public Criteria andSegment4NotEqualTo(String value) {
            addCriterion("segment4 <>", value, "segment4");
            return (Criteria) this;
        }

        public Criteria andSegment4GreaterThan(String value) {
            addCriterion("segment4 >", value, "segment4");
            return (Criteria) this;
        }

        public Criteria andSegment4GreaterThanOrEqualTo(String value) {
            addCriterion("segment4 >=", value, "segment4");
            return (Criteria) this;
        }

        public Criteria andSegment4LessThan(String value) {
            addCriterion("segment4 <", value, "segment4");
            return (Criteria) this;
        }

        public Criteria andSegment4LessThanOrEqualTo(String value) {
            addCriterion("segment4 <=", value, "segment4");
            return (Criteria) this;
        }

        public Criteria andSegment4Like(String value) {
            addCriterion("segment4 like", value, "segment4");
            return (Criteria) this;
        }

        public Criteria andSegment4NotLike(String value) {
            addCriterion("segment4 not like", value, "segment4");
            return (Criteria) this;
        }

        public Criteria andSegment4In(List<String> values) {
            addCriterion("segment4 in", values, "segment4");
            return (Criteria) this;
        }

        public Criteria andSegment4NotIn(List<String> values) {
            addCriterion("segment4 not in", values, "segment4");
            return (Criteria) this;
        }

        public Criteria andSegment4Between(String value1, String value2) {
            addCriterion("segment4 between", value1, value2, "segment4");
            return (Criteria) this;
        }

        public Criteria andSegment4NotBetween(String value1, String value2) {
            addCriterion("segment4 not between", value1, value2, "segment4");
            return (Criteria) this;
        }

        public Criteria andSegment5IsNull() {
            addCriterion("segment5 is null");
            return (Criteria) this;
        }

        public Criteria andSegment5IsNotNull() {
            addCriterion("segment5 is not null");
            return (Criteria) this;
        }

        public Criteria andSegment5EqualTo(String value) {
            addCriterion("segment5 =", value, "segment5");
            return (Criteria) this;
        }

        public Criteria andSegment5NotEqualTo(String value) {
            addCriterion("segment5 <>", value, "segment5");
            return (Criteria) this;
        }

        public Criteria andSegment5GreaterThan(String value) {
            addCriterion("segment5 >", value, "segment5");
            return (Criteria) this;
        }

        public Criteria andSegment5GreaterThanOrEqualTo(String value) {
            addCriterion("segment5 >=", value, "segment5");
            return (Criteria) this;
        }

        public Criteria andSegment5LessThan(String value) {
            addCriterion("segment5 <", value, "segment5");
            return (Criteria) this;
        }

        public Criteria andSegment5LessThanOrEqualTo(String value) {
            addCriterion("segment5 <=", value, "segment5");
            return (Criteria) this;
        }

        public Criteria andSegment5Like(String value) {
            addCriterion("segment5 like", value, "segment5");
            return (Criteria) this;
        }

        public Criteria andSegment5NotLike(String value) {
            addCriterion("segment5 not like", value, "segment5");
            return (Criteria) this;
        }

        public Criteria andSegment5In(List<String> values) {
            addCriterion("segment5 in", values, "segment5");
            return (Criteria) this;
        }

        public Criteria andSegment5NotIn(List<String> values) {
            addCriterion("segment5 not in", values, "segment5");
            return (Criteria) this;
        }

        public Criteria andSegment5Between(String value1, String value2) {
            addCriterion("segment5 between", value1, value2, "segment5");
            return (Criteria) this;
        }

        public Criteria andSegment5NotBetween(String value1, String value2) {
            addCriterion("segment5 not between", value1, value2, "segment5");
            return (Criteria) this;
        }

        public Criteria andSegment6IsNull() {
            addCriterion("segment6 is null");
            return (Criteria) this;
        }

        public Criteria andSegment6IsNotNull() {
            addCriterion("segment6 is not null");
            return (Criteria) this;
        }

        public Criteria andSegment6EqualTo(String value) {
            addCriterion("segment6 =", value, "segment6");
            return (Criteria) this;
        }

        public Criteria andSegment6NotEqualTo(String value) {
            addCriterion("segment6 <>", value, "segment6");
            return (Criteria) this;
        }

        public Criteria andSegment6GreaterThan(String value) {
            addCriterion("segment6 >", value, "segment6");
            return (Criteria) this;
        }

        public Criteria andSegment6GreaterThanOrEqualTo(String value) {
            addCriterion("segment6 >=", value, "segment6");
            return (Criteria) this;
        }

        public Criteria andSegment6LessThan(String value) {
            addCriterion("segment6 <", value, "segment6");
            return (Criteria) this;
        }

        public Criteria andSegment6LessThanOrEqualTo(String value) {
            addCriterion("segment6 <=", value, "segment6");
            return (Criteria) this;
        }

        public Criteria andSegment6Like(String value) {
            addCriterion("segment6 like", value, "segment6");
            return (Criteria) this;
        }

        public Criteria andSegment6NotLike(String value) {
            addCriterion("segment6 not like", value, "segment6");
            return (Criteria) this;
        }

        public Criteria andSegment6In(List<String> values) {
            addCriterion("segment6 in", values, "segment6");
            return (Criteria) this;
        }

        public Criteria andSegment6NotIn(List<String> values) {
            addCriterion("segment6 not in", values, "segment6");
            return (Criteria) this;
        }

        public Criteria andSegment6Between(String value1, String value2) {
            addCriterion("segment6 between", value1, value2, "segment6");
            return (Criteria) this;
        }

        public Criteria andSegment6NotBetween(String value1, String value2) {
            addCriterion("segment6 not between", value1, value2, "segment6");
            return (Criteria) this;
        }

        public Criteria andSegment7IsNull() {
            addCriterion("segment7 is null");
            return (Criteria) this;
        }

        public Criteria andSegment7IsNotNull() {
            addCriterion("segment7 is not null");
            return (Criteria) this;
        }

        public Criteria andSegment7EqualTo(String value) {
            addCriterion("segment7 =", value, "segment7");
            return (Criteria) this;
        }

        public Criteria andSegment7NotEqualTo(String value) {
            addCriterion("segment7 <>", value, "segment7");
            return (Criteria) this;
        }

        public Criteria andSegment7GreaterThan(String value) {
            addCriterion("segment7 >", value, "segment7");
            return (Criteria) this;
        }

        public Criteria andSegment7GreaterThanOrEqualTo(String value) {
            addCriterion("segment7 >=", value, "segment7");
            return (Criteria) this;
        }

        public Criteria andSegment7LessThan(String value) {
            addCriterion("segment7 <", value, "segment7");
            return (Criteria) this;
        }

        public Criteria andSegment7LessThanOrEqualTo(String value) {
            addCriterion("segment7 <=", value, "segment7");
            return (Criteria) this;
        }

        public Criteria andSegment7Like(String value) {
            addCriterion("segment7 like", value, "segment7");
            return (Criteria) this;
        }

        public Criteria andSegment7NotLike(String value) {
            addCriterion("segment7 not like", value, "segment7");
            return (Criteria) this;
        }

        public Criteria andSegment7In(List<String> values) {
            addCriterion("segment7 in", values, "segment7");
            return (Criteria) this;
        }

        public Criteria andSegment7NotIn(List<String> values) {
            addCriterion("segment7 not in", values, "segment7");
            return (Criteria) this;
        }

        public Criteria andSegment7Between(String value1, String value2) {
            addCriterion("segment7 between", value1, value2, "segment7");
            return (Criteria) this;
        }

        public Criteria andSegment7NotBetween(String value1, String value2) {
            addCriterion("segment7 not between", value1, value2, "segment7");
            return (Criteria) this;
        }

        public Criteria andSegment8IsNull() {
            addCriterion("segment8 is null");
            return (Criteria) this;
        }

        public Criteria andSegment8IsNotNull() {
            addCriterion("segment8 is not null");
            return (Criteria) this;
        }

        public Criteria andSegment8EqualTo(String value) {
            addCriterion("segment8 =", value, "segment8");
            return (Criteria) this;
        }

        public Criteria andSegment8NotEqualTo(String value) {
            addCriterion("segment8 <>", value, "segment8");
            return (Criteria) this;
        }

        public Criteria andSegment8GreaterThan(String value) {
            addCriterion("segment8 >", value, "segment8");
            return (Criteria) this;
        }

        public Criteria andSegment8GreaterThanOrEqualTo(String value) {
            addCriterion("segment8 >=", value, "segment8");
            return (Criteria) this;
        }

        public Criteria andSegment8LessThan(String value) {
            addCriterion("segment8 <", value, "segment8");
            return (Criteria) this;
        }

        public Criteria andSegment8LessThanOrEqualTo(String value) {
            addCriterion("segment8 <=", value, "segment8");
            return (Criteria) this;
        }

        public Criteria andSegment8Like(String value) {
            addCriterion("segment8 like", value, "segment8");
            return (Criteria) this;
        }

        public Criteria andSegment8NotLike(String value) {
            addCriterion("segment8 not like", value, "segment8");
            return (Criteria) this;
        }

        public Criteria andSegment8In(List<String> values) {
            addCriterion("segment8 in", values, "segment8");
            return (Criteria) this;
        }

        public Criteria andSegment8NotIn(List<String> values) {
            addCriterion("segment8 not in", values, "segment8");
            return (Criteria) this;
        }

        public Criteria andSegment8Between(String value1, String value2) {
            addCriterion("segment8 between", value1, value2, "segment8");
            return (Criteria) this;
        }

        public Criteria andSegment8NotBetween(String value1, String value2) {
            addCriterion("segment8 not between", value1, value2, "segment8");
            return (Criteria) this;
        }

        public Criteria andSegment9IsNull() {
            addCriterion("segment9 is null");
            return (Criteria) this;
        }

        public Criteria andSegment9IsNotNull() {
            addCriterion("segment9 is not null");
            return (Criteria) this;
        }

        public Criteria andSegment9EqualTo(String value) {
            addCriterion("segment9 =", value, "segment9");
            return (Criteria) this;
        }

        public Criteria andSegment9NotEqualTo(String value) {
            addCriterion("segment9 <>", value, "segment9");
            return (Criteria) this;
        }

        public Criteria andSegment9GreaterThan(String value) {
            addCriterion("segment9 >", value, "segment9");
            return (Criteria) this;
        }

        public Criteria andSegment9GreaterThanOrEqualTo(String value) {
            addCriterion("segment9 >=", value, "segment9");
            return (Criteria) this;
        }

        public Criteria andSegment9LessThan(String value) {
            addCriterion("segment9 <", value, "segment9");
            return (Criteria) this;
        }

        public Criteria andSegment9LessThanOrEqualTo(String value) {
            addCriterion("segment9 <=", value, "segment9");
            return (Criteria) this;
        }

        public Criteria andSegment9Like(String value) {
            addCriterion("segment9 like", value, "segment9");
            return (Criteria) this;
        }

        public Criteria andSegment9NotLike(String value) {
            addCriterion("segment9 not like", value, "segment9");
            return (Criteria) this;
        }

        public Criteria andSegment9In(List<String> values) {
            addCriterion("segment9 in", values, "segment9");
            return (Criteria) this;
        }

        public Criteria andSegment9NotIn(List<String> values) {
            addCriterion("segment9 not in", values, "segment9");
            return (Criteria) this;
        }

        public Criteria andSegment9Between(String value1, String value2) {
            addCriterion("segment9 between", value1, value2, "segment9");
            return (Criteria) this;
        }

        public Criteria andSegment9NotBetween(String value1, String value2) {
            addCriterion("segment9 not between", value1, value2, "segment9");
            return (Criteria) this;
        }

        public Criteria andSegment10IsNull() {
            addCriterion("segment10 is null");
            return (Criteria) this;
        }

        public Criteria andSegment10IsNotNull() {
            addCriterion("segment10 is not null");
            return (Criteria) this;
        }

        public Criteria andSegment10EqualTo(String value) {
            addCriterion("segment10 =", value, "segment10");
            return (Criteria) this;
        }

        public Criteria andSegment10NotEqualTo(String value) {
            addCriterion("segment10 <>", value, "segment10");
            return (Criteria) this;
        }

        public Criteria andSegment10GreaterThan(String value) {
            addCriterion("segment10 >", value, "segment10");
            return (Criteria) this;
        }

        public Criteria andSegment10GreaterThanOrEqualTo(String value) {
            addCriterion("segment10 >=", value, "segment10");
            return (Criteria) this;
        }

        public Criteria andSegment10LessThan(String value) {
            addCriterion("segment10 <", value, "segment10");
            return (Criteria) this;
        }

        public Criteria andSegment10LessThanOrEqualTo(String value) {
            addCriterion("segment10 <=", value, "segment10");
            return (Criteria) this;
        }

        public Criteria andSegment10Like(String value) {
            addCriterion("segment10 like", value, "segment10");
            return (Criteria) this;
        }

        public Criteria andSegment10NotLike(String value) {
            addCriterion("segment10 not like", value, "segment10");
            return (Criteria) this;
        }

        public Criteria andSegment10In(List<String> values) {
            addCriterion("segment10 in", values, "segment10");
            return (Criteria) this;
        }

        public Criteria andSegment10NotIn(List<String> values) {
            addCriterion("segment10 not in", values, "segment10");
            return (Criteria) this;
        }

        public Criteria andSegment10Between(String value1, String value2) {
            addCriterion("segment10 between", value1, value2, "segment10");
            return (Criteria) this;
        }

        public Criteria andSegment10NotBetween(String value1, String value2) {
            addCriterion("segment10 not between", value1, value2, "segment10");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNull() {
            addCriterion("group_id is null");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNotNull() {
            addCriterion("group_id is not null");
            return (Criteria) this;
        }

        public Criteria andGroupIdEqualTo(Long value) {
            addCriterion("group_id =", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotEqualTo(Long value) {
            addCriterion("group_id <>", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThan(Long value) {
            addCriterion("group_id >", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThanOrEqualTo(Long value) {
            addCriterion("group_id >=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThan(Long value) {
            addCriterion("group_id <", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThanOrEqualTo(Long value) {
            addCriterion("group_id <=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdIn(List<Long> values) {
            addCriterion("group_id in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotIn(List<Long> values) {
            addCriterion("group_id not in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdBetween(Long value1, Long value2) {
            addCriterion("group_id between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotBetween(Long value1, Long value2) {
            addCriterion("group_id not between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andCurrencyConversionDateIsNull() {
            addCriterion("currency_conversion_date is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyConversionDateIsNotNull() {
            addCriterion("currency_conversion_date is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyConversionDateEqualTo(Date value) {
            addCriterion("currency_conversion_date =", value, "currencyConversionDate");
            return (Criteria) this;
        }

        public Criteria andCurrencyConversionDateNotEqualTo(Date value) {
            addCriterion("currency_conversion_date <>", value, "currencyConversionDate");
            return (Criteria) this;
        }

        public Criteria andCurrencyConversionDateGreaterThan(Date value) {
            addCriterion("currency_conversion_date >", value, "currencyConversionDate");
            return (Criteria) this;
        }

        public Criteria andCurrencyConversionDateGreaterThanOrEqualTo(Date value) {
            addCriterion("currency_conversion_date >=", value, "currencyConversionDate");
            return (Criteria) this;
        }

        public Criteria andCurrencyConversionDateLessThan(Date value) {
            addCriterion("currency_conversion_date <", value, "currencyConversionDate");
            return (Criteria) this;
        }

        public Criteria andCurrencyConversionDateLessThanOrEqualTo(Date value) {
            addCriterion("currency_conversion_date <=", value, "currencyConversionDate");
            return (Criteria) this;
        }

        public Criteria andCurrencyConversionDateIn(List<Date> values) {
            addCriterion("currency_conversion_date in", values, "currencyConversionDate");
            return (Criteria) this;
        }

        public Criteria andCurrencyConversionDateNotIn(List<Date> values) {
            addCriterion("currency_conversion_date not in", values, "currencyConversionDate");
            return (Criteria) this;
        }

        public Criteria andCurrencyConversionDateBetween(Date value1, Date value2) {
            addCriterion("currency_conversion_date between", value1, value2, "currencyConversionDate");
            return (Criteria) this;
        }

        public Criteria andCurrencyConversionDateNotBetween(Date value1, Date value2) {
            addCriterion("currency_conversion_date not between", value1, value2, "currencyConversionDate");
            return (Criteria) this;
        }

        public Criteria andCurrencyConversionRateIsNull() {
            addCriterion("currency_conversion_rate is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyConversionRateIsNotNull() {
            addCriterion("currency_conversion_rate is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyConversionRateEqualTo(BigDecimal value) {
            addCriterion("currency_conversion_rate =", value, "currencyConversionRate");
            return (Criteria) this;
        }

        public Criteria andCurrencyConversionRateNotEqualTo(BigDecimal value) {
            addCriterion("currency_conversion_rate <>", value, "currencyConversionRate");
            return (Criteria) this;
        }

        public Criteria andCurrencyConversionRateGreaterThan(BigDecimal value) {
            addCriterion("currency_conversion_rate >", value, "currencyConversionRate");
            return (Criteria) this;
        }

        public Criteria andCurrencyConversionRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("currency_conversion_rate >=", value, "currencyConversionRate");
            return (Criteria) this;
        }

        public Criteria andCurrencyConversionRateLessThan(BigDecimal value) {
            addCriterion("currency_conversion_rate <", value, "currencyConversionRate");
            return (Criteria) this;
        }

        public Criteria andCurrencyConversionRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("currency_conversion_rate <=", value, "currencyConversionRate");
            return (Criteria) this;
        }

        public Criteria andCurrencyConversionRateIn(List<BigDecimal> values) {
            addCriterion("currency_conversion_rate in", values, "currencyConversionRate");
            return (Criteria) this;
        }

        public Criteria andCurrencyConversionRateNotIn(List<BigDecimal> values) {
            addCriterion("currency_conversion_rate not in", values, "currencyConversionRate");
            return (Criteria) this;
        }

        public Criteria andCurrencyConversionRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("currency_conversion_rate between", value1, value2, "currencyConversionRate");
            return (Criteria) this;
        }

        public Criteria andCurrencyConversionRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("currency_conversion_rate not between", value1, value2, "currencyConversionRate");
            return (Criteria) this;
        }

        public Criteria andUserCurrencyConversionTypeIsNull() {
            addCriterion("user_currency_conversion_type is null");
            return (Criteria) this;
        }

        public Criteria andUserCurrencyConversionTypeIsNotNull() {
            addCriterion("user_currency_conversion_type is not null");
            return (Criteria) this;
        }

        public Criteria andUserCurrencyConversionTypeEqualTo(String value) {
            addCriterion("user_currency_conversion_type =", value, "userCurrencyConversionType");
            return (Criteria) this;
        }

        public Criteria andUserCurrencyConversionTypeNotEqualTo(String value) {
            addCriterion("user_currency_conversion_type <>", value, "userCurrencyConversionType");
            return (Criteria) this;
        }

        public Criteria andUserCurrencyConversionTypeGreaterThan(String value) {
            addCriterion("user_currency_conversion_type >", value, "userCurrencyConversionType");
            return (Criteria) this;
        }

        public Criteria andUserCurrencyConversionTypeGreaterThanOrEqualTo(String value) {
            addCriterion("user_currency_conversion_type >=", value, "userCurrencyConversionType");
            return (Criteria) this;
        }

        public Criteria andUserCurrencyConversionTypeLessThan(String value) {
            addCriterion("user_currency_conversion_type <", value, "userCurrencyConversionType");
            return (Criteria) this;
        }

        public Criteria andUserCurrencyConversionTypeLessThanOrEqualTo(String value) {
            addCriterion("user_currency_conversion_type <=", value, "userCurrencyConversionType");
            return (Criteria) this;
        }

        public Criteria andUserCurrencyConversionTypeLike(String value) {
            addCriterion("user_currency_conversion_type like", value, "userCurrencyConversionType");
            return (Criteria) this;
        }

        public Criteria andUserCurrencyConversionTypeNotLike(String value) {
            addCriterion("user_currency_conversion_type not like", value, "userCurrencyConversionType");
            return (Criteria) this;
        }

        public Criteria andUserCurrencyConversionTypeIn(List<String> values) {
            addCriterion("user_currency_conversion_type in", values, "userCurrencyConversionType");
            return (Criteria) this;
        }

        public Criteria andUserCurrencyConversionTypeNotIn(List<String> values) {
            addCriterion("user_currency_conversion_type not in", values, "userCurrencyConversionType");
            return (Criteria) this;
        }

        public Criteria andUserCurrencyConversionTypeBetween(String value1, String value2) {
            addCriterion("user_currency_conversion_type between", value1, value2, "userCurrencyConversionType");
            return (Criteria) this;
        }

        public Criteria andUserCurrencyConversionTypeNotBetween(String value1, String value2) {
            addCriterion("user_currency_conversion_type not between", value1, value2, "userCurrencyConversionType");
            return (Criteria) this;
        }

        public Criteria andProcessStatusIsNull() {
            addCriterion("process_status is null");
            return (Criteria) this;
        }

        public Criteria andProcessStatusIsNotNull() {
            addCriterion("process_status is not null");
            return (Criteria) this;
        }

        public Criteria andProcessStatusEqualTo(String value) {
            addCriterion("process_status =", value, "processStatus");
            return (Criteria) this;
        }

        public Criteria andProcessStatusNotEqualTo(String value) {
            addCriterion("process_status <>", value, "processStatus");
            return (Criteria) this;
        }

        public Criteria andProcessStatusGreaterThan(String value) {
            addCriterion("process_status >", value, "processStatus");
            return (Criteria) this;
        }

        public Criteria andProcessStatusGreaterThanOrEqualTo(String value) {
            addCriterion("process_status >=", value, "processStatus");
            return (Criteria) this;
        }

        public Criteria andProcessStatusLessThan(String value) {
            addCriterion("process_status <", value, "processStatus");
            return (Criteria) this;
        }

        public Criteria andProcessStatusLessThanOrEqualTo(String value) {
            addCriterion("process_status <=", value, "processStatus");
            return (Criteria) this;
        }

        public Criteria andProcessStatusLike(String value) {
            addCriterion("process_status like", value, "processStatus");
            return (Criteria) this;
        }

        public Criteria andProcessStatusNotLike(String value) {
            addCriterion("process_status not like", value, "processStatus");
            return (Criteria) this;
        }

        public Criteria andProcessStatusIn(List<String> values) {
            addCriterion("process_status in", values, "processStatus");
            return (Criteria) this;
        }

        public Criteria andProcessStatusNotIn(List<String> values) {
            addCriterion("process_status not in", values, "processStatus");
            return (Criteria) this;
        }

        public Criteria andProcessStatusBetween(String value1, String value2) {
            addCriterion("process_status between", value1, value2, "processStatus");
            return (Criteria) this;
        }

        public Criteria andProcessStatusNotBetween(String value1, String value2) {
            addCriterion("process_status not between", value1, value2, "processStatus");
            return (Criteria) this;
        }

        public Criteria andProcessMessageIsNull() {
            addCriterion("process_message is null");
            return (Criteria) this;
        }

        public Criteria andProcessMessageIsNotNull() {
            addCriterion("process_message is not null");
            return (Criteria) this;
        }

        public Criteria andProcessMessageEqualTo(String value) {
            addCriterion("process_message =", value, "processMessage");
            return (Criteria) this;
        }

        public Criteria andProcessMessageNotEqualTo(String value) {
            addCriterion("process_message <>", value, "processMessage");
            return (Criteria) this;
        }

        public Criteria andProcessMessageGreaterThan(String value) {
            addCriterion("process_message >", value, "processMessage");
            return (Criteria) this;
        }

        public Criteria andProcessMessageGreaterThanOrEqualTo(String value) {
            addCriterion("process_message >=", value, "processMessage");
            return (Criteria) this;
        }

        public Criteria andProcessMessageLessThan(String value) {
            addCriterion("process_message <", value, "processMessage");
            return (Criteria) this;
        }

        public Criteria andProcessMessageLessThanOrEqualTo(String value) {
            addCriterion("process_message <=", value, "processMessage");
            return (Criteria) this;
        }

        public Criteria andProcessMessageLike(String value) {
            addCriterion("process_message like", value, "processMessage");
            return (Criteria) this;
        }

        public Criteria andProcessMessageNotLike(String value) {
            addCriterion("process_message not like", value, "processMessage");
            return (Criteria) this;
        }

        public Criteria andProcessMessageIn(List<String> values) {
            addCriterion("process_message in", values, "processMessage");
            return (Criteria) this;
        }

        public Criteria andProcessMessageNotIn(List<String> values) {
            addCriterion("process_message not in", values, "processMessage");
            return (Criteria) this;
        }

        public Criteria andProcessMessageBetween(String value1, String value2) {
            addCriterion("process_message between", value1, value2, "processMessage");
            return (Criteria) this;
        }

        public Criteria andProcessMessageNotBetween(String value1, String value2) {
            addCriterion("process_message not between", value1, value2, "processMessage");
            return (Criteria) this;
        }

        public Criteria andJeHeaderIdIsNull() {
            addCriterion("je_header_id is null");
            return (Criteria) this;
        }

        public Criteria andJeHeaderIdIsNotNull() {
            addCriterion("je_header_id is not null");
            return (Criteria) this;
        }

        public Criteria andJeHeaderIdEqualTo(Long value) {
            addCriterion("je_header_id =", value, "jeHeaderId");
            return (Criteria) this;
        }

        public Criteria andJeHeaderIdNotEqualTo(Long value) {
            addCriterion("je_header_id <>", value, "jeHeaderId");
            return (Criteria) this;
        }

        public Criteria andJeHeaderIdGreaterThan(Long value) {
            addCriterion("je_header_id >", value, "jeHeaderId");
            return (Criteria) this;
        }

        public Criteria andJeHeaderIdGreaterThanOrEqualTo(Long value) {
            addCriterion("je_header_id >=", value, "jeHeaderId");
            return (Criteria) this;
        }

        public Criteria andJeHeaderIdLessThan(Long value) {
            addCriterion("je_header_id <", value, "jeHeaderId");
            return (Criteria) this;
        }

        public Criteria andJeHeaderIdLessThanOrEqualTo(Long value) {
            addCriterion("je_header_id <=", value, "jeHeaderId");
            return (Criteria) this;
        }

        public Criteria andJeHeaderIdIn(List<Long> values) {
            addCriterion("je_header_id in", values, "jeHeaderId");
            return (Criteria) this;
        }

        public Criteria andJeHeaderIdNotIn(List<Long> values) {
            addCriterion("je_header_id not in", values, "jeHeaderId");
            return (Criteria) this;
        }

        public Criteria andJeHeaderIdBetween(Long value1, Long value2) {
            addCriterion("je_header_id between", value1, value2, "jeHeaderId");
            return (Criteria) this;
        }

        public Criteria andJeHeaderIdNotBetween(Long value1, Long value2) {
            addCriterion("je_header_id not between", value1, value2, "jeHeaderId");
            return (Criteria) this;
        }

        public Criteria andJournalNameIsNull() {
            addCriterion("journal_name is null");
            return (Criteria) this;
        }

        public Criteria andJournalNameIsNotNull() {
            addCriterion("journal_name is not null");
            return (Criteria) this;
        }

        public Criteria andJournalNameEqualTo(String value) {
            addCriterion("journal_name =", value, "journalName");
            return (Criteria) this;
        }

        public Criteria andJournalNameNotEqualTo(String value) {
            addCriterion("journal_name <>", value, "journalName");
            return (Criteria) this;
        }

        public Criteria andJournalNameGreaterThan(String value) {
            addCriterion("journal_name >", value, "journalName");
            return (Criteria) this;
        }

        public Criteria andJournalNameGreaterThanOrEqualTo(String value) {
            addCriterion("journal_name >=", value, "journalName");
            return (Criteria) this;
        }

        public Criteria andJournalNameLessThan(String value) {
            addCriterion("journal_name <", value, "journalName");
            return (Criteria) this;
        }

        public Criteria andJournalNameLessThanOrEqualTo(String value) {
            addCriterion("journal_name <=", value, "journalName");
            return (Criteria) this;
        }

        public Criteria andJournalNameLike(String value) {
            addCriterion("journal_name like", value, "journalName");
            return (Criteria) this;
        }

        public Criteria andJournalNameNotLike(String value) {
            addCriterion("journal_name not like", value, "journalName");
            return (Criteria) this;
        }

        public Criteria andJournalNameIn(List<String> values) {
            addCriterion("journal_name in", values, "journalName");
            return (Criteria) this;
        }

        public Criteria andJournalNameNotIn(List<String> values) {
            addCriterion("journal_name not in", values, "journalName");
            return (Criteria) this;
        }

        public Criteria andJournalNameBetween(String value1, String value2) {
            addCriterion("journal_name between", value1, value2, "journalName");
            return (Criteria) this;
        }

        public Criteria andJournalNameNotBetween(String value1, String value2) {
            addCriterion("journal_name not between", value1, value2, "journalName");
            return (Criteria) this;
        }

        public Criteria andJeLineNumIsNull() {
            addCriterion("je_line_num is null");
            return (Criteria) this;
        }

        public Criteria andJeLineNumIsNotNull() {
            addCriterion("je_line_num is not null");
            return (Criteria) this;
        }

        public Criteria andJeLineNumEqualTo(Long value) {
            addCriterion("je_line_num =", value, "jeLineNum");
            return (Criteria) this;
        }

        public Criteria andJeLineNumNotEqualTo(Long value) {
            addCriterion("je_line_num <>", value, "jeLineNum");
            return (Criteria) this;
        }

        public Criteria andJeLineNumGreaterThan(Long value) {
            addCriterion("je_line_num >", value, "jeLineNum");
            return (Criteria) this;
        }

        public Criteria andJeLineNumGreaterThanOrEqualTo(Long value) {
            addCriterion("je_line_num >=", value, "jeLineNum");
            return (Criteria) this;
        }

        public Criteria andJeLineNumLessThan(Long value) {
            addCriterion("je_line_num <", value, "jeLineNum");
            return (Criteria) this;
        }

        public Criteria andJeLineNumLessThanOrEqualTo(Long value) {
            addCriterion("je_line_num <=", value, "jeLineNum");
            return (Criteria) this;
        }

        public Criteria andJeLineNumIn(List<Long> values) {
            addCriterion("je_line_num in", values, "jeLineNum");
            return (Criteria) this;
        }

        public Criteria andJeLineNumNotIn(List<Long> values) {
            addCriterion("je_line_num not in", values, "jeLineNum");
            return (Criteria) this;
        }

        public Criteria andJeLineNumBetween(Long value1, Long value2) {
            addCriterion("je_line_num between", value1, value2, "jeLineNum");
            return (Criteria) this;
        }

        public Criteria andJeLineNumNotBetween(Long value1, Long value2) {
            addCriterion("je_line_num not between", value1, value2, "jeLineNum");
            return (Criteria) this;
        }

        public Criteria andDocumentIdIsNull() {
            addCriterion("document_id is null");
            return (Criteria) this;
        }

        public Criteria andDocumentIdIsNotNull() {
            addCriterion("document_id is not null");
            return (Criteria) this;
        }

        public Criteria andDocumentIdEqualTo(Long value) {
            addCriterion("document_id =", value, "documentId");
            return (Criteria) this;
        }

        public Criteria andDocumentIdNotEqualTo(Long value) {
            addCriterion("document_id <>", value, "documentId");
            return (Criteria) this;
        }

        public Criteria andDocumentIdGreaterThan(Long value) {
            addCriterion("document_id >", value, "documentId");
            return (Criteria) this;
        }

        public Criteria andDocumentIdGreaterThanOrEqualTo(Long value) {
            addCriterion("document_id >=", value, "documentId");
            return (Criteria) this;
        }

        public Criteria andDocumentIdLessThan(Long value) {
            addCriterion("document_id <", value, "documentId");
            return (Criteria) this;
        }

        public Criteria andDocumentIdLessThanOrEqualTo(Long value) {
            addCriterion("document_id <=", value, "documentId");
            return (Criteria) this;
        }

        public Criteria andDocumentIdIn(List<Long> values) {
            addCriterion("document_id in", values, "documentId");
            return (Criteria) this;
        }

        public Criteria andDocumentIdNotIn(List<Long> values) {
            addCriterion("document_id not in", values, "documentId");
            return (Criteria) this;
        }

        public Criteria andDocumentIdBetween(Long value1, Long value2) {
            addCriterion("document_id between", value1, value2, "documentId");
            return (Criteria) this;
        }

        public Criteria andDocumentIdNotBetween(Long value1, Long value2) {
            addCriterion("document_id not between", value1, value2, "documentId");
            return (Criteria) this;
        }

        public Criteria andLoadRequestIdIsNull() {
            addCriterion("load_request_id is null");
            return (Criteria) this;
        }

        public Criteria andLoadRequestIdIsNotNull() {
            addCriterion("load_request_id is not null");
            return (Criteria) this;
        }

        public Criteria andLoadRequestIdEqualTo(Long value) {
            addCriterion("load_request_id =", value, "loadRequestId");
            return (Criteria) this;
        }

        public Criteria andLoadRequestIdNotEqualTo(Long value) {
            addCriterion("load_request_id <>", value, "loadRequestId");
            return (Criteria) this;
        }

        public Criteria andLoadRequestIdGreaterThan(Long value) {
            addCriterion("load_request_id >", value, "loadRequestId");
            return (Criteria) this;
        }

        public Criteria andLoadRequestIdGreaterThanOrEqualTo(Long value) {
            addCriterion("load_request_id >=", value, "loadRequestId");
            return (Criteria) this;
        }

        public Criteria andLoadRequestIdLessThan(Long value) {
            addCriterion("load_request_id <", value, "loadRequestId");
            return (Criteria) this;
        }

        public Criteria andLoadRequestIdLessThanOrEqualTo(Long value) {
            addCriterion("load_request_id <=", value, "loadRequestId");
            return (Criteria) this;
        }

        public Criteria andLoadRequestIdIn(List<Long> values) {
            addCriterion("load_request_id in", values, "loadRequestId");
            return (Criteria) this;
        }

        public Criteria andLoadRequestIdNotIn(List<Long> values) {
            addCriterion("load_request_id not in", values, "loadRequestId");
            return (Criteria) this;
        }

        public Criteria andLoadRequestIdBetween(Long value1, Long value2) {
            addCriterion("load_request_id between", value1, value2, "loadRequestId");
            return (Criteria) this;
        }

        public Criteria andLoadRequestIdNotBetween(Long value1, Long value2) {
            addCriterion("load_request_id not between", value1, value2, "loadRequestId");
            return (Criteria) this;
        }

        public Criteria andImportRequestIdIsNull() {
            addCriterion("import_request_id is null");
            return (Criteria) this;
        }

        public Criteria andImportRequestIdIsNotNull() {
            addCriterion("import_request_id is not null");
            return (Criteria) this;
        }

        public Criteria andImportRequestIdEqualTo(Long value) {
            addCriterion("import_request_id =", value, "importRequestId");
            return (Criteria) this;
        }

        public Criteria andImportRequestIdNotEqualTo(Long value) {
            addCriterion("import_request_id <>", value, "importRequestId");
            return (Criteria) this;
        }

        public Criteria andImportRequestIdGreaterThan(Long value) {
            addCriterion("import_request_id >", value, "importRequestId");
            return (Criteria) this;
        }

        public Criteria andImportRequestIdGreaterThanOrEqualTo(Long value) {
            addCriterion("import_request_id >=", value, "importRequestId");
            return (Criteria) this;
        }

        public Criteria andImportRequestIdLessThan(Long value) {
            addCriterion("import_request_id <", value, "importRequestId");
            return (Criteria) this;
        }

        public Criteria andImportRequestIdLessThanOrEqualTo(Long value) {
            addCriterion("import_request_id <=", value, "importRequestId");
            return (Criteria) this;
        }

        public Criteria andImportRequestIdIn(List<Long> values) {
            addCriterion("import_request_id in", values, "importRequestId");
            return (Criteria) this;
        }

        public Criteria andImportRequestIdNotIn(List<Long> values) {
            addCriterion("import_request_id not in", values, "importRequestId");
            return (Criteria) this;
        }

        public Criteria andImportRequestIdBetween(Long value1, Long value2) {
            addCriterion("import_request_id between", value1, value2, "importRequestId");
            return (Criteria) this;
        }

        public Criteria andImportRequestIdNotBetween(Long value1, Long value2) {
            addCriterion("import_request_id not between", value1, value2, "importRequestId");
            return (Criteria) this;
        }

        public Criteria andObjectVersionNumberIsNull() {
            addCriterion("object_version_number is null");
            return (Criteria) this;
        }

        public Criteria andObjectVersionNumberIsNotNull() {
            addCriterion("object_version_number is not null");
            return (Criteria) this;
        }

        public Criteria andObjectVersionNumberEqualTo(Long value) {
            addCriterion("object_version_number =", value, "objectVersionNumber");
            return (Criteria) this;
        }

        public Criteria andObjectVersionNumberNotEqualTo(Long value) {
            addCriterion("object_version_number <>", value, "objectVersionNumber");
            return (Criteria) this;
        }

        public Criteria andObjectVersionNumberGreaterThan(Long value) {
            addCriterion("object_version_number >", value, "objectVersionNumber");
            return (Criteria) this;
        }

        public Criteria andObjectVersionNumberGreaterThanOrEqualTo(Long value) {
            addCriterion("object_version_number >=", value, "objectVersionNumber");
            return (Criteria) this;
        }

        public Criteria andObjectVersionNumberLessThan(Long value) {
            addCriterion("object_version_number <", value, "objectVersionNumber");
            return (Criteria) this;
        }

        public Criteria andObjectVersionNumberLessThanOrEqualTo(Long value) {
            addCriterion("object_version_number <=", value, "objectVersionNumber");
            return (Criteria) this;
        }

        public Criteria andObjectVersionNumberIn(List<Long> values) {
            addCriterion("object_version_number in", values, "objectVersionNumber");
            return (Criteria) this;
        }

        public Criteria andObjectVersionNumberNotIn(List<Long> values) {
            addCriterion("object_version_number not in", values, "objectVersionNumber");
            return (Criteria) this;
        }

        public Criteria andObjectVersionNumberBetween(Long value1, Long value2) {
            addCriterion("object_version_number between", value1, value2, "objectVersionNumber");
            return (Criteria) this;
        }

        public Criteria andObjectVersionNumberNotBetween(Long value1, Long value2) {
            addCriterion("object_version_number not between", value1, value2, "objectVersionNumber");
            return (Criteria) this;
        }

        public Criteria andCreationDateIsNull() {
            addCriterion("creation_date is null");
            return (Criteria) this;
        }

        public Criteria andCreationDateIsNotNull() {
            addCriterion("creation_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreationDateEqualTo(Date value) {
            addCriterion("creation_date =", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateNotEqualTo(Date value) {
            addCriterion("creation_date <>", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateGreaterThan(Date value) {
            addCriterion("creation_date >", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateGreaterThanOrEqualTo(Date value) {
            addCriterion("creation_date >=", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateLessThan(Date value) {
            addCriterion("creation_date <", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateLessThanOrEqualTo(Date value) {
            addCriterion("creation_date <=", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateIn(List<Date> values) {
            addCriterion("creation_date in", values, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateNotIn(List<Date> values) {
            addCriterion("creation_date not in", values, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateBetween(Date value1, Date value2) {
            addCriterion("creation_date between", value1, value2, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateNotBetween(Date value1, Date value2) {
            addCriterion("creation_date not between", value1, value2, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andLastModifyDateIsNull() {
            addCriterion("last_modify_date is null");
            return (Criteria) this;
        }

        public Criteria andLastModifyDateIsNotNull() {
            addCriterion("last_modify_date is not null");
            return (Criteria) this;
        }

        public Criteria andLastModifyDateEqualTo(Date value) {
            addCriterion("last_modify_date =", value, "lastModifyDate");
            return (Criteria) this;
        }

        public Criteria andLastModifyDateNotEqualTo(Date value) {
            addCriterion("last_modify_date <>", value, "lastModifyDate");
            return (Criteria) this;
        }

        public Criteria andLastModifyDateGreaterThan(Date value) {
            addCriterion("last_modify_date >", value, "lastModifyDate");
            return (Criteria) this;
        }

        public Criteria andLastModifyDateGreaterThanOrEqualTo(Date value) {
            addCriterion("last_modify_date >=", value, "lastModifyDate");
            return (Criteria) this;
        }

        public Criteria andLastModifyDateLessThan(Date value) {
            addCriterion("last_modify_date <", value, "lastModifyDate");
            return (Criteria) this;
        }

        public Criteria andLastModifyDateLessThanOrEqualTo(Date value) {
            addCriterion("last_modify_date <=", value, "lastModifyDate");
            return (Criteria) this;
        }

        public Criteria andLastModifyDateIn(List<Date> values) {
            addCriterion("last_modify_date in", values, "lastModifyDate");
            return (Criteria) this;
        }

        public Criteria andLastModifyDateNotIn(List<Date> values) {
            addCriterion("last_modify_date not in", values, "lastModifyDate");
            return (Criteria) this;
        }

        public Criteria andLastModifyDateBetween(Date value1, Date value2) {
            addCriterion("last_modify_date between", value1, value2, "lastModifyDate");
            return (Criteria) this;
        }

        public Criteria andLastModifyDateNotBetween(Date value1, Date value2) {
            addCriterion("last_modify_date not between", value1, value2, "lastModifyDate");
            return (Criteria) this;
        }

        public Criteria andLastModifiedByIsNull() {
            addCriterion("last_modified_by is null");
            return (Criteria) this;
        }

        public Criteria andLastModifiedByIsNotNull() {
            addCriterion("last_modified_by is not null");
            return (Criteria) this;
        }

        public Criteria andLastModifiedByEqualTo(String value) {
            addCriterion("last_modified_by =", value, "lastModifiedBy");
            return (Criteria) this;
        }

        public Criteria andLastModifiedByNotEqualTo(String value) {
            addCriterion("last_modified_by <>", value, "lastModifiedBy");
            return (Criteria) this;
        }

        public Criteria andLastModifiedByGreaterThan(String value) {
            addCriterion("last_modified_by >", value, "lastModifiedBy");
            return (Criteria) this;
        }

        public Criteria andLastModifiedByGreaterThanOrEqualTo(String value) {
            addCriterion("last_modified_by >=", value, "lastModifiedBy");
            return (Criteria) this;
        }

        public Criteria andLastModifiedByLessThan(String value) {
            addCriterion("last_modified_by <", value, "lastModifiedBy");
            return (Criteria) this;
        }

        public Criteria andLastModifiedByLessThanOrEqualTo(String value) {
            addCriterion("last_modified_by <=", value, "lastModifiedBy");
            return (Criteria) this;
        }

        public Criteria andLastModifiedByLike(String value) {
            addCriterion("last_modified_by like", value, "lastModifiedBy");
            return (Criteria) this;
        }

        public Criteria andLastModifiedByNotLike(String value) {
            addCriterion("last_modified_by not like", value, "lastModifiedBy");
            return (Criteria) this;
        }

        public Criteria andLastModifiedByIn(List<String> values) {
            addCriterion("last_modified_by in", values, "lastModifiedBy");
            return (Criteria) this;
        }

        public Criteria andLastModifiedByNotIn(List<String> values) {
            addCriterion("last_modified_by not in", values, "lastModifiedBy");
            return (Criteria) this;
        }

        public Criteria andLastModifiedByBetween(String value1, String value2) {
            addCriterion("last_modified_by between", value1, value2, "lastModifiedBy");
            return (Criteria) this;
        }

        public Criteria andLastModifiedByNotBetween(String value1, String value2) {
            addCriterion("last_modified_by not between", value1, value2, "lastModifiedBy");
            return (Criteria) this;
        }

        public Criteria andProcessDayLikeInsensitive(String value) {
            addCriterion("upper(process_day) like", value.toUpperCase(), "processDay");
            return (Criteria) this;
        }

        public Criteria andFileNameLikeInsensitive(String value) {
            addCriterion("upper(file_name) like", value.toUpperCase(), "fileName");
            return (Criteria) this;
        }

        public Criteria andBatchIdLikeInsensitive(String value) {
            addCriterion("upper(batch_id) like", value.toUpperCase(), "batchId");
            return (Criteria) this;
        }

        public Criteria andSourceSysLikeInsensitive(String value) {
            addCriterion("upper(source_sys) like", value.toUpperCase(), "sourceSys");
            return (Criteria) this;
        }

        public Criteria andFechaLikeInsensitive(String value) {
            addCriterion("upper(fecha) like", value.toUpperCase(), "fecha");
            return (Criteria) this;
        }

        public Criteria andCuentaCompletaLikeInsensitive(String value) {
            addCriterion("upper(cuenta_completa) like", value.toUpperCase(), "cuentaCompleta");
            return (Criteria) this;
        }

        public Criteria andDescripcionLikeInsensitive(String value) {
            addCriterion("upper(descripcion) like", value.toUpperCase(), "descripcion");
            return (Criteria) this;
        }

        public Criteria andReferenciaLikeInsensitive(String value) {
            addCriterion("upper(referencia) like", value.toUpperCase(), "referencia");
            return (Criteria) this;
        }

        public Criteria andProcedimientoContLikeInsensitive(String value) {
            addCriterion("upper(procedimiento_cont) like", value.toUpperCase(), "procedimientoCont");
            return (Criteria) this;
        }

        public Criteria andTipoInstrumentoIdLikeInsensitive(String value) {
            addCriterion("upper(tipo_instrumento_id) like", value.toUpperCase(), "tipoInstrumentoId");
            return (Criteria) this;
        }

        public Criteria andRfcLikeInsensitive(String value) {
            addCriterion("upper(rfc) like", value.toUpperCase(), "rfc");
            return (Criteria) this;
        }

        public Criteria andFolioUuidLikeInsensitive(String value) {
            addCriterion("upper(folio_uuid) like", value.toUpperCase(), "folioUuid");
            return (Criteria) this;
        }

        public Criteria andFechaActualLikeInsensitive(String value) {
            addCriterion("upper(fecha_actual) like", value.toUpperCase(), "fechaActual");
            return (Criteria) this;
        }

        public Criteria andDireccionIpLikeInsensitive(String value) {
            addCriterion("upper(direccion_ip) like", value.toUpperCase(), "direccionIp");
            return (Criteria) this;
        }

        public Criteria andProgramaIdLikeInsensitive(String value) {
            addCriterion("upper(programa_id) like", value.toUpperCase(), "programaId");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLikeInsensitive(String value) {
            addCriterion("upper(currency_code) like", value.toUpperCase(), "currencyCode");
            return (Criteria) this;
        }

        public Criteria andJournalCategoryLikeInsensitive(String value) {
            addCriterion("upper(journal_category) like", value.toUpperCase(), "journalCategory");
            return (Criteria) this;
        }

        public Criteria andJournalSourceLikeInsensitive(String value) {
            addCriterion("upper(journal_source) like", value.toUpperCase(), "journalSource");
            return (Criteria) this;
        }

        public Criteria andSegment1LikeInsensitive(String value) {
            addCriterion("upper(segment1) like", value.toUpperCase(), "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment2LikeInsensitive(String value) {
            addCriterion("upper(segment2) like", value.toUpperCase(), "segment2");
            return (Criteria) this;
        }

        public Criteria andSegment3LikeInsensitive(String value) {
            addCriterion("upper(segment3) like", value.toUpperCase(), "segment3");
            return (Criteria) this;
        }

        public Criteria andSegment4LikeInsensitive(String value) {
            addCriterion("upper(segment4) like", value.toUpperCase(), "segment4");
            return (Criteria) this;
        }

        public Criteria andSegment5LikeInsensitive(String value) {
            addCriterion("upper(segment5) like", value.toUpperCase(), "segment5");
            return (Criteria) this;
        }

        public Criteria andSegment6LikeInsensitive(String value) {
            addCriterion("upper(segment6) like", value.toUpperCase(), "segment6");
            return (Criteria) this;
        }

        public Criteria andSegment7LikeInsensitive(String value) {
            addCriterion("upper(segment7) like", value.toUpperCase(), "segment7");
            return (Criteria) this;
        }

        public Criteria andSegment8LikeInsensitive(String value) {
            addCriterion("upper(segment8) like", value.toUpperCase(), "segment8");
            return (Criteria) this;
        }

        public Criteria andSegment9LikeInsensitive(String value) {
            addCriterion("upper(segment9) like", value.toUpperCase(), "segment9");
            return (Criteria) this;
        }

        public Criteria andSegment10LikeInsensitive(String value) {
            addCriterion("upper(segment10) like", value.toUpperCase(), "segment10");
            return (Criteria) this;
        }

        public Criteria andUserCurrencyConversionTypeLikeInsensitive(String value) {
            addCriterion("upper(user_currency_conversion_type) like", value.toUpperCase(), "userCurrencyConversionType");
            return (Criteria) this;
        }

        public Criteria andProcessStatusLikeInsensitive(String value) {
            addCriterion("upper(process_status) like", value.toUpperCase(), "processStatus");
            return (Criteria) this;
        }

        public Criteria andProcessMessageLikeInsensitive(String value) {
            addCriterion("upper(process_message) like", value.toUpperCase(), "processMessage");
            return (Criteria) this;
        }

        public Criteria andJournalNameLikeInsensitive(String value) {
            addCriterion("upper(journal_name) like", value.toUpperCase(), "journalName");
            return (Criteria) this;
        }

        public Criteria andCreatedByLikeInsensitive(String value) {
            addCriterion("upper(created_by) like", value.toUpperCase(), "createdBy");
            return (Criteria) this;
        }

        public Criteria andLastModifiedByLikeInsensitive(String value) {
            addCriterion("upper(last_modified_by) like", value.toUpperCase(), "lastModifiedBy");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}