package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain;

import lombok.Data;

@Data
public class SequenceCache {
    private long current;
    private long max;
    private final int batchSize;
    private final int increment = 1;

    public SequenceCache(int batchSize) {
        this.batchSize = batchSize;
        this.current = 0;
        this.max = 0;
    }

    public synchronized boolean hasAvailable() {
        return current < max;
    }

    public synchronized long next() {
        if (!hasAvailable()) {
            throw new IllegalStateException("缓存耗尽");
        }
        return current++;
    }

    public synchronized void refill(long start, int count) {
        this.current = start;
        this.max = start + count * increment;
    }
}
