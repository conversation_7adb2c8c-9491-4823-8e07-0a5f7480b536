package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.enums.SourceSysEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.CoaRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.ValidationRequestDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.ValidationResultDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.CoaCustomerMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Repository
public class CoaRepositoryImpl implements CoaRepository {
    @Resource
    private CoaCustomerMapper coaCustomerMapper;

    @Override
    public List<ValidationRequestDO> queryCoaList(String processDay) {
        return Optional.ofNullable(coaCustomerMapper.selectCoaList(processDay)).orElse(Collections.emptyList());
    }

    @Override
    public List<Long> queryIdListByCoa(String processDay, String processStatus, ValidationResultDO coa) {
        return coaCustomerMapper.selectIdListByCoa(processDay, processStatus, coa);
    }

    @Override
    public PageInfo<Long> pagingQueryIdListByCoa(String processDay, String processStatus, ValidationResultDO coa, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<Long> list = coaCustomerMapper.selectIdListByCoa(processDay, processStatus, coa);
        return new PageInfo<>(list);
    }

    @Override
    public int updateCoaStatus(String processDay, ValidationResultDO coa, String processStatus, String processMessage) {
        return coaCustomerMapper.updateCoaStatus(processDay, coa, processStatus, processMessage);
    }

    @Override
    public int updateCoaStatusById(String processDay, String systemCode, Long id, String processStatus, String processMessage) {
        return coaCustomerMapper.updateCoaStatusById(processDay, systemCode, id, processStatus, processMessage);
    }

    @Override
    public int updateCoaStatusByIdBatch(String processDay, ValidationResultDO coa, String processStatus, String processMessage) {
        PageInfo<Long> pageInfo;
        int pageNum = 1;
        int pageSize = 1000;
        int totalUpdated = 0;

        do {
            pageInfo = pagingQueryIdListByCoa(processDay, ProcessStatusEnum.VALIDATED.getCode(), coa, pageNum, pageSize);
            List<Long> coaList = pageInfo.getList();
            totalUpdated += coaList.size();
            coaList.forEach(id -> coaCustomerMapper.updateCoaStatusById(processDay, SourceSysEnum.SAFI.getCode(), id, processStatus, processMessage));
            pageNum++;
        } while (pageInfo.isHasNextPage());

        return totalUpdated;
    }

    @Override
    public int updateSafiHeaderStatus(String processDay) {
        return coaCustomerMapper.updateSafiHeaderStatus(processDay);
    }

    @Override
    public int updateShenmaHeaderStatus(String processDay) {
        return coaCustomerMapper.updateShenMaHeaderStatus(processDay);
    }


    @Override
    public PageInfo<ValidationRequestDO> pagingSelectCoaList(String processDay, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<ValidationRequestDO> list = coaCustomerMapper.selectCoaList(processDay);
        return new PageInfo<>(list);
    }

    @Override
    public PageInfo<ValidationRequestDO> pagingSelectShenMaCoaList(String processDay, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<ValidationRequestDO> list = coaCustomerMapper.selectShenMaCoaList(processDay);
        return new PageInfo<>(list);
    }

    @Override
    public int updateCommonCoaStatusByIdBatch(String processDay, ValidationResultDO coa, String processStatus, String processMessage) {
        PageInfo<Long> pageInfo;
        int pageNum = 1;
        int pageSize = 1000;
        int totalUpdated = 0;

        do {
            pageInfo = pagingInterfaceCommonQueryIdListByCoa(processDay, ProcessStatusEnum.VALIDATED.getCode(), coa, pageNum, pageSize);
            List<Long> coaList = pageInfo.getList();
            totalUpdated += coaList.size();
            coaList.forEach(id -> coaCustomerMapper.updateInterfaceCommonCoaStatusById(processDay, SourceSysEnum.SHEN_MA.getCode(), id, processStatus, processMessage));
            pageNum++;
        } while (pageInfo.isHasNextPage());

        return totalUpdated;
    }

    @Override
    public PageInfo<Long> pagingInterfaceCommonQueryIdListByCoa(String processDay, String processStatus, ValidationResultDO coa, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<Long> list = coaCustomerMapper.selectInterfaceCommonIdListByCoa(processDay, processStatus, coa);
        return new PageInfo<>(list);
    }

    @Override
    public int updateShenmaCoaStatusByIdBatch(String processDay, ValidationResultDO coa, String processStatus,String processMessage) {
        PageInfo<Long> pageInfo;
        int pageNum = 1;
        int pageSize = 1000;
        int totalUpdated = 0;

        do {
            pageInfo = pagingShenmaQueryIdListByCoa(processDay, ProcessStatusEnum.VALIDATED.getCode(), coa, pageNum, pageSize);
            List<Long> coaList = pageInfo.getList();
            totalUpdated += coaList.size();
            coaList.forEach(id -> coaCustomerMapper.updateShenmaCoaStatusById(id, processStatus,processMessage));
            pageNum++;
        } while (pageInfo.isHasNextPage());

        return totalUpdated;
    }

    @Override
    public PageInfo<Long> pagingShenmaQueryIdListByCoa(String processDay, String processStatus, ValidationResultDO coa, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<Long> list = coaCustomerMapper.selectShenmaIdListByCoa(processDay, processStatus, coa);
        return new PageInfo<>(list);
    }

}
