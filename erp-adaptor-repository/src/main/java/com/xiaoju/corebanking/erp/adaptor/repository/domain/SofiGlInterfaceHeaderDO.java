package com.xiaoju.corebanking.erp.adaptor.repository.domain;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
@Data
public class SofiGlInterfaceHeaderDO implements Serializable {
    private Long id;

    private String systemCode;

    private String processDay;

    private String batchId;

    private String externalReference;

    private Long journalCount;

    private ProcessStatusEnum processStatus;

    private String processMessage;

    private Long objectVersionNumber;

    private Date creationDate;

    private String createdBy;

    private Date lastModifyDate;

    private String lastModifiedBy;

    private Long groupId;

    private static final long serialVersionUID = 1L;

}