package com.xiaoju.corebanking.erp.adaptor.repository.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class SofiGlAcctHistDO implements Serializable {
    private Long id;

    private String processDay;

    private Long internalKey;

    private String branch;

    private String ccy;

    private String glCode;

    private String clientNo;

    private String profitCenter;

    private String seqNo;

    private Date acctOpenDate;

    private Date openTranDate;

    private Date acctCloseDate;

    private String acctCloseReason;

    private String glAcctNo;

    private String acctStatus;

    private BigDecimal actualBal;

    private BigDecimal drActualBal;

    private BigDecimal crActualBal;

    private Integer ctdDays;

    private Integer mtdDays;

    private Integer ytdDays;

    private BigDecimal mtdBal;

    private BigDecimal ytdBal;

    private BigDecimal aggBalCtd;

    private BigDecimal aggBalMtd;

    private BigDecimal aggBalYtd;

    private String periodNo;

    private String userId;

    private String manualAccount;

    private String odFacility;

    private Date lastChangeDate;

    private String acctName;

    private String company;

    private Date backupDate;

    private String tranTimestamp;

    private String systemId;

    private String groupClient;

    private BigDecimal drTranAmt;

    private BigDecimal crTranAmt;

    private BigDecimal lastActualBal;

    private BigDecimal lastDrActualBal;

    private BigDecimal lastCrActualBal;

    private Long objectVersionNumber;

    private Date creationDate;

    private String createdBy;

    private Date lastModifyDate;

    private String lastModifiedBy;

    private static final long serialVersionUID = 1L;
}