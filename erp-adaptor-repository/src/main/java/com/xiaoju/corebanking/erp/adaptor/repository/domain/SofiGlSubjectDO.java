package com.xiaoju.corebanking.erp.adaptor.repository.domain;

import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlSubjectPOKey;
import lombok.Data;

import java.io.Serializable;

@Data
public class SofiGlSubjectDO implements Serializable {
    private String subjectCode;

    private String subjectSetNo;

    private String subjectDesc;

    private String subjectDescEn;

    private String controlSubject;

    private String bsplType;

    private String glType;

    private String subjectType;

    private String balanceWay;

    private String subjectStatus;

    private String subjectLevel;

    private String manualAccount;

    private String specialBookkeeping;

    private String odFacility;

    private String rangeNo;

    private String revalueRateType;

    private String systemId;

    private String measurementAttr;

    private String itemSegregation;

    private String company;

    private String tranTimestamp;

    private String payRec;

    private String subjectRemark;

    private static final long serialVersionUID = 1L;
}