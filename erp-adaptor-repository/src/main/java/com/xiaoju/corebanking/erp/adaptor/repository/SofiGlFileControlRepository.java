package com.xiaoju.corebanking.erp.adaptor.repository;


import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlFileControlDO;

import java.util.List;

public interface SofiGlFileControlRepository {
    void insertSelective(SofiGlFileControlDO sofiGlFileControlDO);
    void updateByExampleSelective(SofiGlFileControlDO sourceDO);
    List<SofiGlFileControlDO> findByIndexFields(String processDay, String fileName, String systemCode);
    int deleteByIndexFields(String processDay, String fileName, String systemCode);
}
