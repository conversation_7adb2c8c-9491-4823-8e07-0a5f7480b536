package com.xiaoju.corebanking.erp.adaptor.repository.converter;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.utils.CommonUtils;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaHisDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfacePO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlShenmaHisPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlShenmaPO;
import org.springframework.stereotype.Component;

@Component
public class SofiGlShenmaHisModelConverter {

    public SofiGlShenmaHisDO convert(SofiGlShenmaHisPO sofiGlShenmaHisPO) {
        SofiGlShenmaHisDO sofiGlShenmaHisDO = new SofiGlShenmaHisDO();
        sofiGlShenmaHisDO.setId(sofiGlShenmaHisPO.getId());
        sofiGlShenmaHisDO.setProcessDay(sofiGlShenmaHisPO.getProcessDay());
        sofiGlShenmaHisDO.setFileName(sofiGlShenmaHisPO.getFileName());
        sofiGlShenmaHisDO.setLinkReference(sofiGlShenmaHisPO.getLinkReference());
        sofiGlShenmaHisDO.setSourceBranch(sofiGlShenmaHisPO.getSourceBranch());
        sofiGlShenmaHisDO.setCcy(sofiGlShenmaHisPO.getCcy());
        sofiGlShenmaHisDO.setGlCode(sofiGlShenmaHisPO.getGlCode());
        sofiGlShenmaHisDO.setEnteredDebitAmount(sofiGlShenmaHisPO.getEnteredDebitAmount());
        sofiGlShenmaHisDO.setEnteredCreditAmount(sofiGlShenmaHisPO.getEnteredCreditAmount());
        sofiGlShenmaHisDO.setProfitCenter(sofiGlShenmaHisPO.getProfitCenter());
        sofiGlShenmaHisDO.setSourceModule(sofiGlShenmaHisPO.getSourceModule());
        sofiGlShenmaHisDO.setClientType(sofiGlShenmaHisPO.getClientType());
        sofiGlShenmaHisDO.setAmtType(sofiGlShenmaHisPO.getAmtType());
        sofiGlShenmaHisDO.setTranType(sofiGlShenmaHisPO.getTranType());
        sofiGlShenmaHisDO.setEventType(sofiGlShenmaHisPO.getEventType());
        sofiGlShenmaHisDO.setProdType(sofiGlShenmaHisPO.getProdType());
        sofiGlShenmaHisDO.setPostDate(sofiGlShenmaHisPO.getPostDate());
        sofiGlShenmaHisDO.setValueDate(sofiGlShenmaHisPO.getValueDate());
        sofiGlShenmaHisDO.setNarrative(sofiGlShenmaHisPO.getNarrative());
        sofiGlShenmaHisDO.setChannelSeqNo(sofiGlShenmaHisPO.getChannelSeqNo());
        sofiGlShenmaHisDO.setIntercompany(sofiGlShenmaHisPO.getIntercompany());
        sofiGlShenmaHisDO.setFlatRate(sofiGlShenmaHisPO.getFlatRate());
        sofiGlShenmaHisDO.setCustRate(sofiGlShenmaHisPO.getCustRate());
        sofiGlShenmaHisDO.setInlandOffshore(sofiGlShenmaHisPO.getInlandOffshore());
        sofiGlShenmaHisDO.setClientNo(sofiGlShenmaHisPO.getClientNo());
        sofiGlShenmaHisDO.setSeqNo(sofiGlShenmaHisPO.getSeqNo());
        sofiGlShenmaHisDO.setSystemId(sofiGlShenmaHisPO.getSystemId());
        sofiGlShenmaHisDO.setCompany(sofiGlShenmaHisPO.getCompany());
        sofiGlShenmaHisDO.setGroupClient(sofiGlShenmaHisPO.getGroupClient());
        sofiGlShenmaHisDO.setVoucherGroup(sofiGlShenmaHisPO.getVoucherGroup());
        sofiGlShenmaHisDO.setGroupId(sofiGlShenmaHisPO.getGroupId());
        sofiGlShenmaHisDO.setProcessStatus(ProcessStatusEnum.getByCode(sofiGlShenmaHisPO.getProcessStatus()));
        sofiGlShenmaHisDO.setProcessMessage(sofiGlShenmaHisPO.getProcessMessage());
        sofiGlShenmaHisDO.setObjectVersionNumber(sofiGlShenmaHisPO.getObjectVersionNumber());
        sofiGlShenmaHisDO.setCreationDate(sofiGlShenmaHisPO.getCreationDate());
        sofiGlShenmaHisDO.setCreatedBy(sofiGlShenmaHisPO.getCreatedBy());
        sofiGlShenmaHisDO.setLastModifyDate(sofiGlShenmaHisPO.getLastModifyDate());
        sofiGlShenmaHisDO.setLastModifiedBy(sofiGlShenmaHisPO.getLastModifiedBy());
        return sofiGlShenmaHisDO;
    }

    public SofiGlShenmaHisPO convert(SofiGlShenmaHisDO sofiGlShenmaHisDO) {
        SofiGlShenmaHisPO sofiGlShenmaHisPO = new SofiGlShenmaHisPO();
        sofiGlShenmaHisPO.setProcessDay(sofiGlShenmaHisDO.getProcessDay());
        sofiGlShenmaHisPO.setFileName(sofiGlShenmaHisDO.getFileName());
        sofiGlShenmaHisPO.setLinkReference(sofiGlShenmaHisDO.getLinkReference());
        sofiGlShenmaHisPO.setSourceBranch(sofiGlShenmaHisDO.getSourceBranch());
        sofiGlShenmaHisPO.setCcy(sofiGlShenmaHisDO.getCcy());
        sofiGlShenmaHisPO.setGlCode(sofiGlShenmaHisDO.getGlCode());
        sofiGlShenmaHisPO.setEnteredDebitAmount(sofiGlShenmaHisDO.getEnteredDebitAmount());
        sofiGlShenmaHisPO.setEnteredCreditAmount(sofiGlShenmaHisDO.getEnteredCreditAmount());
        sofiGlShenmaHisPO.setProfitCenter(sofiGlShenmaHisDO.getProfitCenter());
        sofiGlShenmaHisPO.setSourceModule(sofiGlShenmaHisDO.getSourceModule());
        sofiGlShenmaHisPO.setClientType(sofiGlShenmaHisDO.getClientType());
        sofiGlShenmaHisPO.setAmtType(sofiGlShenmaHisDO.getAmtType());
        sofiGlShenmaHisPO.setTranType(sofiGlShenmaHisDO.getTranType());
        sofiGlShenmaHisPO.setEventType(sofiGlShenmaHisDO.getEventType());
        sofiGlShenmaHisPO.setProdType(sofiGlShenmaHisDO.getProdType());
        sofiGlShenmaHisPO.setPostDate(sofiGlShenmaHisDO.getPostDate());
        sofiGlShenmaHisPO.setValueDate(sofiGlShenmaHisDO.getValueDate());
        sofiGlShenmaHisPO.setNarrative(sofiGlShenmaHisDO.getNarrative());
        sofiGlShenmaHisPO.setChannelSeqNo(sofiGlShenmaHisDO.getChannelSeqNo());
        sofiGlShenmaHisPO.setIntercompany(sofiGlShenmaHisDO.getIntercompany());
        sofiGlShenmaHisPO.setFlatRate(sofiGlShenmaHisDO.getFlatRate());
        sofiGlShenmaHisPO.setCustRate(sofiGlShenmaHisDO.getCustRate());
        sofiGlShenmaHisPO.setInlandOffshore(sofiGlShenmaHisDO.getInlandOffshore());
        sofiGlShenmaHisPO.setClientNo(sofiGlShenmaHisDO.getClientNo());
        sofiGlShenmaHisPO.setSeqNo(sofiGlShenmaHisDO.getSeqNo());
        sofiGlShenmaHisPO.setSystemId(sofiGlShenmaHisDO.getSystemId());
        sofiGlShenmaHisPO.setCompany(sofiGlShenmaHisDO.getCompany());
        sofiGlShenmaHisPO.setGroupClient(sofiGlShenmaHisDO.getGroupClient());
        sofiGlShenmaHisPO.setVoucherGroup(sofiGlShenmaHisDO.getVoucherGroup());
        sofiGlShenmaHisPO.setGroupId(sofiGlShenmaHisDO.getGroupId());
        sofiGlShenmaHisPO.setProcessStatus(sofiGlShenmaHisDO.getProcessStatus().getCode());
        sofiGlShenmaHisPO.setProcessMessage(sofiGlShenmaHisDO.getProcessMessage());
        sofiGlShenmaHisPO.setObjectVersionNumber(sofiGlShenmaHisDO.getObjectVersionNumber());
        sofiGlShenmaHisPO.setCreationDate(sofiGlShenmaHisDO.getCreationDate());
        sofiGlShenmaHisPO.setCreatedBy(sofiGlShenmaHisDO.getCreatedBy());
        sofiGlShenmaHisPO.setLastModifyDate(sofiGlShenmaHisDO.getLastModifyDate());
        sofiGlShenmaHisPO.setLastModifiedBy(sofiGlShenmaHisDO.getLastModifiedBy());
        return sofiGlShenmaHisPO;
    }
}
