package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderQueryDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHeaderPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfaceHeaderPOMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SofiGlInterfaceHeaderCustomerMapper extends SofiGlInterfaceHeaderPOMapper {
    List<SofiGlInterfaceHeaderPO> selectInterfaceHeaderList(SofiGlInterfaceHeaderQueryDO sofiGlInterfaceHeaderQueryDO);

    int batchInsert(@Param("list") List<SofiGlInterfaceHeaderPO> list);
    int updatePolizaIdSelective(@Param("systemCode") String systemCode, @Param("processDay") String processDay,@Param("externalReference") String externalReference,@Param("groupId") Long groupId, @Param("sofiGlInterfaceHeaderPO") SofiGlInterfaceHeaderPO sofiGlInterfaceHeaderPO);


    List<SofiGlInterfaceHeaderPO> findByPolizaIds(List<String> externalReferences);

    int batchDeleteByPolizaIds(List<String> externalReferences);

    List<Long> selectGroupId(@Param("processDay") String processDay, @Param("systemCode") String systemCode);

    int updateInterfaceHeaderByGroupId(@Param("systemCode") String systemCode,@Param("processDay") String processDay,@Param("groupId") Long groupId, @Param("newProcessStatus") String processStatus,@Param("oldProcessStatus") String oldProcessStatus);
}