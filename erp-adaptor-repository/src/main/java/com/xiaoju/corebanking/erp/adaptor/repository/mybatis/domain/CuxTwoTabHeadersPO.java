package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain;

import java.io.Serializable;
import java.util.Date;

public class CuxTwoTabHeadersPO implements Serializable {
    private Long id;

    private String formCode;

    private String formName;

    private String enableFlag;

    private String lockedFlag;

    private String uniqueFields;

    private Date dateFrom;

    private Date dateTo;

    private Date lastUpdateDate;

    private String lastUpdatedBy;

    private Integer lastUpdateLogin;

    private String createdBy;

    private Date creationDate;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFormCode() {
        return formCode;
    }

    public void setFormCode(String formCode) {
        this.formCode = formCode == null ? null : formCode.trim();
    }

    public String getFormName() {
        return formName;
    }

    public void setFormName(String formName) {
        this.formName = formName == null ? null : formName.trim();
    }

    public String getEnableFlag() {
        return enableFlag;
    }

    public void setEnableFlag(String enableFlag) {
        this.enableFlag = enableFlag == null ? null : enableFlag.trim();
    }

    public String getLockedFlag() {
        return lockedFlag;
    }

    public void setLockedFlag(String lockedFlag) {
        this.lockedFlag = lockedFlag == null ? null : lockedFlag.trim();
    }

    public String getUniqueFields() {
        return uniqueFields;
    }

    public void setUniqueFields(String uniqueFields) {
        this.uniqueFields = uniqueFields == null ? null : uniqueFields.trim();
    }

    public Date getDateFrom() {
        return dateFrom;
    }

    public void setDateFrom(Date dateFrom) {
        this.dateFrom = dateFrom;
    }

    public Date getDateTo() {
        return dateTo;
    }

    public void setDateTo(Date dateTo) {
        this.dateTo = dateTo;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Integer getLastUpdateLogin() {
        return lastUpdateLogin;
    }

    public void setLastUpdateLogin(Integer lastUpdateLogin) {
        this.lastUpdateLogin = lastUpdateLogin;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        CuxTwoTabHeadersPO other = (CuxTwoTabHeadersPO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getFormCode() == null ? other.getFormCode() == null : this.getFormCode().equals(other.getFormCode()))
            && (this.getFormName() == null ? other.getFormName() == null : this.getFormName().equals(other.getFormName()))
            && (this.getEnableFlag() == null ? other.getEnableFlag() == null : this.getEnableFlag().equals(other.getEnableFlag()))
            && (this.getLockedFlag() == null ? other.getLockedFlag() == null : this.getLockedFlag().equals(other.getLockedFlag()))
            && (this.getUniqueFields() == null ? other.getUniqueFields() == null : this.getUniqueFields().equals(other.getUniqueFields()))
            && (this.getDateFrom() == null ? other.getDateFrom() == null : this.getDateFrom().equals(other.getDateFrom()))
            && (this.getDateTo() == null ? other.getDateTo() == null : this.getDateTo().equals(other.getDateTo()))
            && (this.getLastUpdateDate() == null ? other.getLastUpdateDate() == null : this.getLastUpdateDate().equals(other.getLastUpdateDate()))
            && (this.getLastUpdatedBy() == null ? other.getLastUpdatedBy() == null : this.getLastUpdatedBy().equals(other.getLastUpdatedBy()))
            && (this.getLastUpdateLogin() == null ? other.getLastUpdateLogin() == null : this.getLastUpdateLogin().equals(other.getLastUpdateLogin()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreationDate() == null ? other.getCreationDate() == null : this.getCreationDate().equals(other.getCreationDate()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getFormCode() == null) ? 0 : getFormCode().hashCode());
        result = prime * result + ((getFormName() == null) ? 0 : getFormName().hashCode());
        result = prime * result + ((getEnableFlag() == null) ? 0 : getEnableFlag().hashCode());
        result = prime * result + ((getLockedFlag() == null) ? 0 : getLockedFlag().hashCode());
        result = prime * result + ((getUniqueFields() == null) ? 0 : getUniqueFields().hashCode());
        result = prime * result + ((getDateFrom() == null) ? 0 : getDateFrom().hashCode());
        result = prime * result + ((getDateTo() == null) ? 0 : getDateTo().hashCode());
        result = prime * result + ((getLastUpdateDate() == null) ? 0 : getLastUpdateDate().hashCode());
        result = prime * result + ((getLastUpdatedBy() == null) ? 0 : getLastUpdatedBy().hashCode());
        result = prime * result + ((getLastUpdateLogin() == null) ? 0 : getLastUpdateLogin().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreationDate() == null) ? 0 : getCreationDate().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", formCode=").append(formCode);
        sb.append(", formName=").append(formName);
        sb.append(", enableFlag=").append(enableFlag);
        sb.append(", lockedFlag=").append(lockedFlag);
        sb.append(", uniqueFields=").append(uniqueFields);
        sb.append(", dateFrom=").append(dateFrom);
        sb.append(", dateTo=").append(dateTo);
        sb.append(", lastUpdateDate=").append(lastUpdateDate);
        sb.append(", lastUpdatedBy=").append(lastUpdatedBy);
        sb.append(", lastUpdateLogin=").append(lastUpdateLogin);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", creationDate=").append(creationDate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}