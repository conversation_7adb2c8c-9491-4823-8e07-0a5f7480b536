package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.xiaoju.corebanking.erp.adaptor.common.exception.ErpAdaptorBusinessException;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceHisRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.converter.SofiGlInterfaceModelConverter;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHisPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfacePO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfaceHisPOMapper;
import com.xiaoju.digitalbank.errorno.CommonErrorNo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Repository
@Slf4j
public class SofiGlInterfaceHisRepositoryImpl implements SofiGlInterfaceHisRepository {

    @Resource
    private SofiGlInterfaceHisPOMapper sofiGlInterfaceHisPOMapper;

    @Resource
    private SofiGlInterfaceModelConverter sofiGlInterfaceModelConverter;

    private SofiGlInterfaceHisPO convertToHisPO(SofiGlInterfacePO sofiGlInterfacePO) {
        if (sofiGlInterfacePO == null) {
            return null;
        }

        SofiGlInterfaceHisPO hisPO = new SofiGlInterfaceHisPO();

        BeanUtils.copyProperties(sofiGlInterfaceModelConverter.convert(sofiGlInterfacePO), hisPO);

        if (sofiGlInterfacePO.getProcessStatus() != null) {
            hisPO.setProcessStatus(sofiGlInterfacePO.getProcessStatus());
        }

        return hisPO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int backupInterfaceHis(List<SofiGlInterfacePO> sofiGlInterfaceDOList) {
        if (sofiGlInterfaceDOList == null || sofiGlInterfaceDOList.isEmpty()) {
            log.error("尝试备份null或空对象到历史表");
            return 0;
        }

        int insertCount = 0;
        for (SofiGlInterfacePO sofiGlInterfacePO : sofiGlInterfaceDOList) {
            SofiGlInterfaceHisPO hisPO = convertToHisPO(sofiGlInterfacePO);
            log.info("备份接口数据到历史表: polizaId={}", sofiGlInterfacePO.getPolizaId());

            try {
                insertCount += sofiGlInterfaceHisPOMapper.insertSelective(hisPO);
            } catch (Exception e) {
                log.error("备份接口数据到历史表失败: {}", e.getMessage(), e);
                throw new ErpAdaptorBusinessException(CommonErrorNo.DB_SAVE_ERROR, "备份数据到历史表失败");
            }
        }
        return insertCount;
    }
} 