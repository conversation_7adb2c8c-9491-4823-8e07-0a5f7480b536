package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.utils.LogUtils;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlShenmaRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.converter.SofiGlShenmaModelConverter;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.CargosAndAbonosDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SummaryResultDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlShenmaPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SofiGlShenmaCustomerMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 类描述
 * @author: zhangcc
 * @date: 2025/7/1$
 **/
@Slf4j
@Repository
public class SofiGlShenmaRepositoryImpl implements SofiGlShenmaRepository {
    @Autowired
    private SofiGlShenmaCustomerMapper sofiGlShenmaCustomerMapper;
    @Autowired
    private SofiGlShenmaModelConverter sofiGlShenmaModelConverter;

    @Override
    public List<SofiGlShenmaDO> selectSofiGlShenmaByReference(String reference) {
        return sofiGlShenmaCustomerMapper.queryByReference(reference).stream().map(sofiGlShenmaModelConverter::convert).collect(Collectors.toList());
    }

    @Override
    public void updateShenmaInterfaceByReference(SofiGlShenmaDO sofiGlShenmaDO, SofiGlShenmaDO update) {
        Assert.notNull(sofiGlShenmaDO.getLinkReference(), "reference can not be null!");
        update.setObjectVersionNumber(sofiGlShenmaDO.getObjectVersionNumber() + 1);
        log.info("updateShenmaInterfaceByReference sofiGlShenmaDO={},update={}", sofiGlShenmaDO, update);
        SofiGlShenmaPO sofiGlShenmaPO = sofiGlShenmaModelConverter.convert(update);
        log.info("updateShenmaInterfaceByReference sofiGlShenmaPO={}", LogUtils.toString(sofiGlShenmaPO));
        int effected = sofiGlShenmaCustomerMapper.updateReferenceSelective(sofiGlShenmaDO.getProcessDay(),sofiGlShenmaDO.getVoucherGroup(),sofiGlShenmaDO.getLinkReference(),sofiGlShenmaPO);
        log.info("updateShenmaInterfaceByReference effected={},sofiGlInterfaceDO={},update={}", effected, sofiGlShenmaDO, update);
        // 数据回填
        SofiGlShenmaDO backup = new SofiGlShenmaDO();
        sofiGlShenmaModelConverter.copyIgnoreNullValue(sofiGlShenmaDO, backup);
        sofiGlShenmaModelConverter.copyIgnoreNullValue(update, sofiGlShenmaDO);
    }

    @Override
    public void batchInsert(List<SofiGlShenmaDO> shenmaList) {
        if (shenmaList == null || shenmaList.isEmpty()) {
            return;
        }

        List<SofiGlShenmaPO> poList = shenmaList.stream()
                .map(sofiGlShenmaModelConverter::convert)
                .collect(Collectors.toList());

        sofiGlShenmaCustomerMapper.batchInsert(poList);
        log.info("批量插入Shenma数据完成，共{}条记录", poList.size());
    }

    @Override
    public List<SofiGlShenmaDO> selectPendingShenmaRecords(ProcessStatusEnum processStatus, String processDay) {
        String status = processStatus != null ? processStatus.getCode() : null;
        log.info("查询待处理的Shenma记录，状态: {}, 处理日期: {}", status, processDay);

        List<SofiGlShenmaPO> poList = sofiGlShenmaCustomerMapper.queryPendingShenmaRecords(status, processDay);

        List<SofiGlShenmaDO> result = poList.stream()
                .map(sofiGlShenmaModelConverter::convert)
                .collect(Collectors.toList());

        log.info("查询到{}条待处理的Shenma记录", result.size());
        return result;
    }
    @Override
    public CargosAndAbonosDO selectCargosAndAbonosDO(String reference) {
        return sofiGlShenmaCustomerMapper.selectCargosAndAbonosDO(reference);
    }

    @Override
    public List<SofiGlShenmaDO> querySofiGlShenmaByGroupIdAndProcessDay(Long groupId, String processDay) {
        List<SofiGlShenmaPO> sofiGlShenmaPOS = sofiGlShenmaCustomerMapper.querySofiGlShenmaByGroupIdAndProcessDay(groupId, processDay);
        if(CollectionUtils.isEmpty(sofiGlShenmaPOS)) {
            return Collections.emptyList();
        }
        return sofiGlShenmaPOS.stream()
                .map(sofiGlShenmaModelConverter::convert)
                .collect(Collectors.toList());

    }

    @Override
    public void updateByProcessDayAndGroupIdAndStatus(String processDay, Long groupId, String processStatus, SofiGlShenmaDO update) {
        SofiGlShenmaPO sofiGlShenmaPO = sofiGlShenmaModelConverter.convert(update);
        log.info("updateShenmaInterfaceByReference sofiGlShenmaPO={}", LogUtils.toString(sofiGlShenmaPO));
        int effected = sofiGlShenmaCustomerMapper.updateByProcessDayAndGroupIdAndStatus(processDay,groupId,processStatus,sofiGlShenmaPO);
        log.info("updateShenMaByTradeNo effected={},update={}", effected, update);

    }

    @Override
    public List<SummaryResultDO> selectShenMaSummary(String processDay) {
        return sofiGlShenmaCustomerMapper.selectShenMaSummary(processDay);
    }

    @Override
    public List<SummaryResultDO> selectShenMaSumByVoucherGroup(String processDay) {
        return sofiGlShenmaCustomerMapper.selectShenMaSumByVoucherGroup(processDay);
    }

    @Override
    public List<SofiGlShenmaDO> queryByPolizaIdsAndProcessDay(List<String> externalReferenceList, String processDay) {
        if (CollectionUtils.isEmpty(externalReferenceList)) {
            log.warn("查询SHENMA数据时外部引用列表为空");
            return Collections.emptyList();
        }

        log.info("根据外部引用列表查询SHENMA数据，externalReferenceList={}, processDay={}", externalReferenceList, processDay);

        List<SofiGlShenmaPO> sofiGlShenmaPOS = sofiGlShenmaCustomerMapper.queryByPolizaIdsAndProcessDay(externalReferenceList, processDay);
        if (CollectionUtils.isEmpty(sofiGlShenmaPOS)) {
            log.info("没有找到匹配的SHENMA数据");
            return Collections.emptyList();
        }

        List<SofiGlShenmaDO> result = sofiGlShenmaPOS.stream()
                .map(sofiGlShenmaModelConverter::convert)
                .collect(Collectors.toList());

        log.info("查询到{}条SHENMA数据", result.size());
        return result;
    }

    @Override
    public List<SofiGlShenmaDO> findByIndexFields(String processDay, String linkReference) {
        log.info("根据索引字段查询现有数据: processDay={},  linkReference={}", processDay, linkReference);
        
        List<SofiGlShenmaPO> sofiGlShenmaPOS = sofiGlShenmaCustomerMapper.findByIndexFields(processDay, linkReference);
        if (CollectionUtils.isEmpty(sofiGlShenmaPOS)) {
            log.info("没有找到匹配的现有数据");
            return Collections.emptyList();
        }

        List<SofiGlShenmaDO> result = sofiGlShenmaPOS.stream()
                .map(sofiGlShenmaModelConverter::convert)
                .collect(Collectors.toList());

        log.info("查询到{}条现有数据", result.size());
        return result;
    }

    @Override
    public int deleteByIndexFields(String processDay,  String linkReference) {
        log.info("根据索引字段删除数据: processDay={},  linkReference={}", processDay, linkReference);
        int deleted = sofiGlShenmaCustomerMapper.deleteByIndexFields(processDay,linkReference);
        log.info("删除了{}条数据", deleted);
        return deleted;
    }
}
