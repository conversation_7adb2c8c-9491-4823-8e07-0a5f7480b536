package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated;

import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlShenmaPO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface SofiGlShenmaPOMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SofiGlShenmaPO record);

    int insertSelective(SofiGlShenmaPO record);

    SofiGlShenmaPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SofiGlShenmaPO record);

    int updateByPrimaryKey(SofiGlShenmaPO record);
}