package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated;

import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHisPOExample;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHisPO;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface SofiGlInterfaceHisPOMapper {
    int deleteByExample(SofiGlInterfaceHisPOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SofiGlInterfaceHisPO record);

    int insertSelective(SofiGlInterfaceHisPO record);

    List<SofiGlInterfaceHisPO> selectByExampleWithRowbounds(SofiGlInterfaceHisPOExample example, RowBounds rowBounds);

    List<SofiGlInterfaceHisPO> selectByExample(SofiGlInterfaceHisPOExample example);

    SofiGlInterfaceHisPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SofiGlInterfaceHisPO record, @Param("example") SofiGlInterfaceHisPOExample example);

    int updateByExample(@Param("record") SofiGlInterfaceHisPO record, @Param("example") SofiGlInterfaceHisPOExample example);

    int updateByPrimaryKeySelective(SofiGlInterfaceHisPO record);

    int updateByPrimaryKey(SofiGlInterfaceHisPO record);

    void batchInsert(List<SofiGlInterfaceHisPO> records);
}