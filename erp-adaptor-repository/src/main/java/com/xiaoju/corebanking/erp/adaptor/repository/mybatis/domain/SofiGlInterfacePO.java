package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
@Data
public class SofiGlInterfacePO implements Serializable {
    private Long id;

    private Long fileId;

    private String processDay;

    private String fileName;

    private String batchId;

    private Long detallePolizaId;

    private String sourceSys;

    private Long empresaId;

    private Long polizaId;

    private String fecha;

    private Long centroCostoId;

    private String cuentaCompleta;

    private Long instrumento;

    private Long monedaId;

    private BigDecimal cargos;

    private BigDecimal abonos;

    private String descripcion;

    private String referencia;

    private String procedimientoCont;

    private String tipoInstrumentoId;

    private String rfc;

    private BigDecimal totalFactura;

    private String folioUuid;

    private Long usuario;

    private String fechaActual;

    private String direccionIp;

    private String programaId;

    private Long sucursal;

    private Long numTransaccion;

    private Long ledgerId;

    private String ledgerName;

    private String currencyCode;

    private String journalCategory;

    private String journalSource;

    private String segment1;

    private String segment2;

    private String segment3;

    private String segment4;

    private String segment5;

    private String segment6;

    private String segment7;

    private String segment8;

    private String segment9;

    private String segment10;

    private Long groupId;

    private Date currencyConversionDate;

    private BigDecimal currencyConversionRate;

    private String userCurrencyConversionType;

    private String processStatus;

    private String processMessage;

    private Long jeHeaderId;

    private String journalName;

    private Long jeLineNum;

    private Long documentId;

    private Long loadRequestId;

    private Long importRequestId;

    private Long objectVersionNumber;

    private Date creationDate;

    private String createdBy;

    private Date lastModifyDate;

    private String lastModifiedBy;

    private String journalSourceName;
    private static final long serialVersionUID = 1L;

}