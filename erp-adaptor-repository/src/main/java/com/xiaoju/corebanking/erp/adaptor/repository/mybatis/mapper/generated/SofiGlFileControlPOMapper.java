package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated;

import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlFileControlPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlFileControlPOExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

@Mapper
public interface SofiGlFileControlPOMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SofiGlFileControlPO record);

    int insertSelective(SofiGlFileControlPO record);

    List<SofiGlFileControlPO> selectByExampleWithRowbounds(SofiGlFileControlPOExample example, RowBounds rowBounds);

    List<SofiGlFileControlPO> selectByExample(SofiGlFileControlPOExample example);

    SofiGlFileControlPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SofiGlFileControlPO record, @Param("example") SofiGlFileControlPOExample example);

    int updateByExample(@Param("record") SofiGlFileControlPO record, @Param("example") SofiGlFileControlPOExample example);

    int updateByPrimaryKeySelective(SofiGlFileControlPO record);

    int updateByPrimaryKey(SofiGlFileControlPO record);

    int deleteByControl(@Param("endId") Long endId);
}