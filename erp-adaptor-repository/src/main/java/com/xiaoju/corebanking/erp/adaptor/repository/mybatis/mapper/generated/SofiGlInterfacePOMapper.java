package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated;

import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfacePO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfacePOExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

@Mapper
public interface SofiGlInterfacePOMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SofiGlInterfacePO record);

    int insertSelective(SofiGlInterfacePO record);

    List<SofiGlInterfacePO> selectByExampleWithRowbounds(SofiGlInterfacePOExample example, RowBounds rowBounds);

    List<SofiGlInterfacePO> selectByExample(SofiGlInterfacePOExample example);

    SofiGlInterfacePO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SofiGlInterfacePO record, @Param("example") SofiGlInterfacePOExample example);

    int updateByExample(@Param("record") SofiGlInterfacePO record, @Param("example") SofiGlInterfacePOExample example);

    int updateByPrimaryKeySelective(SofiGlInterfacePO record);

    int updateByPrimaryKey(SofiGlInterfacePO record);

    int deleteByIdRange(@Param("endId") Long endId);
}