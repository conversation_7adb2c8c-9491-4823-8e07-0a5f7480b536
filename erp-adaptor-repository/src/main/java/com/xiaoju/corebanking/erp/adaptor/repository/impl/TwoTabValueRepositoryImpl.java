package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabHeadersPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValueExtraPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePOExample;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.CuxTwoTabValuesExtraMapper;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.CuxTwoTabValuePOMapper;
import com.xiaoju.corebanking.erp.adaptor.repository.TwoTabHeaderRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.TwoTabValueRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.CuxTwoTabValueDO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class TwoTabValueRepositoryImpl implements TwoTabValueRepository {

    @Resource
    CuxTwoTabValuePOMapper cuxTwoTabValuePOMapper;

    @Resource
    CuxTwoTabValuesExtraMapper cuxTwoTabValuesExtraMapper;

    @Resource
    TwoTabHeaderRepository twoTabHeaderRepository;

    @Override
    public CuxTwoTabValuePO insertValue(CuxTwoTabValueDO value) {
        CuxTwoTabValuePO valuePO = new CuxTwoTabValuePO();

        CuxTwoTabHeadersPO headersPo = twoTabHeaderRepository.selectByFormCode(value.getFormCode());
        if(Objects.isNull(headersPo)){
            throw new RuntimeException("没找到二维表" + value.getFormCode());
        }
        Long headerId = headersPo.getId();

        BeanUtils.copyProperties(value, valuePO);
        valuePO.setEnabledFlag("Y");
        valuePO.setHeaderId(headerId);
        if (Objects.isNull(valuePO.getDateFrom())) {
            valuePO.setDateFrom(new Date());
        }
        if (Objects.isNull(valuePO.getDateTo())) {
            valuePO.setDateTo(new GregorianCalendar(9999, Calendar.DECEMBER, 31).getTime());
        }

        if (Objects.isNull(value.getValue1())) {
            valuePO.setValue1("");
        }
        if (Objects.isNull(value.getValue2())) {
            valuePO.setValue2("");
        }
        if (Objects.isNull(value.getValue3())) {
            valuePO.setValue3("");
        }
        if (Objects.isNull(value.getValue4())) {
            valuePO.setValue4("");
        }
        if (Objects.isNull(value.getValue5())) {
            valuePO.setValue5("");
        }
        if (Objects.isNull(value.getValue6())) {
            valuePO.setValue6("");
        }
        if (Objects.isNull(value.getValue7())) {
            valuePO.setValue7("");
        }
        if (Objects.isNull(value.getValue8())) {
            valuePO.setValue8("");
        }
        if (Objects.isNull(value.getValue9())) {
            valuePO.setValue9("");
        }
        if (Objects.isNull(value.getValue10())) {
            valuePO.setValue10("");
        }
        if (Objects.isNull(value.getValue11())) {
            valuePO.setValue11("");
        }
        if (Objects.isNull(value.getValue12())) {
            valuePO.setValue12("");
        }
        if (Objects.isNull(value.getValue13())) {
            valuePO.setValue13("");
        }
        if (Objects.isNull(value.getValue14())) {
            valuePO.setValue14("");
        }
        if (Objects.isNull(value.getValue15())) {
            valuePO.setValue15("");
        }
        if (Objects.isNull(value.getValue16())) {
            valuePO.setValue16("");
        }
        if (Objects.isNull(value.getValue17())) {
            valuePO.setValue17("");
        }
        if (Objects.isNull(value.getValue18())) {
            valuePO.setValue18("");
        }
        if (Objects.isNull(value.getValue19())) {
            valuePO.setValue19("");
        }
        if (Objects.isNull(value.getValue20())) {
            valuePO.setValue20("");
        }
        if (Objects.isNull(value.getValue21())) {
            valuePO.setValue21("");
        }
        if (Objects.isNull(value.getValue22())) {
            valuePO.setValue22("");
        }
        if (Objects.isNull(value.getValue23())) {
            valuePO.setValue23("");
        }
        if (Objects.isNull(value.getValue24())) {
            valuePO.setValue24("");
        }
        if (Objects.isNull(value.getValue25())) {
            valuePO.setValue25("");
        }
        if (Objects.isNull(value.getValue26())) {
            valuePO.setValue26("");
        }
        if (Objects.isNull(value.getValue27())) {
            valuePO.setValue27("");
        }
        if (Objects.isNull(value.getValue28())) {
            valuePO.setValue28("");
        }
        if (Objects.isNull(value.getValue29())) {
            valuePO.setValue29("");
        }
        if (Objects.isNull(value.getValue30())) {
            valuePO.setValue30("");
        }
        if (Objects.isNull(value.getValue31())) {
            valuePO.setValue31("");
        }
        if (Objects.isNull(value.getValue32())) {
            valuePO.setValue32("");
        }
        if (Objects.isNull(value.getValue33())) {
            valuePO.setValue33("");
        }
        if (Objects.isNull(value.getValue34())) {
            valuePO.setValue34("");
        }
        if (Objects.isNull(value.getValue35())) {
            valuePO.setValue35("");
        }
        if (Objects.isNull(value.getValue36())) {
            valuePO.setValue36("");
        }
        if (Objects.isNull(value.getValue37())) {
            valuePO.setValue37("");
        }
        if (Objects.isNull(value.getValue38())) {
            valuePO.setValue38("");
        }
        if (Objects.isNull(value.getValue39())) {
            valuePO.setValue39("");
        }
        if (Objects.isNull(value.getValue40())) {
            valuePO.setValue40("");
        }
        if (Objects.isNull(value.getValue41())) {
            valuePO.setValue41("");
        }
        if (Objects.isNull(value.getValue42())) {
            valuePO.setValue42("");
        }
        if (Objects.isNull(value.getValue43())) {
            valuePO.setValue43("");
        }
        if (Objects.isNull(value.getValue44())) {
            valuePO.setValue44("");
        }
        if (Objects.isNull(value.getValue45())) {
            valuePO.setValue45("");
        }
        if (Objects.isNull(value.getValue46())) {
            valuePO.setValue46("");
        }
        if (Objects.isNull(value.getValue47())) {
            valuePO.setValue47("");
        }
        if (Objects.isNull(value.getValue48())) {
            valuePO.setValue48("");
        }
        if (Objects.isNull(value.getValue49())) {
            valuePO.setValue49("");
        }
        if (Objects.isNull(value.getValue50())) {
            valuePO.setValue50("");
        }

        valuePO.setDateTo(new Date());
        valuePO.setLastUpdateLogin(0);
        valuePO.setCreatedBy(CommonConstant.SYSTEM_USER);
        valuePO.setLastUpdatedBy(CommonConstant.SYSTEM_USER);
        valuePO.setCreationDate(new Date());
        valuePO.setLastUpdateDate(new Date());
        cuxTwoTabValuePOMapper.insert(valuePO);
        return valuePO;
    }

    @Override
    public void updateByExample(CuxTwoTabValueDO value) {
//        CuxTwoTabHeadersPO headerPO = this.selectByFormCode(header.getFormCode());
//        BeanUtils.copyProperties(value, headerPO);
//        CuxTwoTabHeadersPOExample example = new CuxTwoTabHeadersPOExample();
//        CuxTwoTabHeadersPOExample.Criteria criteria = example.createCriteria();
//        criteria.andFormCodeEqualTo(header.getFormCode());
//        headerPO.setLastUpdatedBy(CommonConstant.SYSTEM_USER);
//        headerPO.setLastUpdateDate(new Date());
//        cuxTwoTabValuePOMapper.updateByExampleSelective(headerPO, example);

    }

    @Override
    public int insertOrUpdate(List<CuxTwoTabValueDO> list) {
        if (list == null || list.isEmpty()) {
            return 0;
        }

        List<CuxTwoTabValuePO> toInsert = new ArrayList<>();
        List<CuxTwoTabValuePO> toUpdate = new ArrayList<>();
        
        Map<String, CuxTwoTabHeadersPO> headerMap = new HashMap<>();
        
        for (CuxTwoTabValueDO value : list) {
            CuxTwoTabHeadersPO header = headerMap.computeIfAbsent(value.getFormCode(),
                formCode -> twoTabHeaderRepository.selectByFormCode(formCode));
                
            if (header == null) {
                throw new RuntimeException("Header not found for form code: " + value.getFormCode());
            }
            
            CuxTwoTabValuePO valuePO = new CuxTwoTabValuePO();
            BeanUtils.copyProperties(value, valuePO);
            valuePO.setHeaderId(header.getId());
            valuePO.setEnabledFlag("Y");
            valuePO.setLastUpdateLogin(0);
            valuePO.setCreatedBy(CommonConstant.SYSTEM_USER);
            valuePO.setLastUpdatedBy(CommonConstant.SYSTEM_USER);

            String uniqueFieldsStr = header.getUniqueFields();
            
            List<String> uniqueFields = Arrays.asList(uniqueFieldsStr.split(","));
            List<String> values = new ArrayList<>();
            for (String field : uniqueFields) {
                String fieldNum = field.trim();
                String fieldName = fieldNum.startsWith("value") ? fieldNum : "value" + fieldNum;
                try {
                    java.lang.reflect.Method getter = CuxTwoTabValuePO.class.getMethod("get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1));
                    String fieldValue = (String) getter.invoke(valuePO);
                    values.add(fieldValue);
                } catch (Exception e) {
                    throw new RuntimeException("Error accessing field: " + fieldName + " for unique field: " + field, e);
                }
            }
            
            List<CuxTwoTabValuePO> existing = cuxTwoTabValuesExtraMapper.findByUniqueFields(
                header.getId(), "US", uniqueFields, values);
                
            if (existing != null && !existing.isEmpty()) {
                CuxTwoTabValuePO existingPO = existing.get(0);
                valuePO.setId(existingPO.getId());
                valuePO.setLastUpdateDate(new Date());
                valuePO.setVersion(existingPO.getVersion() + 1);
                toUpdate.add(valuePO);
            } else {
                valuePO.setVersion(1);
                valuePO.setCreationDate(new Date());
                toInsert.add(valuePO);
            }
        }
        
        int result = 0;
        if (!toInsert.isEmpty()) {
            result += cuxTwoTabValuesExtraMapper.batchInsert(toInsert);
        }
        if (!toUpdate.isEmpty()) {
            //更新一行行更新
            for (CuxTwoTabValuePO toUpdateValue: toUpdate) {
                CuxTwoTabValuePOExample example = new CuxTwoTabValuePOExample();
                CuxTwoTabValuePOExample.Criteria criteria = example.createCriteria();
                criteria.andIdEqualTo(toUpdateValue.getId());
                criteria.andHeaderIdEqualTo(toUpdateValue.getHeaderId());
                cuxTwoTabValuePOMapper.updateByExampleSelective(toUpdateValue, example);
            }
        }
        return result;
    }

    @Override
    public List<CuxTwoTabValuePO> selectByFormCode(String formCode) {
        CuxTwoTabHeadersPO headersPo = twoTabHeaderRepository.selectByFormCode(formCode);
        if(Objects.isNull(headersPo)){
            throw new RuntimeException("没找到二维表" + formCode);
        }

        CuxTwoTabValuePOExample example = new CuxTwoTabValuePOExample();
        CuxTwoTabValuePOExample.Criteria criteria = example.createCriteria();
        criteria.andHeaderIdEqualTo(headersPo.getId());
        criteria.andEnabledFlagEqualTo("Y");
        criteria.andLangEqualTo("US");
        //criteria.andDateFromBetween();
        //criteria.andDateToBetween();
        return cuxTwoTabValuePOMapper.selectByExample(example);
    }

    @Override
    public List<CuxTwoTabValuePO> selectSimpleByFormCode(String formCode) {
        return cuxTwoTabValuesExtraMapper.selectByFormCode(formCode);
    }

    @Override
    public List<CuxTwoTabValueExtraPO> selectAllByFormCode() {
        return cuxTwoTabValuesExtraMapper.selectAllByFormCode();
    }
}
