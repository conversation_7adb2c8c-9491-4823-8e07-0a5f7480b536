package com.xiaoju.corebanking.erp.adaptor.repository.domain;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
@Data
public class SofiGlShenmaDO extends SofiGlInterfaceHeaderDO{
    private Long id;

    private String processDay;

    private String fileName;

    private String externalReference;

    private String linkReference;

    private String tradeNo;

    private String sourceBranch;

    private String ccy;

    private String glCode;

    private String crDrInd;

    private BigDecimal enteredDebitAmount;

    private BigDecimal enteredCreditAmount;

    private String profitCenter;

    private String sourceModule;

    private String clientType;

    private String amtType;

    private String tranType;

    private String eventType;

    private String prodType;

    private String postDate;

    private String valueDate;

    private String narrative;

    private String channelSeqNo;

    private String intercompany;

    private BigDecimal flatRate;

    private BigDecimal custRate;

    private String inlandOffshore;
    private String clientNo;
    private String seqNo;
    private String systemId;
    private String company;
    private String groupClient;

    private Long groupId;

    private ProcessStatusEnum processStatus;

    private String processMessage;

    private Long objectVersionNumber;

    private Date creationDate;

    private String createdBy;

    private Date lastModifyDate;

    private String lastModifiedBy;

    private String voucherGroup;

    private static final long serialVersionUID = 1L;

    public void initializeDefaultValues() {
        this.setProcessDay("");
        this.setFileName("");
        this.setExternalReference("");
        this.setLinkReference("");
        this.setTradeNo("");
        this.setSourceBranch("");
        this.setCcy("");
        this.setGlCode("");
        this.setCrDrInd("");
        this.setEnteredDebitAmount(new BigDecimal("0"));
        this.setEnteredCreditAmount(new BigDecimal("0"));
        this.setProfitCenter("");
        this.setSourceModule("");
        this.setClientType("");
        this.setAmtType("");
        this.setTranType("");
        this.setEventType("");
        this.setProdType("");
        this.setPostDate("");
        this.setValueDate("");
        this.setNarrative("");
        this.setChannelSeqNo("");
        this.setIntercompany("");
        this.setFlatRate(new BigDecimal("0"));
        this.setCustRate(new BigDecimal("0"));
        this.setInlandOffshore("");
        this.setClientNo("");
        this.setSeqNo("");
        this.setSystemId("");
        this.setCompany("");
        this.setGroupClient("");
        this.setGroupId(0L);
        this.setProcessStatus(ProcessStatusEnum.NEW);
        this.setProcessMessage("");
        this.setObjectVersionNumber(1L);
        this.setCreationDate(new Date());
        this.setCreatedBy("");
        this.setLastModifyDate(new Date());
        this.setLastModifiedBy("");
        this.setVoucherGroup("");
    }
}