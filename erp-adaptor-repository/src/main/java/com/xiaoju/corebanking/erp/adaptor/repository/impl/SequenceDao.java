package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SequenceMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

@Repository
public class SequenceDao {
    @Resource
    private SequenceMapper sequenceMapper;

    public Long getCurrentValue(String name) {
        return sequenceMapper.getCurrentValue(name);
    }

    public Long getCurrentValueWithLock(String name) {
        return sequenceMapper.getCurrentValueWithLock(name);
    }

    public int atomicIncrement(String name, long expectedValue, int increment) {
        return sequenceMapper.atomicIncrement(name, expectedValue, increment);
    }

    public int createSequence(String name, long startValue, int batchSize) {
        return sequenceMapper.createSequence(name, startValue, batchSize);
    }

    public int updateValue(String name, long newValue) {
        return sequenceMapper.updateValue(name, newValue);
    }

    public Integer getBatchSize(String name) {
        return sequenceMapper.getBatchSize(name);
    }

    public int updateBatchSize(String name, int batchSize) {
        return sequenceMapper.updateBatchSize(name, batchSize);
    }
}
