package com.xiaoju.corebanking.erp.adaptor.repository;


import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderQueryDO;

import java.util.List;

public interface SofiGlInterfaceHeaderRepository {

    /**
     * 查询
     */
    List<SofiGlInterfaceHeaderDO> queryList(SofiGlInterfaceHeaderQueryDO sofiGlInterfaceHeaderQueryDO);

    /**
     *
     * 更新
     */
    void updateInterfaceHeader(SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO, SofiGlInterfaceHeaderDO update,String systemCode);
    void insertSelective(SofiGlInterfaceHeaderDO sfiGlInterfaceHeaderDO);
    void batchInsertInterfaceHeader(List<SofiGlInterfaceHeaderDO> interfaceHeaderDOList);
    void batchInsert(List<SofiGlInterfaceHeaderDO> interfaceHeaderDOList);

    List<SofiGlInterfaceHeaderDO> queryData(SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO,String filePath);
    List<Long> selectGroupByProcessDay(String processDay, String systemCode);
    void updateInterfaceHeaderByGroupId(String systemCode, String processDay, Long groupId, ProcessStatusEnum oldProcessStatus,ProcessStatusEnum newProcessStatus);

}
