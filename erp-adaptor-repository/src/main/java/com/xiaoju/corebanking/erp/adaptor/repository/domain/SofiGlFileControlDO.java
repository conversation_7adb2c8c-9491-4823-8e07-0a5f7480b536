package com.xiaoju.corebanking.erp.adaptor.repository.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class SofiGlFileControlDO implements Serializable {
    private String processDay;

    private String fileName;

    private String systemCode;

    private Long fileSize;

    private Long fileCount;

    private String processStatus;

    private String processMessage;

    private Integer objectVersionNumber;

    private Date creationDate;

    private String createdBy;

    private Date lastModifyDate;

    private String lastModifiedBy;
}