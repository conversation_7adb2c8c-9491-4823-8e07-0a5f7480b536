package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.xiaoju.corebanking.erp.adaptor.common.constant.CommonConstant;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlAcctHistRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.converter.SofiGlAcctHistConverter;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlAcctHistDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SummaryResultDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlAcctHistPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SofiGlAcctHistCustomerMapper;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlAcctHistPOMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * SofiGlAcctHist数据仓储实现类
 * <AUTHOR>
 * @date 2025/1/16
 */
@Slf4j
@Repository
public class SofiGlAcctHistRepositoryImpl implements SofiGlAcctHistRepository {

    @Resource
    private SofiGlAcctHistPOMapper sofiGlAcctHistPOMapper;

    @Resource
    private SofiGlAcctHistConverter sofiGlAcctHistConverter;

    @Resource
    private SofiGlAcctHistCustomerMapper sofiGlAcctHistCustomerMapper;

    @Override
    public int syncSofiGlAcctHistData(List<SofiGlAcctHistDO> sofiGlAcctHistList) {
        if (sofiGlAcctHistList == null || sofiGlAcctHistList.isEmpty()) {
            log.warn("SofiGlAcctHist数据列表为空，无需同步");
            return 0;
        }

        int syncCount = 0;
        Date now = new Date();

        for (SofiGlAcctHistDO sofiGlAcctHistDO : sofiGlAcctHistList) {
            try {
                sofiGlAcctHistDO.setObjectVersionNumber(1L);
                sofiGlAcctHistDO.setCreationDate(now);
                sofiGlAcctHistDO.setCreatedBy(CommonConstant.SYSTEM_USER);
                sofiGlAcctHistDO.setLastModifyDate(now);
                sofiGlAcctHistDO.setLastModifiedBy(CommonConstant.SYSTEM_USER);

                SofiGlAcctHistPO po = sofiGlAcctHistConverter.toPO(sofiGlAcctHistDO);
                int result = sofiGlAcctHistPOMapper.insertSelective(po);
                if (result > 0) {
                    syncCount++;
                }

            } catch (Exception e) {
                log.error("同步SofiGlAcctHist数据失败，internalKey: {}", 
                        sofiGlAcctHistDO.getInternalKey(), e);
            }
        }

        log.info("SofiGlAcctHist数据同步完成，共处理{}条记录，成功同步{}条", 
                sofiGlAcctHistList.size(), syncCount);
        
        return syncCount;
    }

    @Override
    public List<SummaryResultDO> selectAcctHisSummary(String processDay) {
        return sofiGlAcctHistCustomerMapper.selectAcctHisSummary(processDay);
    }
} 