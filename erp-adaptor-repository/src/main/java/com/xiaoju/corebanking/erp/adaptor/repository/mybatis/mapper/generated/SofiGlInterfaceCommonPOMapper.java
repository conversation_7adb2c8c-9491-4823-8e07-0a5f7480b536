package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated;

import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceCommonPO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface SofiGlInterfaceCommonPOMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SofiGlInterfaceCommonPO record);

    int insertSelective(SofiGlInterfaceCommonPO record);

    SofiGlInterfaceCommonPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SofiGlInterfaceCommonPO record);

    int updateByPrimaryKey(SofiGlInterfaceCommonPO record);
}