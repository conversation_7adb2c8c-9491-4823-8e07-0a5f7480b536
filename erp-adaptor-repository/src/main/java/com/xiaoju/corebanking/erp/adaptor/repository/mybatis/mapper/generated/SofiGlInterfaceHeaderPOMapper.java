package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated;

import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHeaderPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHeaderPOExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface SofiGlInterfaceHeaderPOMapper {
    int deleteByExample(SofiGlInterfaceHeaderPOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SofiGlInterfaceHeaderPO record);

    int insertSelective(SofiGlInterfaceHeaderPO record);

    List<SofiGlInterfaceHeaderPO> selectByExampleWithRowbounds(SofiGlInterfaceHeaderPOExample example, RowBounds rowBounds);

    List<SofiGlInterfaceHeaderPO> selectByExample(SofiGlInterfaceHeaderPOExample example);

    SofiGlInterfaceHeaderPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SofiGlInterfaceHeaderPO record, @Param("example") SofiGlInterfaceHeaderPOExample example);

    int updateByExample(@Param("record") SofiGlInterfaceHeaderPO record, @Param("example") SofiGlInterfaceHeaderPOExample example);

    int updateByPrimaryKeySelective(SofiGlInterfaceHeaderPO record);

    int updateByPrimaryKey(SofiGlInterfaceHeaderPO record);
}