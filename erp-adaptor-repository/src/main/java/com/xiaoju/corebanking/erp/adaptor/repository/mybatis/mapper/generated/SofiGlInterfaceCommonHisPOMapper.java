package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated;

import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceCommonHisPO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface SofiGlInterfaceCommonHisPOMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SofiGlInterfaceCommonHisPO record);

    int insertSelective(SofiGlInterfaceCommonHisPO record);

    SofiGlInterfaceCommonHisPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SofiGlInterfaceCommonHisPO record);

    int updateByPrimaryKey(SofiGlInterfaceCommonHisPO record);
}