package com.xiaoju.corebanking.erp.adaptor.repository.domain;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/8/7 15:24
 */
@Data
public class VoucherGroupData {
    private final String voucherGroup;
    private final String voucherGroupDesc;
    private final String voucherGroupDescEng;
    private final String eventType;
    private final String tranType;

    private VoucherGroupData(Builder builder) {
        this.voucherGroup = builder.voucherGroup;
        this.voucherGroupDesc = builder.voucherGroupDesc;
        this.voucherGroupDescEng = builder.voucherGroupDescEng;
        this.eventType = builder.eventType;
        this.tranType = builder.tranType;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private String voucherGroup;
        private String voucherGroupDesc;
        private String voucherGroupDescEng;
        private String eventType;
        private String tranType;

        public Builder voucherGroup(String voucherGroup) {
            this.voucherGroup = voucherGroup;
            return this;
        }

        public Builder voucherGroupDesc(String voucherGroupDesc) {
            this.voucherGroupDesc = voucherGroupDesc;
            return this;
        }

        public Builder voucherGroupDescEng(String voucherGroupDescEng) {
            this.voucherGroupDescEng = voucherGroupDescEng;
            return this;
        }

        public Builder eventType(String eventType) {
            this.eventType = eventType;
            return this;
        }

        public Builder tranType(String tranType) {
            this.tranType = tranType;
            return this;
        }

        public VoucherGroupData build() {
            return new VoucherGroupData(this);
        }
    }
}
