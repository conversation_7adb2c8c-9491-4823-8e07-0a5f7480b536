package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class SofiGlInterfaceCommonHisPO implements Serializable {
    private Long id;

    private String systemCode;

    private String processDay;

    private Date accountingDate;

    private String periodName;

    private Long ledgerId;

    private String ledgerName;

    private String currencyCode;

    private String journalCategory;

    private String journalSource;

    private String journalSourceName;

    private String reference1;

    private String reference2;

    private String reference3;

    private String reference4;

    private String reference5;

    private String reference6;

    private String reference7;

    private String reference8;

    private String reference9;

    private String reference10;

    private String reference21;

    private String reference22;

    private String reference23;

    private String reference24;

    private String reference25;

    private String reference26;

    private String reference27;

    private String reference28;

    private String reference29;

    private String reference30;

    private String segment1;

    private String segment2;

    private String segment3;

    private String segment4;

    private String segment5;

    private String segment6;

    private String segment7;

    private String segment8;

    private String segment9;

    private String segment10;

    private BigDecimal enteredDr;

    private BigDecimal enteredCr;

    private BigDecimal accountedDr;

    private BigDecimal accountedCr;

    private Long groupId;

    private Date currencyConversionDate;

    private BigDecimal currencyConversionRate;

    private String currencyConversionType;

    private String attributeCategory;

    private String headerAttribute1;

    private String headerAttribute2;

    private String headerAttribute3;

    private String headerAttribute4;

    private String headerAttribute5;

    private String headerAttribute6;

    private String headerAttribute7;

    private String headerAttribute8;

    private String headerAttribute9;

    private String headerAttribute10;

    private String headerAttribute11;

    private String headerAttribute12;

    private String headerAttribute13;

    private String headerAttribute14;

    private String headerAttribute15;

    private String attributeCategory3;

    private String lineAttribute1;

    private String lineAttribute2;

    private String lineAttribute3;

    private String lineAttribute4;

    private String lineAttribute5;

    private String lineAttribute6;

    private String lineAttribute7;

    private String lineAttribute8;

    private String lineAttribute9;

    private String lineAttribute10;

    private String lineAttribute11;

    private String lineAttribute12;

    private String lineAttribute13;

    private String lineAttribute14;

    private String lineAttribute15;

    private String lineAttribute16;

    private String lineAttribute17;

    private String lineAttribute18;

    private String lineAttribute19;

    private String lineAttribute20;

    private String processStatus;

    private String processMessage;

    private Long jeHeaderId;

    private String journalName;

    private Long jeLineNum;

    private Long documentId;

    private Long loadRequestId;

    private Long importRequestId;

    private Long objectVersionNumber;

    private Date creationDate;

    private String createdBy;

    private Date lastModifyDate;

    private String lastModifiedBy;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSystemCode() {
        return systemCode;
    }

    public void setSystemCode(String systemCode) {
        this.systemCode = systemCode == null ? null : systemCode.trim();
    }

    public String getProcessDay() {
        return processDay;
    }

    public void setProcessDay(String processDay) {
        this.processDay = processDay == null ? null : processDay.trim();
    }

    public Date getAccountingDate() {
        return accountingDate;
    }

    public void setAccountingDate(Date accountingDate) {
        this.accountingDate = accountingDate;
    }

    public String getPeriodName() {
        return periodName;
    }

    public void setPeriodName(String periodName) {
        this.periodName = periodName == null ? null : periodName.trim();
    }

    public Long getLedgerId() {
        return ledgerId;
    }

    public void setLedgerId(Long ledgerId) {
        this.ledgerId = ledgerId;
    }

    public String getLedgerName() {
        return ledgerName;
    }

    public void setLedgerName(String ledgerName) {
        this.ledgerName = ledgerName == null ? null : ledgerName.trim();
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode == null ? null : currencyCode.trim();
    }

    public String getJournalCategory() {
        return journalCategory;
    }

    public void setJournalCategory(String journalCategory) {
        this.journalCategory = journalCategory == null ? null : journalCategory.trim();
    }

    public String getJournalSource() {
        return journalSource;
    }

    public void setJournalSource(String journalSource) {
        this.journalSource = journalSource == null ? null : journalSource.trim();
    }

    public String getJournalSourceName() {
        return journalSourceName;
    }

    public void setJournalSourceName(String journalSourceName) {
        this.journalSourceName = journalSourceName == null ? null : journalSourceName.trim();
    }

    public String getReference1() {
        return reference1;
    }

    public void setReference1(String reference1) {
        this.reference1 = reference1 == null ? null : reference1.trim();
    }

    public String getReference2() {
        return reference2;
    }

    public void setReference2(String reference2) {
        this.reference2 = reference2 == null ? null : reference2.trim();
    }

    public String getReference3() {
        return reference3;
    }

    public void setReference3(String reference3) {
        this.reference3 = reference3 == null ? null : reference3.trim();
    }

    public String getReference4() {
        return reference4;
    }

    public void setReference4(String reference4) {
        this.reference4 = reference4 == null ? null : reference4.trim();
    }

    public String getReference5() {
        return reference5;
    }

    public void setReference5(String reference5) {
        this.reference5 = reference5 == null ? null : reference5.trim();
    }

    public String getReference6() {
        return reference6;
    }

    public void setReference6(String reference6) {
        this.reference6 = reference6 == null ? null : reference6.trim();
    }

    public String getReference7() {
        return reference7;
    }

    public void setReference7(String reference7) {
        this.reference7 = reference7 == null ? null : reference7.trim();
    }

    public String getReference8() {
        return reference8;
    }

    public void setReference8(String reference8) {
        this.reference8 = reference8 == null ? null : reference8.trim();
    }

    public String getReference9() {
        return reference9;
    }

    public void setReference9(String reference9) {
        this.reference9 = reference9 == null ? null : reference9.trim();
    }

    public String getReference10() {
        return reference10;
    }

    public void setReference10(String reference10) {
        this.reference10 = reference10 == null ? null : reference10.trim();
    }

    public String getReference21() {
        return reference21;
    }

    public void setReference21(String reference21) {
        this.reference21 = reference21 == null ? null : reference21.trim();
    }

    public String getReference22() {
        return reference22;
    }

    public void setReference22(String reference22) {
        this.reference22 = reference22 == null ? null : reference22.trim();
    }

    public String getReference23() {
        return reference23;
    }

    public void setReference23(String reference23) {
        this.reference23 = reference23 == null ? null : reference23.trim();
    }

    public String getReference24() {
        return reference24;
    }

    public void setReference24(String reference24) {
        this.reference24 = reference24 == null ? null : reference24.trim();
    }

    public String getReference25() {
        return reference25;
    }

    public void setReference25(String reference25) {
        this.reference25 = reference25 == null ? null : reference25.trim();
    }

    public String getReference26() {
        return reference26;
    }

    public void setReference26(String reference26) {
        this.reference26 = reference26 == null ? null : reference26.trim();
    }

    public String getReference27() {
        return reference27;
    }

    public void setReference27(String reference27) {
        this.reference27 = reference27 == null ? null : reference27.trim();
    }

    public String getReference28() {
        return reference28;
    }

    public void setReference28(String reference28) {
        this.reference28 = reference28 == null ? null : reference28.trim();
    }

    public String getReference29() {
        return reference29;
    }

    public void setReference29(String reference29) {
        this.reference29 = reference29 == null ? null : reference29.trim();
    }

    public String getReference30() {
        return reference30;
    }

    public void setReference30(String reference30) {
        this.reference30 = reference30 == null ? null : reference30.trim();
    }

    public String getSegment1() {
        return segment1;
    }

    public void setSegment1(String segment1) {
        this.segment1 = segment1 == null ? null : segment1.trim();
    }

    public String getSegment2() {
        return segment2;
    }

    public void setSegment2(String segment2) {
        this.segment2 = segment2 == null ? null : segment2.trim();
    }

    public String getSegment3() {
        return segment3;
    }

    public void setSegment3(String segment3) {
        this.segment3 = segment3 == null ? null : segment3.trim();
    }

    public String getSegment4() {
        return segment4;
    }

    public void setSegment4(String segment4) {
        this.segment4 = segment4 == null ? null : segment4.trim();
    }

    public String getSegment5() {
        return segment5;
    }

    public void setSegment5(String segment5) {
        this.segment5 = segment5 == null ? null : segment5.trim();
    }

    public String getSegment6() {
        return segment6;
    }

    public void setSegment6(String segment6) {
        this.segment6 = segment6 == null ? null : segment6.trim();
    }

    public String getSegment7() {
        return segment7;
    }

    public void setSegment7(String segment7) {
        this.segment7 = segment7 == null ? null : segment7.trim();
    }

    public String getSegment8() {
        return segment8;
    }

    public void setSegment8(String segment8) {
        this.segment8 = segment8 == null ? null : segment8.trim();
    }

    public String getSegment9() {
        return segment9;
    }

    public void setSegment9(String segment9) {
        this.segment9 = segment9 == null ? null : segment9.trim();
    }

    public String getSegment10() {
        return segment10;
    }

    public void setSegment10(String segment10) {
        this.segment10 = segment10 == null ? null : segment10.trim();
    }

    public BigDecimal getEnteredDr() {
        return enteredDr;
    }

    public void setEnteredDr(BigDecimal enteredDr) {
        this.enteredDr = enteredDr;
    }

    public BigDecimal getEnteredCr() {
        return enteredCr;
    }

    public void setEnteredCr(BigDecimal enteredCr) {
        this.enteredCr = enteredCr;
    }

    public BigDecimal getAccountedDr() {
        return accountedDr;
    }

    public void setAccountedDr(BigDecimal accountedDr) {
        this.accountedDr = accountedDr;
    }

    public BigDecimal getAccountedCr() {
        return accountedCr;
    }

    public void setAccountedCr(BigDecimal accountedCr) {
        this.accountedCr = accountedCr;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Date getCurrencyConversionDate() {
        return currencyConversionDate;
    }

    public void setCurrencyConversionDate(Date currencyConversionDate) {
        this.currencyConversionDate = currencyConversionDate;
    }

    public BigDecimal getCurrencyConversionRate() {
        return currencyConversionRate;
    }

    public void setCurrencyConversionRate(BigDecimal currencyConversionRate) {
        this.currencyConversionRate = currencyConversionRate;
    }

    public String getCurrencyConversionType() {
        return currencyConversionType;
    }

    public void setCurrencyConversionType(String currencyConversionType) {
        this.currencyConversionType = currencyConversionType == null ? null : currencyConversionType.trim();
    }

    public String getAttributeCategory() {
        return attributeCategory;
    }

    public void setAttributeCategory(String attributeCategory) {
        this.attributeCategory = attributeCategory == null ? null : attributeCategory.trim();
    }

    public String getHeaderAttribute1() {
        return headerAttribute1;
    }

    public void setHeaderAttribute1(String headerAttribute1) {
        this.headerAttribute1 = headerAttribute1 == null ? null : headerAttribute1.trim();
    }

    public String getHeaderAttribute2() {
        return headerAttribute2;
    }

    public void setHeaderAttribute2(String headerAttribute2) {
        this.headerAttribute2 = headerAttribute2 == null ? null : headerAttribute2.trim();
    }

    public String getHeaderAttribute3() {
        return headerAttribute3;
    }

    public void setHeaderAttribute3(String headerAttribute3) {
        this.headerAttribute3 = headerAttribute3 == null ? null : headerAttribute3.trim();
    }

    public String getHeaderAttribute4() {
        return headerAttribute4;
    }

    public void setHeaderAttribute4(String headerAttribute4) {
        this.headerAttribute4 = headerAttribute4 == null ? null : headerAttribute4.trim();
    }

    public String getHeaderAttribute5() {
        return headerAttribute5;
    }

    public void setHeaderAttribute5(String headerAttribute5) {
        this.headerAttribute5 = headerAttribute5 == null ? null : headerAttribute5.trim();
    }

    public String getHeaderAttribute6() {
        return headerAttribute6;
    }

    public void setHeaderAttribute6(String headerAttribute6) {
        this.headerAttribute6 = headerAttribute6 == null ? null : headerAttribute6.trim();
    }

    public String getHeaderAttribute7() {
        return headerAttribute7;
    }

    public void setHeaderAttribute7(String headerAttribute7) {
        this.headerAttribute7 = headerAttribute7 == null ? null : headerAttribute7.trim();
    }

    public String getHeaderAttribute8() {
        return headerAttribute8;
    }

    public void setHeaderAttribute8(String headerAttribute8) {
        this.headerAttribute8 = headerAttribute8 == null ? null : headerAttribute8.trim();
    }

    public String getHeaderAttribute9() {
        return headerAttribute9;
    }

    public void setHeaderAttribute9(String headerAttribute9) {
        this.headerAttribute9 = headerAttribute9 == null ? null : headerAttribute9.trim();
    }

    public String getHeaderAttribute10() {
        return headerAttribute10;
    }

    public void setHeaderAttribute10(String headerAttribute10) {
        this.headerAttribute10 = headerAttribute10 == null ? null : headerAttribute10.trim();
    }

    public String getHeaderAttribute11() {
        return headerAttribute11;
    }

    public void setHeaderAttribute11(String headerAttribute11) {
        this.headerAttribute11 = headerAttribute11 == null ? null : headerAttribute11.trim();
    }

    public String getHeaderAttribute12() {
        return headerAttribute12;
    }

    public void setHeaderAttribute12(String headerAttribute12) {
        this.headerAttribute12 = headerAttribute12 == null ? null : headerAttribute12.trim();
    }

    public String getHeaderAttribute13() {
        return headerAttribute13;
    }

    public void setHeaderAttribute13(String headerAttribute13) {
        this.headerAttribute13 = headerAttribute13 == null ? null : headerAttribute13.trim();
    }

    public String getHeaderAttribute14() {
        return headerAttribute14;
    }

    public void setHeaderAttribute14(String headerAttribute14) {
        this.headerAttribute14 = headerAttribute14 == null ? null : headerAttribute14.trim();
    }

    public String getHeaderAttribute15() {
        return headerAttribute15;
    }

    public void setHeaderAttribute15(String headerAttribute15) {
        this.headerAttribute15 = headerAttribute15 == null ? null : headerAttribute15.trim();
    }

    public String getAttributeCategory3() {
        return attributeCategory3;
    }

    public void setAttributeCategory3(String attributeCategory3) {
        this.attributeCategory3 = attributeCategory3 == null ? null : attributeCategory3.trim();
    }

    public String getLineAttribute1() {
        return lineAttribute1;
    }

    public void setLineAttribute1(String lineAttribute1) {
        this.lineAttribute1 = lineAttribute1 == null ? null : lineAttribute1.trim();
    }

    public String getLineAttribute2() {
        return lineAttribute2;
    }

    public void setLineAttribute2(String lineAttribute2) {
        this.lineAttribute2 = lineAttribute2 == null ? null : lineAttribute2.trim();
    }

    public String getLineAttribute3() {
        return lineAttribute3;
    }

    public void setLineAttribute3(String lineAttribute3) {
        this.lineAttribute3 = lineAttribute3 == null ? null : lineAttribute3.trim();
    }

    public String getLineAttribute4() {
        return lineAttribute4;
    }

    public void setLineAttribute4(String lineAttribute4) {
        this.lineAttribute4 = lineAttribute4 == null ? null : lineAttribute4.trim();
    }

    public String getLineAttribute5() {
        return lineAttribute5;
    }

    public void setLineAttribute5(String lineAttribute5) {
        this.lineAttribute5 = lineAttribute5 == null ? null : lineAttribute5.trim();
    }

    public String getLineAttribute6() {
        return lineAttribute6;
    }

    public void setLineAttribute6(String lineAttribute6) {
        this.lineAttribute6 = lineAttribute6 == null ? null : lineAttribute6.trim();
    }

    public String getLineAttribute7() {
        return lineAttribute7;
    }

    public void setLineAttribute7(String lineAttribute7) {
        this.lineAttribute7 = lineAttribute7 == null ? null : lineAttribute7.trim();
    }

    public String getLineAttribute8() {
        return lineAttribute8;
    }

    public void setLineAttribute8(String lineAttribute8) {
        this.lineAttribute8 = lineAttribute8 == null ? null : lineAttribute8.trim();
    }

    public String getLineAttribute9() {
        return lineAttribute9;
    }

    public void setLineAttribute9(String lineAttribute9) {
        this.lineAttribute9 = lineAttribute9 == null ? null : lineAttribute9.trim();
    }

    public String getLineAttribute10() {
        return lineAttribute10;
    }

    public void setLineAttribute10(String lineAttribute10) {
        this.lineAttribute10 = lineAttribute10 == null ? null : lineAttribute10.trim();
    }

    public String getLineAttribute11() {
        return lineAttribute11;
    }

    public void setLineAttribute11(String lineAttribute11) {
        this.lineAttribute11 = lineAttribute11 == null ? null : lineAttribute11.trim();
    }

    public String getLineAttribute12() {
        return lineAttribute12;
    }

    public void setLineAttribute12(String lineAttribute12) {
        this.lineAttribute12 = lineAttribute12 == null ? null : lineAttribute12.trim();
    }

    public String getLineAttribute13() {
        return lineAttribute13;
    }

    public void setLineAttribute13(String lineAttribute13) {
        this.lineAttribute13 = lineAttribute13 == null ? null : lineAttribute13.trim();
    }

    public String getLineAttribute14() {
        return lineAttribute14;
    }

    public void setLineAttribute14(String lineAttribute14) {
        this.lineAttribute14 = lineAttribute14 == null ? null : lineAttribute14.trim();
    }

    public String getLineAttribute15() {
        return lineAttribute15;
    }

    public void setLineAttribute15(String lineAttribute15) {
        this.lineAttribute15 = lineAttribute15 == null ? null : lineAttribute15.trim();
    }

    public String getLineAttribute16() {
        return lineAttribute16;
    }

    public void setLineAttribute16(String lineAttribute16) {
        this.lineAttribute16 = lineAttribute16 == null ? null : lineAttribute16.trim();
    }

    public String getLineAttribute17() {
        return lineAttribute17;
    }

    public void setLineAttribute17(String lineAttribute17) {
        this.lineAttribute17 = lineAttribute17 == null ? null : lineAttribute17.trim();
    }

    public String getLineAttribute18() {
        return lineAttribute18;
    }

    public void setLineAttribute18(String lineAttribute18) {
        this.lineAttribute18 = lineAttribute18 == null ? null : lineAttribute18.trim();
    }

    public String getLineAttribute19() {
        return lineAttribute19;
    }

    public void setLineAttribute19(String lineAttribute19) {
        this.lineAttribute19 = lineAttribute19 == null ? null : lineAttribute19.trim();
    }

    public String getLineAttribute20() {
        return lineAttribute20;
    }

    public void setLineAttribute20(String lineAttribute20) {
        this.lineAttribute20 = lineAttribute20 == null ? null : lineAttribute20.trim();
    }

    public String getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(String processStatus) {
        this.processStatus = processStatus == null ? null : processStatus.trim();
    }

    public String getProcessMessage() {
        return processMessage;
    }

    public void setProcessMessage(String processMessage) {
        this.processMessage = processMessage == null ? null : processMessage.trim();
    }

    public Long getJeHeaderId() {
        return jeHeaderId;
    }

    public void setJeHeaderId(Long jeHeaderId) {
        this.jeHeaderId = jeHeaderId;
    }

    public String getJournalName() {
        return journalName;
    }

    public void setJournalName(String journalName) {
        this.journalName = journalName == null ? null : journalName.trim();
    }

    public Long getJeLineNum() {
        return jeLineNum;
    }

    public void setJeLineNum(Long jeLineNum) {
        this.jeLineNum = jeLineNum;
    }

    public Long getDocumentId() {
        return documentId;
    }

    public void setDocumentId(Long documentId) {
        this.documentId = documentId;
    }

    public Long getLoadRequestId() {
        return loadRequestId;
    }

    public void setLoadRequestId(Long loadRequestId) {
        this.loadRequestId = loadRequestId;
    }

    public Long getImportRequestId() {
        return importRequestId;
    }

    public void setImportRequestId(Long importRequestId) {
        this.importRequestId = importRequestId;
    }

    public Long getObjectVersionNumber() {
        return objectVersionNumber;
    }

    public void setObjectVersionNumber(Long objectVersionNumber) {
        this.objectVersionNumber = objectVersionNumber;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getLastModifyDate() {
        return lastModifyDate;
    }

    public void setLastModifyDate(Date lastModifyDate) {
        this.lastModifyDate = lastModifyDate;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy == null ? null : lastModifiedBy.trim();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SofiGlInterfaceCommonHisPO other = (SofiGlInterfaceCommonHisPO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSystemCode() == null ? other.getSystemCode() == null : this.getSystemCode().equals(other.getSystemCode()))
            && (this.getProcessDay() == null ? other.getProcessDay() == null : this.getProcessDay().equals(other.getProcessDay()))
            && (this.getAccountingDate() == null ? other.getAccountingDate() == null : this.getAccountingDate().equals(other.getAccountingDate()))
            && (this.getPeriodName() == null ? other.getPeriodName() == null : this.getPeriodName().equals(other.getPeriodName()))
            && (this.getLedgerId() == null ? other.getLedgerId() == null : this.getLedgerId().equals(other.getLedgerId()))
            && (this.getLedgerName() == null ? other.getLedgerName() == null : this.getLedgerName().equals(other.getLedgerName()))
            && (this.getCurrencyCode() == null ? other.getCurrencyCode() == null : this.getCurrencyCode().equals(other.getCurrencyCode()))
            && (this.getJournalCategory() == null ? other.getJournalCategory() == null : this.getJournalCategory().equals(other.getJournalCategory()))
            && (this.getJournalSource() == null ? other.getJournalSource() == null : this.getJournalSource().equals(other.getJournalSource()))
            && (this.getJournalSourceName() == null ? other.getJournalSourceName() == null : this.getJournalSourceName().equals(other.getJournalSourceName()))
            && (this.getReference1() == null ? other.getReference1() == null : this.getReference1().equals(other.getReference1()))
            && (this.getReference2() == null ? other.getReference2() == null : this.getReference2().equals(other.getReference2()))
            && (this.getReference3() == null ? other.getReference3() == null : this.getReference3().equals(other.getReference3()))
            && (this.getReference4() == null ? other.getReference4() == null : this.getReference4().equals(other.getReference4()))
            && (this.getReference5() == null ? other.getReference5() == null : this.getReference5().equals(other.getReference5()))
            && (this.getReference6() == null ? other.getReference6() == null : this.getReference6().equals(other.getReference6()))
            && (this.getReference7() == null ? other.getReference7() == null : this.getReference7().equals(other.getReference7()))
            && (this.getReference8() == null ? other.getReference8() == null : this.getReference8().equals(other.getReference8()))
            && (this.getReference9() == null ? other.getReference9() == null : this.getReference9().equals(other.getReference9()))
            && (this.getReference10() == null ? other.getReference10() == null : this.getReference10().equals(other.getReference10()))
            && (this.getReference21() == null ? other.getReference21() == null : this.getReference21().equals(other.getReference21()))
            && (this.getReference22() == null ? other.getReference22() == null : this.getReference22().equals(other.getReference22()))
            && (this.getReference23() == null ? other.getReference23() == null : this.getReference23().equals(other.getReference23()))
            && (this.getReference24() == null ? other.getReference24() == null : this.getReference24().equals(other.getReference24()))
            && (this.getReference25() == null ? other.getReference25() == null : this.getReference25().equals(other.getReference25()))
            && (this.getReference26() == null ? other.getReference26() == null : this.getReference26().equals(other.getReference26()))
            && (this.getReference27() == null ? other.getReference27() == null : this.getReference27().equals(other.getReference27()))
            && (this.getReference28() == null ? other.getReference28() == null : this.getReference28().equals(other.getReference28()))
            && (this.getReference29() == null ? other.getReference29() == null : this.getReference29().equals(other.getReference29()))
            && (this.getReference30() == null ? other.getReference30() == null : this.getReference30().equals(other.getReference30()))
            && (this.getSegment1() == null ? other.getSegment1() == null : this.getSegment1().equals(other.getSegment1()))
            && (this.getSegment2() == null ? other.getSegment2() == null : this.getSegment2().equals(other.getSegment2()))
            && (this.getSegment3() == null ? other.getSegment3() == null : this.getSegment3().equals(other.getSegment3()))
            && (this.getSegment4() == null ? other.getSegment4() == null : this.getSegment4().equals(other.getSegment4()))
            && (this.getSegment5() == null ? other.getSegment5() == null : this.getSegment5().equals(other.getSegment5()))
            && (this.getSegment6() == null ? other.getSegment6() == null : this.getSegment6().equals(other.getSegment6()))
            && (this.getSegment7() == null ? other.getSegment7() == null : this.getSegment7().equals(other.getSegment7()))
            && (this.getSegment8() == null ? other.getSegment8() == null : this.getSegment8().equals(other.getSegment8()))
            && (this.getSegment9() == null ? other.getSegment9() == null : this.getSegment9().equals(other.getSegment9()))
            && (this.getSegment10() == null ? other.getSegment10() == null : this.getSegment10().equals(other.getSegment10()))
            && (this.getEnteredDr() == null ? other.getEnteredDr() == null : this.getEnteredDr().equals(other.getEnteredDr()))
            && (this.getEnteredCr() == null ? other.getEnteredCr() == null : this.getEnteredCr().equals(other.getEnteredCr()))
            && (this.getAccountedDr() == null ? other.getAccountedDr() == null : this.getAccountedDr().equals(other.getAccountedDr()))
            && (this.getAccountedCr() == null ? other.getAccountedCr() == null : this.getAccountedCr().equals(other.getAccountedCr()))
            && (this.getGroupId() == null ? other.getGroupId() == null : this.getGroupId().equals(other.getGroupId()))
            && (this.getCurrencyConversionDate() == null ? other.getCurrencyConversionDate() == null : this.getCurrencyConversionDate().equals(other.getCurrencyConversionDate()))
            && (this.getCurrencyConversionRate() == null ? other.getCurrencyConversionRate() == null : this.getCurrencyConversionRate().equals(other.getCurrencyConversionRate()))
            && (this.getCurrencyConversionType() == null ? other.getCurrencyConversionType() == null : this.getCurrencyConversionType().equals(other.getCurrencyConversionType()))
            && (this.getAttributeCategory() == null ? other.getAttributeCategory() == null : this.getAttributeCategory().equals(other.getAttributeCategory()))
            && (this.getHeaderAttribute1() == null ? other.getHeaderAttribute1() == null : this.getHeaderAttribute1().equals(other.getHeaderAttribute1()))
            && (this.getHeaderAttribute2() == null ? other.getHeaderAttribute2() == null : this.getHeaderAttribute2().equals(other.getHeaderAttribute2()))
            && (this.getHeaderAttribute3() == null ? other.getHeaderAttribute3() == null : this.getHeaderAttribute3().equals(other.getHeaderAttribute3()))
            && (this.getHeaderAttribute4() == null ? other.getHeaderAttribute4() == null : this.getHeaderAttribute4().equals(other.getHeaderAttribute4()))
            && (this.getHeaderAttribute5() == null ? other.getHeaderAttribute5() == null : this.getHeaderAttribute5().equals(other.getHeaderAttribute5()))
            && (this.getHeaderAttribute6() == null ? other.getHeaderAttribute6() == null : this.getHeaderAttribute6().equals(other.getHeaderAttribute6()))
            && (this.getHeaderAttribute7() == null ? other.getHeaderAttribute7() == null : this.getHeaderAttribute7().equals(other.getHeaderAttribute7()))
            && (this.getHeaderAttribute8() == null ? other.getHeaderAttribute8() == null : this.getHeaderAttribute8().equals(other.getHeaderAttribute8()))
            && (this.getHeaderAttribute9() == null ? other.getHeaderAttribute9() == null : this.getHeaderAttribute9().equals(other.getHeaderAttribute9()))
            && (this.getHeaderAttribute10() == null ? other.getHeaderAttribute10() == null : this.getHeaderAttribute10().equals(other.getHeaderAttribute10()))
            && (this.getHeaderAttribute11() == null ? other.getHeaderAttribute11() == null : this.getHeaderAttribute11().equals(other.getHeaderAttribute11()))
            && (this.getHeaderAttribute12() == null ? other.getHeaderAttribute12() == null : this.getHeaderAttribute12().equals(other.getHeaderAttribute12()))
            && (this.getHeaderAttribute13() == null ? other.getHeaderAttribute13() == null : this.getHeaderAttribute13().equals(other.getHeaderAttribute13()))
            && (this.getHeaderAttribute14() == null ? other.getHeaderAttribute14() == null : this.getHeaderAttribute14().equals(other.getHeaderAttribute14()))
            && (this.getHeaderAttribute15() == null ? other.getHeaderAttribute15() == null : this.getHeaderAttribute15().equals(other.getHeaderAttribute15()))
            && (this.getAttributeCategory3() == null ? other.getAttributeCategory3() == null : this.getAttributeCategory3().equals(other.getAttributeCategory3()))
            && (this.getLineAttribute1() == null ? other.getLineAttribute1() == null : this.getLineAttribute1().equals(other.getLineAttribute1()))
            && (this.getLineAttribute2() == null ? other.getLineAttribute2() == null : this.getLineAttribute2().equals(other.getLineAttribute2()))
            && (this.getLineAttribute3() == null ? other.getLineAttribute3() == null : this.getLineAttribute3().equals(other.getLineAttribute3()))
            && (this.getLineAttribute4() == null ? other.getLineAttribute4() == null : this.getLineAttribute4().equals(other.getLineAttribute4()))
            && (this.getLineAttribute5() == null ? other.getLineAttribute5() == null : this.getLineAttribute5().equals(other.getLineAttribute5()))
            && (this.getLineAttribute6() == null ? other.getLineAttribute6() == null : this.getLineAttribute6().equals(other.getLineAttribute6()))
            && (this.getLineAttribute7() == null ? other.getLineAttribute7() == null : this.getLineAttribute7().equals(other.getLineAttribute7()))
            && (this.getLineAttribute8() == null ? other.getLineAttribute8() == null : this.getLineAttribute8().equals(other.getLineAttribute8()))
            && (this.getLineAttribute9() == null ? other.getLineAttribute9() == null : this.getLineAttribute9().equals(other.getLineAttribute9()))
            && (this.getLineAttribute10() == null ? other.getLineAttribute10() == null : this.getLineAttribute10().equals(other.getLineAttribute10()))
            && (this.getLineAttribute11() == null ? other.getLineAttribute11() == null : this.getLineAttribute11().equals(other.getLineAttribute11()))
            && (this.getLineAttribute12() == null ? other.getLineAttribute12() == null : this.getLineAttribute12().equals(other.getLineAttribute12()))
            && (this.getLineAttribute13() == null ? other.getLineAttribute13() == null : this.getLineAttribute13().equals(other.getLineAttribute13()))
            && (this.getLineAttribute14() == null ? other.getLineAttribute14() == null : this.getLineAttribute14().equals(other.getLineAttribute14()))
            && (this.getLineAttribute15() == null ? other.getLineAttribute15() == null : this.getLineAttribute15().equals(other.getLineAttribute15()))
            && (this.getLineAttribute16() == null ? other.getLineAttribute16() == null : this.getLineAttribute16().equals(other.getLineAttribute16()))
            && (this.getLineAttribute17() == null ? other.getLineAttribute17() == null : this.getLineAttribute17().equals(other.getLineAttribute17()))
            && (this.getLineAttribute18() == null ? other.getLineAttribute18() == null : this.getLineAttribute18().equals(other.getLineAttribute18()))
            && (this.getLineAttribute19() == null ? other.getLineAttribute19() == null : this.getLineAttribute19().equals(other.getLineAttribute19()))
            && (this.getLineAttribute20() == null ? other.getLineAttribute20() == null : this.getLineAttribute20().equals(other.getLineAttribute20()))
            && (this.getProcessStatus() == null ? other.getProcessStatus() == null : this.getProcessStatus().equals(other.getProcessStatus()))
            && (this.getProcessMessage() == null ? other.getProcessMessage() == null : this.getProcessMessage().equals(other.getProcessMessage()))
            && (this.getJeHeaderId() == null ? other.getJeHeaderId() == null : this.getJeHeaderId().equals(other.getJeHeaderId()))
            && (this.getJournalName() == null ? other.getJournalName() == null : this.getJournalName().equals(other.getJournalName()))
            && (this.getJeLineNum() == null ? other.getJeLineNum() == null : this.getJeLineNum().equals(other.getJeLineNum()))
            && (this.getDocumentId() == null ? other.getDocumentId() == null : this.getDocumentId().equals(other.getDocumentId()))
            && (this.getLoadRequestId() == null ? other.getLoadRequestId() == null : this.getLoadRequestId().equals(other.getLoadRequestId()))
            && (this.getImportRequestId() == null ? other.getImportRequestId() == null : this.getImportRequestId().equals(other.getImportRequestId()))
            && (this.getObjectVersionNumber() == null ? other.getObjectVersionNumber() == null : this.getObjectVersionNumber().equals(other.getObjectVersionNumber()))
            && (this.getCreationDate() == null ? other.getCreationDate() == null : this.getCreationDate().equals(other.getCreationDate()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getLastModifyDate() == null ? other.getLastModifyDate() == null : this.getLastModifyDate().equals(other.getLastModifyDate()))
            && (this.getLastModifiedBy() == null ? other.getLastModifiedBy() == null : this.getLastModifiedBy().equals(other.getLastModifiedBy()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSystemCode() == null) ? 0 : getSystemCode().hashCode());
        result = prime * result + ((getProcessDay() == null) ? 0 : getProcessDay().hashCode());
        result = prime * result + ((getAccountingDate() == null) ? 0 : getAccountingDate().hashCode());
        result = prime * result + ((getPeriodName() == null) ? 0 : getPeriodName().hashCode());
        result = prime * result + ((getLedgerId() == null) ? 0 : getLedgerId().hashCode());
        result = prime * result + ((getLedgerName() == null) ? 0 : getLedgerName().hashCode());
        result = prime * result + ((getCurrencyCode() == null) ? 0 : getCurrencyCode().hashCode());
        result = prime * result + ((getJournalCategory() == null) ? 0 : getJournalCategory().hashCode());
        result = prime * result + ((getJournalSource() == null) ? 0 : getJournalSource().hashCode());
        result = prime * result + ((getJournalSourceName() == null) ? 0 : getJournalSourceName().hashCode());
        result = prime * result + ((getReference1() == null) ? 0 : getReference1().hashCode());
        result = prime * result + ((getReference2() == null) ? 0 : getReference2().hashCode());
        result = prime * result + ((getReference3() == null) ? 0 : getReference3().hashCode());
        result = prime * result + ((getReference4() == null) ? 0 : getReference4().hashCode());
        result = prime * result + ((getReference5() == null) ? 0 : getReference5().hashCode());
        result = prime * result + ((getReference6() == null) ? 0 : getReference6().hashCode());
        result = prime * result + ((getReference7() == null) ? 0 : getReference7().hashCode());
        result = prime * result + ((getReference8() == null) ? 0 : getReference8().hashCode());
        result = prime * result + ((getReference9() == null) ? 0 : getReference9().hashCode());
        result = prime * result + ((getReference10() == null) ? 0 : getReference10().hashCode());
        result = prime * result + ((getReference21() == null) ? 0 : getReference21().hashCode());
        result = prime * result + ((getReference22() == null) ? 0 : getReference22().hashCode());
        result = prime * result + ((getReference23() == null) ? 0 : getReference23().hashCode());
        result = prime * result + ((getReference24() == null) ? 0 : getReference24().hashCode());
        result = prime * result + ((getReference25() == null) ? 0 : getReference25().hashCode());
        result = prime * result + ((getReference26() == null) ? 0 : getReference26().hashCode());
        result = prime * result + ((getReference27() == null) ? 0 : getReference27().hashCode());
        result = prime * result + ((getReference28() == null) ? 0 : getReference28().hashCode());
        result = prime * result + ((getReference29() == null) ? 0 : getReference29().hashCode());
        result = prime * result + ((getReference30() == null) ? 0 : getReference30().hashCode());
        result = prime * result + ((getSegment1() == null) ? 0 : getSegment1().hashCode());
        result = prime * result + ((getSegment2() == null) ? 0 : getSegment2().hashCode());
        result = prime * result + ((getSegment3() == null) ? 0 : getSegment3().hashCode());
        result = prime * result + ((getSegment4() == null) ? 0 : getSegment4().hashCode());
        result = prime * result + ((getSegment5() == null) ? 0 : getSegment5().hashCode());
        result = prime * result + ((getSegment6() == null) ? 0 : getSegment6().hashCode());
        result = prime * result + ((getSegment7() == null) ? 0 : getSegment7().hashCode());
        result = prime * result + ((getSegment8() == null) ? 0 : getSegment8().hashCode());
        result = prime * result + ((getSegment9() == null) ? 0 : getSegment9().hashCode());
        result = prime * result + ((getSegment10() == null) ? 0 : getSegment10().hashCode());
        result = prime * result + ((getEnteredDr() == null) ? 0 : getEnteredDr().hashCode());
        result = prime * result + ((getEnteredCr() == null) ? 0 : getEnteredCr().hashCode());
        result = prime * result + ((getAccountedDr() == null) ? 0 : getAccountedDr().hashCode());
        result = prime * result + ((getAccountedCr() == null) ? 0 : getAccountedCr().hashCode());
        result = prime * result + ((getGroupId() == null) ? 0 : getGroupId().hashCode());
        result = prime * result + ((getCurrencyConversionDate() == null) ? 0 : getCurrencyConversionDate().hashCode());
        result = prime * result + ((getCurrencyConversionRate() == null) ? 0 : getCurrencyConversionRate().hashCode());
        result = prime * result + ((getCurrencyConversionType() == null) ? 0 : getCurrencyConversionType().hashCode());
        result = prime * result + ((getAttributeCategory() == null) ? 0 : getAttributeCategory().hashCode());
        result = prime * result + ((getHeaderAttribute1() == null) ? 0 : getHeaderAttribute1().hashCode());
        result = prime * result + ((getHeaderAttribute2() == null) ? 0 : getHeaderAttribute2().hashCode());
        result = prime * result + ((getHeaderAttribute3() == null) ? 0 : getHeaderAttribute3().hashCode());
        result = prime * result + ((getHeaderAttribute4() == null) ? 0 : getHeaderAttribute4().hashCode());
        result = prime * result + ((getHeaderAttribute5() == null) ? 0 : getHeaderAttribute5().hashCode());
        result = prime * result + ((getHeaderAttribute6() == null) ? 0 : getHeaderAttribute6().hashCode());
        result = prime * result + ((getHeaderAttribute7() == null) ? 0 : getHeaderAttribute7().hashCode());
        result = prime * result + ((getHeaderAttribute8() == null) ? 0 : getHeaderAttribute8().hashCode());
        result = prime * result + ((getHeaderAttribute9() == null) ? 0 : getHeaderAttribute9().hashCode());
        result = prime * result + ((getHeaderAttribute10() == null) ? 0 : getHeaderAttribute10().hashCode());
        result = prime * result + ((getHeaderAttribute11() == null) ? 0 : getHeaderAttribute11().hashCode());
        result = prime * result + ((getHeaderAttribute12() == null) ? 0 : getHeaderAttribute12().hashCode());
        result = prime * result + ((getHeaderAttribute13() == null) ? 0 : getHeaderAttribute13().hashCode());
        result = prime * result + ((getHeaderAttribute14() == null) ? 0 : getHeaderAttribute14().hashCode());
        result = prime * result + ((getHeaderAttribute15() == null) ? 0 : getHeaderAttribute15().hashCode());
        result = prime * result + ((getAttributeCategory3() == null) ? 0 : getAttributeCategory3().hashCode());
        result = prime * result + ((getLineAttribute1() == null) ? 0 : getLineAttribute1().hashCode());
        result = prime * result + ((getLineAttribute2() == null) ? 0 : getLineAttribute2().hashCode());
        result = prime * result + ((getLineAttribute3() == null) ? 0 : getLineAttribute3().hashCode());
        result = prime * result + ((getLineAttribute4() == null) ? 0 : getLineAttribute4().hashCode());
        result = prime * result + ((getLineAttribute5() == null) ? 0 : getLineAttribute5().hashCode());
        result = prime * result + ((getLineAttribute6() == null) ? 0 : getLineAttribute6().hashCode());
        result = prime * result + ((getLineAttribute7() == null) ? 0 : getLineAttribute7().hashCode());
        result = prime * result + ((getLineAttribute8() == null) ? 0 : getLineAttribute8().hashCode());
        result = prime * result + ((getLineAttribute9() == null) ? 0 : getLineAttribute9().hashCode());
        result = prime * result + ((getLineAttribute10() == null) ? 0 : getLineAttribute10().hashCode());
        result = prime * result + ((getLineAttribute11() == null) ? 0 : getLineAttribute11().hashCode());
        result = prime * result + ((getLineAttribute12() == null) ? 0 : getLineAttribute12().hashCode());
        result = prime * result + ((getLineAttribute13() == null) ? 0 : getLineAttribute13().hashCode());
        result = prime * result + ((getLineAttribute14() == null) ? 0 : getLineAttribute14().hashCode());
        result = prime * result + ((getLineAttribute15() == null) ? 0 : getLineAttribute15().hashCode());
        result = prime * result + ((getLineAttribute16() == null) ? 0 : getLineAttribute16().hashCode());
        result = prime * result + ((getLineAttribute17() == null) ? 0 : getLineAttribute17().hashCode());
        result = prime * result + ((getLineAttribute18() == null) ? 0 : getLineAttribute18().hashCode());
        result = prime * result + ((getLineAttribute19() == null) ? 0 : getLineAttribute19().hashCode());
        result = prime * result + ((getLineAttribute20() == null) ? 0 : getLineAttribute20().hashCode());
        result = prime * result + ((getProcessStatus() == null) ? 0 : getProcessStatus().hashCode());
        result = prime * result + ((getProcessMessage() == null) ? 0 : getProcessMessage().hashCode());
        result = prime * result + ((getJeHeaderId() == null) ? 0 : getJeHeaderId().hashCode());
        result = prime * result + ((getJournalName() == null) ? 0 : getJournalName().hashCode());
        result = prime * result + ((getJeLineNum() == null) ? 0 : getJeLineNum().hashCode());
        result = prime * result + ((getDocumentId() == null) ? 0 : getDocumentId().hashCode());
        result = prime * result + ((getLoadRequestId() == null) ? 0 : getLoadRequestId().hashCode());
        result = prime * result + ((getImportRequestId() == null) ? 0 : getImportRequestId().hashCode());
        result = prime * result + ((getObjectVersionNumber() == null) ? 0 : getObjectVersionNumber().hashCode());
        result = prime * result + ((getCreationDate() == null) ? 0 : getCreationDate().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getLastModifyDate() == null) ? 0 : getLastModifyDate().hashCode());
        result = prime * result + ((getLastModifiedBy() == null) ? 0 : getLastModifiedBy().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", systemCode=").append(systemCode);
        sb.append(", processDay=").append(processDay);
        sb.append(", accountingDate=").append(accountingDate);
        sb.append(", periodName=").append(periodName);
        sb.append(", ledgerId=").append(ledgerId);
        sb.append(", ledgerName=").append(ledgerName);
        sb.append(", currencyCode=").append(currencyCode);
        sb.append(", journalCategory=").append(journalCategory);
        sb.append(", journalSource=").append(journalSource);
        sb.append(", journalSourceName=").append(journalSourceName);
        sb.append(", reference1=").append(reference1);
        sb.append(", reference2=").append(reference2);
        sb.append(", reference3=").append(reference3);
        sb.append(", reference4=").append(reference4);
        sb.append(", reference5=").append(reference5);
        sb.append(", reference6=").append(reference6);
        sb.append(", reference7=").append(reference7);
        sb.append(", reference8=").append(reference8);
        sb.append(", reference9=").append(reference9);
        sb.append(", reference10=").append(reference10);
        sb.append(", reference21=").append(reference21);
        sb.append(", reference22=").append(reference22);
        sb.append(", reference23=").append(reference23);
        sb.append(", reference24=").append(reference24);
        sb.append(", reference25=").append(reference25);
        sb.append(", reference26=").append(reference26);
        sb.append(", reference27=").append(reference27);
        sb.append(", reference28=").append(reference28);
        sb.append(", reference29=").append(reference29);
        sb.append(", reference30=").append(reference30);
        sb.append(", segment1=").append(segment1);
        sb.append(", segment2=").append(segment2);
        sb.append(", segment3=").append(segment3);
        sb.append(", segment4=").append(segment4);
        sb.append(", segment5=").append(segment5);
        sb.append(", segment6=").append(segment6);
        sb.append(", segment7=").append(segment7);
        sb.append(", segment8=").append(segment8);
        sb.append(", segment9=").append(segment9);
        sb.append(", segment10=").append(segment10);
        sb.append(", enteredDr=").append(enteredDr);
        sb.append(", enteredCr=").append(enteredCr);
        sb.append(", accountedDr=").append(accountedDr);
        sb.append(", accountedCr=").append(accountedCr);
        sb.append(", groupId=").append(groupId);
        sb.append(", currencyConversionDate=").append(currencyConversionDate);
        sb.append(", currencyConversionRate=").append(currencyConversionRate);
        sb.append(", currencyConversionType=").append(currencyConversionType);
        sb.append(", attributeCategory=").append(attributeCategory);
        sb.append(", headerAttribute1=").append(headerAttribute1);
        sb.append(", headerAttribute2=").append(headerAttribute2);
        sb.append(", headerAttribute3=").append(headerAttribute3);
        sb.append(", headerAttribute4=").append(headerAttribute4);
        sb.append(", headerAttribute5=").append(headerAttribute5);
        sb.append(", headerAttribute6=").append(headerAttribute6);
        sb.append(", headerAttribute7=").append(headerAttribute7);
        sb.append(", headerAttribute8=").append(headerAttribute8);
        sb.append(", headerAttribute9=").append(headerAttribute9);
        sb.append(", headerAttribute10=").append(headerAttribute10);
        sb.append(", headerAttribute11=").append(headerAttribute11);
        sb.append(", headerAttribute12=").append(headerAttribute12);
        sb.append(", headerAttribute13=").append(headerAttribute13);
        sb.append(", headerAttribute14=").append(headerAttribute14);
        sb.append(", headerAttribute15=").append(headerAttribute15);
        sb.append(", attributeCategory3=").append(attributeCategory3);
        sb.append(", lineAttribute1=").append(lineAttribute1);
        sb.append(", lineAttribute2=").append(lineAttribute2);
        sb.append(", lineAttribute3=").append(lineAttribute3);
        sb.append(", lineAttribute4=").append(lineAttribute4);
        sb.append(", lineAttribute5=").append(lineAttribute5);
        sb.append(", lineAttribute6=").append(lineAttribute6);
        sb.append(", lineAttribute7=").append(lineAttribute7);
        sb.append(", lineAttribute8=").append(lineAttribute8);
        sb.append(", lineAttribute9=").append(lineAttribute9);
        sb.append(", lineAttribute10=").append(lineAttribute10);
        sb.append(", lineAttribute11=").append(lineAttribute11);
        sb.append(", lineAttribute12=").append(lineAttribute12);
        sb.append(", lineAttribute13=").append(lineAttribute13);
        sb.append(", lineAttribute14=").append(lineAttribute14);
        sb.append(", lineAttribute15=").append(lineAttribute15);
        sb.append(", lineAttribute16=").append(lineAttribute16);
        sb.append(", lineAttribute17=").append(lineAttribute17);
        sb.append(", lineAttribute18=").append(lineAttribute18);
        sb.append(", lineAttribute19=").append(lineAttribute19);
        sb.append(", lineAttribute20=").append(lineAttribute20);
        sb.append(", processStatus=").append(processStatus);
        sb.append(", processMessage=").append(processMessage);
        sb.append(", jeHeaderId=").append(jeHeaderId);
        sb.append(", journalName=").append(journalName);
        sb.append(", jeLineNum=").append(jeLineNum);
        sb.append(", documentId=").append(documentId);
        sb.append(", loadRequestId=").append(loadRequestId);
        sb.append(", importRequestId=").append(importRequestId);
        sb.append(", objectVersionNumber=").append(objectVersionNumber);
        sb.append(", creationDate=").append(creationDate);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", lastModifyDate=").append(lastModifyDate);
        sb.append(", lastModifiedBy=").append(lastModifiedBy);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}