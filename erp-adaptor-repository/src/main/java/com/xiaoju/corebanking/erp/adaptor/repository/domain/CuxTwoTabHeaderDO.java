package com.xiaoju.corebanking.erp.adaptor.repository.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.sql.Date;

@Data
public class CuxTwoTabHeaderDO {
    private String formCode;
    private String formName;
    private String uniqueFields;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateFrom;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateTo;
    private String enabledFlag;
    private String lockedFlag;
    private Integer pageNum;
    private Integer pageSize;
}
