package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
@Data
public class SofiGlShenmaPO implements Serializable {
    private Long id;

    private String processDay;

    private String fileName;

    private String linkReference;

    private String sourceBranch;

    private String ccy;

    private String glCode;

    private BigDecimal enteredDebitAmount;

    private BigDecimal enteredCreditAmount;

    private String profitCenter;

    private String sourceModule;

    private String clientType;

    private String amtType;

    private String tranType;

    private String eventType;

    private String prodType;

    private String postDate;

    private String valueDate;

    private String narrative;

    private String channelSeqNo;

    private String intercompany;

    private BigDecimal flatRate;

    private BigDecimal custRate;

    private String inlandOffshore;

    private String clientNo;

    private String seqNo;

    private String systemId;

    private String company;

    private String groupClient;

    private String voucherGroup;

    private Long groupId;

    private String processStatus;

    private String processMessage;

    private Long objectVersionNumber;

    private Date creationDate;

    private String createdBy;

    private Date lastModifyDate;

    private String lastModifiedBy;

    private String crDrInd;

    private String externalReference;

}