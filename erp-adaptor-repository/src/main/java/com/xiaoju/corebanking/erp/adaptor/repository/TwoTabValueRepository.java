package com.xiaoju.corebanking.erp.adaptor.repository;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.CuxTwoTabValueDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValueExtraPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePO;

import java.util.List;

public interface TwoTabValueRepository {
    CuxTwoTabValuePO insertValue(CuxTwoTabValueDO value);
    void updateByExample(CuxTwoTabValueDO header);
    int insertOrUpdate(List<CuxTwoTabValueDO> list);
    List<CuxTwoTabValuePO> selectByFormCode(String formCode);

    List<CuxTwoTabValuePO> selectSimpleByFormCode(String formCode);
    List<CuxTwoTabValueExtraPO> selectAllByFormCode();

}
