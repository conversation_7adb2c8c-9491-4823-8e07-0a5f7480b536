package com.xiaoju.corebanking.erp.adaptor.repository.impl;


import com.github.pagehelper.PageHelper;
import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.enums.SourceSysEnum;
import com.xiaoju.corebanking.erp.adaptor.common.utils.LogUtils;
import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlInterfaceHeaderRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.converter.SofiGlInterfaceHeaderModelConverter;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderQueryDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHeaderPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHeaderPOExample;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SofiGlInterfaceHeaderCustomerMapper;
import com.xiaoju.godson.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class SofiGlInterfaceHeaderRepositoryImpl implements SofiGlInterfaceHeaderRepository {
    @Autowired
    private SofiGlInterfaceHeaderCustomerMapper sofiGlInterfaceHeaderCustomerMapper;

    @Autowired
    private SofiGlInterfaceHeaderModelConverter sofiGlInterfaceHeaderModelConverter;


    @Override
    public List<SofiGlInterfaceHeaderDO> queryList(SofiGlInterfaceHeaderQueryDO sofiGlInterfaceHeaderQueryDO) {
        PageHelper.startPage(sofiGlInterfaceHeaderQueryDO.getPageNum(), sofiGlInterfaceHeaderQueryDO.getPageSize());
        List<SofiGlInterfaceHeaderPO> sofiGlInterfaceHeaderPOS = sofiGlInterfaceHeaderCustomerMapper.selectInterfaceHeaderList(sofiGlInterfaceHeaderQueryDO);
        return sofiGlInterfaceHeaderPOS.stream().map(sofiGlInterfaceHeaderModelConverter::convert).collect(Collectors.toList());
    }

    @Override
    public void updateInterfaceHeader(SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO, SofiGlInterfaceHeaderDO update, String systemCode) {
        Assert.notNull(sofiGlInterfaceHeaderDO.getExternalReference(), "polizaId can not be null!");
        log.info("updateInterfaceHeader sofiGlInterfaceHeaderDO={},update={}", sofiGlInterfaceHeaderDO, update);
        SofiGlInterfaceHeaderPO sofiGlInterfaceHeaderPO = sofiGlInterfaceHeaderModelConverter.convert(update);
        log.info("updateInterfaceHeader sofiGlInterfaceHeaderDO={}", LogUtils.toString(sofiGlInterfaceHeaderPO));
        int effected = sofiGlInterfaceHeaderCustomerMapper.updatePolizaIdSelective(systemCode,sofiGlInterfaceHeaderDO.getProcessDay(), sofiGlInterfaceHeaderDO.getExternalReference(), sofiGlInterfaceHeaderDO.getGroupId(), sofiGlInterfaceHeaderPO);
        log.info("updateInterfaceHeader effected={},sofiGlInterfaceHeaderDO={},update={}", effected, sofiGlInterfaceHeaderDO, update);

        // 数据回填
        SofiGlInterfaceHeaderDO backup = new SofiGlInterfaceHeaderDO();
        sofiGlInterfaceHeaderModelConverter.copyIgnoreNullValue(sofiGlInterfaceHeaderDO, backup);
        sofiGlInterfaceHeaderModelConverter.copyIgnoreNullValue(update, sofiGlInterfaceHeaderDO);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertSelective(SofiGlInterfaceHeaderDO sfiGlInterfaceHeaderDO) {
        SofiGlInterfaceHeaderPO sofiGlInterfaceHeaderPO = sofiGlInterfaceHeaderModelConverter.convert(sfiGlInterfaceHeaderDO);
        sofiGlInterfaceHeaderCustomerMapper.insertSelective(sofiGlInterfaceHeaderPO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertInterfaceHeader(List<SofiGlInterfaceHeaderDO> headerList) {
        if (headerList == null || headerList.isEmpty()) {
            return;
        }

        List<String> externalRefs = headerList.stream()
                .map(SofiGlInterfaceHeaderDO::getExternalReference)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (!externalRefs.isEmpty()) {
            List<SofiGlInterfaceHeaderPO> existingRecords = sofiGlInterfaceHeaderCustomerMapper.findByPolizaIds(externalRefs);

            if (!existingRecords.isEmpty()) {
                int deleteCount = sofiGlInterfaceHeaderCustomerMapper.batchDeleteByPolizaIds(externalRefs);
                log.info("批量删除已存在的记录{}条，poliza_ids: {}", deleteCount, externalRefs);

                if (log.isDebugEnabled()) {
                    log.debug("删除的记录详情: {}", JsonUtil.toString(existingRecords));
                }
            }
        }

        List<SofiGlInterfaceHeaderPO> poList = headerList.stream()
                .map(sofiGlInterfaceHeaderModelConverter::convert)
                .collect(Collectors.toList());

        sofiGlInterfaceHeaderCustomerMapper.batchInsert(poList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsert(List<SofiGlInterfaceHeaderDO> interfaceHeaderDOList) {
        batchInsertInterfaceHeader(interfaceHeaderDOList);
    }

    @Override
    public List<SofiGlInterfaceHeaderDO> queryData(SofiGlInterfaceHeaderDO sofiGlInterfaceHeaderDO, String systemCode) {
        SofiGlInterfaceHeaderPOExample example = new SofiGlInterfaceHeaderPOExample();
        SofiGlInterfaceHeaderPOExample.Criteria criteria = example.createCriteria().andProcessDayEqualTo(sofiGlInterfaceHeaderDO.getProcessDay())
                .andProcessStatusEqualTo(sofiGlInterfaceHeaderDO.getProcessStatus().getCode());
        if (SourceSysEnum.SAFI.getCode().equals(systemCode)) {
            criteria.andSystemCodeEqualTo(SourceSysEnum.SAFI.getCode());
        } else {
            criteria.andSystemCodeEqualTo(SourceSysEnum.SHEN_MA.getCode());
        }
        List<SofiGlInterfaceHeaderPO> sofiGlInterfaceHeaderPOS = sofiGlInterfaceHeaderCustomerMapper.selectByExample(example);
        return sofiGlInterfaceHeaderPOS.stream().map(sofiGlInterfaceHeaderModelConverter::convert).collect(Collectors.toList());
    }

    @Override
    public List<Long> selectGroupByProcessDay(String processDay,String systemCode) {
        return sofiGlInterfaceHeaderCustomerMapper.selectGroupId(processDay, systemCode);
    }

    @Override
    public void updateInterfaceHeaderByGroupId(String systemCode, String processDay, Long groupId, ProcessStatusEnum newProcessStatus, ProcessStatusEnum oldProcessStatus) {
        sofiGlInterfaceHeaderCustomerMapper.updateInterfaceHeaderByGroupId(systemCode, processDay, groupId, oldProcessStatus.getCode(),newProcessStatus.getCode());
    }

}
