package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated;

import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePOExample;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface CuxTwoTabValuePOMapper {
    int deleteByExample(CuxTwoTabValuePOExample example);

    int deleteByPrimaryKey(Long id);

    int insert(CuxTwoTabValuePO record);

    int insertSelective(CuxTwoTabValuePO record);

    List<CuxTwoTabValuePO> selectByExampleWithRowbounds(CuxTwoTabValuePOExample example, RowBounds rowBounds);

    List<CuxTwoTabValuePO> selectByExample(CuxTwoTabValuePOExample example);

    CuxTwoTabValuePO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") CuxTwoTabValuePO record, @Param("example") CuxTwoTabValuePOExample example);

    int updateByExample(@Param("record") CuxTwoTabValuePO record, @Param("example") CuxTwoTabValuePOExample example);

    int updateByPrimaryKeySelective(CuxTwoTabValuePO record);

    int updateByPrimaryKey(CuxTwoTabValuePO record);
}