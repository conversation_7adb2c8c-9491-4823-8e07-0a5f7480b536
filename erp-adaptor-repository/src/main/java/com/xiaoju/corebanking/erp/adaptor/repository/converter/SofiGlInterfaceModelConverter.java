package com.xiaoju.corebanking.erp.adaptor.repository.converter;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.utils.CommonUtils;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfacePO;
import org.springframework.stereotype.Component;

@Component
public class SofiGlInterfaceModelConverter {

    public SofiGlInterfaceDO convert(SofiGlInterfacePO sofiGlInterfacePO) {
        SofiGlInterfaceDO sofiGlInterfaceDO = new SofiGlInterfaceDO();
        sofiGlInterfaceDO.setId(sofiGlInterfacePO.getId());
        sofiGlInterfaceDO.setFileId(sofiGlInterfacePO.getFileId());
        sofiGlInterfaceDO.setProcessDay(sofiGlInterfacePO.getProcessDay());
        sofiGlInterfaceDO.setFileName(sofiGlInterfacePO.getFileName());
        sofiGlInterfaceDO.setBatchId(sofiGlInterfacePO.getBatchId());
        sofiGlInterfaceDO.setDetallePolizaId(sofiGlInterfacePO.getDetallePolizaId());
        sofiGlInterfaceDO.setSourceSys(sofiGlInterfacePO.getSourceSys());
        sofiGlInterfaceDO.setEmpresaId(sofiGlInterfacePO.getEmpresaId());
        sofiGlInterfaceDO.setPolizaId(sofiGlInterfacePO.getPolizaId());
        sofiGlInterfaceDO.setFecha(sofiGlInterfacePO.getFecha());
        sofiGlInterfaceDO.setCentroCostoId(sofiGlInterfacePO.getCentroCostoId());
        sofiGlInterfaceDO.setCuentaCompleta(sofiGlInterfacePO.getCuentaCompleta());
        sofiGlInterfaceDO.setInstrumento(sofiGlInterfacePO.getInstrumento());
        sofiGlInterfaceDO.setMonedaId(sofiGlInterfacePO.getMonedaId());
        sofiGlInterfaceDO.setCargos(sofiGlInterfacePO.getCargos());
        sofiGlInterfaceDO.setAbonos(sofiGlInterfacePO.getAbonos());
        sofiGlInterfaceDO.setDescripcion(sofiGlInterfacePO.getDescripcion());
        sofiGlInterfaceDO.setReferencia(sofiGlInterfacePO.getReferencia());
        sofiGlInterfaceDO.setProcedimientoCont(sofiGlInterfacePO.getProcedimientoCont());
        sofiGlInterfaceDO.setTipoInstrumentoId(sofiGlInterfacePO.getTipoInstrumentoId());
        sofiGlInterfaceDO.setRfc(sofiGlInterfacePO.getRfc());
        sofiGlInterfaceDO.setTotalFactura(sofiGlInterfacePO.getTotalFactura());
        sofiGlInterfaceDO.setFolioUuid(sofiGlInterfacePO.getFolioUuid());
        sofiGlInterfaceDO.setUsuario(sofiGlInterfacePO.getUsuario());
        sofiGlInterfaceDO.setFechaActual(sofiGlInterfacePO.getFechaActual());
        sofiGlInterfaceDO.setDireccionIp(sofiGlInterfacePO.getDireccionIp());
        sofiGlInterfaceDO.setProgramaId(sofiGlInterfacePO.getProgramaId());
        sofiGlInterfaceDO.setSucursal(sofiGlInterfacePO.getSucursal());
        sofiGlInterfaceDO.setNumTransaccion(sofiGlInterfacePO.getNumTransaccion());
        sofiGlInterfaceDO.setLedgerId(sofiGlInterfacePO.getLedgerId());
        sofiGlInterfaceDO.setLedgerName(sofiGlInterfacePO.getLedgerName());
        sofiGlInterfaceDO.setCurrencyCode(sofiGlInterfacePO.getCurrencyCode());
        sofiGlInterfaceDO.setJournalCategory(sofiGlInterfacePO.getJournalCategory());
        sofiGlInterfaceDO.setJournalSource(sofiGlInterfacePO.getJournalSource());
        sofiGlInterfaceDO.setSegment1(sofiGlInterfacePO.getSegment1());
        sofiGlInterfaceDO.setSegment2(sofiGlInterfacePO.getSegment2());
        sofiGlInterfaceDO.setSegment3(sofiGlInterfacePO.getSegment3());
        sofiGlInterfaceDO.setSegment4(sofiGlInterfacePO.getSegment4());
        sofiGlInterfaceDO.setSegment5(sofiGlInterfacePO.getSegment5());
        sofiGlInterfaceDO.setSegment6(sofiGlInterfacePO.getSegment6());
        sofiGlInterfaceDO.setSegment7(sofiGlInterfacePO.getSegment7());
        sofiGlInterfaceDO.setSegment8(sofiGlInterfacePO.getSegment8());
        sofiGlInterfaceDO.setSegment9(sofiGlInterfacePO.getSegment9());
        sofiGlInterfaceDO.setSegment10(sofiGlInterfacePO.getSegment10());
        sofiGlInterfaceDO.setGroupId(sofiGlInterfacePO.getGroupId());
        sofiGlInterfaceDO.setCurrencyConversionDate(sofiGlInterfacePO.getCurrencyConversionDate());
        sofiGlInterfaceDO.setCurrencyConversionRate(sofiGlInterfacePO.getCurrencyConversionRate());
        sofiGlInterfaceDO.setUserCurrencyConversionType(sofiGlInterfacePO.getUserCurrencyConversionType());
        sofiGlInterfaceDO.setProcessStatus(ProcessStatusEnum.getByCode(sofiGlInterfacePO.getProcessStatus()));
        sofiGlInterfaceDO.setProcessMessage(sofiGlInterfacePO.getProcessMessage());
        sofiGlInterfaceDO.setJeHeaderId(sofiGlInterfacePO.getJeHeaderId());
        sofiGlInterfaceDO.setJournalName(sofiGlInterfacePO.getJournalName());
        sofiGlInterfaceDO.setJeLineNum(sofiGlInterfacePO.getJeLineNum());
        sofiGlInterfaceDO.setDocumentId(sofiGlInterfacePO.getDocumentId());
        sofiGlInterfaceDO.setLoadRequestId(sofiGlInterfacePO.getLoadRequestId());
        sofiGlInterfaceDO.setImportRequestId(sofiGlInterfacePO.getImportRequestId());
        sofiGlInterfaceDO.setObjectVersionNumber(sofiGlInterfacePO.getObjectVersionNumber());
        sofiGlInterfaceDO.setCreationDate(sofiGlInterfacePO.getCreationDate());
        sofiGlInterfaceDO.setCreatedBy(sofiGlInterfacePO.getCreatedBy());
        sofiGlInterfaceDO.setLastModifyDate(sofiGlInterfacePO.getLastModifyDate());
        sofiGlInterfaceDO.setLastModifiedBy(sofiGlInterfacePO.getLastModifiedBy());
        sofiGlInterfaceDO.setJournalSourceName(sofiGlInterfacePO.getJournalSourceName());
        return sofiGlInterfaceDO;
    }

    public SofiGlInterfacePO convert(SofiGlInterfaceDO sofiGlInterfaceDO) {
        SofiGlInterfacePO sofiGlInterfacePO = new SofiGlInterfacePO();
        sofiGlInterfacePO.setFileId(sofiGlInterfaceDO.getFileId());
        sofiGlInterfacePO.setProcessDay(sofiGlInterfaceDO.getProcessDay());
        sofiGlInterfacePO.setFileName(sofiGlInterfaceDO.getFileName());
        sofiGlInterfacePO.setBatchId(sofiGlInterfaceDO.getBatchId());
        sofiGlInterfacePO.setDetallePolizaId(sofiGlInterfaceDO.getDetallePolizaId());
        sofiGlInterfacePO.setSourceSys(sofiGlInterfaceDO.getSourceSys());
        sofiGlInterfacePO.setEmpresaId(sofiGlInterfaceDO.getEmpresaId());
        sofiGlInterfacePO.setPolizaId(sofiGlInterfaceDO.getPolizaId());
        sofiGlInterfacePO.setFecha(sofiGlInterfaceDO.getFecha());
        sofiGlInterfacePO.setCentroCostoId(sofiGlInterfaceDO.getCentroCostoId());
        sofiGlInterfacePO.setCuentaCompleta(sofiGlInterfaceDO.getCuentaCompleta());
        sofiGlInterfacePO.setInstrumento(sofiGlInterfaceDO.getInstrumento());
        sofiGlInterfacePO.setMonedaId(sofiGlInterfaceDO.getMonedaId());
        sofiGlInterfacePO.setCargos(sofiGlInterfaceDO.getCargos());
        sofiGlInterfacePO.setAbonos(sofiGlInterfaceDO.getAbonos());
        sofiGlInterfacePO.setDescripcion(sofiGlInterfaceDO.getDescripcion());
        sofiGlInterfacePO.setReferencia(sofiGlInterfaceDO.getReferencia());
        sofiGlInterfacePO.setProcedimientoCont(sofiGlInterfaceDO.getProcedimientoCont());
        sofiGlInterfacePO.setTipoInstrumentoId(sofiGlInterfaceDO.getTipoInstrumentoId());
        sofiGlInterfacePO.setRfc(sofiGlInterfaceDO.getRfc());
        sofiGlInterfacePO.setTotalFactura(sofiGlInterfaceDO.getTotalFactura());
        sofiGlInterfacePO.setFolioUuid(sofiGlInterfaceDO.getFolioUuid());
        sofiGlInterfacePO.setUsuario(sofiGlInterfaceDO.getUsuario());
        sofiGlInterfacePO.setFechaActual(sofiGlInterfaceDO.getFechaActual());
        sofiGlInterfacePO.setDireccionIp(sofiGlInterfaceDO.getDireccionIp());
        sofiGlInterfacePO.setProgramaId(sofiGlInterfaceDO.getProgramaId());
        sofiGlInterfacePO.setSucursal(sofiGlInterfaceDO.getSucursal());
        sofiGlInterfacePO.setNumTransaccion(sofiGlInterfaceDO.getNumTransaccion());
        sofiGlInterfacePO.setLedgerId(sofiGlInterfaceDO.getLedgerId());
        sofiGlInterfacePO.setLedgerName(sofiGlInterfaceDO.getLedgerName());
        sofiGlInterfacePO.setCurrencyCode(sofiGlInterfaceDO.getCurrencyCode());
        sofiGlInterfacePO.setJournalCategory(sofiGlInterfaceDO.getJournalCategory());
        sofiGlInterfacePO.setJournalSource(sofiGlInterfaceDO.getJournalSource());
        sofiGlInterfacePO.setSegment1(sofiGlInterfaceDO.getSegment1());
        sofiGlInterfacePO.setSegment2(sofiGlInterfaceDO.getSegment2());
        sofiGlInterfacePO.setSegment3(sofiGlInterfaceDO.getSegment3());
        sofiGlInterfacePO.setSegment4(sofiGlInterfaceDO.getSegment4());
        sofiGlInterfacePO.setSegment5(sofiGlInterfaceDO.getSegment5());
        sofiGlInterfacePO.setSegment6(sofiGlInterfaceDO.getSegment6());
        sofiGlInterfacePO.setSegment7(sofiGlInterfaceDO.getSegment7());
        sofiGlInterfacePO.setSegment8(sofiGlInterfaceDO.getSegment8());
        sofiGlInterfacePO.setSegment9(sofiGlInterfaceDO.getSegment9());
        sofiGlInterfacePO.setSegment10(sofiGlInterfaceDO.getSegment10());
        sofiGlInterfacePO.setGroupId(sofiGlInterfaceDO.getGroupId());
        sofiGlInterfacePO.setCurrencyConversionDate(sofiGlInterfaceDO.getCurrencyConversionDate());
        sofiGlInterfacePO.setCurrencyConversionRate(sofiGlInterfaceDO.getCurrencyConversionRate());
        sofiGlInterfacePO.setUserCurrencyConversionType(sofiGlInterfaceDO.getUserCurrencyConversionType());
        sofiGlInterfacePO.setProcessStatus(sofiGlInterfaceDO.getProcessStatus() != null ? sofiGlInterfaceDO.getProcessStatus().getCode() : null);
        sofiGlInterfacePO.setProcessMessage(sofiGlInterfaceDO.getProcessMessage());
        sofiGlInterfacePO.setJeHeaderId(sofiGlInterfaceDO.getJeHeaderId());
        sofiGlInterfacePO.setJournalName(sofiGlInterfaceDO.getJournalName());
        sofiGlInterfacePO.setJeLineNum(sofiGlInterfaceDO.getJeLineNum());
        sofiGlInterfacePO.setDocumentId(sofiGlInterfaceDO.getDocumentId());
        sofiGlInterfacePO.setLoadRequestId(sofiGlInterfaceDO.getLoadRequestId());
        sofiGlInterfacePO.setImportRequestId(sofiGlInterfaceDO.getImportRequestId());
        sofiGlInterfacePO.setObjectVersionNumber(sofiGlInterfaceDO.getObjectVersionNumber());
        sofiGlInterfacePO.setCreationDate(sofiGlInterfaceDO.getCreationDate());
        sofiGlInterfacePO.setCreatedBy(sofiGlInterfaceDO.getCreatedBy());
        sofiGlInterfacePO.setLastModifyDate(sofiGlInterfaceDO.getLastModifyDate());
        sofiGlInterfacePO.setLastModifiedBy(sofiGlInterfaceDO.getLastModifiedBy());
        sofiGlInterfacePO.setJournalSourceName(sofiGlInterfaceDO.getJournalSourceName());
        return sofiGlInterfacePO;
    }


    /**
     * DO -> DO
     */
    public void copyIgnoreNullValue(SofiGlInterfaceDO source, SofiGlInterfaceDO target) {
        CommonUtils.copyIgnoreNull(source::getId, target::setId);
        CommonUtils.copyIgnoreNull(source::getFileId, target::setFileId);
        CommonUtils.copyIgnoreNull(source::getProcessDay, target::setProcessDay);
        CommonUtils.copyIgnoreNull(source::getFileName, target::setFileName);
        CommonUtils.copyIgnoreNull(source::getBatchId, target::setBatchId);
        CommonUtils.copyIgnoreNull(source::getDetallePolizaId, target::setDetallePolizaId);
        CommonUtils.copyIgnoreNull(source::getSourceSys, target::setSourceSys);
        CommonUtils.copyIgnoreNull(source::getEmpresaId, target::setEmpresaId);
        CommonUtils.copyIgnoreNull(source::getPolizaId, target::setPolizaId);
        CommonUtils.copyIgnoreNull(source::getFecha, target::setFecha);
        CommonUtils.copyIgnoreNull(source::getCentroCostoId, target::setCentroCostoId);
        CommonUtils.copyIgnoreNull(source::getCuentaCompleta, target::setCuentaCompleta);
        CommonUtils.copyIgnoreNull(source::getInstrumento, target::setInstrumento);
        CommonUtils.copyIgnoreNull(source::getMonedaId, target::setMonedaId);
        CommonUtils.copyIgnoreNull(source::getCargos, target::setCargos);
        CommonUtils.copyIgnoreNull(source::getAbonos, target::setAbonos);
        CommonUtils.copyIgnoreNull(source::getDescripcion, target::setDescripcion);
        CommonUtils.copyIgnoreNull(source::getReferencia, target::setReferencia);
        CommonUtils.copyIgnoreNull(source::getProcedimientoCont, target::setProcedimientoCont);
        CommonUtils.copyIgnoreNull(source::getTipoInstrumentoId, target::setTipoInstrumentoId);
        CommonUtils.copyIgnoreNull(source::getRfc, target::setRfc);
        CommonUtils.copyIgnoreNull(source::getTotalFactura, target::setTotalFactura);
        CommonUtils.copyIgnoreNull(source::getFolioUuid, target::setFolioUuid);
        CommonUtils.copyIgnoreNull(source::getUsuario, target::setUsuario);
        CommonUtils.copyIgnoreNull(source::getFechaActual, target::setFechaActual);
        CommonUtils.copyIgnoreNull(source::getDireccionIp, target::setDireccionIp);
        CommonUtils.copyIgnoreNull(source::getProgramaId, target::setProgramaId);
        CommonUtils.copyIgnoreNull(source::getSucursal, target::setSucursal);
        CommonUtils.copyIgnoreNull(source::getNumTransaccion, target::setNumTransaccion);
        CommonUtils.copyIgnoreNull(source::getLedgerId, target::setLedgerId);
        CommonUtils.copyIgnoreNull(source::getCurrencyCode, target::setCurrencyCode);
        CommonUtils.copyIgnoreNull(source::getJournalCategory, target::setJournalCategory);
        CommonUtils.copyIgnoreNull(source::getJournalSource, target::setJournalSource);
        CommonUtils.copyIgnoreNull(source::getSegment1, target::setSegment1);
        CommonUtils.copyIgnoreNull(source::getSegment2, target::setSegment2);
        CommonUtils.copyIgnoreNull(source::getSegment3, target::setSegment3);
        CommonUtils.copyIgnoreNull(source::getSegment4, target::setSegment4);
        CommonUtils.copyIgnoreNull(source::getSegment5, target::setSegment5);
        CommonUtils.copyIgnoreNull(source::getSegment6, target::setSegment6);
        CommonUtils.copyIgnoreNull(source::getSegment7, target::setSegment7);
        CommonUtils.copyIgnoreNull(source::getSegment8, target::setSegment8);
        CommonUtils.copyIgnoreNull(source::getSegment9, target::setSegment9);
        CommonUtils.copyIgnoreNull(source::getSegment10, target::setSegment10);
        CommonUtils.copyIgnoreNull(source::getGroupId, target::setGroupId);
        CommonUtils.copyIgnoreNull(source::getCurrencyConversionDate, target::setCurrencyConversionDate);
        CommonUtils.copyIgnoreNull(source::getCurrencyConversionRate, target::setCurrencyConversionRate);
        CommonUtils.copyIgnoreNull(source::getUserCurrencyConversionType, target::setUserCurrencyConversionType);
        CommonUtils.copyIgnoreNull(source::getProcessStatus, target::setProcessStatus);
        CommonUtils.copyIgnoreNull(source::getProcessMessage, target::setProcessMessage);
        CommonUtils.copyIgnoreNull(source::getJeHeaderId, target::setJeHeaderId);
        CommonUtils.copyIgnoreNull(source::getJournalName, target::setJournalName);
        CommonUtils.copyIgnoreNull(source::getJeLineNum, target::setJeLineNum);
        CommonUtils.copyIgnoreNull(source::getDocumentId, target::setDocumentId);
        CommonUtils.copyIgnoreNull(source::getLoadRequestId, target::setLoadRequestId);
        CommonUtils.copyIgnoreNull(source::getImportRequestId, target::setImportRequestId);
        CommonUtils.copyIgnoreNull(source::getObjectVersionNumber, target::setObjectVersionNumber);
        CommonUtils.copyIgnoreNull(source::getCreationDate, target::setCreationDate);
        CommonUtils.copyIgnoreNull(source::getCreatedBy, target::setCreatedBy);
        CommonUtils.copyIgnoreNull(source::getLastModifyDate, target::setLastModifyDate);
        CommonUtils.copyIgnoreNull(source::getCreatedBy, target::setCreatedBy);
        CommonUtils.copyIgnoreNull(source::getLastModifyDate, target::setLastModifyDate);
        CommonUtils.copyIgnoreNull(source::getLastModifiedBy, target::setLastModifiedBy);

    }

}
