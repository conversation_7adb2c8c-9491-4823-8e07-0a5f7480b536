package com.xiaoju.corebanking.erp.adaptor.repository.converter;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlAcctHistDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlAcctHistPO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * SofiGlAcctHist转换器
 * <AUTHOR>
 * @date 2025/1/16
 */
@Component
public class SofiGlAcctHistConverter {

    /**
     * DO转PO
     */
    public SofiGlAcctHistPO toPO(SofiGlAcctHistDO sofiGlAcctHistDO) {
        if (sofiGlAcctHistDO == null) {
            return null;
        }
        
        SofiGlAcctHistPO po = new SofiGlAcctHistPO();
        BeanUtils.copyProperties(sofiGlAcctHistDO, po);
        return po;
    }

    /**
     * PO转DO
     */
    public SofiGlAcctHistDO toDO(SofiGlAcctHistPO sofiGlAcctHistPO) {
        if (sofiGlAcctHistPO == null) {
            return null;
        }
        
        SofiGlAcctHistDO sofiGlAcctHistDO = new SofiGlAcctHistDO();
        BeanUtils.copyProperties(sofiGlAcctHistPO, sofiGlAcctHistDO);
        return sofiGlAcctHistDO;
    }
} 