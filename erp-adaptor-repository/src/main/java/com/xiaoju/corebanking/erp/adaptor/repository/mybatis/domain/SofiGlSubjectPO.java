package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain;

import java.io.Serializable;

public class SofiGlSubjectPO extends SofiGlSubjectPOKey implements Serializable {
    private String subjectDesc;

    private String subjectDescEn;

    private String controlSubject;

    private String bsplType;

    private String glType;

    private String subjectType;

    private String balanceWay;

    private String subjectStatus;

    private String subjectLevel;

    private String manualAccount;

    private String specialBookkeeping;

    private String odFacility;

    private String rangeNo;

    private String revalueRateType;

    private String systemId;

    private String measurementAttr;

    private String itemSegregation;

    private String company;

    private String tranTimestamp;

    private String payRec;

    private String subjectRemark;

    private static final long serialVersionUID = 1L;

    public String getSubjectDesc() {
        return subjectDesc;
    }

    public void setSubjectDesc(String subjectDesc) {
        this.subjectDesc = subjectDesc == null ? null : subjectDesc.trim();
    }

    public String getSubjectDescEn() {
        return subjectDescEn;
    }

    public void setSubjectDescEn(String subjectDescEn) {
        this.subjectDescEn = subjectDescEn == null ? null : subjectDescEn.trim();
    }

    public String getControlSubject() {
        return controlSubject;
    }

    public void setControlSubject(String controlSubject) {
        this.controlSubject = controlSubject == null ? null : controlSubject.trim();
    }

    public String getBsplType() {
        return bsplType;
    }

    public void setBsplType(String bsplType) {
        this.bsplType = bsplType == null ? null : bsplType.trim();
    }

    public String getGlType() {
        return glType;
    }

    public void setGlType(String glType) {
        this.glType = glType == null ? null : glType.trim();
    }

    public String getSubjectType() {
        return subjectType;
    }

    public void setSubjectType(String subjectType) {
        this.subjectType = subjectType == null ? null : subjectType.trim();
    }

    public String getBalanceWay() {
        return balanceWay;
    }

    public void setBalanceWay(String balanceWay) {
        this.balanceWay = balanceWay == null ? null : balanceWay.trim();
    }

    public String getSubjectStatus() {
        return subjectStatus;
    }

    public void setSubjectStatus(String subjectStatus) {
        this.subjectStatus = subjectStatus == null ? null : subjectStatus.trim();
    }

    public String getSubjectLevel() {
        return subjectLevel;
    }

    public void setSubjectLevel(String subjectLevel) {
        this.subjectLevel = subjectLevel == null ? null : subjectLevel.trim();
    }

    public String getManualAccount() {
        return manualAccount;
    }

    public void setManualAccount(String manualAccount) {
        this.manualAccount = manualAccount == null ? null : manualAccount.trim();
    }

    public String getSpecialBookkeeping() {
        return specialBookkeeping;
    }

    public void setSpecialBookkeeping(String specialBookkeeping) {
        this.specialBookkeeping = specialBookkeeping == null ? null : specialBookkeeping.trim();
    }

    public String getOdFacility() {
        return odFacility;
    }

    public void setOdFacility(String odFacility) {
        this.odFacility = odFacility == null ? null : odFacility.trim();
    }

    public String getRangeNo() {
        return rangeNo;
    }

    public void setRangeNo(String rangeNo) {
        this.rangeNo = rangeNo == null ? null : rangeNo.trim();
    }

    public String getRevalueRateType() {
        return revalueRateType;
    }

    public void setRevalueRateType(String revalueRateType) {
        this.revalueRateType = revalueRateType == null ? null : revalueRateType.trim();
    }

    public String getSystemId() {
        return systemId;
    }

    public void setSystemId(String systemId) {
        this.systemId = systemId == null ? null : systemId.trim();
    }

    public String getMeasurementAttr() {
        return measurementAttr;
    }

    public void setMeasurementAttr(String measurementAttr) {
        this.measurementAttr = measurementAttr == null ? null : measurementAttr.trim();
    }

    public String getItemSegregation() {
        return itemSegregation;
    }

    public void setItemSegregation(String itemSegregation) {
        this.itemSegregation = itemSegregation == null ? null : itemSegregation.trim();
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company == null ? null : company.trim();
    }

    public String getTranTimestamp() {
        return tranTimestamp;
    }

    public void setTranTimestamp(String tranTimestamp) {
        this.tranTimestamp = tranTimestamp == null ? null : tranTimestamp.trim();
    }

    public String getPayRec() {
        return payRec;
    }

    public void setPayRec(String payRec) {
        this.payRec = payRec == null ? null : payRec.trim();
    }

    public String getSubjectRemark() {
        return subjectRemark;
    }

    public void setSubjectRemark(String subjectRemark) {
        this.subjectRemark = subjectRemark == null ? null : subjectRemark.trim();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SofiGlSubjectPO other = (SofiGlSubjectPO) that;
        return (this.getSubjectCode() == null ? other.getSubjectCode() == null : this.getSubjectCode().equals(other.getSubjectCode()))
            && (this.getSubjectSetNo() == null ? other.getSubjectSetNo() == null : this.getSubjectSetNo().equals(other.getSubjectSetNo()))
            && (this.getSubjectDesc() == null ? other.getSubjectDesc() == null : this.getSubjectDesc().equals(other.getSubjectDesc()))
            && (this.getSubjectDescEn() == null ? other.getSubjectDescEn() == null : this.getSubjectDescEn().equals(other.getSubjectDescEn()))
            && (this.getControlSubject() == null ? other.getControlSubject() == null : this.getControlSubject().equals(other.getControlSubject()))
            && (this.getBsplType() == null ? other.getBsplType() == null : this.getBsplType().equals(other.getBsplType()))
            && (this.getGlType() == null ? other.getGlType() == null : this.getGlType().equals(other.getGlType()))
            && (this.getSubjectType() == null ? other.getSubjectType() == null : this.getSubjectType().equals(other.getSubjectType()))
            && (this.getBalanceWay() == null ? other.getBalanceWay() == null : this.getBalanceWay().equals(other.getBalanceWay()))
            && (this.getSubjectStatus() == null ? other.getSubjectStatus() == null : this.getSubjectStatus().equals(other.getSubjectStatus()))
            && (this.getSubjectLevel() == null ? other.getSubjectLevel() == null : this.getSubjectLevel().equals(other.getSubjectLevel()))
            && (this.getManualAccount() == null ? other.getManualAccount() == null : this.getManualAccount().equals(other.getManualAccount()))
            && (this.getSpecialBookkeeping() == null ? other.getSpecialBookkeeping() == null : this.getSpecialBookkeeping().equals(other.getSpecialBookkeeping()))
            && (this.getOdFacility() == null ? other.getOdFacility() == null : this.getOdFacility().equals(other.getOdFacility()))
            && (this.getRangeNo() == null ? other.getRangeNo() == null : this.getRangeNo().equals(other.getRangeNo()))
            && (this.getRevalueRateType() == null ? other.getRevalueRateType() == null : this.getRevalueRateType().equals(other.getRevalueRateType()))
            && (this.getSystemId() == null ? other.getSystemId() == null : this.getSystemId().equals(other.getSystemId()))
            && (this.getMeasurementAttr() == null ? other.getMeasurementAttr() == null : this.getMeasurementAttr().equals(other.getMeasurementAttr()))
            && (this.getItemSegregation() == null ? other.getItemSegregation() == null : this.getItemSegregation().equals(other.getItemSegregation()))
            && (this.getCompany() == null ? other.getCompany() == null : this.getCompany().equals(other.getCompany()))
            && (this.getTranTimestamp() == null ? other.getTranTimestamp() == null : this.getTranTimestamp().equals(other.getTranTimestamp()))
            && (this.getPayRec() == null ? other.getPayRec() == null : this.getPayRec().equals(other.getPayRec()))
            && (this.getSubjectRemark() == null ? other.getSubjectRemark() == null : this.getSubjectRemark().equals(other.getSubjectRemark()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getSubjectCode() == null) ? 0 : getSubjectCode().hashCode());
        result = prime * result + ((getSubjectSetNo() == null) ? 0 : getSubjectSetNo().hashCode());
        result = prime * result + ((getSubjectDesc() == null) ? 0 : getSubjectDesc().hashCode());
        result = prime * result + ((getSubjectDescEn() == null) ? 0 : getSubjectDescEn().hashCode());
        result = prime * result + ((getControlSubject() == null) ? 0 : getControlSubject().hashCode());
        result = prime * result + ((getBsplType() == null) ? 0 : getBsplType().hashCode());
        result = prime * result + ((getGlType() == null) ? 0 : getGlType().hashCode());
        result = prime * result + ((getSubjectType() == null) ? 0 : getSubjectType().hashCode());
        result = prime * result + ((getBalanceWay() == null) ? 0 : getBalanceWay().hashCode());
        result = prime * result + ((getSubjectStatus() == null) ? 0 : getSubjectStatus().hashCode());
        result = prime * result + ((getSubjectLevel() == null) ? 0 : getSubjectLevel().hashCode());
        result = prime * result + ((getManualAccount() == null) ? 0 : getManualAccount().hashCode());
        result = prime * result + ((getSpecialBookkeeping() == null) ? 0 : getSpecialBookkeeping().hashCode());
        result = prime * result + ((getOdFacility() == null) ? 0 : getOdFacility().hashCode());
        result = prime * result + ((getRangeNo() == null) ? 0 : getRangeNo().hashCode());
        result = prime * result + ((getRevalueRateType() == null) ? 0 : getRevalueRateType().hashCode());
        result = prime * result + ((getSystemId() == null) ? 0 : getSystemId().hashCode());
        result = prime * result + ((getMeasurementAttr() == null) ? 0 : getMeasurementAttr().hashCode());
        result = prime * result + ((getItemSegregation() == null) ? 0 : getItemSegregation().hashCode());
        result = prime * result + ((getCompany() == null) ? 0 : getCompany().hashCode());
        result = prime * result + ((getTranTimestamp() == null) ? 0 : getTranTimestamp().hashCode());
        result = prime * result + ((getPayRec() == null) ? 0 : getPayRec().hashCode());
        result = prime * result + ((getSubjectRemark() == null) ? 0 : getSubjectRemark().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", subjectDesc=").append(subjectDesc);
        sb.append(", subjectDescEn=").append(subjectDescEn);
        sb.append(", controlSubject=").append(controlSubject);
        sb.append(", bsplType=").append(bsplType);
        sb.append(", glType=").append(glType);
        sb.append(", subjectType=").append(subjectType);
        sb.append(", balanceWay=").append(balanceWay);
        sb.append(", subjectStatus=").append(subjectStatus);
        sb.append(", subjectLevel=").append(subjectLevel);
        sb.append(", manualAccount=").append(manualAccount);
        sb.append(", specialBookkeeping=").append(specialBookkeeping);
        sb.append(", odFacility=").append(odFacility);
        sb.append(", rangeNo=").append(rangeNo);
        sb.append(", revalueRateType=").append(revalueRateType);
        sb.append(", systemId=").append(systemId);
        sb.append(", measurementAttr=").append(measurementAttr);
        sb.append(", itemSegregation=").append(itemSegregation);
        sb.append(", company=").append(company);
        sb.append(", tranTimestamp=").append(tranTimestamp);
        sb.append(", payRec=").append(payRec);
        sb.append(", subjectRemark=").append(subjectRemark);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}