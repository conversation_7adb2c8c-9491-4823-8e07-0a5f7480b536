package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class SofiGlAcctHistPO implements Serializable {
    private Long id;

    private String processDay;

    private Long internalKey;

    private String branch;

    private String ccy;

    private String glCode;

    private String clientNo;

    private String profitCenter;

    private String seqNo;

    private Date acctOpenDate;

    private Date openTranDate;

    private Date acctCloseDate;

    private String acctCloseReason;

    private String glAcctNo;

    private String acctStatus;

    private BigDecimal actualBal;

    private BigDecimal drActualBal;

    private BigDecimal crActualBal;

    private Integer ctdDays;

    private Integer mtdDays;

    private Integer ytdDays;

    private BigDecimal mtdBal;

    private BigDecimal ytdBal;

    private BigDecimal aggBalCtd;

    private BigDecimal aggBalMtd;

    private BigDecimal aggBalYtd;

    private String periodNo;

    private String userId;

    private String manualAccount;

    private String odFacility;

    private Date lastChangeDate;

    private String acctName;

    private String company;

    private Date backupDate;

    private String tranTimestamp;

    private String systemId;

    private String groupClient;

    private BigDecimal drTranAmt;

    private BigDecimal crTranAmt;

    private BigDecimal lastActualBal;

    private BigDecimal lastDrActualBal;

    private BigDecimal lastCrActualBal;

    private Long objectVersionNumber;

    private Date creationDate;

    private String createdBy;

    private Date lastModifyDate;

    private String lastModifiedBy;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getProcessDay() {
        return processDay;
    }

    public void setProcessDay(String processDay) {
        this.processDay = processDay == null ? null : processDay.trim();
    }

    public Long getInternalKey() {
        return internalKey;
    }

    public void setInternalKey(Long internalKey) {
        this.internalKey = internalKey;
    }

    public String getBranch() {
        return branch;
    }

    public void setBranch(String branch) {
        this.branch = branch == null ? null : branch.trim();
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy == null ? null : ccy.trim();
    }

    public String getGlCode() {
        return glCode;
    }

    public void setGlCode(String glCode) {
        this.glCode = glCode == null ? null : glCode.trim();
    }

    public String getClientNo() {
        return clientNo;
    }

    public void setClientNo(String clientNo) {
        this.clientNo = clientNo == null ? null : clientNo.trim();
    }

    public String getProfitCenter() {
        return profitCenter;
    }

    public void setProfitCenter(String profitCenter) {
        this.profitCenter = profitCenter == null ? null : profitCenter.trim();
    }

    public String getSeqNo() {
        return seqNo;
    }

    public void setSeqNo(String seqNo) {
        this.seqNo = seqNo == null ? null : seqNo.trim();
    }

    public Date getAcctOpenDate() {
        return acctOpenDate;
    }

    public void setAcctOpenDate(Date acctOpenDate) {
        this.acctOpenDate = acctOpenDate;
    }

    public Date getOpenTranDate() {
        return openTranDate;
    }

    public void setOpenTranDate(Date openTranDate) {
        this.openTranDate = openTranDate;
    }

    public Date getAcctCloseDate() {
        return acctCloseDate;
    }

    public void setAcctCloseDate(Date acctCloseDate) {
        this.acctCloseDate = acctCloseDate;
    }

    public String getAcctCloseReason() {
        return acctCloseReason;
    }

    public void setAcctCloseReason(String acctCloseReason) {
        this.acctCloseReason = acctCloseReason == null ? null : acctCloseReason.trim();
    }

    public String getGlAcctNo() {
        return glAcctNo;
    }

    public void setGlAcctNo(String glAcctNo) {
        this.glAcctNo = glAcctNo == null ? null : glAcctNo.trim();
    }

    public String getAcctStatus() {
        return acctStatus;
    }

    public void setAcctStatus(String acctStatus) {
        this.acctStatus = acctStatus == null ? null : acctStatus.trim();
    }

    public BigDecimal getActualBal() {
        return actualBal;
    }

    public void setActualBal(BigDecimal actualBal) {
        this.actualBal = actualBal;
    }

    public BigDecimal getDrActualBal() {
        return drActualBal;
    }

    public void setDrActualBal(BigDecimal drActualBal) {
        this.drActualBal = drActualBal;
    }

    public BigDecimal getCrActualBal() {
        return crActualBal;
    }

    public void setCrActualBal(BigDecimal crActualBal) {
        this.crActualBal = crActualBal;
    }

    public Integer getCtdDays() {
        return ctdDays;
    }

    public void setCtdDays(Integer ctdDays) {
        this.ctdDays = ctdDays;
    }

    public Integer getMtdDays() {
        return mtdDays;
    }

    public void setMtdDays(Integer mtdDays) {
        this.mtdDays = mtdDays;
    }

    public Integer getYtdDays() {
        return ytdDays;
    }

    public void setYtdDays(Integer ytdDays) {
        this.ytdDays = ytdDays;
    }

    public BigDecimal getMtdBal() {
        return mtdBal;
    }

    public void setMtdBal(BigDecimal mtdBal) {
        this.mtdBal = mtdBal;
    }

    public BigDecimal getYtdBal() {
        return ytdBal;
    }

    public void setYtdBal(BigDecimal ytdBal) {
        this.ytdBal = ytdBal;
    }

    public BigDecimal getAggBalCtd() {
        return aggBalCtd;
    }

    public void setAggBalCtd(BigDecimal aggBalCtd) {
        this.aggBalCtd = aggBalCtd;
    }

    public BigDecimal getAggBalMtd() {
        return aggBalMtd;
    }

    public void setAggBalMtd(BigDecimal aggBalMtd) {
        this.aggBalMtd = aggBalMtd;
    }

    public BigDecimal getAggBalYtd() {
        return aggBalYtd;
    }

    public void setAggBalYtd(BigDecimal aggBalYtd) {
        this.aggBalYtd = aggBalYtd;
    }

    public String getPeriodNo() {
        return periodNo;
    }

    public void setPeriodNo(String periodNo) {
        this.periodNo = periodNo == null ? null : periodNo.trim();
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    public String getManualAccount() {
        return manualAccount;
    }

    public void setManualAccount(String manualAccount) {
        this.manualAccount = manualAccount == null ? null : manualAccount.trim();
    }

    public String getOdFacility() {
        return odFacility;
    }

    public void setOdFacility(String odFacility) {
        this.odFacility = odFacility == null ? null : odFacility.trim();
    }

    public Date getLastChangeDate() {
        return lastChangeDate;
    }

    public void setLastChangeDate(Date lastChangeDate) {
        this.lastChangeDate = lastChangeDate;
    }

    public String getAcctName() {
        return acctName;
    }

    public void setAcctName(String acctName) {
        this.acctName = acctName == null ? null : acctName.trim();
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company == null ? null : company.trim();
    }

    public Date getBackupDate() {
        return backupDate;
    }

    public void setBackupDate(Date backupDate) {
        this.backupDate = backupDate;
    }

    public String getTranTimestamp() {
        return tranTimestamp;
    }

    public void setTranTimestamp(String tranTimestamp) {
        this.tranTimestamp = tranTimestamp == null ? null : tranTimestamp.trim();
    }

    public String getSystemId() {
        return systemId;
    }

    public void setSystemId(String systemId) {
        this.systemId = systemId == null ? null : systemId.trim();
    }

    public String getGroupClient() {
        return groupClient;
    }

    public void setGroupClient(String groupClient) {
        this.groupClient = groupClient == null ? null : groupClient.trim();
    }

    public BigDecimal getDrTranAmt() {
        return drTranAmt;
    }

    public void setDrTranAmt(BigDecimal drTranAmt) {
        this.drTranAmt = drTranAmt;
    }

    public BigDecimal getCrTranAmt() {
        return crTranAmt;
    }

    public void setCrTranAmt(BigDecimal crTranAmt) {
        this.crTranAmt = crTranAmt;
    }

    public BigDecimal getLastActualBal() {
        return lastActualBal;
    }

    public void setLastActualBal(BigDecimal lastActualBal) {
        this.lastActualBal = lastActualBal;
    }

    public BigDecimal getLastDrActualBal() {
        return lastDrActualBal;
    }

    public void setLastDrActualBal(BigDecimal lastDrActualBal) {
        this.lastDrActualBal = lastDrActualBal;
    }

    public BigDecimal getLastCrActualBal() {
        return lastCrActualBal;
    }

    public void setLastCrActualBal(BigDecimal lastCrActualBal) {
        this.lastCrActualBal = lastCrActualBal;
    }

    public Long getObjectVersionNumber() {
        return objectVersionNumber;
    }

    public void setObjectVersionNumber(Long objectVersionNumber) {
        this.objectVersionNumber = objectVersionNumber;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getLastModifyDate() {
        return lastModifyDate;
    }

    public void setLastModifyDate(Date lastModifyDate) {
        this.lastModifyDate = lastModifyDate;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy == null ? null : lastModifiedBy.trim();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SofiGlAcctHistPO other = (SofiGlAcctHistPO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getProcessDay() == null ? other.getProcessDay() == null : this.getProcessDay().equals(other.getProcessDay()))
            && (this.getInternalKey() == null ? other.getInternalKey() == null : this.getInternalKey().equals(other.getInternalKey()))
            && (this.getBranch() == null ? other.getBranch() == null : this.getBranch().equals(other.getBranch()))
            && (this.getCcy() == null ? other.getCcy() == null : this.getCcy().equals(other.getCcy()))
            && (this.getGlCode() == null ? other.getGlCode() == null : this.getGlCode().equals(other.getGlCode()))
            && (this.getClientNo() == null ? other.getClientNo() == null : this.getClientNo().equals(other.getClientNo()))
            && (this.getProfitCenter() == null ? other.getProfitCenter() == null : this.getProfitCenter().equals(other.getProfitCenter()))
            && (this.getSeqNo() == null ? other.getSeqNo() == null : this.getSeqNo().equals(other.getSeqNo()))
            && (this.getAcctOpenDate() == null ? other.getAcctOpenDate() == null : this.getAcctOpenDate().equals(other.getAcctOpenDate()))
            && (this.getOpenTranDate() == null ? other.getOpenTranDate() == null : this.getOpenTranDate().equals(other.getOpenTranDate()))
            && (this.getAcctCloseDate() == null ? other.getAcctCloseDate() == null : this.getAcctCloseDate().equals(other.getAcctCloseDate()))
            && (this.getAcctCloseReason() == null ? other.getAcctCloseReason() == null : this.getAcctCloseReason().equals(other.getAcctCloseReason()))
            && (this.getGlAcctNo() == null ? other.getGlAcctNo() == null : this.getGlAcctNo().equals(other.getGlAcctNo()))
            && (this.getAcctStatus() == null ? other.getAcctStatus() == null : this.getAcctStatus().equals(other.getAcctStatus()))
            && (this.getActualBal() == null ? other.getActualBal() == null : this.getActualBal().equals(other.getActualBal()))
            && (this.getDrActualBal() == null ? other.getDrActualBal() == null : this.getDrActualBal().equals(other.getDrActualBal()))
            && (this.getCrActualBal() == null ? other.getCrActualBal() == null : this.getCrActualBal().equals(other.getCrActualBal()))
            && (this.getCtdDays() == null ? other.getCtdDays() == null : this.getCtdDays().equals(other.getCtdDays()))
            && (this.getMtdDays() == null ? other.getMtdDays() == null : this.getMtdDays().equals(other.getMtdDays()))
            && (this.getYtdDays() == null ? other.getYtdDays() == null : this.getYtdDays().equals(other.getYtdDays()))
            && (this.getMtdBal() == null ? other.getMtdBal() == null : this.getMtdBal().equals(other.getMtdBal()))
            && (this.getYtdBal() == null ? other.getYtdBal() == null : this.getYtdBal().equals(other.getYtdBal()))
            && (this.getAggBalCtd() == null ? other.getAggBalCtd() == null : this.getAggBalCtd().equals(other.getAggBalCtd()))
            && (this.getAggBalMtd() == null ? other.getAggBalMtd() == null : this.getAggBalMtd().equals(other.getAggBalMtd()))
            && (this.getAggBalYtd() == null ? other.getAggBalYtd() == null : this.getAggBalYtd().equals(other.getAggBalYtd()))
            && (this.getPeriodNo() == null ? other.getPeriodNo() == null : this.getPeriodNo().equals(other.getPeriodNo()))
            && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getManualAccount() == null ? other.getManualAccount() == null : this.getManualAccount().equals(other.getManualAccount()))
            && (this.getOdFacility() == null ? other.getOdFacility() == null : this.getOdFacility().equals(other.getOdFacility()))
            && (this.getLastChangeDate() == null ? other.getLastChangeDate() == null : this.getLastChangeDate().equals(other.getLastChangeDate()))
            && (this.getAcctName() == null ? other.getAcctName() == null : this.getAcctName().equals(other.getAcctName()))
            && (this.getCompany() == null ? other.getCompany() == null : this.getCompany().equals(other.getCompany()))
            && (this.getBackupDate() == null ? other.getBackupDate() == null : this.getBackupDate().equals(other.getBackupDate()))
            && (this.getTranTimestamp() == null ? other.getTranTimestamp() == null : this.getTranTimestamp().equals(other.getTranTimestamp()))
            && (this.getSystemId() == null ? other.getSystemId() == null : this.getSystemId().equals(other.getSystemId()))
            && (this.getGroupClient() == null ? other.getGroupClient() == null : this.getGroupClient().equals(other.getGroupClient()))
            && (this.getDrTranAmt() == null ? other.getDrTranAmt() == null : this.getDrTranAmt().equals(other.getDrTranAmt()))
            && (this.getCrTranAmt() == null ? other.getCrTranAmt() == null : this.getCrTranAmt().equals(other.getCrTranAmt()))
            && (this.getLastActualBal() == null ? other.getLastActualBal() == null : this.getLastActualBal().equals(other.getLastActualBal()))
            && (this.getLastDrActualBal() == null ? other.getLastDrActualBal() == null : this.getLastDrActualBal().equals(other.getLastDrActualBal()))
            && (this.getLastCrActualBal() == null ? other.getLastCrActualBal() == null : this.getLastCrActualBal().equals(other.getLastCrActualBal()))
            && (this.getObjectVersionNumber() == null ? other.getObjectVersionNumber() == null : this.getObjectVersionNumber().equals(other.getObjectVersionNumber()))
            && (this.getCreationDate() == null ? other.getCreationDate() == null : this.getCreationDate().equals(other.getCreationDate()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getLastModifyDate() == null ? other.getLastModifyDate() == null : this.getLastModifyDate().equals(other.getLastModifyDate()))
            && (this.getLastModifiedBy() == null ? other.getLastModifiedBy() == null : this.getLastModifiedBy().equals(other.getLastModifiedBy()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getProcessDay() == null) ? 0 : getProcessDay().hashCode());
        result = prime * result + ((getInternalKey() == null) ? 0 : getInternalKey().hashCode());
        result = prime * result + ((getBranch() == null) ? 0 : getBranch().hashCode());
        result = prime * result + ((getCcy() == null) ? 0 : getCcy().hashCode());
        result = prime * result + ((getGlCode() == null) ? 0 : getGlCode().hashCode());
        result = prime * result + ((getClientNo() == null) ? 0 : getClientNo().hashCode());
        result = prime * result + ((getProfitCenter() == null) ? 0 : getProfitCenter().hashCode());
        result = prime * result + ((getSeqNo() == null) ? 0 : getSeqNo().hashCode());
        result = prime * result + ((getAcctOpenDate() == null) ? 0 : getAcctOpenDate().hashCode());
        result = prime * result + ((getOpenTranDate() == null) ? 0 : getOpenTranDate().hashCode());
        result = prime * result + ((getAcctCloseDate() == null) ? 0 : getAcctCloseDate().hashCode());
        result = prime * result + ((getAcctCloseReason() == null) ? 0 : getAcctCloseReason().hashCode());
        result = prime * result + ((getGlAcctNo() == null) ? 0 : getGlAcctNo().hashCode());
        result = prime * result + ((getAcctStatus() == null) ? 0 : getAcctStatus().hashCode());
        result = prime * result + ((getActualBal() == null) ? 0 : getActualBal().hashCode());
        result = prime * result + ((getDrActualBal() == null) ? 0 : getDrActualBal().hashCode());
        result = prime * result + ((getCrActualBal() == null) ? 0 : getCrActualBal().hashCode());
        result = prime * result + ((getCtdDays() == null) ? 0 : getCtdDays().hashCode());
        result = prime * result + ((getMtdDays() == null) ? 0 : getMtdDays().hashCode());
        result = prime * result + ((getYtdDays() == null) ? 0 : getYtdDays().hashCode());
        result = prime * result + ((getMtdBal() == null) ? 0 : getMtdBal().hashCode());
        result = prime * result + ((getYtdBal() == null) ? 0 : getYtdBal().hashCode());
        result = prime * result + ((getAggBalCtd() == null) ? 0 : getAggBalCtd().hashCode());
        result = prime * result + ((getAggBalMtd() == null) ? 0 : getAggBalMtd().hashCode());
        result = prime * result + ((getAggBalYtd() == null) ? 0 : getAggBalYtd().hashCode());
        result = prime * result + ((getPeriodNo() == null) ? 0 : getPeriodNo().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getManualAccount() == null) ? 0 : getManualAccount().hashCode());
        result = prime * result + ((getOdFacility() == null) ? 0 : getOdFacility().hashCode());
        result = prime * result + ((getLastChangeDate() == null) ? 0 : getLastChangeDate().hashCode());
        result = prime * result + ((getAcctName() == null) ? 0 : getAcctName().hashCode());
        result = prime * result + ((getCompany() == null) ? 0 : getCompany().hashCode());
        result = prime * result + ((getBackupDate() == null) ? 0 : getBackupDate().hashCode());
        result = prime * result + ((getTranTimestamp() == null) ? 0 : getTranTimestamp().hashCode());
        result = prime * result + ((getSystemId() == null) ? 0 : getSystemId().hashCode());
        result = prime * result + ((getGroupClient() == null) ? 0 : getGroupClient().hashCode());
        result = prime * result + ((getDrTranAmt() == null) ? 0 : getDrTranAmt().hashCode());
        result = prime * result + ((getCrTranAmt() == null) ? 0 : getCrTranAmt().hashCode());
        result = prime * result + ((getLastActualBal() == null) ? 0 : getLastActualBal().hashCode());
        result = prime * result + ((getLastDrActualBal() == null) ? 0 : getLastDrActualBal().hashCode());
        result = prime * result + ((getLastCrActualBal() == null) ? 0 : getLastCrActualBal().hashCode());
        result = prime * result + ((getObjectVersionNumber() == null) ? 0 : getObjectVersionNumber().hashCode());
        result = prime * result + ((getCreationDate() == null) ? 0 : getCreationDate().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getLastModifyDate() == null) ? 0 : getLastModifyDate().hashCode());
        result = prime * result + ((getLastModifiedBy() == null) ? 0 : getLastModifiedBy().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", processDay=").append(processDay);
        sb.append(", internalKey=").append(internalKey);
        sb.append(", branch=").append(branch);
        sb.append(", ccy=").append(ccy);
        sb.append(", glCode=").append(glCode);
        sb.append(", clientNo=").append(clientNo);
        sb.append(", profitCenter=").append(profitCenter);
        sb.append(", seqNo=").append(seqNo);
        sb.append(", acctOpenDate=").append(acctOpenDate);
        sb.append(", openTranDate=").append(openTranDate);
        sb.append(", acctCloseDate=").append(acctCloseDate);
        sb.append(", acctCloseReason=").append(acctCloseReason);
        sb.append(", glAcctNo=").append(glAcctNo);
        sb.append(", acctStatus=").append(acctStatus);
        sb.append(", actualBal=").append(actualBal);
        sb.append(", drActualBal=").append(drActualBal);
        sb.append(", crActualBal=").append(crActualBal);
        sb.append(", ctdDays=").append(ctdDays);
        sb.append(", mtdDays=").append(mtdDays);
        sb.append(", ytdDays=").append(ytdDays);
        sb.append(", mtdBal=").append(mtdBal);
        sb.append(", ytdBal=").append(ytdBal);
        sb.append(", aggBalCtd=").append(aggBalCtd);
        sb.append(", aggBalMtd=").append(aggBalMtd);
        sb.append(", aggBalYtd=").append(aggBalYtd);
        sb.append(", periodNo=").append(periodNo);
        sb.append(", userId=").append(userId);
        sb.append(", manualAccount=").append(manualAccount);
        sb.append(", odFacility=").append(odFacility);
        sb.append(", lastChangeDate=").append(lastChangeDate);
        sb.append(", acctName=").append(acctName);
        sb.append(", company=").append(company);
        sb.append(", backupDate=").append(backupDate);
        sb.append(", tranTimestamp=").append(tranTimestamp);
        sb.append(", systemId=").append(systemId);
        sb.append(", groupClient=").append(groupClient);
        sb.append(", drTranAmt=").append(drTranAmt);
        sb.append(", crTranAmt=").append(crTranAmt);
        sb.append(", lastActualBal=").append(lastActualBal);
        sb.append(", lastDrActualBal=").append(lastDrActualBal);
        sb.append(", lastCrActualBal=").append(lastCrActualBal);
        sb.append(", objectVersionNumber=").append(objectVersionNumber);
        sb.append(", creationDate=").append(creationDate);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", lastModifyDate=").append(lastModifyDate);
        sb.append(", lastModifiedBy=").append(lastModifiedBy);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}