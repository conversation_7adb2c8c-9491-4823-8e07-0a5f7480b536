package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer;

import com.xiaoju.corebanking.erp.adaptor.common.lock.LockStorage;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.CargosAndAbonosDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiEmailDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfacePO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfacePOMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface SofiGlInterfaceCustomerMapper extends SofiGlInterfacePOMapper, LockStorage {
    int updateDetailPolizaIdSelective(@Param("id") Long id,@Param("polizaId") Long polizaId, @Param("detallePolizaId") Long detallePolizaId, @Param("version") Long version, @Param("processStatus") String processStatus, @Param("sofiGlInterfacePO") SofiGlInterfacePO sofiGlInterfacePO);

    int updatePolizaIdSelective(@Param("polizaId") Long polizaId, @Param("sofiGlInterfacePO") SofiGlInterfacePO sofiGlInterfacePO);

    int countByExample(@Param("processDay") String processDay, @Param("status") String status);

    int batchInsert(@Param("list") List<SofiGlInterfacePO> list);


    List<SofiGlInterfacePO> findByIndexFields(@Param("processDay") String processDay,
                                              @Param("fileName") String fileName,
                                              @Param("polizaId") Long polizaId,
                                              @Param("detallePolizaId") Long detallePolizaId);


    int deleteByIndexFields(@Param("processDay") String processDay,
                            @Param("fileName") String fileName,
                            @Param("polizaId") Long polizaId,
                            @Param("detallePolizaId") Long detallePolizaId);

    List<SofiGlInterfacePO> queryByPolizaId(@Param("polizaIds") List<Long> polizaIds);

    List<SofiGlInterfacePO> findByBatchIndexFields(@Param("processDay") String processDay,
                                                   @Param("fileName") String fileName,
                                                   @Param("indexKeys") List<String> indexKeys);

    int deleteByBatchIndexFields(@Param("processDay") String processDay,
                                 @Param("fileName") String fileName,
                                 @Param("indexKeys") List<String> indexKeys);

    List<Long> queryGroupIdByProcessStatus(@Param("processDay") String processDay);

    List<SofiGlInterfacePO> queryGLInterfaceData(@Param("sofiGlInterfacePO") SofiGlInterfacePO sofiGlInterfacePO);

    CargosAndAbonosDO selectCargosAndAbonosDO(@Param("polizaId") Long polizaId);

    List<SofiEmailDO> selectConutInterfaceEmailData(@Param("processDay") String processDay);

    List<Map<String, Object>> selectGroupByPolizaId(@Param("processDay") String processDay,
                                                    @Param("status") String status);

    List<SofiGlInterfacePO> selectSofiGlInterfaceByGroupIdAndProcessDay(@Param("groupId") Long groupId, @Param("processDay") String processDay);
}