package com.xiaoju.corebanking.erp.adaptor.repository.domain;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/8/7 15:23
 */
@Data
@Slf4j
public class ParseStatistics {
    private int totalLines = 1;
    private int validLines = 0;
    private int skippedLines = 0;

    public void incrementTotalLines() {
        totalLines++;
    }

    public void incrementValidLines() {
        validLines++;
    }

    public void incrementSkippedLines() {
        skippedLines++;
    }

    public void logSummary() {
        log.info("文件解析完成: 总行数={}, 有效数据行={}, 跳过行数={}", totalLines, validLines, skippedLines);
    }
}
