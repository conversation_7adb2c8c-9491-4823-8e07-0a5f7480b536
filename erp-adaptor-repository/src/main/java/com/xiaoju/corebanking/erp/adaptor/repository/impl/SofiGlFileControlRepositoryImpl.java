package com.xiaoju.corebanking.erp.adaptor.repository.impl;

import com.xiaoju.corebanking.erp.adaptor.repository.SofiGlFileControlRepository;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlFileControlDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlFileControlPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlFileControlPOExample;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SofiGlFileControlCustomerMapper;
import com.xiaoju.corebanking.erp.adaptor.repository.converter.SofiGlFileControlModelConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description GL文件控制表数据库操作类
 * @date 2025/5/14 15:25
 */
@Service
@Slf4j
public class SofiGlFileControlRepositoryImpl implements SofiGlFileControlRepository {

    @Resource
    private SofiGlFileControlCustomerMapper sofiGlFileControlCustomerMapper;

    @Resource
    private SofiGlFileControlModelConverter slfiGlFileControlModelConverter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertSelective(SofiGlFileControlDO sofiGlFileControlDO) {
        SofiGlFileControlPO convert = slfiGlFileControlModelConverter.convert(sofiGlFileControlDO);

        List<SofiGlFileControlDO> existingRecords = findByIndexFields(
                sofiGlFileControlDO.getProcessDay(),
                sofiGlFileControlDO.getFileName(),
                sofiGlFileControlDO.getSystemCode()
        );

        if (!CollectionUtils.isEmpty(existingRecords)) {
            int deletedCount = deleteByIndexFields(
                    sofiGlFileControlDO.getProcessDay(),
                    sofiGlFileControlDO.getFileName(),
                    sofiGlFileControlDO.getSystemCode()
            );
            log.info("删除已存在的文件控制记录：processDay={}, fileName={}, systemCode={}, 删除条数={}",
                    sofiGlFileControlDO.getProcessDay(),
                    sofiGlFileControlDO.getFileName(),
                    sofiGlFileControlDO.getSystemCode(),
                    deletedCount
            );
        }
        sofiGlFileControlCustomerMapper.insertSelective(convert);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByExampleSelective(SofiGlFileControlDO sourceDO) {
        SofiGlFileControlPO convert = slfiGlFileControlModelConverter.convert(sourceDO);
        SofiGlFileControlPOExample example = new SofiGlFileControlPOExample();
        example.createCriteria()
                .andProcessDayEqualTo(sourceDO.getProcessDay())
                .andFileNameEqualTo(sourceDO.getFileName())
                .andSystemCodeEqualTo(sourceDO.getSystemCode());

        sofiGlFileControlCustomerMapper.updateByExampleSelective(convert, example);
    }


    @Override
    public List<SofiGlFileControlDO> findByIndexFields(String processDay, String fileName, String systemCode) {
        List<SofiGlFileControlPO> poList = sofiGlFileControlCustomerMapper.findByIndexFields(processDay, fileName, systemCode);

        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptyList();
        }

        return poList.stream()
                .map(slfiGlFileControlModelConverter::convert)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByIndexFields(String processDay, String fileName, String systemCode) {
        return sofiGlFileControlCustomerMapper.deleteByIndexFields(processDay, fileName, systemCode);
    }
}
