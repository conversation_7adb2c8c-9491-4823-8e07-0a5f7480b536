package com.xiaoju.corebanking.erp.adaptor.repository;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.CuxTwoTabHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabHeadersPO;

import java.util.List;

public interface TwoTabHeaderRepository {
    CuxTwoTabHeadersPO selectByFormCode(String formCode);
    CuxTwoTabHeadersPO insertByExample(CuxTwoTabHeaderDO header);
    List<CuxTwoTabHeadersPO> selectAll();
    void updateByExample(CuxTwoTabHeaderDO header);
    void saveHeader(CuxTwoTabHeaderDO header);
}
