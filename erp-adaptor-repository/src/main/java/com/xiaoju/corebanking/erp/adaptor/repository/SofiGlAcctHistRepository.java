package com.xiaoju.corebanking.erp.adaptor.repository;

import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlAcctHistDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SummaryResultDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/16
 */
public interface SofiGlAcctHistRepository {

    int syncSofiGlAcctHistData(List<SofiGlAcctHistDO> sofiGlAcctHistList);
    List<SummaryResultDO> selectAcctHisSummary(String processDay);

} 