package com.xiaoju.corebanking.erp.adaptor.repository.converter;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.utils.CommonUtils;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlInterfaceHeaderDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHeaderPO;
import org.springframework.stereotype.Component;

@Component
public class SofiGlInterfaceHeaderModelConverter {

    public SofiGlInterfaceHeaderDO convert(SofiGlInterfaceHeaderPO sofiGlInterfacePO) {
        SofiGlInterfaceHeaderDO sofiGlInterfaceDO = new SofiGlInterfaceHeaderDO();
        sofiGlInterfaceDO.setId(sofiGlInterfacePO.getId());
        sofiGlInterfaceDO.setSystemCode(sofiGlInterfacePO.getSystemCode());
        sofiGlInterfaceDO.setProcessDay(sofiGlInterfacePO.getProcessDay());
        sofiGlInterfaceDO.setBatchId(sofiGlInterfacePO.getBatchId());
        sofiGlInterfaceDO.setExternalReference(sofiGlInterfacePO.getExternalReference());
        sofiGlInterfaceDO.setJournalCount(sofiGlInterfacePO.getJournalCount());
        sofiGlInterfaceDO.setBatchId(sofiGlInterfacePO.getBatchId());
        sofiGlInterfaceDO.setProcessStatus(ProcessStatusEnum.getByCode(sofiGlInterfacePO.getProcessStatus()));
        sofiGlInterfaceDO.setProcessMessage(sofiGlInterfacePO.getProcessMessage());
        sofiGlInterfaceDO.setObjectVersionNumber(sofiGlInterfacePO.getObjectVersionNumber());
        sofiGlInterfaceDO.setCreationDate(sofiGlInterfacePO.getCreationDate());
        sofiGlInterfaceDO.setCreatedBy(sofiGlInterfacePO.getCreatedBy());
        sofiGlInterfaceDO.setLastModifyDate(sofiGlInterfacePO.getLastModifyDate());
        sofiGlInterfaceDO.setLastModifiedBy(sofiGlInterfacePO.getLastModifiedBy());
        sofiGlInterfaceDO.setGroupId(sofiGlInterfacePO.getGroupId());
        return sofiGlInterfaceDO;
    }

    public SofiGlInterfaceHeaderPO convert(SofiGlInterfaceHeaderDO sofiGlInterfaceDO) {
        SofiGlInterfaceHeaderPO sofiGlInterfacePO = new SofiGlInterfaceHeaderPO();
        sofiGlInterfacePO.setSystemCode(sofiGlInterfaceDO.getSystemCode());
        sofiGlInterfacePO.setProcessDay(sofiGlInterfaceDO.getProcessDay());
        sofiGlInterfacePO.setBatchId(sofiGlInterfaceDO.getBatchId());
        sofiGlInterfacePO.setExternalReference(sofiGlInterfaceDO.getExternalReference());
        sofiGlInterfacePO.setJournalCount(sofiGlInterfaceDO.getJournalCount());
        sofiGlInterfacePO.setProcessStatus(sofiGlInterfaceDO.getProcessStatus().getCode());
        sofiGlInterfacePO.setProcessMessage(sofiGlInterfaceDO.getProcessMessage());
        sofiGlInterfacePO.setObjectVersionNumber(sofiGlInterfaceDO.getObjectVersionNumber());
        sofiGlInterfacePO.setCreationDate(sofiGlInterfaceDO.getCreationDate());
        sofiGlInterfacePO.setCreatedBy(sofiGlInterfaceDO.getCreatedBy());
        sofiGlInterfacePO.setLastModifyDate(sofiGlInterfaceDO.getLastModifyDate());
        sofiGlInterfacePO.setLastModifiedBy(sofiGlInterfaceDO.getLastModifiedBy());
        sofiGlInterfacePO.setGroupId(sofiGlInterfaceDO.getGroupId());
        return sofiGlInterfacePO;
    }


    /**
     * DO -> DO
     */
    public void copyIgnoreNullValue(SofiGlInterfaceHeaderDO source, SofiGlInterfaceHeaderDO target) {
        CommonUtils.copyIgnoreNull(source::getId, target::setId);
        CommonUtils.copyIgnoreNull(source::getProcessDay, target::setProcessDay);
        CommonUtils.copyIgnoreNull(source::getBatchId, target::setBatchId);
        CommonUtils.copyIgnoreNull(source::getExternalReference, target::setExternalReference);
        CommonUtils.copyIgnoreNull(source::getJournalCount, target::setJournalCount);
        CommonUtils.copyIgnoreNull(source::getProcessStatus, target::setProcessStatus);
        CommonUtils.copyIgnoreNull(source::getProcessMessage, target::setProcessMessage);
        CommonUtils.copyIgnoreNull(source::getObjectVersionNumber, target::setObjectVersionNumber);
        CommonUtils.copyIgnoreNull(source::getCreationDate, target::setCreationDate);
        CommonUtils.copyIgnoreNull(source::getCreatedBy, target::setCreatedBy);
        CommonUtils.copyIgnoreNull(source::getLastModifyDate, target::setLastModifyDate);
        CommonUtils.copyIgnoreNull(source::getLastModifiedBy, target::setLastModifiedBy);
    }

}
