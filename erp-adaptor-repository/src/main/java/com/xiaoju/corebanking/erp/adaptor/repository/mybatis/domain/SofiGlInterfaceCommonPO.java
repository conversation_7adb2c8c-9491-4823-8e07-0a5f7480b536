package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
@Data
public class SofiGlInterfaceCommonPO implements Serializable {
    private Long id;

    private String systemCode;

    private String processDay;

    private Date accountingDate;

    private String periodName;

    private Long ledgerId;

    private String ledgerName;

    private String currencyCode;

    private String journalCategory;

    private String journalSource;

    private String journalSourceName;

    private String reference1;

    private String reference2;

    private String reference3;

    private String reference4;

    private String reference5;

    private String reference6;

    private String reference7;

    private String reference8;

    private String reference9;

    private String reference10;

    private String reference21;

    private String reference22;

    private String reference23;

    private String reference24;

    private String reference25;

    private String reference26;

    private String reference27;

    private String reference28;

    private String reference29;

    private String reference30;

    private String segment1;

    private String segment2;

    private String segment3;

    private String segment4;

    private String segment5;

    private String segment6;

    private String segment7;

    private String segment8;

    private String segment9;

    private String segment10;

    private BigDecimal enteredDr;

    private BigDecimal enteredCr;

    private BigDecimal accountedDr;

    private BigDecimal accountedCr;

    private Long groupId;

    private Date currencyConversionDate;

    private BigDecimal currencyConversionRate;

    private String currencyConversionType;

    private String attributeCategory;

    private String headerAttribute1;

    private String headerAttribute2;

    private String headerAttribute3;

    private String headerAttribute4;

    private String headerAttribute5;

    private String headerAttribute6;

    private String headerAttribute7;

    private String headerAttribute8;

    private String headerAttribute9;

    private String headerAttribute10;

    private String headerAttribute11;

    private String headerAttribute12;

    private String headerAttribute13;

    private String headerAttribute14;

    private String headerAttribute15;

    private String attributeCategory3;

    private String lineAttribute1;

    private String lineAttribute2;

    private String lineAttribute3;

    private String lineAttribute4;

    private String lineAttribute5;

    private String lineAttribute6;

    private String lineAttribute7;

    private String lineAttribute8;

    private String lineAttribute9;

    private String lineAttribute10;

    private String lineAttribute11;

    private String lineAttribute12;

    private String lineAttribute13;

    private String lineAttribute14;

    private String lineAttribute15;

    private String lineAttribute16;

    private String lineAttribute17;

    private String lineAttribute18;

    private String lineAttribute19;

    private String lineAttribute20;

    private String processStatus;

    private String processMessage;

    private Long jeHeaderId;

    private String journalName;

    private Long jeLineNum;

    private Long documentId;

    private Long loadRequestId;

    private Long importRequestId;

    private Long objectVersionNumber;

    private Date creationDate;

    private String createdBy;

    private Date lastModifyDate;

    private String lastModifiedBy;

    private static final long serialVersionUID = 1L;

}