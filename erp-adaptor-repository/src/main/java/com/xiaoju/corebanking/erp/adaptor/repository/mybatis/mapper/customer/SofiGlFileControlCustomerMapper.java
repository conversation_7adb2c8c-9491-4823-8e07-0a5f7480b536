package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer;

import com.xiaoju.corebanking.erp.adaptor.common.lock.LockStorage;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlFileControlPO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlFileControlPOMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SofiGlFileControlCustomerMapper extends SofiGlFileControlPOMapper, LockStorage {
    int batchInsert(@Param("list") List<SofiGlFileControlPO> list);

    List<SofiGlFileControlPO> findByIndexFields(@Param("processDay") String processDay,
                                              @Param("fileName") String fileName,
                                              @Param("systemCode") String systemCode);

    int deleteByIndexFields(@Param("processDay") String processDay,
                            @Param("fileName") String fileName,
                            @Param("systemCode") String systemCode);
}