package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class SofiGlInterfaceHisPO implements Serializable {
    private Long id;

    private Long fileId;

    private String processDay;

    private String fileName;

    private String batchId;

    private Long detallePolizaId;

    private String sourceSys;

    private Long empresaId;

    private Long polizaId;

    private String fecha;

    private Long centroCostoId;

    private String cuentaCompleta;

    private Long instrumento;

    private Long monedaId;

    private BigDecimal cargos;

    private BigDecimal abonos;

    private String descripcion;

    private String referencia;

    private String procedimientoCont;

    private String tipoInstrumentoId;

    private String rfc;

    private BigDecimal totalFactura;

    private String folioUuid;

    private Long usuario;

    private String fechaActual;

    private String direccionIp;

    private String programaId;

    private Long sucursal;

    private Long numTransaccion;

    private Long ledgerId;

    private String currencyCode;

    private String journalCategory;

    private String journalSource;

    private String segment1;

    private String segment2;

    private String segment3;

    private String segment4;

    private String segment5;

    private String segment6;

    private String segment7;

    private String segment8;

    private String segment9;

    private String segment10;

    private Long groupId;

    private Date currencyConversionDate;

    private BigDecimal currencyConversionRate;

    private String userCurrencyConversionType;

    private String processStatus;

    private String processMessage;

    private Long jeHeaderId;

    private String journalName;

    private Long jeLineNum;

    private Long documentId;

    private Long loadRequestId;

    private Long importRequestId;

    private Long objectVersionNumber;

    private Date creationDate;

    private String createdBy;

    private Date lastModifyDate;

    private String lastModifiedBy;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getFileId() {
        return fileId;
    }

    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    public String getProcessDay() {
        return processDay;
    }

    public void setProcessDay(String processDay) {
        this.processDay = processDay == null ? null : processDay.trim();
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName == null ? null : fileName.trim();
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId == null ? null : batchId.trim();
    }

    public Long getDetallePolizaId() {
        return detallePolizaId;
    }

    public void setDetallePolizaId(Long detallePolizaId) {
        this.detallePolizaId = detallePolizaId;
    }

    public String getSourceSys() {
        return sourceSys;
    }

    public void setSourceSys(String sourceSys) {
        this.sourceSys = sourceSys == null ? null : sourceSys.trim();
    }

    public Long getEmpresaId() {
        return empresaId;
    }

    public void setEmpresaId(Long empresaId) {
        this.empresaId = empresaId;
    }

    public Long getPolizaId() {
        return polizaId;
    }

    public void setPolizaId(Long polizaId) {
        this.polizaId = polizaId;
    }

    public String getFecha() {
        return fecha;
    }

    public void setFecha(String fecha) {
        this.fecha = fecha == null ? null : fecha.trim();
    }

    public Long getCentroCostoId() {
        return centroCostoId;
    }

    public void setCentroCostoId(Long centroCostoId) {
        this.centroCostoId = centroCostoId;
    }

    public String getCuentaCompleta() {
        return cuentaCompleta;
    }

    public void setCuentaCompleta(String cuentaCompleta) {
        this.cuentaCompleta = cuentaCompleta == null ? null : cuentaCompleta.trim();
    }

    public Long getInstrumento() {
        return instrumento;
    }

    public void setInstrumento(Long instrumento) {
        this.instrumento = instrumento;
    }

    public Long getMonedaId() {
        return monedaId;
    }

    public void setMonedaId(Long monedaId) {
        this.monedaId = monedaId;
    }

    public BigDecimal getCargos() {
        return cargos;
    }

    public void setCargos(BigDecimal cargos) {
        this.cargos = cargos;
    }

    public BigDecimal getAbonos() {
        return abonos;
    }

    public void setAbonos(BigDecimal abonos) {
        this.abonos = abonos;
    }

    public String getDescripcion() {
        return descripcion;
    }

    public void setDescripcion(String descripcion) {
        this.descripcion = descripcion == null ? null : descripcion.trim();
    }

    public String getReferencia() {
        return referencia;
    }

    public void setReferencia(String referencia) {
        this.referencia = referencia == null ? null : referencia.trim();
    }

    public String getProcedimientoCont() {
        return procedimientoCont;
    }

    public void setProcedimientoCont(String procedimientoCont) {
        this.procedimientoCont = procedimientoCont == null ? null : procedimientoCont.trim();
    }

    public String getTipoInstrumentoId() {
        return tipoInstrumentoId;
    }

    public void setTipoInstrumentoId(String tipoInstrumentoId) {
        this.tipoInstrumentoId = tipoInstrumentoId == null ? null : tipoInstrumentoId.trim();
    }

    public String getRfc() {
        return rfc;
    }

    public void setRfc(String rfc) {
        this.rfc = rfc == null ? null : rfc.trim();
    }

    public BigDecimal getTotalFactura() {
        return totalFactura;
    }

    public void setTotalFactura(BigDecimal totalFactura) {
        this.totalFactura = totalFactura;
    }

    public String getFolioUuid() {
        return folioUuid;
    }

    public void setFolioUuid(String folioUuid) {
        this.folioUuid = folioUuid == null ? null : folioUuid.trim();
    }

    public Long getUsuario() {
        return usuario;
    }

    public void setUsuario(Long usuario) {
        this.usuario = usuario;
    }

    public String getFechaActual() {
        return fechaActual;
    }

    public void setFechaActual(String fechaActual) {
        this.fechaActual = fechaActual == null ? null : fechaActual.trim();
    }

    public String getDireccionIp() {
        return direccionIp;
    }

    public void setDireccionIp(String direccionIp) {
        this.direccionIp = direccionIp == null ? null : direccionIp.trim();
    }

    public String getProgramaId() {
        return programaId;
    }

    public void setProgramaId(String programaId) {
        this.programaId = programaId == null ? null : programaId.trim();
    }

    public Long getSucursal() {
        return sucursal;
    }

    public void setSucursal(Long sucursal) {
        this.sucursal = sucursal;
    }

    public Long getNumTransaccion() {
        return numTransaccion;
    }

    public void setNumTransaccion(Long numTransaccion) {
        this.numTransaccion = numTransaccion;
    }

    public Long getLedgerId() {
        return ledgerId;
    }

    public void setLedgerId(Long ledgerId) {
        this.ledgerId = ledgerId;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode == null ? null : currencyCode.trim();
    }

    public String getJournalCategory() {
        return journalCategory;
    }

    public void setJournalCategory(String journalCategory) {
        this.journalCategory = journalCategory == null ? null : journalCategory.trim();
    }

    public String getJournalSource() {
        return journalSource;
    }

    public void setJournalSource(String journalSource) {
        this.journalSource = journalSource == null ? null : journalSource.trim();
    }

    public String getSegment1() {
        return segment1;
    }

    public void setSegment1(String segment1) {
        this.segment1 = segment1 == null ? null : segment1.trim();
    }

    public String getSegment2() {
        return segment2;
    }

    public void setSegment2(String segment2) {
        this.segment2 = segment2 == null ? null : segment2.trim();
    }

    public String getSegment3() {
        return segment3;
    }

    public void setSegment3(String segment3) {
        this.segment3 = segment3 == null ? null : segment3.trim();
    }

    public String getSegment4() {
        return segment4;
    }

    public void setSegment4(String segment4) {
        this.segment4 = segment4 == null ? null : segment4.trim();
    }

    public String getSegment5() {
        return segment5;
    }

    public void setSegment5(String segment5) {
        this.segment5 = segment5 == null ? null : segment5.trim();
    }

    public String getSegment6() {
        return segment6;
    }

    public void setSegment6(String segment6) {
        this.segment6 = segment6 == null ? null : segment6.trim();
    }

    public String getSegment7() {
        return segment7;
    }

    public void setSegment7(String segment7) {
        this.segment7 = segment7 == null ? null : segment7.trim();
    }

    public String getSegment8() {
        return segment8;
    }

    public void setSegment8(String segment8) {
        this.segment8 = segment8 == null ? null : segment8.trim();
    }

    public String getSegment9() {
        return segment9;
    }

    public void setSegment9(String segment9) {
        this.segment9 = segment9 == null ? null : segment9.trim();
    }

    public String getSegment10() {
        return segment10;
    }

    public void setSegment10(String segment10) {
        this.segment10 = segment10 == null ? null : segment10.trim();
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Date getCurrencyConversionDate() {
        return currencyConversionDate;
    }

    public void setCurrencyConversionDate(Date currencyConversionDate) {
        this.currencyConversionDate = currencyConversionDate;
    }

    public BigDecimal getCurrencyConversionRate() {
        return currencyConversionRate;
    }

    public void setCurrencyConversionRate(BigDecimal currencyConversionRate) {
        this.currencyConversionRate = currencyConversionRate;
    }

    public String getUserCurrencyConversionType() {
        return userCurrencyConversionType;
    }

    public void setUserCurrencyConversionType(String userCurrencyConversionType) {
        this.userCurrencyConversionType = userCurrencyConversionType == null ? null : userCurrencyConversionType.trim();
    }

    public String getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(String processStatus) {
        this.processStatus = processStatus == null ? null : processStatus.trim();
    }

    public String getProcessMessage() {
        return processMessage;
    }

    public void setProcessMessage(String processMessage) {
        this.processMessage = processMessage == null ? null : processMessage.trim();
    }

    public Long getJeHeaderId() {
        return jeHeaderId;
    }

    public void setJeHeaderId(Long jeHeaderId) {
        this.jeHeaderId = jeHeaderId;
    }

    public String getJournalName() {
        return journalName;
    }

    public void setJournalName(String journalName) {
        this.journalName = journalName == null ? null : journalName.trim();
    }

    public Long getJeLineNum() {
        return jeLineNum;
    }

    public void setJeLineNum(Long jeLineNum) {
        this.jeLineNum = jeLineNum;
    }

    public Long getDocumentId() {
        return documentId;
    }

    public void setDocumentId(Long documentId) {
        this.documentId = documentId;
    }

    public Long getLoadRequestId() {
        return loadRequestId;
    }

    public void setLoadRequestId(Long loadRequestId) {
        this.loadRequestId = loadRequestId;
    }

    public Long getImportRequestId() {
        return importRequestId;
    }

    public void setImportRequestId(Long importRequestId) {
        this.importRequestId = importRequestId;
    }

    public Long getObjectVersionNumber() {
        return objectVersionNumber;
    }

    public void setObjectVersionNumber(Long objectVersionNumber) {
        this.objectVersionNumber = objectVersionNumber;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getLastModifyDate() {
        return lastModifyDate;
    }

    public void setLastModifyDate(Date lastModifyDate) {
        this.lastModifyDate = lastModifyDate;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy == null ? null : lastModifiedBy.trim();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SofiGlInterfaceHisPO other = (SofiGlInterfaceHisPO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getFileId() == null ? other.getFileId() == null : this.getFileId().equals(other.getFileId()))
            && (this.getProcessDay() == null ? other.getProcessDay() == null : this.getProcessDay().equals(other.getProcessDay()))
            && (this.getFileName() == null ? other.getFileName() == null : this.getFileName().equals(other.getFileName()))
            && (this.getBatchId() == null ? other.getBatchId() == null : this.getBatchId().equals(other.getBatchId()))
            && (this.getDetallePolizaId() == null ? other.getDetallePolizaId() == null : this.getDetallePolizaId().equals(other.getDetallePolizaId()))
            && (this.getSourceSys() == null ? other.getSourceSys() == null : this.getSourceSys().equals(other.getSourceSys()))
            && (this.getEmpresaId() == null ? other.getEmpresaId() == null : this.getEmpresaId().equals(other.getEmpresaId()))
            && (this.getPolizaId() == null ? other.getPolizaId() == null : this.getPolizaId().equals(other.getPolizaId()))
            && (this.getFecha() == null ? other.getFecha() == null : this.getFecha().equals(other.getFecha()))
            && (this.getCentroCostoId() == null ? other.getCentroCostoId() == null : this.getCentroCostoId().equals(other.getCentroCostoId()))
            && (this.getCuentaCompleta() == null ? other.getCuentaCompleta() == null : this.getCuentaCompleta().equals(other.getCuentaCompleta()))
            && (this.getInstrumento() == null ? other.getInstrumento() == null : this.getInstrumento().equals(other.getInstrumento()))
            && (this.getMonedaId() == null ? other.getMonedaId() == null : this.getMonedaId().equals(other.getMonedaId()))
            && (this.getCargos() == null ? other.getCargos() == null : this.getCargos().equals(other.getCargos()))
            && (this.getAbonos() == null ? other.getAbonos() == null : this.getAbonos().equals(other.getAbonos()))
            && (this.getDescripcion() == null ? other.getDescripcion() == null : this.getDescripcion().equals(other.getDescripcion()))
            && (this.getReferencia() == null ? other.getReferencia() == null : this.getReferencia().equals(other.getReferencia()))
            && (this.getProcedimientoCont() == null ? other.getProcedimientoCont() == null : this.getProcedimientoCont().equals(other.getProcedimientoCont()))
            && (this.getTipoInstrumentoId() == null ? other.getTipoInstrumentoId() == null : this.getTipoInstrumentoId().equals(other.getTipoInstrumentoId()))
            && (this.getRfc() == null ? other.getRfc() == null : this.getRfc().equals(other.getRfc()))
            && (this.getTotalFactura() == null ? other.getTotalFactura() == null : this.getTotalFactura().equals(other.getTotalFactura()))
            && (this.getFolioUuid() == null ? other.getFolioUuid() == null : this.getFolioUuid().equals(other.getFolioUuid()))
            && (this.getUsuario() == null ? other.getUsuario() == null : this.getUsuario().equals(other.getUsuario()))
            && (this.getFechaActual() == null ? other.getFechaActual() == null : this.getFechaActual().equals(other.getFechaActual()))
            && (this.getDireccionIp() == null ? other.getDireccionIp() == null : this.getDireccionIp().equals(other.getDireccionIp()))
            && (this.getProgramaId() == null ? other.getProgramaId() == null : this.getProgramaId().equals(other.getProgramaId()))
            && (this.getSucursal() == null ? other.getSucursal() == null : this.getSucursal().equals(other.getSucursal()))
            && (this.getNumTransaccion() == null ? other.getNumTransaccion() == null : this.getNumTransaccion().equals(other.getNumTransaccion()))
            && (this.getLedgerId() == null ? other.getLedgerId() == null : this.getLedgerId().equals(other.getLedgerId()))
            && (this.getCurrencyCode() == null ? other.getCurrencyCode() == null : this.getCurrencyCode().equals(other.getCurrencyCode()))
            && (this.getJournalCategory() == null ? other.getJournalCategory() == null : this.getJournalCategory().equals(other.getJournalCategory()))
            && (this.getJournalSource() == null ? other.getJournalSource() == null : this.getJournalSource().equals(other.getJournalSource()))
            && (this.getSegment1() == null ? other.getSegment1() == null : this.getSegment1().equals(other.getSegment1()))
            && (this.getSegment2() == null ? other.getSegment2() == null : this.getSegment2().equals(other.getSegment2()))
            && (this.getSegment3() == null ? other.getSegment3() == null : this.getSegment3().equals(other.getSegment3()))
            && (this.getSegment4() == null ? other.getSegment4() == null : this.getSegment4().equals(other.getSegment4()))
            && (this.getSegment5() == null ? other.getSegment5() == null : this.getSegment5().equals(other.getSegment5()))
            && (this.getSegment6() == null ? other.getSegment6() == null : this.getSegment6().equals(other.getSegment6()))
            && (this.getSegment7() == null ? other.getSegment7() == null : this.getSegment7().equals(other.getSegment7()))
            && (this.getSegment8() == null ? other.getSegment8() == null : this.getSegment8().equals(other.getSegment8()))
            && (this.getSegment9() == null ? other.getSegment9() == null : this.getSegment9().equals(other.getSegment9()))
            && (this.getSegment10() == null ? other.getSegment10() == null : this.getSegment10().equals(other.getSegment10()))
            && (this.getGroupId() == null ? other.getGroupId() == null : this.getGroupId().equals(other.getGroupId()))
            && (this.getCurrencyConversionDate() == null ? other.getCurrencyConversionDate() == null : this.getCurrencyConversionDate().equals(other.getCurrencyConversionDate()))
            && (this.getCurrencyConversionRate() == null ? other.getCurrencyConversionRate() == null : this.getCurrencyConversionRate().equals(other.getCurrencyConversionRate()))
            && (this.getUserCurrencyConversionType() == null ? other.getUserCurrencyConversionType() == null : this.getUserCurrencyConversionType().equals(other.getUserCurrencyConversionType()))
            && (this.getProcessStatus() == null ? other.getProcessStatus() == null : this.getProcessStatus().equals(other.getProcessStatus()))
            && (this.getProcessMessage() == null ? other.getProcessMessage() == null : this.getProcessMessage().equals(other.getProcessMessage()))
            && (this.getJeHeaderId() == null ? other.getJeHeaderId() == null : this.getJeHeaderId().equals(other.getJeHeaderId()))
            && (this.getJournalName() == null ? other.getJournalName() == null : this.getJournalName().equals(other.getJournalName()))
            && (this.getJeLineNum() == null ? other.getJeLineNum() == null : this.getJeLineNum().equals(other.getJeLineNum()))
            && (this.getDocumentId() == null ? other.getDocumentId() == null : this.getDocumentId().equals(other.getDocumentId()))
            && (this.getLoadRequestId() == null ? other.getLoadRequestId() == null : this.getLoadRequestId().equals(other.getLoadRequestId()))
            && (this.getImportRequestId() == null ? other.getImportRequestId() == null : this.getImportRequestId().equals(other.getImportRequestId()))
            && (this.getObjectVersionNumber() == null ? other.getObjectVersionNumber() == null : this.getObjectVersionNumber().equals(other.getObjectVersionNumber()))
            && (this.getCreationDate() == null ? other.getCreationDate() == null : this.getCreationDate().equals(other.getCreationDate()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getLastModifyDate() == null ? other.getLastModifyDate() == null : this.getLastModifyDate().equals(other.getLastModifyDate()))
            && (this.getLastModifiedBy() == null ? other.getLastModifiedBy() == null : this.getLastModifiedBy().equals(other.getLastModifiedBy()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getFileId() == null) ? 0 : getFileId().hashCode());
        result = prime * result + ((getProcessDay() == null) ? 0 : getProcessDay().hashCode());
        result = prime * result + ((getFileName() == null) ? 0 : getFileName().hashCode());
        result = prime * result + ((getBatchId() == null) ? 0 : getBatchId().hashCode());
        result = prime * result + ((getDetallePolizaId() == null) ? 0 : getDetallePolizaId().hashCode());
        result = prime * result + ((getSourceSys() == null) ? 0 : getSourceSys().hashCode());
        result = prime * result + ((getEmpresaId() == null) ? 0 : getEmpresaId().hashCode());
        result = prime * result + ((getPolizaId() == null) ? 0 : getPolizaId().hashCode());
        result = prime * result + ((getFecha() == null) ? 0 : getFecha().hashCode());
        result = prime * result + ((getCentroCostoId() == null) ? 0 : getCentroCostoId().hashCode());
        result = prime * result + ((getCuentaCompleta() == null) ? 0 : getCuentaCompleta().hashCode());
        result = prime * result + ((getInstrumento() == null) ? 0 : getInstrumento().hashCode());
        result = prime * result + ((getMonedaId() == null) ? 0 : getMonedaId().hashCode());
        result = prime * result + ((getCargos() == null) ? 0 : getCargos().hashCode());
        result = prime * result + ((getAbonos() == null) ? 0 : getAbonos().hashCode());
        result = prime * result + ((getDescripcion() == null) ? 0 : getDescripcion().hashCode());
        result = prime * result + ((getReferencia() == null) ? 0 : getReferencia().hashCode());
        result = prime * result + ((getProcedimientoCont() == null) ? 0 : getProcedimientoCont().hashCode());
        result = prime * result + ((getTipoInstrumentoId() == null) ? 0 : getTipoInstrumentoId().hashCode());
        result = prime * result + ((getRfc() == null) ? 0 : getRfc().hashCode());
        result = prime * result + ((getTotalFactura() == null) ? 0 : getTotalFactura().hashCode());
        result = prime * result + ((getFolioUuid() == null) ? 0 : getFolioUuid().hashCode());
        result = prime * result + ((getUsuario() == null) ? 0 : getUsuario().hashCode());
        result = prime * result + ((getFechaActual() == null) ? 0 : getFechaActual().hashCode());
        result = prime * result + ((getDireccionIp() == null) ? 0 : getDireccionIp().hashCode());
        result = prime * result + ((getProgramaId() == null) ? 0 : getProgramaId().hashCode());
        result = prime * result + ((getSucursal() == null) ? 0 : getSucursal().hashCode());
        result = prime * result + ((getNumTransaccion() == null) ? 0 : getNumTransaccion().hashCode());
        result = prime * result + ((getLedgerId() == null) ? 0 : getLedgerId().hashCode());
        result = prime * result + ((getCurrencyCode() == null) ? 0 : getCurrencyCode().hashCode());
        result = prime * result + ((getJournalCategory() == null) ? 0 : getJournalCategory().hashCode());
        result = prime * result + ((getJournalSource() == null) ? 0 : getJournalSource().hashCode());
        result = prime * result + ((getSegment1() == null) ? 0 : getSegment1().hashCode());
        result = prime * result + ((getSegment2() == null) ? 0 : getSegment2().hashCode());
        result = prime * result + ((getSegment3() == null) ? 0 : getSegment3().hashCode());
        result = prime * result + ((getSegment4() == null) ? 0 : getSegment4().hashCode());
        result = prime * result + ((getSegment5() == null) ? 0 : getSegment5().hashCode());
        result = prime * result + ((getSegment6() == null) ? 0 : getSegment6().hashCode());
        result = prime * result + ((getSegment7() == null) ? 0 : getSegment7().hashCode());
        result = prime * result + ((getSegment8() == null) ? 0 : getSegment8().hashCode());
        result = prime * result + ((getSegment9() == null) ? 0 : getSegment9().hashCode());
        result = prime * result + ((getSegment10() == null) ? 0 : getSegment10().hashCode());
        result = prime * result + ((getGroupId() == null) ? 0 : getGroupId().hashCode());
        result = prime * result + ((getCurrencyConversionDate() == null) ? 0 : getCurrencyConversionDate().hashCode());
        result = prime * result + ((getCurrencyConversionRate() == null) ? 0 : getCurrencyConversionRate().hashCode());
        result = prime * result + ((getUserCurrencyConversionType() == null) ? 0 : getUserCurrencyConversionType().hashCode());
        result = prime * result + ((getProcessStatus() == null) ? 0 : getProcessStatus().hashCode());
        result = prime * result + ((getProcessMessage() == null) ? 0 : getProcessMessage().hashCode());
        result = prime * result + ((getJeHeaderId() == null) ? 0 : getJeHeaderId().hashCode());
        result = prime * result + ((getJournalName() == null) ? 0 : getJournalName().hashCode());
        result = prime * result + ((getJeLineNum() == null) ? 0 : getJeLineNum().hashCode());
        result = prime * result + ((getDocumentId() == null) ? 0 : getDocumentId().hashCode());
        result = prime * result + ((getLoadRequestId() == null) ? 0 : getLoadRequestId().hashCode());
        result = prime * result + ((getImportRequestId() == null) ? 0 : getImportRequestId().hashCode());
        result = prime * result + ((getObjectVersionNumber() == null) ? 0 : getObjectVersionNumber().hashCode());
        result = prime * result + ((getCreationDate() == null) ? 0 : getCreationDate().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getLastModifyDate() == null) ? 0 : getLastModifyDate().hashCode());
        result = prime * result + ((getLastModifiedBy() == null) ? 0 : getLastModifiedBy().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", fileId=").append(fileId);
        sb.append(", processDay=").append(processDay);
        sb.append(", fileName=").append(fileName);
        sb.append(", batchId=").append(batchId);
        sb.append(", detallePolizaId=").append(detallePolizaId);
        sb.append(", sourceSys=").append(sourceSys);
        sb.append(", empresaId=").append(empresaId);
        sb.append(", polizaId=").append(polizaId);
        sb.append(", fecha=").append(fecha);
        sb.append(", centroCostoId=").append(centroCostoId);
        sb.append(", cuentaCompleta=").append(cuentaCompleta);
        sb.append(", instrumento=").append(instrumento);
        sb.append(", monedaId=").append(monedaId);
        sb.append(", cargos=").append(cargos);
        sb.append(", abonos=").append(abonos);
        sb.append(", descripcion=").append(descripcion);
        sb.append(", referencia=").append(referencia);
        sb.append(", procedimientoCont=").append(procedimientoCont);
        sb.append(", tipoInstrumentoId=").append(tipoInstrumentoId);
        sb.append(", rfc=").append(rfc);
        sb.append(", totalFactura=").append(totalFactura);
        sb.append(", folioUuid=").append(folioUuid);
        sb.append(", usuario=").append(usuario);
        sb.append(", fechaActual=").append(fechaActual);
        sb.append(", direccionIp=").append(direccionIp);
        sb.append(", programaId=").append(programaId);
        sb.append(", sucursal=").append(sucursal);
        sb.append(", numTransaccion=").append(numTransaccion);
        sb.append(", ledgerId=").append(ledgerId);
        sb.append(", currencyCode=").append(currencyCode);
        sb.append(", journalCategory=").append(journalCategory);
        sb.append(", journalSource=").append(journalSource);
        sb.append(", segment1=").append(segment1);
        sb.append(", segment2=").append(segment2);
        sb.append(", segment3=").append(segment3);
        sb.append(", segment4=").append(segment4);
        sb.append(", segment5=").append(segment5);
        sb.append(", segment6=").append(segment6);
        sb.append(", segment7=").append(segment7);
        sb.append(", segment8=").append(segment8);
        sb.append(", segment9=").append(segment9);
        sb.append(", segment10=").append(segment10);
        sb.append(", groupId=").append(groupId);
        sb.append(", currencyConversionDate=").append(currencyConversionDate);
        sb.append(", currencyConversionRate=").append(currencyConversionRate);
        sb.append(", userCurrencyConversionType=").append(userCurrencyConversionType);
        sb.append(", processStatus=").append(processStatus);
        sb.append(", processMessage=").append(processMessage);
        sb.append(", jeHeaderId=").append(jeHeaderId);
        sb.append(", journalName=").append(journalName);
        sb.append(", jeLineNum=").append(jeLineNum);
        sb.append(", documentId=").append(documentId);
        sb.append(", loadRequestId=").append(loadRequestId);
        sb.append(", importRequestId=").append(importRequestId);
        sb.append(", objectVersionNumber=").append(objectVersionNumber);
        sb.append(", creationDate=").append(creationDate);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", lastModifyDate=").append(lastModifyDate);
        sb.append(", lastModifiedBy=").append(lastModifiedBy);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}