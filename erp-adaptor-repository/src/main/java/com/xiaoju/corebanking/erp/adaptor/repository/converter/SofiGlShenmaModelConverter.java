package com.xiaoju.corebanking.erp.adaptor.repository.converter;

import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.common.utils.CommonUtils;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlShenmaPO;
import org.springframework.stereotype.Component;

@Component
public class SofiGlShenmaModelConverter {

    /*po->do*/
    public SofiGlShenmaDO convert(SofiGlShenmaPO sofiGlShenmaPO) {
        SofiGlShenmaDO sofiGlShenmaDO = new SofiGlShenmaDO();
        sofiGlShenmaDO.setId(sofiGlShenmaPO.getId());
        sofiGlShenmaDO.setProcessDay(sofiGlShenmaPO.getProcessDay());
        sofiGlShenmaDO.setFileName(sofiGlShenmaPO.getFileName());
        sofiGlShenmaDO.setLinkReference(sofiGlShenmaPO.getLinkReference());
        sofiGlShenmaDO.setExternalReference(sofiGlShenmaPO.getExternalReference());
        sofiGlShenmaDO.setSourceBranch(sofiGlShenmaPO.getSourceBranch());
        sofiGlShenmaDO.setCcy(sofiGlShenmaPO.getCcy());
        sofiGlShenmaDO.setGlCode(sofiGlShenmaPO.getGlCode());
        sofiGlShenmaDO.setCrDrInd(sofiGlShenmaPO.getCrDrInd());
        sofiGlShenmaDO.setEnteredDebitAmount(sofiGlShenmaPO.getEnteredDebitAmount());
        sofiGlShenmaDO.setEnteredCreditAmount(sofiGlShenmaPO.getEnteredCreditAmount());
        sofiGlShenmaDO.setProfitCenter(sofiGlShenmaPO.getProfitCenter());
        sofiGlShenmaDO.setSourceModule(sofiGlShenmaPO.getSourceModule());
        sofiGlShenmaDO.setClientType(sofiGlShenmaPO.getClientType());
        sofiGlShenmaDO.setAmtType(sofiGlShenmaPO.getAmtType());
        sofiGlShenmaDO.setTranType(sofiGlShenmaPO.getTranType());
        sofiGlShenmaDO.setEventType(sofiGlShenmaPO.getEventType());
        sofiGlShenmaDO.setProdType(sofiGlShenmaPO.getProdType());
        sofiGlShenmaDO.setPostDate(sofiGlShenmaPO.getPostDate());
        sofiGlShenmaDO.setValueDate(sofiGlShenmaPO.getValueDate());
        sofiGlShenmaDO.setNarrative(sofiGlShenmaPO.getNarrative());
        sofiGlShenmaDO.setChannelSeqNo(sofiGlShenmaPO.getChannelSeqNo());
        sofiGlShenmaDO.setIntercompany(sofiGlShenmaPO.getIntercompany());
        sofiGlShenmaDO.setFlatRate(sofiGlShenmaPO.getFlatRate());
        sofiGlShenmaDO.setCustRate(sofiGlShenmaPO.getCustRate());
        sofiGlShenmaDO.setInlandOffshore(sofiGlShenmaPO.getInlandOffshore());
        sofiGlShenmaDO.setGroupId(sofiGlShenmaPO.getGroupId());
        sofiGlShenmaDO.setProcessStatus(ProcessStatusEnum.getByCode(sofiGlShenmaPO.getProcessStatus()));
        sofiGlShenmaDO.setProcessMessage(sofiGlShenmaPO.getProcessMessage());
        sofiGlShenmaDO.setObjectVersionNumber(sofiGlShenmaPO.getObjectVersionNumber());
        sofiGlShenmaDO.setCreationDate(sofiGlShenmaPO.getCreationDate());
        sofiGlShenmaDO.setCreatedBy(sofiGlShenmaPO.getCreatedBy());
        sofiGlShenmaDO.setLastModifyDate(sofiGlShenmaPO.getLastModifyDate());
        sofiGlShenmaDO.setLastModifiedBy(sofiGlShenmaPO.getLastModifiedBy());
        sofiGlShenmaDO.setVoucherGroup(sofiGlShenmaPO.getVoucherGroup());
        sofiGlShenmaDO.setClientNo(sofiGlShenmaPO.getClientNo());
        sofiGlShenmaDO.setSeqNo(sofiGlShenmaPO.getSeqNo());
        sofiGlShenmaDO.setSystemId(sofiGlShenmaPO.getSystemId());
        sofiGlShenmaDO.setCompany(sofiGlShenmaPO.getCompany());
        sofiGlShenmaDO.setGroupClient(sofiGlShenmaPO.getGroupClient());
        return sofiGlShenmaDO;
    }

    /*do->po*/
    public SofiGlShenmaPO convert(SofiGlShenmaDO sofiGlShenmaDO) {
        SofiGlShenmaPO sofiGlShenmaPO = new SofiGlShenmaPO();
        sofiGlShenmaPO.setProcessDay(sofiGlShenmaDO.getProcessDay());
        sofiGlShenmaPO.setFileName(sofiGlShenmaDO.getFileName());
        sofiGlShenmaPO.setLinkReference(sofiGlShenmaDO.getLinkReference());
        sofiGlShenmaPO.setExternalReference(sofiGlShenmaDO.getExternalReference());
        sofiGlShenmaPO.setSourceBranch(sofiGlShenmaDO.getSourceBranch());
        sofiGlShenmaPO.setCcy(sofiGlShenmaDO.getCcy());
        sofiGlShenmaPO.setGlCode(sofiGlShenmaDO.getGlCode());
        sofiGlShenmaPO.setEnteredDebitAmount(sofiGlShenmaDO.getEnteredDebitAmount());
        sofiGlShenmaPO.setEnteredCreditAmount(sofiGlShenmaDO.getEnteredCreditAmount());
        sofiGlShenmaPO.setProfitCenter(sofiGlShenmaDO.getProfitCenter());
        sofiGlShenmaPO.setSourceModule(sofiGlShenmaDO.getSourceModule());
        sofiGlShenmaPO.setClientType(sofiGlShenmaDO.getClientType());
        sofiGlShenmaPO.setAmtType(sofiGlShenmaDO.getAmtType());
        sofiGlShenmaPO.setTranType(sofiGlShenmaDO.getTranType());
        sofiGlShenmaPO.setEventType(sofiGlShenmaDO.getEventType());
        sofiGlShenmaPO.setProdType(sofiGlShenmaDO.getProdType());
        sofiGlShenmaPO.setPostDate(sofiGlShenmaDO.getPostDate());
        sofiGlShenmaPO.setValueDate(sofiGlShenmaDO.getValueDate());
        sofiGlShenmaPO.setNarrative(sofiGlShenmaDO.getNarrative());
        sofiGlShenmaPO.setChannelSeqNo(sofiGlShenmaDO.getChannelSeqNo());
        sofiGlShenmaPO.setIntercompany(sofiGlShenmaDO.getIntercompany());
        sofiGlShenmaPO.setFlatRate(sofiGlShenmaDO.getFlatRate());
        sofiGlShenmaPO.setCustRate(sofiGlShenmaDO.getCustRate());
        sofiGlShenmaPO.setInlandOffshore(sofiGlShenmaDO.getInlandOffshore());
        sofiGlShenmaPO.setClientNo(sofiGlShenmaDO.getClientNo());
        sofiGlShenmaPO.setSeqNo(sofiGlShenmaDO.getSeqNo());
        sofiGlShenmaPO.setSystemId(sofiGlShenmaDO.getSystemId());
        sofiGlShenmaPO.setCompany(sofiGlShenmaDO.getCompany());
        sofiGlShenmaPO.setGroupClient(sofiGlShenmaDO.getGroupClient());
        sofiGlShenmaPO.setVoucherGroup(sofiGlShenmaDO.getVoucherGroup());
        sofiGlShenmaPO.setGroupId(sofiGlShenmaDO.getGroupId());
        sofiGlShenmaPO.setProcessStatus(sofiGlShenmaDO.getProcessStatus().getCode());
        sofiGlShenmaPO.setProcessMessage(sofiGlShenmaDO.getProcessMessage());
        sofiGlShenmaPO.setObjectVersionNumber(sofiGlShenmaDO.getObjectVersionNumber());
        sofiGlShenmaPO.setCreationDate(sofiGlShenmaDO.getCreationDate());
        sofiGlShenmaPO.setCreatedBy(sofiGlShenmaDO.getCreatedBy());
        sofiGlShenmaPO.setLastModifyDate(sofiGlShenmaDO.getLastModifyDate());
        sofiGlShenmaPO.setLastModifiedBy(sofiGlShenmaDO.getLastModifiedBy());
        sofiGlShenmaPO.setCrDrInd(sofiGlShenmaDO.getCrDrInd());
        return sofiGlShenmaPO;
    }


    /**
     * DO -> DO
     */
    public void copyIgnoreNullValue(SofiGlShenmaDO source, SofiGlShenmaDO target) {
        CommonUtils.copyIgnoreNull(source::getId, target::setId);
        CommonUtils.copyIgnoreNull(source::getProcessDay, target::setProcessDay);
        CommonUtils.copyIgnoreNull(source::getFileName, target::setFileName);
        CommonUtils.copyIgnoreNull(source::getLinkReference, target::setLinkReference);
        CommonUtils.copyIgnoreNull(source::getTradeNo, target::setTradeNo);
        CommonUtils.copyIgnoreNull(source::getSourceBranch, target::setSourceBranch);
        CommonUtils.copyIgnoreNull(source::getCcy, target::setCcy);
        CommonUtils.copyIgnoreNull(source::getGlCode, target::setGlCode);
        CommonUtils.copyIgnoreNull(source::getCrDrInd, target::setCrDrInd);
        CommonUtils.copyIgnoreNull(source::getEnteredDebitAmount, target::setEnteredDebitAmount);
        CommonUtils.copyIgnoreNull(source::getEnteredCreditAmount, target::setEnteredCreditAmount);
        CommonUtils.copyIgnoreNull(source::getProfitCenter, target::setProfitCenter);
        CommonUtils.copyIgnoreNull(source::getSourceModule, target::setSourceModule);
        CommonUtils.copyIgnoreNull(source::getClientType, target::setClientType);
        CommonUtils.copyIgnoreNull(source::getAmtType, target::setAmtType);
        CommonUtils.copyIgnoreNull(source::getTranType, target::setTranType);
        CommonUtils.copyIgnoreNull(source::getEventType, target::setEventType);
        CommonUtils.copyIgnoreNull(source::getProdType, target::setProdType);
        CommonUtils.copyIgnoreNull(source::getPostDate, target::setPostDate);
        CommonUtils.copyIgnoreNull(source::getValueDate, target::setValueDate);
        CommonUtils.copyIgnoreNull(source::getNarrative, target::setNarrative);
        CommonUtils.copyIgnoreNull(source::getChannelSeqNo, target::setChannelSeqNo);
        CommonUtils.copyIgnoreNull(source::getIntercompany, target::setIntercompany);
        CommonUtils.copyIgnoreNull(source::getFlatRate, target::setFlatRate);
        CommonUtils.copyIgnoreNull(source::getCustRate, target::setCustRate);
        CommonUtils.copyIgnoreNull(source::getInlandOffshore, target::setInlandOffshore);
        CommonUtils.copyIgnoreNull(source::getGroupId, target::setGroupId);
        CommonUtils.copyIgnoreNull(source::getProcessStatus, target::setProcessStatus);
        CommonUtils.copyIgnoreNull(source::getProcessMessage, target::setProcessMessage);
        CommonUtils.copyIgnoreNull(source::getObjectVersionNumber, target::setObjectVersionNumber);
        CommonUtils.copyIgnoreNull(source::getCreationDate, target::setCreationDate);
        CommonUtils.copyIgnoreNull(source::getCreatedBy, target::setCreatedBy);
        CommonUtils.copyIgnoreNull(source::getLastModifyDate, target::setLastModifyDate);
        CommonUtils.copyIgnoreNull(source::getLastModifiedBy, target::setLastModifiedBy);
        CommonUtils.copyIgnoreNull(source::getVoucherGroup, target::setVoucherGroup);

    }

}
