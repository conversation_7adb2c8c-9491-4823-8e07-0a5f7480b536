package com.xiaoju.corebanking.erp.adaptor.repository;


import com.xiaoju.corebanking.erp.adaptor.common.enums.ProcessStatusEnum;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.CargosAndAbonosDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiGlShenmaDO;
import com.xiaoju.corebanking.erp.adaptor.repository.domain.SummaryResultDO;
import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlShenmaPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SofiGlShenmaRepository {
    List<SofiGlShenmaDO> selectSofiGlShenmaByReference(String reference);
    void updateShenmaInterfaceByReference(SofiGlShenmaDO sofiGlShenmaDO, SofiGlShenmaDO update);
    void batchInsert(List<SofiGlShenmaDO> shenmaList);
    List<SofiGlShenmaDO> selectPendingShenmaRecords(ProcessStatusEnum processStatus, String processDay);
    CargosAndAbonosDO selectCargosAndAbonosDO(String reference);
    List<SofiGlShenmaDO> querySofiGlShenmaByGroupIdAndProcessDay(Long groupId, String processDay);
    void updateByProcessDayAndGroupIdAndStatus(String processDay, Long groupId, String processStatus, SofiGlShenmaDO update);
    List<SummaryResultDO> selectShenMaSummary(String processDay);
    List<SummaryResultDO> selectShenMaSumByVoucherGroup(String processDay);

    List<SofiGlShenmaDO> queryByPolizaIdsAndProcessDay(@Param("externalReferenceList") List<String> externalReferenceList, @Param("processDay") String processDay);

    List<SofiGlShenmaDO> findByIndexFields(String processDay, String linkReference);

    int deleteByIndexFields(String processDay,  String linkReference);
}