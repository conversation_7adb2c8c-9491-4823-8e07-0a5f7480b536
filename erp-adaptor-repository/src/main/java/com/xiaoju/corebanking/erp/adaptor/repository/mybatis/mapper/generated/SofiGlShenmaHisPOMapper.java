package com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated;

import com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlShenmaHisPO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface SofiGlShenmaHisPOMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SofiGlShenmaHisPO record);

    int insertSelective(SofiGlShenmaHisPO record);

    SofiGlShenmaHisPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SofiGlShenmaHisPO record);

    int updateByPrimaryKey(SofiGlShenmaHisPO record);
}