<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SofiGlAcctHistCustomerMapper">
  <resultMap id="summaryResultMap" type="com.xiaoju.corebanking.erp.adaptor.repository.domain.SummaryResultDO">
    <result column="result" property="result" />
    <result column="entered_debit_amount" property="enteredDebitAmount" />
    <result column="entered_credit_amount" property="enteredCreditAmount" />
  </resultMap>
  <select id="selectAcctHisSummary" resultMap= "summaryResultMap">
    select concat(process_day, '_',branch,'_', ccy,'_', gl_code,'_', client_no,'_',profit_center,'_', seq_no,company,'_',system_id,'_', group_client) as result,sum(dr_tran_amt) entered_debit_amount,
           sum(cr_tran_amt) entered_credit_amount
    from sofi_gl_acct_hist
    where process_day = #{processDay,jdbcType=VARCHAR}
    group by
    process_day, branch, ccy, gl_code, client_no, profit_center, seq_no, company, system_id, group_client
    </select>

    <delete id="deleteByProcessDay">
        delete from sofi_gl_acct_hist
        where process_day = #{processDay,jdbcType=VARCHAR}
    </delete>
</mapper>