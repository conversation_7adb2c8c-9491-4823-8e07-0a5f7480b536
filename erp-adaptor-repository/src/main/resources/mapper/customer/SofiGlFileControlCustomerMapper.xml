<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SofiGlFileControlCustomerMapper">
    <insert id="batchInsert" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlFileControlPO">
        insert into sofi_gl_file_control (process_day, file_name, system_code,
        file_count, process_status,
        process_message, object_version_number, creation_date,
        created_by, last_modify_date, last_modified_by
        )
        values
        <foreach collection="list" item="item" separator=",">
            ( #{item.processDay,jdbcType=VARCHAR}, #{item.fileName,jdbcType=VARCHAR}, #{item.systemCode,jdbcType=VARCHAR},
            #{item.fileCount,jdbcType=BIGINT}, #{item.processStatus,jdbcType=VARCHAR},
            #{item.processMessage,jdbcType=VARCHAR}, #{item.objectVersionNumber,jdbcType=INTEGER},
            #{item.creationDate,jdbcType=TIMESTAMP},
            #{item.createdBy,jdbcType=VARCHAR}, #{item.lastModifyDate,jdbcType=TIMESTAMP},
            #{item.lastModifiedBy,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="findByIndexFields" resultMap="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlFileControlPOMapper.BaseResultMap">
        select
        <include refid="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlFileControlPOMapper.Base_Column_List" />
        from sofi_gl_file_control
        where process_day = #{processDay,jdbcType=VARCHAR}
        and file_name = #{fileName,jdbcType=VARCHAR}
        and system_code = #{systemCode,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByIndexFields">
        delete from sofi_gl_file_control
        where process_day = #{processDay,jdbcType=VARCHAR}
          and file_name = #{fileName,jdbcType=VARCHAR}
          and system_code = #{systemCode,jdbcType=VARCHAR}
    </delete>
</mapper>