<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SofiGlShenmaCustomerMapper">
    <resultMap id="summaryResultMap" type="com.xiaoju.corebanking.erp.adaptor.repository.domain.SummaryResultDO">
        <result column="result" property="result" />
        <result column="entered_debit_amount" property="enteredDebitAmount" />
        <result column="entered_credit_amount" property="enteredCreditAmount" />
        <result column="voucher_group" property="voucherGroup" />
        <result column="link_reference" property="linkReference" />
        <result column="process_day" property="processDay" />
        <result column="group_id" property="groupId" />
    </resultMap>
    <select id="queryByReference"
            resultMap="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlShenmaPOMapper.BaseResultMap">
        select
        <include
                refid="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlShenmaPOMapper.Base_Column_List"/>
        from sofi_gl_shenma
        where link_reference = #{reference,jdbcType=VARCHAR}
    </select>
    <select id="selectCargosAndAbonosDO"
            resultType="com.xiaoju.corebanking.erp.adaptor.repository.domain.CargosAndAbonosDO">
        select sum(cargos) cargos, sum(abonos) abonos
        from sofi_gl_shenma
        where link_reference = #{reference,jdbcType=VARCHAR}
        GROUP by link_reference
    </select>

<select id="queryPendingShenmaRecords" resultMap="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlShenmaPOMapper.BaseResultMap">
    select
    <include refid="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlShenmaPOMapper.Base_Column_List"/>
    from sofi_gl_shenma
    <where>
        <if test="processStatus != null">
            process_status = #{processStatus,jdbcType=VARCHAR}
        </if>
        <if test="processDay != null">
            AND process_day = #{processDay,jdbcType=VARCHAR}
        </if>
    </where>
    order by link_reference, id
</select>

<insert id="batchInsert" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlShenmaPO">
    insert into sofi_gl_shenma (
        process_day, file_name, link_reference,
        source_branch, ccy, gl_code,
        entered_debit_amount, entered_credit_amount,
        profit_center, source_module, client_type,
        amt_type, tran_type, event_type,
        prod_type, post_date, value_date,
        narrative, channel_seq_no, intercompany,
        flat_rate, cust_rate, inland_offshore,
        client_no, seq_no, system_id,
        company, group_client, voucher_group,
        group_id, process_status, process_message,
        object_version_number, creation_date, created_by,
        last_modify_date, last_modified_by, cr_dr_ind
    )
    values
    <foreach collection="list" item="item" separator=",">
        (#{item.processDay,jdbcType=VARCHAR}, #{item.fileName,jdbcType=VARCHAR}, #{item.linkReference,jdbcType=VARCHAR},
         #{item.sourceBranch,jdbcType=VARCHAR}, #{item.ccy,jdbcType=VARCHAR},
         #{item.glCode,jdbcType=VARCHAR},
         #{item.enteredDebitAmount,jdbcType=DECIMAL}, #{item.enteredCreditAmount,jdbcType=DECIMAL},
         #{item.profitCenter,jdbcType=VARCHAR}, #{item.sourceModule,jdbcType=VARCHAR}, #{item.clientType,jdbcType=VARCHAR},
         #{item.amtType,jdbcType=VARCHAR}, #{item.tranType,jdbcType=VARCHAR}, #{item.eventType,jdbcType=VARCHAR},
         #{item.prodType,jdbcType=VARCHAR}, #{item.postDate,jdbcType=VARCHAR}, #{item.valueDate,jdbcType=VARCHAR},
         #{item.narrative,jdbcType=VARCHAR}, #{item.channelSeqNo,jdbcType=VARCHAR}, #{item.intercompany,jdbcType=VARCHAR},
         #{item.flatRate,jdbcType=DECIMAL}, #{item.custRate,jdbcType=DECIMAL}, #{item.inlandOffshore,jdbcType=VARCHAR},
         #{item.clientNo,jdbcType=VARCHAR}, #{item.seqNo,jdbcType=VARCHAR}, #{item.systemId,jdbcType=VARCHAR},
         #{item.company,jdbcType=VARCHAR}, #{item.groupClient,jdbcType=VARCHAR}, #{item.voucherGroup,jdbcType=VARCHAR},
         #{item.groupId,jdbcType=BIGINT}, #{item.processStatus,jdbcType=VARCHAR}, #{item.processMessage,jdbcType=VARCHAR},
         #{item.objectVersionNumber,jdbcType=BIGINT}, #{item.creationDate,jdbcType=TIMESTAMP}, #{item.createdBy,jdbcType=VARCHAR},
         #{item.lastModifyDate,jdbcType=TIMESTAMP}, #{item.lastModifiedBy,jdbcType=VARCHAR},#{item.crDrInd,jdbcType=VARCHAR})
    </foreach>
</insert>
    <update id="updateReferenceSelective">
        update sofi_gl_shenma
        <set>
            <if test="sofiGlShenmaPO.processDay != null">
                process_day = #{sofiGlShenmaPO.processDay,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.fileName != null">
                file_name = #{sofiGlShenmaPO.fileName,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.linkReference != null">
                link_reference = #{sofiGlShenmaPO.linkReference,jdbcType=VARCHAR},
            </if>

            <if test="sofiGlShenmaPO.sourceBranch != null">
                source_branch = #{sofiGlShenmaPO.sourceBranch,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.ccy != null">
                ccy = #{sofiGlShenmaPO.ccy,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.glCode != null">
                gl_code = #{sofiGlShenmaPO.glCode,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.crDrInd != null">
                cr_dr_ind = #{sofiGlShenmaPO.crDrInd,jdbcType=VARCHAR},
            </if>

            <if test="sofiGlShenmaPO.enteredDebitAmount != null">
                entered_debit_amount = #{sofiGlShenmaPO.enteredDebitAmount,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlShenmaPO.enteredCreditAmount != null">
                entered_credit_amount = #{sofiGlShenmaPO.enteredCreditAmount,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlShenmaPO.profitCenter != null">
                profit_center = #{sofiGlShenmaPO.profitCenter,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.sourceModule != null">
                source_module = #{sofiGlShenmaPO.sourceModule,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.clientType != null">
                client_type = #{sofiGlShenmaPO.clientType,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.amtType != null">
                amt_type = #{sofiGlShenmaPO.amtType,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.tranType != null">
                tran_type = #{sofiGlShenmaPO.tranType,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.eventType != null">
                event_type = #{sofiGlShenmaPO.eventType,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.prodType != null">
                prod_type = #{sofiGlShenmaPO.prodType,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.postDate != null">
                post_date = #{sofiGlShenmaPO.postDate,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.valueDate != null">
                value_date = #{sofiGlShenmaPO.valueDate,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.narrative != null">
                narrative = #{sofiGlShenmaPO.narrative,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.channelSeqNo != null">
                channel_seq_no = #{sofiGlShenmaPO.channelSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.intercompany != null">
                intercompany = #{sofiGlShenmaPO.intercompany,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.flatRate != null">
                flat_rate = #{sofiGlShenmaPO.flatRate,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlShenmaPO.custRate != null">
                cust_rate = #{sofiGlShenmaPO.custRate,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlShenmaPO.inlandOffshore != null">
                inland_offshore = #{sofiGlShenmaPO.inlandOffshore,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.groupId != null">
                group_id = #{sofiGlShenmaPO.groupId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlShenmaPO.processStatus != null">
                process_status = #{sofiGlShenmaPO.processStatus,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.processMessage != null">
                process_message = #{sofiGlShenmaPO.processMessage,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.objectVersionNumber != null">
                object_version_number = #{sofiGlShenmaPO.objectVersionNumber,jdbcType=BIGINT},
            </if>
            <if test="sofiGlShenmaPO.creationDate != null">
                creation_date = #{sofiGlShenmaPO.creationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="sofiGlShenmaPO.createdBy != null">
                created_by = #{sofiGlShenmaPO.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.lastModifyDate != null">
                last_modify_date = #{sofiGlShenmaPO.lastModifyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="sofiGlShenmaPO.lastModifiedBy != null">
                last_modified_by = #{sofiGlShenmaPO.lastModifiedBy,jdbcType=VARCHAR},
            </if>
        </set>
        where process_day = #{processDay,jdbcType=VARCHAR}
        and voucher_group = #{voucherGroup,jdbcType=VARCHAR}
        and link_reference = #{linkReference,jdbcType=VARCHAR}
    </update>
    <update id="updateTradeNoSelective">
        update sofi_gl_shenma
        <set>
            <if test="sofiGlShenmaPO.processDay != null">
                process_day = #{sofiGlShenmaPO.processDay,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.fileName != null">
                file_name = #{sofiGlShenmaPO.fileName,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.linkReference != null">
                link_reference = #{sofiGlShenmaPO.linkReference,jdbcType=VARCHAR},
            </if>

            <if test="sofiGlShenmaPO.sourceBranch != null">
                source_branch = #{sofiGlShenmaPO.sourceBranch,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.ccy != null">
                ccy = #{sofiGlShenmaPO.ccy,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.glCode != null">
                gl_code = #{sofiGlShenmaPO.glCode,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.crDrInd != null">
                cr_dr_ind = #{sofiGlShenmaPO.crDrInd,jdbcType=VARCHAR},
            </if>

            <if test="sofiGlShenmaPO.enteredDebitAmount != null">
                entered_debit_amount = #{sofiGlShenmaPO.enteredDebitAmount,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlShenmaPO.enteredCreditAmount != null">
                entered_credit_amount = #{sofiGlShenmaPO.enteredCreditAmount,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlShenmaPO.profitCenter != null">
                profit_center = #{sofiGlShenmaPO.profitCenter,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.sourceModule != null">
                source_module = #{sofiGlShenmaPO.sourceModule,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.clientType != null">
                client_type = #{sofiGlShenmaPO.clientType,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.amtType != null">
                amt_type = #{sofiGlShenmaPO.amtType,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.tranType != null">
                tran_type = #{sofiGlShenmaPO.tranType,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.eventType != null">
                event_type = #{sofiGlShenmaPO.eventType,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.prodType != null">
                prod_type = #{sofiGlShenmaPO.prodType,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.postDate != null">
                post_date = #{sofiGlShenmaPO.postDate,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.valueDate != null">
                value_date = #{sofiGlShenmaPO.valueDate,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.narrative != null">
                narrative = #{sofiGlShenmaPO.narrative,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.channelSeqNo != null">
                channel_seq_no = #{sofiGlShenmaPO.channelSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.intercompany != null">
                intercompany = #{sofiGlShenmaPO.intercompany,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.flatRate != null">
                flat_rate = #{sofiGlShenmaPO.flatRate,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlShenmaPO.custRate != null">
                cust_rate = #{sofiGlShenmaPO.custRate,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlShenmaPO.inlandOffshore != null">
                inland_offshore = #{sofiGlShenmaPO.inlandOffshore,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.groupId != null">
                group_id = #{sofiGlShenmaPO.groupId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlShenmaPO.processStatus != null">
                process_status = #{sofiGlShenmaPO.processStatus,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.processMessage != null">
                process_message = #{sofiGlShenmaPO.processMessage,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.objectVersionNumber != null">
                object_version_number = #{sofiGlShenmaPO.objectVersionNumber,jdbcType=BIGINT},
            </if>
            <if test="sofiGlShenmaPO.creationDate != null">
                creation_date = #{sofiGlShenmaPO.creationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="sofiGlShenmaPO.createdBy != null">
                created_by = #{sofiGlShenmaPO.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.lastModifyDate != null">
                last_modify_date = #{sofiGlShenmaPO.lastModifyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="sofiGlShenmaPO.lastModifiedBy != null">
                last_modified_by = #{sofiGlShenmaPO.lastModifiedBy,jdbcType=VARCHAR},
            </if>
        </set>
        where  trade_no = #{sofiGlShenmaPO.tradeNo,jdbcType=VARCHAR}
        and object_version_number = #{version,jdbcType=INTEGER}
        and  process_status = #{processStatus,jdbcType=VARCHAR}
    </update>
    <select id="querySofiGlShenmaByGroupIdAndProcessDay" resultMap="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlShenmaPOMapper.extendBaseResultMap">
        select v.id,
               h.group_id,
               h.system_code,
               h.external_reference,
               v.file_name,
               v.link_reference,
               v.process_day,
               v.source_branch,
               v.ccy,
               v.gl_code,
               v.entered_debit_amount,
               v.entered_credit_amount,
               v.profit_center,
               v.client_type,
               v.amt_type,
               v.tran_type,
               v.event_type,
               v.prod_type,
               v.post_date,
               v.value_date,
               v.narrative,
               v.intercompany,
               v.channel_seq_no,
               v.flat_rate,
               v.cust_rate,
               v.inland_offshore,
               v.client_no,
               v.seq_no,
               v.system_id,
               v.company,
               v.group_client,
               v.voucher_group,
               v.process_status,
               v.process_message,
               v.object_version_number
        from
            sofi_gl_interface_header h,
            sofi_gl_shenma v
        where
            h.external_reference = v.link_reference
          and h.group_id =#{groupId}
          and h.process_day =#{processDay}
          and h.system_code ='SHENMA'
          and h.process_status in ('NEW','MAP_FAILED');
    </select>

    <update id="updateByProcessDayAndGroupIdAndStatus">
        update sofi_gl_shenma
        <set>
            <if test="sofiGlShenmaPO.processDay != null">
                process_day = #{sofiGlShenmaPO.processDay,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.fileName != null">
                file_name = #{sofiGlShenmaPO.fileName,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.linkReference != null">
                link_reference = #{sofiGlShenmaPO.linkReference,jdbcType=VARCHAR},
            </if>

            <if test="sofiGlShenmaPO.sourceBranch != null">
                source_branch = #{sofiGlShenmaPO.sourceBranch,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.ccy != null">
                ccy = #{sofiGlShenmaPO.ccy,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.glCode != null">
                gl_code = #{sofiGlShenmaPO.glCode,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.crDrInd != null">
                cr_dr_ind = #{sofiGlShenmaPO.crDrInd,jdbcType=VARCHAR},
            </if>

            <if test="sofiGlShenmaPO.enteredDebitAmount != null">
                entered_debit_amount = #{sofiGlShenmaPO.enteredDebitAmount,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlShenmaPO.enteredCreditAmount != null">
                entered_credit_amount = #{sofiGlShenmaPO.enteredCreditAmount,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlShenmaPO.profitCenter != null">
                profit_center = #{sofiGlShenmaPO.profitCenter,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.sourceModule != null">
                source_module = #{sofiGlShenmaPO.sourceModule,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.clientType != null">
                client_type = #{sofiGlShenmaPO.clientType,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.amtType != null">
                amt_type = #{sofiGlShenmaPO.amtType,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.tranType != null">
                tran_type = #{sofiGlShenmaPO.tranType,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.eventType != null">
                event_type = #{sofiGlShenmaPO.eventType,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.prodType != null">
                prod_type = #{sofiGlShenmaPO.prodType,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.postDate != null">
                post_date = #{sofiGlShenmaPO.postDate,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.valueDate != null">
                value_date = #{sofiGlShenmaPO.valueDate,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.narrative != null">
                narrative = #{sofiGlShenmaPO.narrative,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.channelSeqNo != null">
                channel_seq_no = #{sofiGlShenmaPO.channelSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.intercompany != null">
                intercompany = #{sofiGlShenmaPO.intercompany,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.flatRate != null">
                flat_rate = #{sofiGlShenmaPO.flatRate,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlShenmaPO.custRate != null">
                cust_rate = #{sofiGlShenmaPO.custRate,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlShenmaPO.inlandOffshore != null">
                inland_offshore = #{sofiGlShenmaPO.inlandOffshore,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.groupId != null">
                group_id = #{sofiGlShenmaPO.groupId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlShenmaPO.processStatus != null">
                process_status = #{sofiGlShenmaPO.processStatus,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.processMessage != null">
                process_message = #{sofiGlShenmaPO.processMessage,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.objectVersionNumber != null">
                object_version_number = #{sofiGlShenmaPO.objectVersionNumber,jdbcType=BIGINT},
            </if>
            <if test="sofiGlShenmaPO.creationDate != null">
                creation_date = #{sofiGlShenmaPO.creationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="sofiGlShenmaPO.createdBy != null">
                created_by = #{sofiGlShenmaPO.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlShenmaPO.lastModifyDate != null">
                last_modify_date = #{sofiGlShenmaPO.lastModifyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="sofiGlShenmaPO.lastModifiedBy != null">
                last_modified_by = #{sofiGlShenmaPO.lastModifiedBy,jdbcType=VARCHAR},
            </if>
        </set>
        where   process_day = #{processDay,jdbcType=VARCHAR}
        and   group_id = #{groupId,jdbcType=BIGINT}
        and process_status = #{processStatus,jdbcType=VARCHAR}
    </update>
    <select id="selectShenMaSummary" resultMap= "summaryResultMap">
        select concat( process_day, '_',source_branch,'_', ccy,'_', gl_code,'_', client_no,'_',profit_center,'_', seq_no,company,'_',system_id,'_', group_client) as result, sum(entered_debit_amount) entered_debit_amount,
               sum(entered_credit_amount) entered_credit_amount
        from sofi_gl_shenma
        where process_day  = #{processDay,jdbcType=VARCHAR}
        group by process_day, source_branch, ccy, gl_code,client_no, profit_center,  seq_no, company,system_id, group_client
    </select>
    <select id="selectShenMaSumByVoucherGroup" resultMap="summaryResultMap">
        select sh.link_reference, sh.voucher_group, sh.process_day, h.group_id
        from sofi_gl_shenma sh,
             sofi_gl_interface_header h
        where sh.voucher_group in (select ss.voucher_group
                                   from sofi_gl_shenma ss
                                   where ss.process_day = #{processDay,jdbcType=VARCHAR}
                                     and ss.entered_debit_amount != ss.entered_debit_amount
                                   group by voucher_group)
          and sh.process_day = #{processDay,jdbcType=VARCHAR}
          and sh.link_reference = h.external_reference
        group by sh.link_reference
    </select>
    
    <select id="queryByPolizaIdsAndProcessDay" resultMap="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlShenmaPOMapper.BaseResultMap">
        select
        <include refid="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlShenmaPOMapper.Base_Column_List"/>
        from sofi_gl_shenma
        <where>
            <if test="processDay != null">
                process_day = #{processDay,jdbcType=VARCHAR}
            </if>
            <if test="externalReferenceList != null and externalReferenceList.size() > 0">
                AND link_reference IN
                <foreach collection="externalReferenceList" item="reference" open="(" separator="," close=")">
                    #{reference,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
        order by group_id, id
    </select>

    <select id="findByIndexFields"
            resultMap="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlShenmaPOMapper.BaseResultMap">
        select
        <include
                refid="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlShenmaPOMapper.Base_Column_List"/>
        from sofi_gl_shenma
        where process_day = #{processDay,jdbcType=VARCHAR}
        and link_reference = #{linkReference,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByIndexFields">
        delete
        from sofi_gl_shenma
        where process_day = #{processDay,jdbcType=VARCHAR}
          and link_reference = #{linkReference,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteByProcessDay">
        delete from sofi_gl_shenma
        where process_day = #{processDay,jdbcType=VARCHAR}
    </delete>
</mapper>