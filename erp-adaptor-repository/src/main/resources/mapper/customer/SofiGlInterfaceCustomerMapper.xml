<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SofiGlInterfaceCustomerMapper">
    <update id="updateDetailPolizaIdSelective">
        update sofi_gl_interface
        <set>
            <if test="sofiGlInterfacePO.fileId != null">
                file_id = #{sofiGlInterfacePO.fileId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.processDay != null">
                process_day = #{sofiGlInterfacePO.processDay,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.fileName != null">
                file_name = #{sofiGlInterfacePO.fileName,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.batchId != null">
                batch_id = #{sofiGlInterfacePO.batchId,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.detallePolizaId != null">
                detalle_poliza_id = #{sofiGlInterfacePO.detallePolizaId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.sourceSys != null">
                source_sys = #{sofiGlInterfacePO.sourceSys,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.empresaId != null">
                empresa_id = #{sofiGlInterfacePO.empresaId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.polizaId != null">
                poliza_id = #{sofiGlInterfacePO.polizaId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.fecha != null">
                fecha = #{sofiGlInterfacePO.fecha,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.centroCostoId != null">
                centro_costo_id = #{sofiGlInterfacePO.centroCostoId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.cuentaCompleta != null">
                cuenta_completa = #{sofiGlInterfacePO.cuentaCompleta,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.instrumento != null">
                instrumento = #{sofiGlInterfacePO.instrumento,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.monedaId != null">
                moneda_id = #{sofiGlInterfacePO.monedaId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.cargos != null">
                cargos = #{sofiGlInterfacePO.cargos,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlInterfacePO.abonos != null">
                abonos = #{sofiGlInterfacePO.abonos,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlInterfacePO.descripcion != null">
                descripcion = #{sofiGlInterfacePO.descripcion,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.referencia != null">
                referencia = #{sofiGlInterfacePO.referencia,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.procedimientoCont != null">
                procedimiento_cont = #{sofiGlInterfacePO.procedimientoCont,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.tipoInstrumentoId != null">
                tipo_instrumento_id = #{sofiGlInterfacePO.tipoInstrumentoId,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.rfc != null">
                rfc = #{sofiGlInterfacePO.rfc,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.totalFactura != null">
                total_factura = #{sofiGlInterfacePO.totalFactura,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlInterfacePO.folioUuid != null">
                folio_uuid = #{sofiGlInterfacePO.folioUuid,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.usuario != null">
                usuario = #{sofiGlInterfacePO.usuario,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.fechaActual != null">
                fecha_actual = #{sofiGlInterfacePO.fechaActual,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.direccionIp != null">
                direccion_ip = #{sofiGlInterfacePO.direccionIp,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.programaId != null">
                programa_id = #{sofiGlInterfacePO.programaId,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.sucursal != null">
                sucursal = #{sofiGlInterfacePO.sucursal,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.numTransaccion != null">
                num_transaccion = #{sofiGlInterfacePO.numTransaccion,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.ledgerId != null">
                ledger_id = #{sofiGlInterfacePO.ledgerId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.ledgerName != null">
                ledger_name = #{sofiGlInterfacePO.ledgerName,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.currencyCode != null">
                currency_code = #{sofiGlInterfacePO.currencyCode,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.journalCategory != null">
                journal_category = #{sofiGlInterfacePO.journalCategory,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.journalSource != null">
                journal_source = #{sofiGlInterfacePO.journalSource,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.segment1 != null">
                segment1 = #{sofiGlInterfacePO.segment1,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.segment2 != null">
                segment2 = #{sofiGlInterfacePO.segment2,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.segment3 != null">
                segment3 = #{sofiGlInterfacePO.segment3,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.segment4 != null">
                segment4 = #{sofiGlInterfacePO.segment4,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.segment5 != null">
                segment5 = #{sofiGlInterfacePO.segment5,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.segment6 != null">
                segment6 = #{sofiGlInterfacePO.segment6,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.segment7 != null">
                segment7 = #{sofiGlInterfacePO.segment7,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.segment8 != null">
                segment8 = #{sofiGlInterfacePO.segment8,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.segment9 != null">
                segment9 = #{sofiGlInterfacePO.segment9,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.segment10 != null">
                segment10 = #{sofiGlInterfacePO.segment10,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.groupId != null">
                group_id = #{sofiGlInterfacePO.groupId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.currencyConversionDate != null">
                currency_conversion_date = #{sofiGlInterfacePO.currencyConversionDate,jdbcType=TIMESTAMP},
            </if>
            <if test="sofiGlInterfacePO.currencyConversionRate != null">
                currency_conversion_rate = #{sofiGlInterfacePO.currencyConversionRate,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlInterfacePO.userCurrencyConversionType != null">
                user_currency_conversion_type = #{sofiGlInterfacePO.userCurrencyConversionType,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.processStatus != null">
                process_status = #{sofiGlInterfacePO.processStatus,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.processMessage != null">
                process_message = #{sofiGlInterfacePO.processMessage,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.jeHeaderId != null">
                je_header_id = #{sofiGlInterfacePO.jeHeaderId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.journalName != null">
                journal_name = #{sofiGlInterfacePO.journalName,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.jeLineNum != null">
                je_line_num = #{sofiGlInterfacePO.jeLineNum,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.documentId != null">
                document_id = #{sofiGlInterfacePO.documentId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.loadRequestId != null">
                load_request_id = #{sofiGlInterfacePO.loadRequestId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.importRequestId != null">
                import_request_id = #{sofiGlInterfacePO.importRequestId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.objectVersionNumber != null">
                object_version_number = #{sofiGlInterfacePO.objectVersionNumber,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.creationDate != null">
                creation_date = #{sofiGlInterfacePO.creationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="sofiGlInterfacePO.createdBy != null">
                created_by = #{sofiGlInterfacePO.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.lastModifyDate != null">
                last_modify_date = #{sofiGlInterfacePO.lastModifyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="sofiGlInterfacePO.lastModifiedBy != null">
                last_modified_by = #{sofiGlInterfacePO.lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.journalSourceName != null">
                journal_source_name = #{sofiGlInterfacePO.journalSourceName,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT} and poliza_id = #{polizaId,jdbcType=BIGINT} and detalle_poliza_id = #{detallePolizaId,jdbcType=BIGINT}
        and process_status = #{processStatus,jdbcType=VARCHAR}
        and object_version_number=#{version}
    </update>
    <update id="updatePolizaIdSelective">
        update sofi_gl_interface
        <set>
            <if test="sofiGlInterfacePO.fileId != null">
                file_id = #{sofiGlInterfacePO.fileId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.processDay != null">
                process_day = #{sofiGlInterfacePO.processDay,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.fileName != null">
                file_name = #{sofiGlInterfacePO.fileName,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.batchId != null">
                batch_id = #{sofiGlInterfacePO.batchId,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.detallePolizaId != null">
                detalle_poliza_id = #{sofiGlInterfacePO.detallePolizaId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.sourceSys != null">
                source_sys = #{sofiGlInterfacePO.sourceSys,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.empresaId != null">
                empresa_id = #{sofiGlInterfacePO.empresaId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.polizaId != null">
                poliza_id = #{sofiGlInterfacePO.polizaId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.fecha != null">
                fecha = #{sofiGlInterfacePO.fecha,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.centroCostoId != null">
                centro_costo_id = #{sofiGlInterfacePO.centroCostoId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.cuentaCompleta != null">
                cuenta_completa = #{sofiGlInterfacePO.cuentaCompleta,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.instrumento != null">
                instrumento = #{sofiGlInterfacePO.instrumento,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.monedaId != null">
                moneda_id = #{sofiGlInterfacePO.monedaId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.cargos != null">
                cargos = #{sofiGlInterfacePO.cargos,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlInterfacePO.abonos != null">
                abonos = #{sofiGlInterfacePO.abonos,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlInterfacePO.descripcion != null">
                descripcion = #{sofiGlInterfacePO.descripcion,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.referencia != null">
                referencia = #{sofiGlInterfacePO.referencia,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.procedimientoCont != null">
                procedimiento_cont = #{sofiGlInterfacePO.procedimientoCont,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.tipoInstrumentoId != null">
                tipo_instrumento_id = #{sofiGlInterfacePO.tipoInstrumentoId,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.rfc != null">
                rfc = #{sofiGlInterfacePO.rfc,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.totalFactura != null">
                total_factura = #{sofiGlInterfacePO.totalFactura,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlInterfacePO.folioUuid != null">
                folio_uuid = #{sofiGlInterfacePO.folioUuid,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.usuario != null">
                usuario = #{sofiGlInterfacePO.usuario,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.fechaActual != null">
                fecha_actual = #{sofiGlInterfacePO.fechaActual,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.direccionIp != null">
                direccion_ip = #{sofiGlInterfacePO.direccionIp,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.programaId != null">
                programa_id = #{sofiGlInterfacePO.programaId,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.sucursal != null">
                sucursal = #{sofiGlInterfacePO.sucursal,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.numTransaccion != null">
                num_transaccion = #{sofiGlInterfacePO.numTransaccion,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.ledgerId != null">
                ledger_id = #{sofiGlInterfacePO.ledgerId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.ledgerName != null">
                ledger_name = #{sofiGlInterfacePO.ledgerName,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.currencyCode != null">
                currency_code = #{sofiGlInterfacePO.currencyCode,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.journalCategory != null">
                journal_category = #{sofiGlInterfacePO.journalCategory,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.journalSource != null">
                journal_source = #{sofiGlInterfacePO.journalSource,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.segment1 != null">
                segment1 = #{sofiGlInterfacePO.segment1,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.segment2 != null">
                segment2 = #{sofiGlInterfacePO.segment2,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.segment3 != null">
                segment3 = #{sofiGlInterfacePO.segment3,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.segment4 != null">
                segment4 = #{sofiGlInterfacePO.segment4,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.segment5 != null">
                segment5 = #{sofiGlInterfacePO.segment5,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.segment6 != null">
                segment6 = #{sofiGlInterfacePO.segment6,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.segment7 != null">
                segment7 = #{sofiGlInterfacePO.segment7,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.segment8 != null">
                segment8 = #{sofiGlInterfacePO.segment8,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.segment9 != null">
                segment9 = #{sofiGlInterfacePO.segment9,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.segment10 != null">
                segment10 = #{sofiGlInterfacePO.segment10,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.groupId != null">
                group_id = #{sofiGlInterfacePO.groupId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.currencyConversionDate != null">
                currency_conversion_date = #{sofiGlInterfacePO.currencyConversionDate,jdbcType=TIMESTAMP},
            </if>
            <if test="sofiGlInterfacePO.currencyConversionRate != null">
                currency_conversion_rate = #{sofiGlInterfacePO.currencyConversionRate,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlInterfacePO.userCurrencyConversionType != null">
                user_currency_conversion_type = #{sofiGlInterfacePO.userCurrencyConversionType,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.processStatus != null">
                process_status = #{sofiGlInterfacePO.processStatus,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.processMessage != null">
                process_message = #{sofiGlInterfacePO.processMessage,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.jeHeaderId != null">
                je_header_id = #{sofiGlInterfacePO.jeHeaderId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.journalName != null">
                journal_name = #{sofiGlInterfacePO.journalName,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.jeLineNum != null">
                je_line_num = #{sofiGlInterfacePO.jeLineNum,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.documentId != null">
                document_id = #{sofiGlInterfacePO.documentId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.loadRequestId != null">
                load_request_id = #{sofiGlInterfacePO.loadRequestId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.importRequestId != null">
                import_request_id = #{sofiGlInterfacePO.importRequestId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.objectVersionNumber != null">
                object_version_number = #{sofiGlInterfacePO.objectVersionNumber,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfacePO.creationDate != null">
                creation_date = #{sofiGlInterfacePO.creationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="sofiGlInterfacePO.createdBy != null">
                created_by = #{sofiGlInterfacePO.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.lastModifyDate != null">
                last_modify_date = #{sofiGlInterfacePO.lastModifyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="sofiGlInterfacePO.lastModifiedBy != null">
                last_modified_by = #{sofiGlInterfacePO.lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfacePO.journalSourceName != null">
                journal_source_name = #{sofiGlInterfacePO.journalSourceName,jdbcType=VARCHAR},
            </if>
        </set>
        where poliza_id = #{polizaId,jdbcType=BIGINT}
    </update>
    <insert id="batchInsert"
            parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfacePO">
        insert into sofi_gl_interface (file_id, process_day,
        file_name, batch_id, detalle_poliza_id,
        source_sys, empresa_id, poliza_id,
        fecha, centro_costo_id, cuenta_completa,
        instrumento, moneda_id, cargos,
        abonos, descripcion, referencia,
        procedimiento_cont, tipo_instrumento_id, rfc,
        total_factura, folio_uuid, usuario,
        fecha_actual, direccion_ip, programa_id,
        sucursal, num_transaccion, ledger_id,
        currency_code, journal_category, journal_source,
        segment1, segment2, segment3,
        segment4, segment5, segment6,
        segment7, segment8, segment9,
        segment10, group_id, currency_conversion_date,
        currency_conversion_rate, user_currency_conversion_type,
        process_status, process_message, je_header_id,
        journal_name, je_line_num, document_id,
        load_request_id, import_request_id, object_version_number,
        creation_date, created_by, last_modify_date,
        last_modified_by)
        values
        <foreach collection="list" item="item" separator=",">
            ( #{item.fileId,jdbcType=BIGINT}, #{item.processDay,jdbcType=VARCHAR},
            #{item.fileName,jdbcType=VARCHAR}, #{item.batchId,jdbcType=VARCHAR},
            #{item.detallePolizaId,jdbcType=BIGINT},
            #{item.sourceSys,jdbcType=VARCHAR}, #{item.empresaId,jdbcType=BIGINT}, #{item.polizaId,jdbcType=BIGINT},
            #{item.fecha,jdbcType=VARCHAR}, #{item.centroCostoId,jdbcType=BIGINT},
            #{item.cuentaCompleta,jdbcType=VARCHAR},
            #{item.instrumento,jdbcType=BIGINT}, #{item.monedaId,jdbcType=BIGINT}, #{item.cargos,jdbcType=DECIMAL},
            #{item.abonos,jdbcType=DECIMAL}, #{item.descripcion,jdbcType=VARCHAR}, #{item.referencia,jdbcType=VARCHAR},
            #{item.procedimientoCont,jdbcType=VARCHAR}, #{item.tipoInstrumentoId,jdbcType=VARCHAR},
            #{item.rfc,jdbcType=VARCHAR},
            #{item.totalFactura,jdbcType=DECIMAL}, #{item.folioUuid,jdbcType=VARCHAR}, #{item.usuario,jdbcType=BIGINT},
            #{item.fechaActual,jdbcType=VARCHAR}, #{item.direccionIp,jdbcType=VARCHAR},
            #{item.programaId,jdbcType=VARCHAR},
            #{item.sucursal,jdbcType=BIGINT}, #{item.numTransaccion,jdbcType=BIGINT}, #{item.ledgerId,jdbcType=BIGINT},
            #{item.currencyCode,jdbcType=VARCHAR}, #{item.journalCategory,jdbcType=VARCHAR},
            #{item.journalSource,jdbcType=VARCHAR},
            #{item.segment1,jdbcType=VARCHAR}, #{item.segment2,jdbcType=VARCHAR}, #{item.segment3,jdbcType=VARCHAR},
            #{item.segment4,jdbcType=VARCHAR}, #{item.segment5,jdbcType=VARCHAR}, #{item.segment6,jdbcType=VARCHAR},
            #{item.segment7,jdbcType=VARCHAR}, #{item.segment8,jdbcType=VARCHAR}, #{item.segment9,jdbcType=VARCHAR},
            #{item.segment10,jdbcType=VARCHAR}, #{item.groupId,jdbcType=BIGINT},
            #{item.currencyConversionDate,jdbcType=TIMESTAMP},
            #{item.currencyConversionRate,jdbcType=DECIMAL}, #{item.userCurrencyConversionType,jdbcType=VARCHAR},
            #{item.processStatus,jdbcType=VARCHAR}, #{item.processMessage,jdbcType=VARCHAR},
            #{item.jeHeaderId,jdbcType=BIGINT},
            #{item.journalName,jdbcType=VARCHAR}, #{item.jeLineNum,jdbcType=BIGINT}, #{item.documentId,jdbcType=BIGINT},
            #{item.loadRequestId,jdbcType=BIGINT}, #{item.importRequestId,jdbcType=BIGINT},
            #{item.objectVersionNumber,jdbcType=BIGINT},
            #{item.creationDate,jdbcType=TIMESTAMP}, #{item.createdBy,jdbcType=VARCHAR},
            #{item.lastModifyDate,jdbcType=TIMESTAMP},
            #{item.lastModifiedBy,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="countByExample" resultType="java.lang.Integer">
        select count(*)
        from sofi_gl_interface
        where process_day = #{processDay,jdbcType=VARCHAR}
          and process_status = #{status,jdbcType=VARCHAR}
    </select>

    <select id="findByIndexFields"
            resultMap="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfacePOMapper.BaseResultMap">
        select
        <include
                refid="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfacePOMapper.Base_Column_List"/>
        from sofi_gl_interface
        where process_day = #{processDay,jdbcType=VARCHAR}
        and file_name = #{fileName,jdbcType=VARCHAR}
        and poliza_id = #{polizaId,jdbcType=BIGINT}
        and detalle_poliza_id = #{detallePolizaId,jdbcType=BIGINT}
        and process_status in ('VALIDATE_FAILED', 'MAP_FAILED','UPLOAD_FAILED','LOAD_FAILED','IMPORT_FAILED')
    </select>

    <delete id="deleteByIndexFields">
        delete
        from sofi_gl_interface
        where process_day = #{processDay,jdbcType=VARCHAR}
          and file_name = #{fileName,jdbcType=VARCHAR}
          and poliza_id = #{polizaId,jdbcType=BIGINT}
          and detalle_poliza_id = #{detallePolizaId,jdbcType=BIGINT}
          and process_status in ('VALIDATE_FAILED', 'MAP_FAILED', 'UPLOAD_FAILED', 'LOAD_FAILED', 'IMPORT_FAILED')
    </delete>
    <select id="queryByPolizaId"
            resultMap="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfacePOMapper.BaseResultMap">
        select
        <include
                refid="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfacePOMapper.Base_Column_List"/>
        from sofi_gl_interface
        where poliza_id in
        <foreach item="polizaId" collection="polizaIds" open="(" separator="," close=")">
            #{polizaId}
        </foreach>
    </select>

    <select id="findByBatchIndexFields"
            resultMap="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfacePOMapper.BaseResultMap">
        select
        <include refid="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfacePOMapper.Base_Column_List"/>
        from sofi_gl_interface
        where process_day = #{processDay,jdbcType=VARCHAR}
        and file_name = #{fileName,jdbcType=VARCHAR}
        and process_status in ('VALIDATE_FAILED', 'MAP_FAILED','UPLOAD_FAILED','LOAD_FAILED','IMPORT_FAILED')
        and CONCAT(poliza_id, '_', detalle_poliza_id) in
        <foreach item="indexKey" collection="indexKeys" open="(" separator="," close=")">
            #{indexKey}
        </foreach>
    </select>

    <delete id="deleteByBatchIndexFields">
        delete from sofi_gl_interface
        where process_day = #{processDay,jdbcType=VARCHAR}
        and file_name = #{fileName,jdbcType=VARCHAR}
        and process_status in ('VALIDATE_FAILED', 'MAP_FAILED', 'UPLOAD_FAILED', 'LOAD_FAILED', 'IMPORT_FAILED')
        and CONCAT(poliza_id, '_', detalle_poliza_id) in
        <foreach item="indexKey" collection="indexKeys" open="(" separator="," close=")">
            #{indexKey}
        </foreach>
    </delete>

    <select id="queryGroupIdByProcessStatus" resultType="java.lang.Long">
        select group_id
        from sofi_gl_interface
        where process_status = 'MAPPED' and process_day =#{processDay,jdbcType=VARCHAR}
        group by group_id
    </select>
    <select id="queryGLInterfaceData"
            resultMap="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfacePOMapper.BaseResultMap">
        select
        <include
                refid="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfacePOMapper.Base_Column_List"/>
        from sofi_gl_interface
        <where>
            <if test="sofiGlInterfacePO.fileId != null">
                file_id = #{sofiGlInterfacePO.fileId,jdbcType=BIGINT}
            </if>
            <if test="sofiGlInterfacePO.processDay != null">
                process_day = #{sofiGlInterfacePO.processDay,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.fileName != null">
                file_name = #{sofiGlInterfacePO.fileName,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.batchId != null">
                batch_id = #{sofiGlInterfacePO.batchId,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.detallePolizaId != null">
                detalle_poliza_id = #{sofiGlInterfacePO.detallePolizaId,jdbcType=BIGINT}
            </if>
            <if test="sofiGlInterfacePO.sourceSys != null">
                source_sys = #{sofiGlInterfacePO.sourceSys,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.empresaId != null">
                empresa_id = #{sofiGlInterfacePO.empresaId,jdbcType=BIGINT}
            </if>
            <if test="sofiGlInterfacePO.polizaId != null">
                poliza_id = #{sofiGlInterfacePO.polizaId,jdbcType=BIGINT}
            </if>
            <if test="sofiGlInterfacePO.fecha != null">
                fecha = #{sofiGlInterfacePO.fecha,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.centroCostoId != null">
                centro_costo_id = #{sofiGlInterfacePO.centroCostoId,jdbcType=BIGINT}
            </if>
            <if test="sofiGlInterfacePO.cuentaCompleta != null">
                cuenta_completa = #{sofiGlInterfacePO.cuentaCompleta,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.instrumento != null">
                instrumento = #{sofiGlInterfacePO.instrumento,jdbcType=BIGINT}
            </if>
            <if test="sofiGlInterfacePO.monedaId != null">
                moneda_id = #{sofiGlInterfacePO.monedaId,jdbcType=BIGINT}
            </if>
            <if test="sofiGlInterfacePO.cargos != null">
                cargos = #{sofiGlInterfacePO.cargos,jdbcType=DECIMAL}
            </if>
            <if test="sofiGlInterfacePO.abonos != null">
                abonos = #{sofiGlInterfacePO.abonos,jdbcType=DECIMAL}
            </if>
            <if test="sofiGlInterfacePO.descripcion != null">
                descripcion = #{sofiGlInterfacePO.descripcion,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.referencia != null">
                referencia = #{sofiGlInterfacePO.referencia,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.procedimientoCont != null">
                procedimiento_cont = #{sofiGlInterfacePO.procedimientoCont,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.tipoInstrumentoId != null">
                tipo_instrumento_id = #{sofiGlInterfacePO.tipoInstrumentoId,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.rfc != null">
                rfc = #{sofiGlInterfacePO.rfc,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.totalFactura != null">
                total_factura = #{sofiGlInterfacePO.totalFactura,jdbcType=DECIMAL}
            </if>
            <if test="sofiGlInterfacePO.folioUuid != null">
                folio_uuid = #{sofiGlInterfacePO.folioUuid,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.usuario != null">
                usuario = #{sofiGlInterfacePO.usuario,jdbcType=BIGINT}
            </if>
            <if test="sofiGlInterfacePO.fechaActual != null">
                fecha_actual = #{sofiGlInterfacePO.fechaActual,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.direccionIp != null">
                direccion_ip = #{sofiGlInterfacePO.direccionIp,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.programaId != null">
                programa_id = #{sofiGlInterfacePO.programaId,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.sucursal != null">
                sucursal = #{sofiGlInterfacePO.sucursal,jdbcType=BIGINT}
            </if>
            <if test="sofiGlInterfacePO.numTransaccion != null">
                num_transaccion = #{sofiGlInterfacePO.numTransaccion,jdbcType=BIGINT}
            </if>
            <if test="sofiGlInterfacePO.ledgerId != null">
                ledger_id = #{sofiGlInterfacePO.ledgerId,jdbcType=BIGINT}
            </if>
            <if test="sofiGlInterfacePO.currencyCode != null">
                currency_code = #{sofiGlInterfacePO.currencyCode,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.journalCategory != null">
                journal_category = #{sofiGlInterfacePO.journalCategory,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.journalSource != null">
                journal_source = #{sofiGlInterfacePO.journalSource,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.segment1 != null">
                segment1 = #{sofiGlInterfacePO.segment1,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.segment2 != null">
                segment2 = #{sofiGlInterfacePO.segment2,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.segment3 != null">
                segment3 = #{sofiGlInterfacePO.segment3,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.segment4 != null">
                segment4 = #{sofiGlInterfacePO.segment4,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.segment5 != null">
                segment5 = #{sofiGlInterfacePO.segment5,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.segment6 != null">
                segment6 = #{sofiGlInterfacePO.segment6,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.segment7 != null">
                segment7 = #{sofiGlInterfacePO.segment7,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.segment8 != null">
                segment8 = #{sofiGlInterfacePO.segment8,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.segment9 != null">
                segment9 = #{sofiGlInterfacePO.segment9,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.segment10 != null">
                segment10 = #{sofiGlInterfacePO.segment10,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.groupId != null">
                group_id = #{sofiGlInterfacePO.groupId,jdbcType=BIGINT}
            </if>
            <if test="sofiGlInterfacePO.currencyConversionDate != null">
                currency_conversion_date = #{sofiGlInterfacePO.currencyConversionDate,jdbcType=TIMESTAMP}
            </if>
            <if test="sofiGlInterfacePO.currencyConversionRate != null">
                currency_conversion_rate = #{sofiGlInterfacePO.currencyConversionRate,jdbcType=DECIMAL}
            </if>
            <if test="sofiGlInterfacePO.userCurrencyConversionType != null">
                user_currency_conversion_type = #{sofiGlInterfacePO.userCurrencyConversionType,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.processStatus != null">
                process_status = #{sofiGlInterfacePO.processStatus,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.processMessage != null">
                process_message = #{sofiGlInterfacePO.processMessage,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.jeHeaderId != null">
                je_header_id = #{sofiGlInterfacePO.jeHeaderId,jdbcType=BIGINT}
            </if>
            <if test="sofiGlInterfacePO.journalName != null">
                journal_name = #{sofiGlInterfacePO.journalName,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.jeLineNum != null">
                je_line_num = #{sofiGlInterfacePO.jeLineNum,jdbcType=BIGINT}
            </if>
            <if test="sofiGlInterfacePO.documentId != null">
                document_id = #{sofiGlInterfacePO.documentId,jdbcType=BIGINT}
            </if>
            <if test="sofiGlInterfacePO.loadRequestId != null">
                load_request_id = #{sofiGlInterfacePO.loadRequestId,jdbcType=BIGINT}
            </if>
            <if test="sofiGlInterfacePO.importRequestId != null">
                import_request_id = #{sofiGlInterfacePO.importRequestId,jdbcType=BIGINT}
            </if>
            <if test="sofiGlInterfacePO.objectVersionNumber != null">
                object_version_number = #{sofiGlInterfacePO.objectVersionNumber,jdbcType=BIGINT}
            </if>
            <if test="sofiGlInterfacePO.creationDate != null">
                creation_date = #{sofiGlInterfacePO.creationDate,jdbcType=TIMESTAMP}
            </if>
            <if test="sofiGlInterfacePO.createdBy != null">
                created_by = #{sofiGlInterfacePO.createdBy,jdbcType=VARCHAR}
            </if>
            <if test="sofiGlInterfacePO.lastModifyDate != null">
                last_modify_date = #{sofiGlInterfacePO.lastModifyDate,jdbcType=TIMESTAMP}
            </if>
            <if test="sofiGlInterfacePO.lastModifiedBy != null">
                last_modified_by = #{sofiGlInterfacePO.lastModifiedBy,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="selectCargosAndAbonosDO"
            resultType="com.xiaoju.corebanking.erp.adaptor.repository.domain.CargosAndAbonosDO">
        select sum(cargos) cargos, sum(abonos) abonos
        from sofi_gl_interface
        where poliza_id = #{polizaId,jdbcType=BIGINT}
        GROUP by poliza_id
    </select>

    <select id="selectConutInterfaceEmailData"
            resultType="com.xiaoju.corebanking.erp.adaptor.repository.domain.SofiEmailDO">
        select *
        from (select source_sys sourceSys, file_name fileName, process_status processStatus, count(*) count
              from sofi_gl_interface
              where process_day = #{processDay,jdbcType=VARCHAR}
              GROUP by source_sys, file_name, process_status) t
        order by t.count asc
    </select>

    <select id="selectGroupByPolizaId" resultType="java.util.Map">
        SELECT
            poliza_id,
            batch_id,
            COUNT(*) as record_count
        FROM sofi_gl_interface
        WHERE process_day = #{processDay}
          AND process_status = #{status}
          AND poliza_id IS NOT NULL
        GROUP BY poliza_id,process_day
    </select>

    <select id="selectSofiGlInterfaceByGroupIdAndProcessDay" resultMap="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfacePOMapper.BaseResultMap">
        select v.id,
            h.group_id,v.file_id,
            v.process_day,
            v.file_name,
            v.batch_id,
            v.detalle_poliza_id,
            v.source_sys,
            v.empresa_id,
            v.poliza_id,
            v.fecha,
            v.centro_costo_id,
            v.cuenta_completa,
            v.instrumento,
            v.moneda_id,
            v.cargos,
            v.abonos,
            v.descripcion,
            v.referencia,
            v.procedimiento_cont,
            v.tipo_instrumento_id,
            v.rfc,
            v.total_factura,
            v.folio_uuid,
            v.usuario,
            v.fecha_actual,
            v.direccion_ip,
            v.programa_id,
            v.sucursal,
            v.num_transaccion,
            v.ledger_id,
            v.currency_code,
            v.journal_category,
            v.journal_source,
            v.segment1,
            v.segment2,
            v.segment3,
            v.segment4,
            v.segment5,
            v.segment6,
            v.segment7,
            v.segment8,
            v.segment9,
            v.segment10,
            v.currency_conversion_date,
            v.currency_conversion_rate,
            v.user_currency_conversion_type,
            v.process_status,
            v.process_message,
            v.je_header_id,
            v.journal_name,
            v.je_line_num,
            v.document_id,
            v.load_request_id,
            v.import_request_id,
            v.object_version_number,
            v.creation_date,
            v.created_by,
            v.last_modify_date,
            v.last_modified_by
        from
            sofi_gl_interface_header h,
            sofi_gl_interface v
        where
            h.external_reference = v.poliza_id
          and h.group_id = #{groupId}
          and h.process_day =#{processDay}
          and h.system_code ='SAFI'
          and h.process_status in ('NEW','MAP_FAILED')

    </select>

</mapper>