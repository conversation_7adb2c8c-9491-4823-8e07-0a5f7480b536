<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.CoaCustomerMapper">

    <resultMap id="ValidationRequestDOResultMap" type="com.xiaoju.corebanking.erp.adaptor.repository.domain.ValidationRequestDO">
        <result column="ledger_name" property="ledgerName" />
        <result column="segment1" property="segment1" />
        <result column="segment2" property="segment2" />
        <result column="segment3" property="segment3" />
        <result column="segment4" property="segment4" />
        <result column="segment5" property="segment5" />
        <result column="segment6" property="segment6" />
        <result column="segment7" property="segment7" />
        <result column="segment8" property="segment8" />
        <result column="segment9" property="segment9" />
        <result column="segment10" property="segment10" />
    </resultMap>

    <select id="selectCoaListBySegments" resultMap="ValidationRequestDOResultMap">
        SELECT ledger_name,segment1,segment2,segment3,segment4,segment5,segment6,segment7,segment8,segment9,segment10
        FROM sofi_gl_interface
        WHERE process_status = 'VALIDATED'
        and process_day = #{processDay}
        <if test="ledgerName != null and ledgerName != ''">
            and ledger_name = #{ledgerName}
        </if>
        <if test="segment1 != null and segment1 != ''">
            and segment1 = #{segment1}
        </if>
        <if test="segment2 != null and segment2 != ''">
            and segment2 = #{segment2}
        </if>
        <if test="segment3 != null and segment3 != ''">
            and segment3 = #{segment3}
        </if>
        <if test="segment4 != null and segment4 != ''">
            and segment4 = #{segment4}
        </if>
        <if test="segment5 != null and segment5 != ''">
            and segment5 = #{segment5}
        </if>
        <if test="segment6 != null and segment6 != ''">
            and segment6 = #{segment6}
        </if>
        <if test="segment7 != null and segment7 != ''">
            and segment7 = #{segment7}
        </if>
        <if test="segment8 != null and segment8 != ''">
            and segment8 = #{segment8}
        </if>
        <if test="segment9 != null and segment9 != ''">
            and segment9 = #{segment9}
        </if>
        <if test="segment10 != null and segment10 != ''">
            and segment10 = #{segment10}
        </if>
        group by ledger_name,segment1,segment2,segment3,segment4,segment5,segment6,segment7,segment8,segment9,segment10
    </select>

    <select id="selectCoaListNoGroup" resultMap="ValidationRequestDOResultMap">
        SELECT ledger_name,segment1,segment2,segment3,segment4,segment5,segment6,segment7,segment8,segment9,segment10
        FROM sofi_gl_interface
        WHERE process_status = 'VALIDATED'
        and process_day = #{processDay}
    </select>

    <select id="selectCoaList" resultMap="ValidationRequestDOResultMap">
        SELECT ledger_name,
               segment1,
               segment2,
               segment3,
               segment4,
               segment5,
               segment6,
               segment7,
               segment8,
               segment9,
               segment10
        FROM sofi_gl_interface
        where process_day = #{processDay}
          and process_status = 'VALIDATED'
        group by ledger_name, segment1, segment2, segment3, segment4, segment5, segment6, segment7, segment8, segment9,
                 segment10
    </select>

    <select id="selectShenMaCoaList" resultMap="ValidationRequestDOResultMap">
        SELECT ledger_name,
               segment1,
               segment2,
               segment3,
               segment4,
               segment5,
               segment6,
               segment7,
               segment8,
               segment9,
               segment10
        FROM sofi_gl_interface_common
        where process_day = #{processDay}
          and process_status = 'VALIDATED'
        group by ledger_name, segment1, segment2, segment3, segment4, segment5, segment6, segment7, segment8, segment9,
                 segment10
    </select>

    <select id="selectIdListByCoa" resultType="java.lang.Long">
        SELECT id
        FROM sofi_gl_interface
        WHERE process_status =  #{processStatus}
        and process_day = #{processDay}
        <if test="coa.ledgerName != null and coa.ledgerName != ''">
            and ledger_name = #{coa.ledgerName}
        </if>
        <if test="coa.segment1 != null and coa.segment1 != ''">
            and segment1 = #{coa.segment1}
        </if>
        <if test="coa.segment2 != null and coa.segment2 != ''">
            and segment2 = #{coa.segment2}
        </if>
        <if test="coa.segment3 != null and coa.segment3 != ''">
            and segment3 = #{coa.segment3}
        </if>
        <if test="coa.segment4 != null and coa.segment4 != ''">
            and segment4 = #{coa.segment4}
        </if>
        <if test="coa.segment5 != null and coa.segment5 != ''">
            and segment5 = #{coa.segment5}
        </if>
        <if test="coa.segment6 != null and coa.segment6 != ''">
            and segment6 = #{coa.segment6}
        </if>
        <if test="coa.segment7 != null and coa.segment7 != ''">
            and segment7 = #{coa.segment7}
        </if>
        <if test="coa.segment8 != null and coa.segment8 != ''">
            and segment8 = #{coa.segment8}
        </if>
        <if test="coa.segment9 != null and coa.segment9 != ''">
            and segment9 = #{coa.segment9}
        </if>
        <if test="coa.segment10 != null and coa.segment10 != ''">
            and segment10 = #{coa.segment10}
        </if>

    </select>

    <select id="selectInterfaceCommonIdListByCoa" resultType="java.lang.Long">
        SELECT id
        FROM sofi_gl_interface_common
        WHERE process_status =  #{processStatus}
        and process_day = #{processDay}
        <if test="coa.ledgerName != null and coa.ledgerName != ''">
            and ledger_name = #{coa.ledgerName}
        </if>
        <if test="coa.segment1 != null and coa.segment1 != ''">
            and segment1 = #{coa.segment1}
        </if>
        <if test="coa.segment2 != null and coa.segment2 != ''">
            and segment2 = #{coa.segment2}
        </if>
        <if test="coa.segment3 != null and coa.segment3 != ''">
            and segment3 = #{coa.segment3}
        </if>
        <if test="coa.segment4 != null and coa.segment4 != ''">
            and segment4 = #{coa.segment4}
        </if>
        <if test="coa.segment5 != null and coa.segment5 != ''">
            and segment5 = #{coa.segment5}
        </if>
        <if test="coa.segment6 != null and coa.segment6 != ''">
            and segment6 = #{coa.segment6}
        </if>
        <if test="coa.segment7 != null and coa.segment7 != ''">
            and segment7 = #{coa.segment7}
        </if>
        <if test="coa.segment8 != null and coa.segment8 != ''">
            and segment8 = #{coa.segment8}
        </if>
        <if test="coa.segment9 != null and coa.segment9 != ''">
            and segment9 = #{coa.segment9}
        </if>
        <if test="coa.segment10 != null and coa.segment10 != ''">
            and segment10 = #{coa.segment10}
        </if>

    </select>

    <select id="selectShenmaIdListByCoa" resultType="java.lang.Long">
        SELECT sm.id
        FROM sofi_gl_shenma sm,sofi_gl_interface_common sc
        WHERE sm.process_status =  #{processStatus}
        and sm.process_day = #{processDay}
        and sm.link_reference = sc.reference6
        <if test="coa.ledgerName != null and coa.ledgerName != ''">
            and sc.ledger_name = #{coa.ledgerName}
        </if>
        <if test="coa.segment1 != null and coa.segment1 != ''">
            and sc.segment1 = #{coa.segment1}
        </if>
        <if test="coa.segment2 != null and coa.segment2 != ''">
            and sc.segment2 = #{coa.segment2}
        </if>
        <if test="coa.segment3 != null and coa.segment3 != ''">
            and sc.segment3 = #{coa.segment3}
        </if>
        <if test="coa.segment4 != null and coa.segment4 != ''">
            and sc.segment4 = #{coa.segment4}
        </if>
        <if test="coa.segment5 != null and coa.segment5 != ''">
            and sc.segment5 = #{coa.segment5}
        </if>
        <if test="coa.segment6 != null and coa.segment6 != ''">
            and sc.segment6 = #{coa.segment6}
        </if>
        <if test="coa.segment7 != null and coa.segment7 != ''">
            and sc.segment7 = #{coa.segment7}
        </if>
        <if test="coa.segment8 != null and coa.segment8 != ''">
            and sc.segment8 = #{coa.segment8}
        </if>
        <if test="coa.segment9 != null and coa.segment9 != ''">
            and sc.segment9 = #{coa.segment9}
        </if>
        <if test="coa.segment10 != null and coa.segment10 != ''">
            and sc.segment10 = #{coa.segment10}
        </if>

    </select>

    <update id="updateCoaStatusById">
        UPDATE sofi_gl_interface
        set process_status = #{processStatus,jdbcType=VARCHAR}, process_message = #{processMessage,jdbcType=VARCHAR}
        WHERE process_day = #{processDay,jdbcType=VARCHAR}
          AND system_code = #{systemCode,jdbcType=VARCHAR}
          AND id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateInterfaceCommonCoaStatusById">
        UPDATE sofi_gl_interface_common
        set process_status = #{processStatus,jdbcType=VARCHAR}, process_message = #{processMessage,jdbcType=VARCHAR}
        WHERE process_day = #{processDay,jdbcType=VARCHAR}
          AND system_code = #{systemCode,jdbcType=VARCHAR}
          AND id = #{id,jdbcType=BIGINT}
    </update>


    <update id="updateShenmaCoaStatusById">
        UPDATE sofi_gl_shenma
        set process_status = #{processStatus,jdbcType=VARCHAR}, process_message = #{processMessage,jdbcType=VARCHAR}
        WHERE  id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateSafiHeaderStatus">
        UPDATE sofi_gl_interface_header dest
            JOIN sofi_gl_interface src
        ON src.poliza_id = dest.external_reference
            AND src.process_day = dest.process_day
            SET
                dest.process_status = src.process_status,
                dest.process_message = src.process_message
        WHERE
            dest.process_day = #{processDay}
          AND dest.system_code = 'SAFI'
          AND src.process_day = #{processDay}
    </update>

    <update id="updateShenMaHeaderStatus">
        UPDATE sofi_gl_interface_header dest
            JOIN sofi_gl_shenma src
        ON src.link_reference = dest.external_reference
            AND src.process_day = dest.process_day
            SET
                dest.process_status = src.process_status,
                dest.process_message = src.process_message
        WHERE
            dest.process_day = #{processDay}
          AND dest.system_code = 'SHENMA'
          AND src.process_day = #{processDay}
    </update>

    <update id="updateCoaStatus">
        UPDATE sofi_gl_interface
        SET
        process_status = #{processStatus},
        process_message = #{processMessage}
        WHERE process_status = 'VALIDATED'
        AND process_day = #{processDay}
        <choose>
            <when test="coa != null">
                <if test="coa.ledgerName != null and coa.ledgerName != ''">
                    AND ledger_name = #{coa.ledgerName}
                </if>
                <if test="coa.segment1 != null and coa.segment1 != ''">
                    AND segment1 = #{coa.segment1}
                </if>
                <if test="coa.segment2 != null and coa.segment2 != ''">
                    AND segment2 = #{coa.segment2}
                </if>
                <if test="coa.segment3 != null and coa.segment3 != ''">
                    AND segment3 = #{coa.segment3}
                </if>
                <if test="coa.segment4 != null and coa.segment4 != ''">
                    AND segment4 = #{coa.segment4}
                </if>
                <if test="coa.segment5 != null and coa.segment5 != ''">
                    AND segment5 = #{coa.segment5}
                </if>
                <if test="coa.segment6 != null and coa.segment6 != ''">
                    AND segment6 = #{coa.segment6}
                </if>
                <if test="coa.segment7 != null and coa.segment7 != ''">
                    AND segment7 = #{coa.segment7}
                </if>
                <if test="coa.segment8 != null and coa.segment8 != ''">
                    AND segment8 = #{coa.segment8}
                </if>
                <if test="coa.segment9 != null and coa.segment9 != ''">
                    AND segment9 = #{coa.segment9}
                </if>
                <if test="coa.segment10 != null and coa.segment10 != ''">
                    AND segment10 = #{coa.segment10}
                </if>
            </when>
            <otherwise>
                <if test="ledgerName != null and ledgerName != ''">
                    AND ledger_name = #{ledgerName}
                </if>
                <if test="segment1 != null and segment1 != ''">
                    AND segment1 = #{segment1}
                </if>
                <if test="segment2 != null and segment2 != ''">
                    AND segment2 = #{segment2}
                </if>
                <if test="segment3 != null and segment3 != ''">
                    AND segment3 = #{segment3}
                </if>
                <if test="segment4 != null and segment4 != ''">
                    AND segment4 = #{segment4}
                </if>
                <if test="segment5 != null and segment5 != ''">
                    AND segment5 = #{segment5}
                </if>
                <if test="segment6 != null and segment6 != ''">
                    AND segment6 = #{segment6}
                </if>
                <if test="segment7 != null and segment7 != ''">
                    AND segment7 = #{segment7}
                </if>
                <if test="segment8 != null and segment8 != ''">
                    AND segment8 = #{segment8}
                </if>
                <if test="segment9 != null and segment9 != ''">
                    AND segment9 = #{segment9}
                </if>
                <if test="segment10 != null and segment10 != ''">
                    AND segment10 = #{segment10}
                </if>
            </otherwise>
        </choose>
    </update>
</mapper>
