<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.CuxTwoTabValuesExtraMapper">
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO cux_two_tab_values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="list[0].id != null">id,</if>
            <if test="list[0].headerId != null">header_id,</if>
            <if test="list[0].value1 != null">value1,</if>
            <if test="list[0].value2 != null">value2,</if>
            <if test="list[0].value3 != null">value3,</if>
            <if test="list[0].value4 != null">value4,</if>
            <if test="list[0].value5 != null">value5,</if>
            <if test="list[0].value6 != null">value6,</if>
            <if test="list[0].value7 != null">value7,</if>
            <if test="list[0].value8 != null">value8,</if>
            <if test="list[0].value9 != null">value9,</if>
            <if test="list[0].value10 != null">value10,</if>
            <if test="list[0].value11 != null">value11,</if>
            <if test="list[0].value12 != null">value12,</if>
            <if test="list[0].value13 != null">value13,</if>
            <if test="list[0].value14 != null">value14,</if>
            <if test="list[0].value15 != null">value15,</if>
            <if test="list[0].value16 != null">value16,</if>
            <if test="list[0].value17 != null">value17,</if>
            <if test="list[0].value18 != null">value18,</if>
            <if test="list[0].value19 != null">value19,</if>
            <if test="list[0].value20 != null">value20,</if>
            <if test="list[0].value21 != null">value21,</if>
            <if test="list[0].value22 != null">value22,</if>
            <if test="list[0].value23 != null">value23,</if>
            <if test="list[0].value24 != null">value24,</if>
            <if test="list[0].value25 != null">value25,</if>
            <if test="list[0].value26 != null">value26,</if>
            <if test="list[0].value27 != null">value27,</if>
            <if test="list[0].value28 != null">value28,</if>
            <if test="list[0].value29 != null">value29,</if>
            <if test="list[0].value30 != null">value30,</if>
            <if test="list[0].value31 != null">value31,</if>
            <if test="list[0].value32 != null">value32,</if>
            <if test="list[0].value33 != null">value33,</if>
            <if test="list[0].value34 != null">value34,</if>
            <if test="list[0].value35 != null">value35,</if>
            <if test="list[0].value36 != null">value36,</if>
            <if test="list[0].value37 != null">value37,</if>
            <if test="list[0].value38 != null">value38,</if>
            <if test="list[0].value39 != null">value39,</if>
            <if test="list[0].value40 != null">value40,</if>
            <if test="list[0].value41 != null">value41,</if>
            <if test="list[0].value42 != null">value42,</if>
            <if test="list[0].value43 != null">value43,</if>
            <if test="list[0].value44 != null">value44,</if>
            <if test="list[0].value45 != null">value45,</if>
            <if test="list[0].value46 != null">value46,</if>
            <if test="list[0].value47 != null">value47,</if>
            <if test="list[0].value48 != null">value48,</if>
            <if test="list[0].value49 != null">value49,</if>
            <if test="list[0].value50 != null">value50,</if>
            <if test="list[0].enabledFlag != null">enabled_flag,</if>
            <if test="list[0].dateFrom != null">date_from,</if>
            <if test="list[0].dateTo != null">date_to,</if>
            <if test="list[0].lang != null">lang,</if>
            <if test="list[0].version != null">version,</if>
            <if test="list[0].lastUpdateDate != null">last_update_date,</if>
            <if test="list[0].lastUpdatedBy != null">last_updated_by,</if>
            <if test="list[0].lastUpdateLogin != null">last_update_login,</if>
            <if test="list[0].createdBy != null">created_by,</if>
            <if test="list[0].creationDate != null">creation_date,</if>
        </trim>
        VALUES
        <foreach collection="list" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">#{item.id},</if>
                <if test="item.headerId != null">#{item.headerId},</if>
                <if test="item.value1 != null">#{item.value1},</if>
                <if test="item.value2 != null">#{item.value2},</if>
                <if test="item.value3 != null">#{item.value3},</if>
                <if test="item.value4 != null">#{item.value4},</if>
                <if test="item.value5 != null">#{item.value5},</if>
                <if test="item.value6 != null">#{item.value6},</if>
                <if test="item.value7 != null">#{item.value7},</if>
                <if test="item.value8 != null">#{item.value8},</if>
                <if test="item.value9 != null">#{item.value9},</if>
                <if test="item.value10 != null">#{item.value10},</if>
                <if test="item.value11 != null">#{item.value11},</if>
                <if test="item.value12 != null">#{item.value12},</if>
                <if test="item.value13 != null">#{item.value13},</if>
                <if test="item.value14 != null">#{item.value14},</if>
                <if test="item.value15 != null">#{item.value15},</if>
                <if test="item.value16 != null">#{item.value16},</if>
                <if test="item.value17 != null">#{item.value17},</if>
                <if test="item.value18 != null">#{item.value18},</if>
                <if test="item.value19 != null">#{item.value19},</if>
                <if test="item.value20 != null">#{item.value20},</if>
                <if test="item.value21 != null">#{item.value21},</if>
                <if test="item.value22 != null">#{item.value22},</if>
                <if test="item.value23 != null">#{item.value23},</if>
                <if test="item.value24 != null">#{item.value24},</if>
                <if test="item.value25 != null">#{item.value25},</if>
                <if test="item.value26 != null">#{item.value26},</if>
                <if test="item.value27 != null">#{item.value27},</if>
                <if test="item.value28 != null">#{item.value28},</if>
                <if test="item.value29 != null">#{item.value29},</if>
                <if test="item.value30 != null">#{item.value30},</if>
                <if test="item.value31 != null">#{item.value31},</if>
                <if test="item.value32 != null">#{item.value32},</if>
                <if test="item.value33 != null">#{item.value33},</if>
                <if test="item.value34 != null">#{item.value34},</if>
                <if test="item.value35 != null">#{item.value35},</if>
                <if test="item.value36 != null">#{item.value36},</if>
                <if test="item.value37 != null">#{item.value37},</if>
                <if test="item.value38 != null">#{item.value38},</if>
                <if test="item.value39 != null">#{item.value39},</if>
                <if test="item.value40 != null">#{item.value40},</if>
                <if test="item.value41 != null">#{item.value41},</if>
                <if test="item.value42 != null">#{item.value42},</if>
                <if test="item.value43 != null">#{item.value43},</if>
                <if test="item.value44 != null">#{item.value44},</if>
                <if test="item.value45 != null">#{item.value45},</if>
                <if test="item.value46 != null">#{item.value46},</if>
                <if test="item.value47 != null">#{item.value47},</if>
                <if test="item.value48 != null">#{item.value48},</if>
                <if test="item.value49 != null">#{item.value49},</if>
                <if test="item.value50 != null">#{item.value50},</if>
                <if test="item.enabledFlag != null">#{item.enabledFlag},</if>
                <if test="item.dateFrom != null">#{item.dateFrom},</if>
                <if test="item.dateTo != null">#{item.dateTo},</if>
                <if test="item.lang != null">#{item.lang},</if>
                <if test="item.version != null">#{item.version},</if>
                <if test="item.lastUpdateDate != null">#{item.lastUpdateDate},</if>
                <if test="item.lastUpdatedBy != null">#{item.lastUpdatedBy},</if>
                <if test="item.lastUpdateLogin != null">#{item.lastUpdateLogin},</if>
                <if test="item.createdBy != null">#{item.createdBy},</if>
                <if test="item.creationDate != null">#{item.creationDate},</if>
            </trim>
        </foreach>
    </insert>

    <delete id="batchDelete" parameterType="java.util.List">
        DELETE FROM cux_two_tab_values
        WHERE (id, veersion) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.id}, #{item.version})
        </foreach>
    </delete>

    <select id="findByUniqueFields"
            resultType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePO">
        SELECT * FROM cux_two_tab_values
        WHERE header_id = #{headerId}
        and lang = #{lang}
        <foreach collection="uniqueFields" item="field" index="index">
            AND ${field} = #{values[${index}]}
        </foreach>
    </select>
    <select id="selectByFormCode" parameterType="java.lang.String"
            resultMap="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.CuxTwoTabValuePOMapper.BaseResultMap">
        select value1,
               value2,
               value3,
               value4,
               value5,
               value6,
               value7,
               value8,
               value9,
               value10
        from cux_two_tab_headers h,
             cux_two_tab_values v
        where h.id = v.header_id
          and h.form_code = #{formCode,jdbcType=VARCHAR}
          and h.enable_flag = 'Y'
          and v.enabled_flag = 'Y'
          and v.lang = 'US'
    </select>

    <select id="selectAllByFormCode"
            resultMap="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.CuxTwoTabValuePOMapper.ExtraResultMap">
        select value1,
               value2,
               value3,
               value4,
               value5,
               value6,
               value7,
               value8,
               value9,
               value10,
               h.form_code
        from cux_two_tab_headers h,
             cux_two_tab_values v
        where h.id = v.header_id
          and h.form_code in ('COMPANY_LEDGER_MAPPINGS', 'CATEGORY_MAPPINGS', 'CURRENCY_MAPPINGS', 'PRODUCT_MAPPINGS',
                              'GL_JOURNAL_SOURCE_MAPPINGS', 'GL_CODE_MAPPINGS', 'PROFIT_CENTER_MAPPINGS',
                              'INTERCOMPANY_MAPPINGS')
          and h.enable_flag = 'Y'
          and v.enabled_flag = 'Y'
          and v.lang = 'US'
    </select>
</mapper>