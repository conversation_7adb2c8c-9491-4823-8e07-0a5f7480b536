<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SofiGlInterfaceHeaderCustomerMapper">
  <select id="selectInterfaceHeaderList" resultMap="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfaceHeaderPOMapper.BaseResultMap">
      select
      <include
              refid="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfaceHeaderPOMapper.Base_Column_List"/>
      from sofi_gl_interface_header
      <where>
          <if test="statusList != null and statusList.size() > 0">
              and process_status in
              <foreach collection="statusList" item="status" open="(" separator="," close=")">
                  #{status}
              </foreach>
          </if>
          <if test="groupId != null and groupId !=0">
              and group_id =#{groupId,jdbcType=BIGINT}
          </if>
          <if test="systemCode != null">
              and system_code =#{systemCode,jdbcType=VARCHAR}
          </if>
          <if test="processDay != null">
              and process_day =#{processDay,jdbcType=VARCHAR}
          </if>

      </where>
  </select>

    <insert id="batchInsert"
            parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHeaderPO">
        insert into sofi_gl_interface_header (
        system_code, process_day, batch_id, external_reference, journal_count, process_status,
        process_message, object_version_number, creation_date, created_by, last_modify_date,
        last_modified_by,group_id
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.systemCode,jdbcType=VARCHAR},#{item.processDay,jdbcType=VARCHAR}, #{item.batchId,jdbcType=VARCHAR},
            #{item.externalReference,jdbcType=VARCHAR}, #{item.journalCount,jdbcType=BIGINT},
            #{item.processStatus,jdbcType=VARCHAR},
            #{item.processMessage,jdbcType=VARCHAR}, #{item.objectVersionNumber,jdbcType=INTEGER},
            #{item.creationDate,jdbcType=TIMESTAMP},
            #{item.createdBy,jdbcType=VARCHAR}, #{item.lastModifyDate,jdbcType=TIMESTAMP},
            #{item.lastModifiedBy,jdbcType=VARCHAR},
            #{item.groupId,jdbcType=BIGINT}
            )
        </foreach>
    </insert>
  <update id="updatePolizaIdSelective">
      update sofi_gl_interface_header
      <set>
          <if test="sofiGlInterfaceHeaderPO.id != null">
              id = #{sofiGlInterfaceHeaderPO.id,jdbcType=BIGINT},
          </if>
          <if test="sofiGlInterfaceHeaderPO.processDay != null">
              process_day = #{sofiGlInterfaceHeaderPO.processDay,jdbcType=VARCHAR},
          </if>
          <if test="sofiGlInterfaceHeaderPO.batchId != null">
              batch_id = #{sofiGlInterfaceHeaderPO.batchId,jdbcType=VARCHAR},
          </if>
          <if test="sofiGlInterfaceHeaderPO.externalReference != null">
              external_reference = #{sofiGlInterfaceHeaderPO.externalReference,jdbcType=VARCHAR},
          </if>
          <if test="sofiGlInterfaceHeaderPO.journalCount != null">
              journal_count = #{sofiGlInterfaceHeaderPO.journalCount,jdbcType=INTEGER},
          </if>
          <if test="sofiGlInterfaceHeaderPO.processStatus != null">
              process_status = #{sofiGlInterfaceHeaderPO.processStatus,jdbcType=VARCHAR},
          </if>
          <if test="sofiGlInterfaceHeaderPO.processMessage != null">
              process_message = #{sofiGlInterfaceHeaderPO.processMessage,jdbcType=VARCHAR},
          </if>
          <if test="sofiGlInterfaceHeaderPO.objectVersionNumber != null">
              object_version_number = #{sofiGlInterfaceHeaderPO.objectVersionNumber,jdbcType=INTEGER},
          </if>
          <if test="sofiGlInterfaceHeaderPO.createdBy != null">
              created_by = #{sofiGlInterfaceHeaderPO.createdBy,jdbcType=VARCHAR},
          </if>
          <if test="sofiGlInterfaceHeaderPO.lastModifiedBy != null">
              last_modified_by = #{sofiGlInterfaceHeaderPO.lastModifiedBy,jdbcType=VARCHAR},
          </if>
          <if test="sofiGlInterfaceHeaderPO.groupId != null">
              group_id = #{sofiGlInterfaceHeaderPO.groupId,jdbcType=BIGINT},
          </if>
      </set>
      where system_code=#{systemCode,jdbcType=VARCHAR}
          and process_day=#{processDay,jdbcType=VARCHAR}
          and group_id=#{groupId,jdbcType=BIGINT}
          and external_reference = #{externalReference,jdbcType=VARCHAR}
  </update>
    <select id="findByPolizaIds"
            resultMap="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfaceHeaderPOMapper.BaseResultMap">
        select
        <include
                refid="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfaceHeaderPOMapper.Base_Column_List"/>
        from sofi_gl_interface_header
        where external_reference in
        <foreach collection="list" item="externalReference" open="(" separator="," close=")">
            #{externalReference,jdbcType=VARCHAR}
        </foreach>
        and system_code = 'SAFI'
    </select>
    <delete id="batchDeleteByPolizaIds">
        delete from sofi_gl_interface_header
        where external_reference in
        <foreach collection="list" item="externalReference" open="(" separator="," close=")">
            #{externalReference,jdbcType=VARCHAR}
        </foreach>
        and system_code = 'SAFI'
    </delete>

    <!-- 根据groupId分组查询 -->
    <select id="selectGroupId" resultType="java.lang.Long">
        select  group_id  groupId from sofi_gl_interface_header
        where system_code = #{systemCode,jdbcType=VARCHAR} and process_status in ('NEW','MAP_FAILED')
        and process_day =#{processDay,jdbcType=VARCHAR}
        group by group_id
    </select>
    <update id="updateInterfaceHeaderByGroupId">
        update sofi_gl_interface_header
         set  process_status=#{newProcessStatus,jdbcType=VARCHAR}
        where system_code = #{systemCode,jdbcType=VARCHAR}
            and process_day =#{processDay,jdbcType=VARCHAR}
            and group_id=#{groupId,jdbcType=BIGINT}
            and process_status=#{oldProcessStatus,jdbcType=VARCHAR}
    </update>
</mapper>