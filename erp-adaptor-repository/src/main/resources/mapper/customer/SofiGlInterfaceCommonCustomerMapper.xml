<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SofiGlInterfaceCommonCustomerMapper">
    <insert id="batchInsert"
            parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceCommonPO">
        insert into sofi_gl_interface_common (system_code, process_day, accounting_date,
        period_name, ledger_id, ledger_name,
        currency_code, journal_category, journal_source,
        journal_source_name, reference1, reference2,
        reference4, reference5,
        reference6, reference8,
        reference9, reference10, reference21,
        reference22, reference23, reference24,
        reference25, reference26, reference27,
        reference28, reference29, reference30,
        segment1, segment2, segment3,
        segment4, segment5, segment6,
        segment7, segment8, segment9,
        segment10, entered_dr, entered_cr,
        accounted_dr, accounted_cr, group_id,
        currency_conversion_rate,
        currency_conversion_type, attribute_category,
        header_attribute1, header_attribute2, header_attribute3,
        header_attribute4, header_attribute5, header_attribute6,
        header_attribute7, header_attribute8, header_attribute9,
        header_attribute10, header_attribute11, header_attribute12,
        header_attribute13, header_attribute14, header_attribute15,
        attribute_category3, line_attribute1, line_attribute2,
        line_attribute3, line_attribute4, line_attribute5,
        line_attribute6, line_attribute7, line_attribute8,
        line_attribute9, line_attribute10, line_attribute11,
        line_attribute12, line_attribute13, line_attribute14,
        line_attribute15, line_attribute16, line_attribute17,
        line_attribute18, line_attribute19, line_attribute20,
        process_status, process_message, je_header_id,
        journal_name, je_line_num, document_id,
        load_request_id, import_request_id, object_version_number,
        created_by,
        last_modified_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.systemCode,jdbcType=VARCHAR},
            #{item.processDay,jdbcType=VARCHAR},
            #{item.accountingDate,jdbcType=DATE},
            #{item.periodName,jdbcType=VARCHAR},
            #{item.ledgerId,jdbcType=BIGINT},
            #{item.ledgerName,jdbcType=VARCHAR},
            #{item.currencyCode,jdbcType=VARCHAR},
            #{item.journalCategory,jdbcType=VARCHAR},
            #{item.journalSource,jdbcType=VARCHAR},
            #{item.journalSourceName,jdbcType=VARCHAR},
            #{item.reference1,jdbcType=VARCHAR},
            #{item.reference2,jdbcType=VARCHAR},
            #{item.reference4,jdbcType=VARCHAR},
            #{item.reference5,jdbcType=VARCHAR},
            #{item.reference6,jdbcType=VARCHAR},
            #{item.reference8,jdbcType=VARCHAR},
            #{item.reference9,jdbcType=VARCHAR}, #{item.reference10,jdbcType=VARCHAR},
            #{item.reference21,jdbcType=VARCHAR},
            #{item.reference22,jdbcType=VARCHAR}, #{item.reference23,jdbcType=VARCHAR},
            #{item.reference24,jdbcType=VARCHAR},
            #{item.reference25,jdbcType=VARCHAR}, #{item.reference26,jdbcType=VARCHAR},
            #{item.reference27,jdbcType=VARCHAR},
            #{item.reference28,jdbcType=VARCHAR}, #{item.reference29,jdbcType=VARCHAR},
            #{item.reference30,jdbcType=VARCHAR},
            #{item.segment1,jdbcType=VARCHAR}, #{item.segment2,jdbcType=VARCHAR}, #{item.segment3,jdbcType=VARCHAR},
            #{item.segment4,jdbcType=VARCHAR}, #{item.segment5,jdbcType=VARCHAR}, #{item.segment6,jdbcType=VARCHAR},
            #{item.segment7,jdbcType=VARCHAR}, #{item.segment8,jdbcType=VARCHAR}, #{item.segment9,jdbcType=VARCHAR},
            #{item.segment10,jdbcType=VARCHAR}, #{item.enteredDr,jdbcType=DECIMAL}, #{item.enteredCr,jdbcType=DECIMAL},
            #{item.accountedDr,jdbcType=DECIMAL}, #{item.accountedCr,jdbcType=DECIMAL}, #{item.groupId,jdbcType=BIGINT},
            #{item.currencyConversionRate,jdbcType=DECIMAL},
            #{item.currencyConversionType,jdbcType=VARCHAR}, #{item.attributeCategory,jdbcType=VARCHAR},
            #{item.headerAttribute1,jdbcType=VARCHAR}, #{item.headerAttribute2,jdbcType=VARCHAR},
            #{item.headerAttribute3,jdbcType=VARCHAR},
            #{item.headerAttribute4,jdbcType=VARCHAR}, #{item.headerAttribute5,jdbcType=VARCHAR},
            #{item.headerAttribute6,jdbcType=VARCHAR},
            #{item.headerAttribute7,jdbcType=VARCHAR}, #{item.headerAttribute8,jdbcType=VARCHAR},
            #{item.headerAttribute9,jdbcType=VARCHAR},
            #{item.headerAttribute10,jdbcType=VARCHAR}, #{item.headerAttribute11,jdbcType=VARCHAR},
            #{item.headerAttribute12,jdbcType=VARCHAR},
            #{item.headerAttribute13,jdbcType=VARCHAR}, #{item.headerAttribute14,jdbcType=VARCHAR},
            #{item.headerAttribute15,jdbcType=VARCHAR},
            #{item.attributeCategory3,jdbcType=VARCHAR}, #{item.lineAttribute1,jdbcType=VARCHAR},
            #{item.lineAttribute2,jdbcType=VARCHAR},
            #{item.lineAttribute3,jdbcType=VARCHAR}, #{item.lineAttribute4,jdbcType=VARCHAR},
            #{item.lineAttribute5,jdbcType=VARCHAR},
            #{item.lineAttribute6,jdbcType=VARCHAR}, #{item.lineAttribute7,jdbcType=VARCHAR},
            #{item.lineAttribute8,jdbcType=VARCHAR},
            #{item.lineAttribute9,jdbcType=VARCHAR}, #{item.lineAttribute10,jdbcType=VARCHAR},
            #{item.lineAttribute11,jdbcType=VARCHAR},
            #{item.lineAttribute12,jdbcType=VARCHAR}, #{item.lineAttribute13,jdbcType=VARCHAR},
            #{item.lineAttribute14,jdbcType=VARCHAR},
            #{item.lineAttribute15,jdbcType=VARCHAR}, #{item.lineAttribute16,jdbcType=VARCHAR},
            #{item.lineAttribute17,jdbcType=VARCHAR},
            #{item.lineAttribute18,jdbcType=VARCHAR}, #{item.lineAttribute19,jdbcType=VARCHAR},
            #{item.lineAttribute20,jdbcType=VARCHAR},
            #{item.processStatus,jdbcType=VARCHAR}, #{item.processMessage,jdbcType=VARCHAR},
            #{item.jeHeaderId,jdbcType=BIGINT},
            #{item.journalName,jdbcType=VARCHAR}, #{item.jeLineNum,jdbcType=BIGINT}, #{item.documentId,jdbcType=BIGINT},
            #{item.loadRequestId,jdbcType=BIGINT}, #{item.importRequestId,jdbcType=BIGINT},
            #{item.objectVersionNumber,jdbcType=BIGINT},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.lastModifiedBy,jdbcType=VARCHAR})
        </foreach>

    </insert>
    <select id="selectGroupIds" resultType="java.lang.Long">
        select group_id
        from sofi_gl_interface_common
        where system_code = 'SHENMA'
          and process_status = 'MAPPED'
          and process_day = #{processDay,jdbcType=VARCHAR}
        group by group_id
    </select>
    <select id="selectSofiGlInterfaceCommonDOList"
            resultMap="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfaceCommonPOMapper.BaseResultMap">
        select
        <include
                refid="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfaceCommonPOMapper.Base_Column_List"/>
        from sofi_gl_interface_common
        where system_code =#{systemCode,jdbcType=VARCHAR}
        and process_day =#{processDay,jdbcType=VARCHAR}
        and process_status = 'MAPPED'
        and group_id=#{groupId,jdbcType=BIGINT}
    </select>

    <update id="updateBySystemCodeAndProcessDayAndGroupIdAndStatus">
        update sofi_gl_interface_common
        <set>
            <if test="sofiGlInterfaceCommonPO.systemCode != null">
                system_code = #{sofiGlInterfaceCommonPO.systemCode,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.processDay != null">
                process_day = #{sofiGlInterfaceCommonPO.processDay,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.accountingDate != null">
                accounting_date = #{sofiGlInterfaceCommonPO.accountingDate,jdbcType=DATE},
            </if>
            <if test="sofiGlInterfaceCommonPO.periodName != null">
                period_name = #{sofiGlInterfaceCommonPO.periodName,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.ledgerId != null">
                ledger_id = #{sofiGlInterfaceCommonPO.ledgerId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfaceCommonPO.ledgerName != null">
                ledger_name = #{sofiGlInterfaceCommonPO.ledgerName,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.currencyCode != null">
                currency_code = #{sofiGlInterfaceCommonPO.currencyCode,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.journalCategory != null">
                journal_category = #{sofiGlInterfaceCommonPO.journalCategory,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.journalSource != null">
                journal_source = #{sofiGlInterfaceCommonPO.journalSource,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.journalSourceName != null">
                journal_source_name = #{sofiGlInterfaceCommonPO.journalSourceName,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.reference1 != null">
                reference1 = #{sofiGlInterfaceCommonPO.reference1,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.reference2 != null">
                reference2 = #{sofiGlInterfaceCommonPO.reference2,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.reference3 != null">
                reference3 = #{sofiGlInterfaceCommonPO.reference3,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.reference4 != null">
                reference4 = #{sofiGlInterfaceCommonPO.reference4,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.reference5 != null">
                reference5 = #{sofiGlInterfaceCommonPO.reference5,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.reference6 != null">
                reference6 = #{sofiGlInterfaceCommonPO.reference6,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.reference7 != null">
                reference7 = #{sofiGlInterfaceCommonPO.reference7,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.reference8 != null">
                reference8 = #{sofiGlInterfaceCommonPO.reference8,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.reference9 != null">
                reference9 = #{sofiGlInterfaceCommonPO.reference9,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.reference10 != null">
                reference10 = #{sofiGlInterfaceCommonPO.reference10,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.reference21 != null">
                reference21 = #{sofiGlInterfaceCommonPO.reference21,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.reference22 != null">
                reference22 = #{sofiGlInterfaceCommonPO.reference22,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.reference23 != null">
                reference23 = #{sofiGlInterfaceCommonPO.reference23,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.reference24 != null">
                reference24 = #{sofiGlInterfaceCommonPO.reference24,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.reference25 != null">
                reference25 = #{sofiGlInterfaceCommonPO.reference25,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.reference26 != null">
                reference26 = #{sofiGlInterfaceCommonPO.reference26,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.reference27 != null">
                reference27 = #{sofiGlInterfaceCommonPO.reference27,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.reference28 != null">
                reference28 = #{sofiGlInterfaceCommonPO.reference28,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.reference29 != null">
                reference29 = #{sofiGlInterfaceCommonPO.reference29,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.reference30 != null">
                reference30 = #{sofiGlInterfaceCommonPO.reference30,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.segment1 != null">
                segment1 = #{sofiGlInterfaceCommonPO.segment1,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.segment2 != null">
                segment2 = #{sofiGlInterfaceCommonPO.segment2,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.segment3 != null">
                segment3 = #{sofiGlInterfaceCommonPO.segment3,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.segment4 != null">
                segment4 = #{sofiGlInterfaceCommonPO.segment4,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.segment5 != null">
                segment5 = #{sofiGlInterfaceCommonPO.segment5,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.segment6 != null">
                segment6 = #{sofiGlInterfaceCommonPO.segment6,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.segment7 != null">
                segment7 = #{sofiGlInterfaceCommonPO.segment7,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.segment8 != null">
                segment8 = #{sofiGlInterfaceCommonPO.segment8,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.segment9 != null">
                segment9 = #{sofiGlInterfaceCommonPO.segment9,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.segment10 != null">
                segment10 = #{sofiGlInterfaceCommonPO.segment10,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.enteredDr != null">
                entered_dr = #{sofiGlInterfaceCommonPO.enteredDr,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlInterfaceCommonPO.enteredCr != null">
                entered_cr = #{sofiGlInterfaceCommonPO.enteredCr,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlInterfaceCommonPO.accountedDr != null">
                accounted_dr = #{sofiGlInterfaceCommonPO.accountedDr,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlInterfaceCommonPO.accountedCr != null">
                accounted_cr = #{sofiGlInterfaceCommonPO.accountedCr,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlInterfaceCommonPO.groupId != null">
                group_id = #{sofiGlInterfaceCommonPO.groupId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfaceCommonPO.currencyConversionDate != null">
                currency_conversion_date = #{sofiGlInterfaceCommonPO.currencyConversionDate,jdbcType=TIMESTAMP},
            </if>
            <if test="sofiGlInterfaceCommonPO.currencyConversionRate != null">
                currency_conversion_rate = #{sofiGlInterfaceCommonPO.currencyConversionRate,jdbcType=DECIMAL},
            </if>
            <if test="sofiGlInterfaceCommonPO.currencyConversionType != null">
                currency_conversion_type = #{sofiGlInterfaceCommonPO.currencyConversionType,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.attributeCategory != null">
                attribute_category = #{sofiGlInterfaceCommonPO.attributeCategory,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.headerAttribute1 != null">
                header_attribute1 = #{sofiGlInterfaceCommonPO.headerAttribute1,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.headerAttribute2 != null">
                header_attribute2 = #{sofiGlInterfaceCommonPO.headerAttribute2,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.headerAttribute3 != null">
                header_attribute3 = #{sofiGlInterfaceCommonPO.headerAttribute3,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.headerAttribute4 != null">
                header_attribute4 = #{sofiGlInterfaceCommonPO.headerAttribute4,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.headerAttribute5 != null">
                header_attribute5 = #{sofiGlInterfaceCommonPO.headerAttribute5,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.headerAttribute6 != null">
                header_attribute6 = #{sofiGlInterfaceCommonPO.headerAttribute6,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.headerAttribute7 != null">
                header_attribute7 = #{sofiGlInterfaceCommonPO.headerAttribute7,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.headerAttribute8 != null">
                header_attribute8 = #{sofiGlInterfaceCommonPO.headerAttribute8,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.headerAttribute9 != null">
                header_attribute9 = #{sofiGlInterfaceCommonPO.headerAttribute9,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.headerAttribute10 != null">
                header_attribute10 = #{sofiGlInterfaceCommonPO.headerAttribute10,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.headerAttribute11 != null">
                header_attribute11 = #{sofiGlInterfaceCommonPO.headerAttribute11,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.headerAttribute12 != null">
                header_attribute12 = #{sofiGlInterfaceCommonPO.headerAttribute12,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.headerAttribute13 != null">
                header_attribute13 = #{sofiGlInterfaceCommonPO.headerAttribute13,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.headerAttribute14 != null">
                header_attribute14 = #{sofiGlInterfaceCommonPO.headerAttribute14,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.headerAttribute15 != null">
                header_attribute15 = #{sofiGlInterfaceCommonPO.headerAttribute15,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.attributeCategory3 != null">
                attribute_category3 = #{sofiGlInterfaceCommonPO.attributeCategory3,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.lineAttribute1 != null">
                line_attribute1 = #{sofiGlInterfaceCommonPO.lineAttribute1,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.lineAttribute2 != null">
                line_attribute2 = #{sofiGlInterfaceCommonPO.lineAttribute2,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.lineAttribute3 != null">
                line_attribute3 = #{sofiGlInterfaceCommonPO.lineAttribute3,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.lineAttribute4 != null">
                line_attribute4 = #{sofiGlInterfaceCommonPO.lineAttribute4,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.lineAttribute5 != null">
                line_attribute5 = #{sofiGlInterfaceCommonPO.lineAttribute5,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.lineAttribute6 != null">
                line_attribute6 = #{sofiGlInterfaceCommonPO.lineAttribute6,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.lineAttribute7 != null">
                line_attribute7 = #{sofiGlInterfaceCommonPO.lineAttribute7,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.lineAttribute8 != null">
                line_attribute8 = #{sofiGlInterfaceCommonPO.lineAttribute8,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.lineAttribute9 != null">
                line_attribute9 = #{sofiGlInterfaceCommonPO.lineAttribute9,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.lineAttribute10 != null">
                line_attribute10 = #{sofiGlInterfaceCommonPO.lineAttribute10,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.lineAttribute11 != null">
                line_attribute11 = #{sofiGlInterfaceCommonPO.lineAttribute11,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.lineAttribute12 != null">
                line_attribute12 = #{sofiGlInterfaceCommonPO.lineAttribute12,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.lineAttribute13 != null">
                line_attribute13 = #{sofiGlInterfaceCommonPO.lineAttribute13,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.lineAttribute14 != null">
                line_attribute14 = #{sofiGlInterfaceCommonPO.lineAttribute14,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.lineAttribute15 != null">
                line_attribute15 = #{sofiGlInterfaceCommonPO.lineAttribute15,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.lineAttribute16 != null">
                line_attribute16 = #{sofiGlInterfaceCommonPO.lineAttribute16,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.lineAttribute17 != null">
                line_attribute17 = #{sofiGlInterfaceCommonPO.lineAttribute17,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.lineAttribute18 != null">
                line_attribute18 = #{sofiGlInterfaceCommonPO.lineAttribute18,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.lineAttribute19 != null">
                line_attribute19 = #{sofiGlInterfaceCommonPO.lineAttribute19,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.lineAttribute20 != null">
                line_attribute20 = #{sofiGlInterfaceCommonPO.lineAttribute20,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.processStatus != null">
                process_status = #{sofiGlInterfaceCommonPO.processStatus,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.processMessage != null">
                process_message = #{sofiGlInterfaceCommonPO.processMessage,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.jeHeaderId != null">
                je_header_id = #{sofiGlInterfaceCommonPO.jeHeaderId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfaceCommonPO.journalName != null">
                journal_name = #{sofiGlInterfaceCommonPO.journalName,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.jeLineNum != null">
                je_line_num = #{sofiGlInterfaceCommonPO.jeLineNum,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfaceCommonPO.documentId != null">
                document_id = #{sofiGlInterfaceCommonPO.documentId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfaceCommonPO.loadRequestId != null">
                load_request_id = #{sofiGlInterfaceCommonPO.loadRequestId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfaceCommonPO.importRequestId != null">
                import_request_id = #{sofiGlInterfaceCommonPO.importRequestId,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfaceCommonPO.objectVersionNumber != null">
                object_version_number = #{sofiGlInterfaceCommonPO.objectVersionNumber,jdbcType=BIGINT},
            </if>
            <if test="sofiGlInterfaceCommonPO.creationDate != null">
                creation_date = #{sofiGlInterfaceCommonPO.creationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="sofiGlInterfaceCommonPO.createdBy != null">
                created_by = #{sofiGlInterfaceCommonPO.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sofiGlInterfaceCommonPO.lastModifyDate != null">
                last_modify_date = #{sofiGlInterfaceCommonPO.lastModifyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="sofiGlInterfaceCommonPO.lastModifiedBy != null">
                last_modified_by = #{sofiGlInterfaceCommonPO.lastModifiedBy,jdbcType=VARCHAR},
            </if>
        </set>
        where  system_code = #{systemCode,jdbcType=VARCHAR}
            and  process_day = #{processDay,jdbcType=VARCHAR}
            and  process_status = #{processStatus,jdbcType=VARCHAR}
            and group_id=#{groupId,jdbcType=BIGINT}
    </update>

    <select id="queryByReference5AndProcessDay" resultMap="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfaceCommonPOMapper.BaseResultMap">
        select
        <include refid="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfaceCommonPOMapper.Base_Column_List"/>
        from sofi_gl_interface_common
        where process_day = #{processDay,jdbcType=VARCHAR}
        and reference6 in
        <foreach collection="externalReferenceList" item="reference" open="(" close=")" separator=",">
            #{reference,jdbcType=VARCHAR}
        </foreach>
    </select>

    <update id="updateByIdAndProcessDay" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceCommonPO">
        update sofi_gl_interface_common
        <set>
            <if test="updatePO.processStatus != null">
                process_status = #{updatePO.processStatus,jdbcType=VARCHAR},
            </if>
            <if test="updatePO.processMessage != null">
                process_message = #{updatePO.processMessage,jdbcType=VARCHAR},
            </if>
            <if test="updatePO.loadRequestId != null">
                load_request_id = #{updatePO.loadRequestId,jdbcType=BIGINT},
            </if>
            <if test="updatePO.importRequestId != null">
                import_request_id = #{updatePO.importRequestId,jdbcType=BIGINT},
            </if>
            <if test="updatePO.lastModifyDate != null">
                last_modify_date = #{updatePO.lastModifyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatePO.lastModifiedBy != null">
                last_modified_by = #{updatePO.lastModifiedBy,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
        and process_day = #{processDay,jdbcType=VARCHAR}
    </update>

    <delete id="deleteByProcessDay">
        delete from sofi_gl_interface_common
        where process_day = #{processDay,jdbcType=VARCHAR}
    </delete>
</mapper>