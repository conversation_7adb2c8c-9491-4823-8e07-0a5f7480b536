<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.customer.SequenceMapper">
    <select id="getCurrentValue" resultType="Long">
        SELECT next_id
        FROM sofi_fbdi_sequence_control
        WHERE name = #{name}
    </select>

    <select id="getCurrentValueWithLock" resultType="Long">
        SELECT next_id
        FROM sofi_fbdi_sequence_control
        WHERE name = #{name} FOR UPDATE
    </select>

    <update id="atomicIncrement">
        UPDATE sofi_fbdi_sequence_control
        SET next_id          = next_id + #{increment},
            last_update_date = NOW()
        WHERE name = #{name}
          AND next_id = #{expectedValue}
    </update>

    <insert id="createSequence">
        INSERT INTO sofi_fbdi_sequence_control (name, next_id, batch_size, last_update_date)
        VALUES (#{name}, #{startValue}, #{batchSize}, NOW())
    </insert>

    <update id="updateValue">
        UPDATE sofi_fbdi_sequence_control
        SET next_id          = #{newValue},
            last_update_date = NOW()
        WHERE name = #{name}
    </update>

    <select id="getBatchSize" resultType="Integer">
        SELECT batch_size
        FROM sofi_fbdi_sequence_control
        WHERE name = #{name}
    </select>

    <update id="updateBatchSize">
        UPDATE sofi_fbdi_sequence_control
        SET batch_size       = #{batchSize},
            last_update_date = NOW()
        WHERE name = #{name}
    </update>
</mapper> 