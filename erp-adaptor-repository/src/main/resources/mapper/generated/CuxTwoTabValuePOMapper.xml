<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.CuxTwoTabValuePOMapper">
  <resultMap id="BaseResultMap" type="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="header_id" jdbcType="BIGINT" property="headerId" />
    <result column="value1" jdbcType="VARCHAR" property="value1" />
    <result column="value2" jdbcType="VARCHAR" property="value2" />
    <result column="value3" jdbcType="VARCHAR" property="value3" />
    <result column="value4" jdbcType="VARCHAR" property="value4" />
    <result column="value5" jdbcType="VARCHAR" property="value5" />
    <result column="value6" jdbcType="VARCHAR" property="value6" />
    <result column="value7" jdbcType="VARCHAR" property="value7" />
    <result column="value8" jdbcType="VARCHAR" property="value8" />
    <result column="value9" jdbcType="VARCHAR" property="value9" />
    <result column="value10" jdbcType="VARCHAR" property="value10" />
    <result column="value11" jdbcType="VARCHAR" property="value11" />
    <result column="value12" jdbcType="VARCHAR" property="value12" />
    <result column="value13" jdbcType="VARCHAR" property="value13" />
    <result column="value14" jdbcType="VARCHAR" property="value14" />
    <result column="value15" jdbcType="VARCHAR" property="value15" />
    <result column="value16" jdbcType="VARCHAR" property="value16" />
    <result column="value17" jdbcType="VARCHAR" property="value17" />
    <result column="value18" jdbcType="VARCHAR" property="value18" />
    <result column="value19" jdbcType="VARCHAR" property="value19" />
    <result column="value20" jdbcType="VARCHAR" property="value20" />
    <result column="value21" jdbcType="VARCHAR" property="value21" />
    <result column="value22" jdbcType="VARCHAR" property="value22" />
    <result column="value23" jdbcType="VARCHAR" property="value23" />
    <result column="value24" jdbcType="VARCHAR" property="value24" />
    <result column="value25" jdbcType="VARCHAR" property="value25" />
    <result column="value26" jdbcType="VARCHAR" property="value26" />
    <result column="value27" jdbcType="VARCHAR" property="value27" />
    <result column="value28" jdbcType="VARCHAR" property="value28" />
    <result column="value29" jdbcType="VARCHAR" property="value29" />
    <result column="value30" jdbcType="VARCHAR" property="value30" />
    <result column="value31" jdbcType="VARCHAR" property="value31" />
    <result column="value32" jdbcType="VARCHAR" property="value32" />
    <result column="value33" jdbcType="VARCHAR" property="value33" />
    <result column="value34" jdbcType="VARCHAR" property="value34" />
    <result column="value35" jdbcType="VARCHAR" property="value35" />
    <result column="value36" jdbcType="VARCHAR" property="value36" />
    <result column="value37" jdbcType="VARCHAR" property="value37" />
    <result column="value38" jdbcType="VARCHAR" property="value38" />
    <result column="value39" jdbcType="VARCHAR" property="value39" />
    <result column="value40" jdbcType="VARCHAR" property="value40" />
    <result column="value41" jdbcType="VARCHAR" property="value41" />
    <result column="value42" jdbcType="VARCHAR" property="value42" />
    <result column="value43" jdbcType="VARCHAR" property="value43" />
    <result column="value44" jdbcType="VARCHAR" property="value44" />
    <result column="value45" jdbcType="VARCHAR" property="value45" />
    <result column="value46" jdbcType="VARCHAR" property="value46" />
    <result column="value47" jdbcType="VARCHAR" property="value47" />
    <result column="value48" jdbcType="VARCHAR" property="value48" />
    <result column="value49" jdbcType="VARCHAR" property="value49" />
    <result column="value50" jdbcType="VARCHAR" property="value50" />
    <result column="enabled_flag" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="date_from" jdbcType="DATE" property="dateFrom" />
    <result column="date_to" jdbcType="DATE" property="dateTo" />
    <result column="lang" jdbcType="VARCHAR" property="lang" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate" />
    <result column="last_updated_by" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="last_update_login" jdbcType="INTEGER" property="lastUpdateLogin" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="creation_date" jdbcType="TIMESTAMP" property="creationDate" />
  </resultMap>
  <resultMap id="ExtraResultMap" extends="BaseResultMap" type="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValueExtraPO">
    <result column="form_code" jdbcType="VARCHAR" property="formCode" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, header_id, value1, value2, value3, value4, value5, value6, value7, value8, value9, 
    value10, value11, value12, value13, value14, value15, value16, value17, value18, 
    value19, value20, value21, value22, value23, value24, value25, value26, value27, 
    value28, value29, value30, value31, value32, value33, value34, value35, value36, 
    value37, value38, value39, value40, value41, value42, value43, value44, value45, 
    value46, value47, value48, value49, value50, enabled_flag, date_from, date_to, lang, 
    version, last_update_date, last_updated_by, last_update_login, created_by, creation_date
  </sql>
  <select id="selectByExample" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from cux_two_tab_values
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cux_two_tab_values
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cux_two_tab_values
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePOExample">
    delete from cux_two_tab_values
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cux_two_tab_values (header_id, value1, value2, 
      value3, value4, value5, 
      value6, value7, value8, 
      value9, value10, value11, 
      value12, value13, value14, 
      value15, value16, value17, 
      value18, value19, value20, 
      value21, value22, value23, 
      value24, value25, value26, 
      value27, value28, value29, 
      value30, value31, value32, 
      value33, value34, value35, 
      value36, value37, value38, 
      value39, value40, value41, 
      value42, value43, value44, 
      value45, value46, value47, 
      value48, value49, value50, 
      enabled_flag, date_from, date_to, 
      lang, version, last_update_date, 
      last_updated_by, last_update_login, created_by, 
      creation_date)
    values (#{headerId,jdbcType=BIGINT}, #{value1,jdbcType=VARCHAR}, #{value2,jdbcType=VARCHAR}, 
      #{value3,jdbcType=VARCHAR}, #{value4,jdbcType=VARCHAR}, #{value5,jdbcType=VARCHAR}, 
      #{value6,jdbcType=VARCHAR}, #{value7,jdbcType=VARCHAR}, #{value8,jdbcType=VARCHAR}, 
      #{value9,jdbcType=VARCHAR}, #{value10,jdbcType=VARCHAR}, #{value11,jdbcType=VARCHAR}, 
      #{value12,jdbcType=VARCHAR}, #{value13,jdbcType=VARCHAR}, #{value14,jdbcType=VARCHAR}, 
      #{value15,jdbcType=VARCHAR}, #{value16,jdbcType=VARCHAR}, #{value17,jdbcType=VARCHAR}, 
      #{value18,jdbcType=VARCHAR}, #{value19,jdbcType=VARCHAR}, #{value20,jdbcType=VARCHAR}, 
      #{value21,jdbcType=VARCHAR}, #{value22,jdbcType=VARCHAR}, #{value23,jdbcType=VARCHAR}, 
      #{value24,jdbcType=VARCHAR}, #{value25,jdbcType=VARCHAR}, #{value26,jdbcType=VARCHAR}, 
      #{value27,jdbcType=VARCHAR}, #{value28,jdbcType=VARCHAR}, #{value29,jdbcType=VARCHAR}, 
      #{value30,jdbcType=VARCHAR}, #{value31,jdbcType=VARCHAR}, #{value32,jdbcType=VARCHAR}, 
      #{value33,jdbcType=VARCHAR}, #{value34,jdbcType=VARCHAR}, #{value35,jdbcType=VARCHAR}, 
      #{value36,jdbcType=VARCHAR}, #{value37,jdbcType=VARCHAR}, #{value38,jdbcType=VARCHAR}, 
      #{value39,jdbcType=VARCHAR}, #{value40,jdbcType=VARCHAR}, #{value41,jdbcType=VARCHAR}, 
      #{value42,jdbcType=VARCHAR}, #{value43,jdbcType=VARCHAR}, #{value44,jdbcType=VARCHAR}, 
      #{value45,jdbcType=VARCHAR}, #{value46,jdbcType=VARCHAR}, #{value47,jdbcType=VARCHAR}, 
      #{value48,jdbcType=VARCHAR}, #{value49,jdbcType=VARCHAR}, #{value50,jdbcType=VARCHAR}, 
      #{enabledFlag,jdbcType=VARCHAR}, #{dateFrom,jdbcType=DATE}, #{dateTo,jdbcType=DATE}, 
      #{lang,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, #{lastUpdateDate,jdbcType=TIMESTAMP}, 
      #{lastUpdatedBy,jdbcType=VARCHAR}, #{lastUpdateLogin,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, 
      #{creationDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cux_two_tab_values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="headerId != null">
        header_id,
      </if>
      <if test="value1 != null">
        value1,
      </if>
      <if test="value2 != null">
        value2,
      </if>
      <if test="value3 != null">
        value3,
      </if>
      <if test="value4 != null">
        value4,
      </if>
      <if test="value5 != null">
        value5,
      </if>
      <if test="value6 != null">
        value6,
      </if>
      <if test="value7 != null">
        value7,
      </if>
      <if test="value8 != null">
        value8,
      </if>
      <if test="value9 != null">
        value9,
      </if>
      <if test="value10 != null">
        value10,
      </if>
      <if test="value11 != null">
        value11,
      </if>
      <if test="value12 != null">
        value12,
      </if>
      <if test="value13 != null">
        value13,
      </if>
      <if test="value14 != null">
        value14,
      </if>
      <if test="value15 != null">
        value15,
      </if>
      <if test="value16 != null">
        value16,
      </if>
      <if test="value17 != null">
        value17,
      </if>
      <if test="value18 != null">
        value18,
      </if>
      <if test="value19 != null">
        value19,
      </if>
      <if test="value20 != null">
        value20,
      </if>
      <if test="value21 != null">
        value21,
      </if>
      <if test="value22 != null">
        value22,
      </if>
      <if test="value23 != null">
        value23,
      </if>
      <if test="value24 != null">
        value24,
      </if>
      <if test="value25 != null">
        value25,
      </if>
      <if test="value26 != null">
        value26,
      </if>
      <if test="value27 != null">
        value27,
      </if>
      <if test="value28 != null">
        value28,
      </if>
      <if test="value29 != null">
        value29,
      </if>
      <if test="value30 != null">
        value30,
      </if>
      <if test="value31 != null">
        value31,
      </if>
      <if test="value32 != null">
        value32,
      </if>
      <if test="value33 != null">
        value33,
      </if>
      <if test="value34 != null">
        value34,
      </if>
      <if test="value35 != null">
        value35,
      </if>
      <if test="value36 != null">
        value36,
      </if>
      <if test="value37 != null">
        value37,
      </if>
      <if test="value38 != null">
        value38,
      </if>
      <if test="value39 != null">
        value39,
      </if>
      <if test="value40 != null">
        value40,
      </if>
      <if test="value41 != null">
        value41,
      </if>
      <if test="value42 != null">
        value42,
      </if>
      <if test="value43 != null">
        value43,
      </if>
      <if test="value44 != null">
        value44,
      </if>
      <if test="value45 != null">
        value45,
      </if>
      <if test="value46 != null">
        value46,
      </if>
      <if test="value47 != null">
        value47,
      </if>
      <if test="value48 != null">
        value48,
      </if>
      <if test="value49 != null">
        value49,
      </if>
      <if test="value50 != null">
        value50,
      </if>
      <if test="enabledFlag != null">
        enabled_flag,
      </if>
      <if test="dateFrom != null">
        date_from,
      </if>
      <if test="dateTo != null">
        date_to,
      </if>
      <if test="lang != null">
        lang,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="lastUpdateDate != null">
        last_update_date,
      </if>
      <if test="lastUpdatedBy != null">
        last_updated_by,
      </if>
      <if test="lastUpdateLogin != null">
        last_update_login,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="creationDate != null">
        creation_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="headerId != null">
        #{headerId,jdbcType=BIGINT},
      </if>
      <if test="value1 != null">
        #{value1,jdbcType=VARCHAR},
      </if>
      <if test="value2 != null">
        #{value2,jdbcType=VARCHAR},
      </if>
      <if test="value3 != null">
        #{value3,jdbcType=VARCHAR},
      </if>
      <if test="value4 != null">
        #{value4,jdbcType=VARCHAR},
      </if>
      <if test="value5 != null">
        #{value5,jdbcType=VARCHAR},
      </if>
      <if test="value6 != null">
        #{value6,jdbcType=VARCHAR},
      </if>
      <if test="value7 != null">
        #{value7,jdbcType=VARCHAR},
      </if>
      <if test="value8 != null">
        #{value8,jdbcType=VARCHAR},
      </if>
      <if test="value9 != null">
        #{value9,jdbcType=VARCHAR},
      </if>
      <if test="value10 != null">
        #{value10,jdbcType=VARCHAR},
      </if>
      <if test="value11 != null">
        #{value11,jdbcType=VARCHAR},
      </if>
      <if test="value12 != null">
        #{value12,jdbcType=VARCHAR},
      </if>
      <if test="value13 != null">
        #{value13,jdbcType=VARCHAR},
      </if>
      <if test="value14 != null">
        #{value14,jdbcType=VARCHAR},
      </if>
      <if test="value15 != null">
        #{value15,jdbcType=VARCHAR},
      </if>
      <if test="value16 != null">
        #{value16,jdbcType=VARCHAR},
      </if>
      <if test="value17 != null">
        #{value17,jdbcType=VARCHAR},
      </if>
      <if test="value18 != null">
        #{value18,jdbcType=VARCHAR},
      </if>
      <if test="value19 != null">
        #{value19,jdbcType=VARCHAR},
      </if>
      <if test="value20 != null">
        #{value20,jdbcType=VARCHAR},
      </if>
      <if test="value21 != null">
        #{value21,jdbcType=VARCHAR},
      </if>
      <if test="value22 != null">
        #{value22,jdbcType=VARCHAR},
      </if>
      <if test="value23 != null">
        #{value23,jdbcType=VARCHAR},
      </if>
      <if test="value24 != null">
        #{value24,jdbcType=VARCHAR},
      </if>
      <if test="value25 != null">
        #{value25,jdbcType=VARCHAR},
      </if>
      <if test="value26 != null">
        #{value26,jdbcType=VARCHAR},
      </if>
      <if test="value27 != null">
        #{value27,jdbcType=VARCHAR},
      </if>
      <if test="value28 != null">
        #{value28,jdbcType=VARCHAR},
      </if>
      <if test="value29 != null">
        #{value29,jdbcType=VARCHAR},
      </if>
      <if test="value30 != null">
        #{value30,jdbcType=VARCHAR},
      </if>
      <if test="value31 != null">
        #{value31,jdbcType=VARCHAR},
      </if>
      <if test="value32 != null">
        #{value32,jdbcType=VARCHAR},
      </if>
      <if test="value33 != null">
        #{value33,jdbcType=VARCHAR},
      </if>
      <if test="value34 != null">
        #{value34,jdbcType=VARCHAR},
      </if>
      <if test="value35 != null">
        #{value35,jdbcType=VARCHAR},
      </if>
      <if test="value36 != null">
        #{value36,jdbcType=VARCHAR},
      </if>
      <if test="value37 != null">
        #{value37,jdbcType=VARCHAR},
      </if>
      <if test="value38 != null">
        #{value38,jdbcType=VARCHAR},
      </if>
      <if test="value39 != null">
        #{value39,jdbcType=VARCHAR},
      </if>
      <if test="value40 != null">
        #{value40,jdbcType=VARCHAR},
      </if>
      <if test="value41 != null">
        #{value41,jdbcType=VARCHAR},
      </if>
      <if test="value42 != null">
        #{value42,jdbcType=VARCHAR},
      </if>
      <if test="value43 != null">
        #{value43,jdbcType=VARCHAR},
      </if>
      <if test="value44 != null">
        #{value44,jdbcType=VARCHAR},
      </if>
      <if test="value45 != null">
        #{value45,jdbcType=VARCHAR},
      </if>
      <if test="value46 != null">
        #{value46,jdbcType=VARCHAR},
      </if>
      <if test="value47 != null">
        #{value47,jdbcType=VARCHAR},
      </if>
      <if test="value48 != null">
        #{value48,jdbcType=VARCHAR},
      </if>
      <if test="value49 != null">
        #{value49,jdbcType=VARCHAR},
      </if>
      <if test="value50 != null">
        #{value50,jdbcType=VARCHAR},
      </if>
      <if test="enabledFlag != null">
        #{enabledFlag,jdbcType=VARCHAR},
      </if>
      <if test="dateFrom != null">
        #{dateFrom,jdbcType=DATE},
      </if>
      <if test="dateTo != null">
        #{dateTo,jdbcType=DATE},
      </if>
      <if test="lang != null">
        #{lang,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="lastUpdateDate != null">
        #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null">
        #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateLogin != null">
        #{lastUpdateLogin,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="creationDate != null">
        #{creationDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map">
    update cux_two_tab_values
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.headerId != null">
        header_id = #{record.headerId,jdbcType=BIGINT},
      </if>
      <if test="record.value1 != null">
        value1 = #{record.value1,jdbcType=VARCHAR},
      </if>
      <if test="record.value2 != null">
        value2 = #{record.value2,jdbcType=VARCHAR},
      </if>
      <if test="record.value3 != null">
        value3 = #{record.value3,jdbcType=VARCHAR},
      </if>
      <if test="record.value4 != null">
        value4 = #{record.value4,jdbcType=VARCHAR},
      </if>
      <if test="record.value5 != null">
        value5 = #{record.value5,jdbcType=VARCHAR},
      </if>
      <if test="record.value6 != null">
        value6 = #{record.value6,jdbcType=VARCHAR},
      </if>
      <if test="record.value7 != null">
        value7 = #{record.value7,jdbcType=VARCHAR},
      </if>
      <if test="record.value8 != null">
        value8 = #{record.value8,jdbcType=VARCHAR},
      </if>
      <if test="record.value9 != null">
        value9 = #{record.value9,jdbcType=VARCHAR},
      </if>
      <if test="record.value10 != null">
        value10 = #{record.value10,jdbcType=VARCHAR},
      </if>
      <if test="record.value11 != null">
        value11 = #{record.value11,jdbcType=VARCHAR},
      </if>
      <if test="record.value12 != null">
        value12 = #{record.value12,jdbcType=VARCHAR},
      </if>
      <if test="record.value13 != null">
        value13 = #{record.value13,jdbcType=VARCHAR},
      </if>
      <if test="record.value14 != null">
        value14 = #{record.value14,jdbcType=VARCHAR},
      </if>
      <if test="record.value15 != null">
        value15 = #{record.value15,jdbcType=VARCHAR},
      </if>
      <if test="record.value16 != null">
        value16 = #{record.value16,jdbcType=VARCHAR},
      </if>
      <if test="record.value17 != null">
        value17 = #{record.value17,jdbcType=VARCHAR},
      </if>
      <if test="record.value18 != null">
        value18 = #{record.value18,jdbcType=VARCHAR},
      </if>
      <if test="record.value19 != null">
        value19 = #{record.value19,jdbcType=VARCHAR},
      </if>
      <if test="record.value20 != null">
        value20 = #{record.value20,jdbcType=VARCHAR},
      </if>
      <if test="record.value21 != null">
        value21 = #{record.value21,jdbcType=VARCHAR},
      </if>
      <if test="record.value22 != null">
        value22 = #{record.value22,jdbcType=VARCHAR},
      </if>
      <if test="record.value23 != null">
        value23 = #{record.value23,jdbcType=VARCHAR},
      </if>
      <if test="record.value24 != null">
        value24 = #{record.value24,jdbcType=VARCHAR},
      </if>
      <if test="record.value25 != null">
        value25 = #{record.value25,jdbcType=VARCHAR},
      </if>
      <if test="record.value26 != null">
        value26 = #{record.value26,jdbcType=VARCHAR},
      </if>
      <if test="record.value27 != null">
        value27 = #{record.value27,jdbcType=VARCHAR},
      </if>
      <if test="record.value28 != null">
        value28 = #{record.value28,jdbcType=VARCHAR},
      </if>
      <if test="record.value29 != null">
        value29 = #{record.value29,jdbcType=VARCHAR},
      </if>
      <if test="record.value30 != null">
        value30 = #{record.value30,jdbcType=VARCHAR},
      </if>
      <if test="record.value31 != null">
        value31 = #{record.value31,jdbcType=VARCHAR},
      </if>
      <if test="record.value32 != null">
        value32 = #{record.value32,jdbcType=VARCHAR},
      </if>
      <if test="record.value33 != null">
        value33 = #{record.value33,jdbcType=VARCHAR},
      </if>
      <if test="record.value34 != null">
        value34 = #{record.value34,jdbcType=VARCHAR},
      </if>
      <if test="record.value35 != null">
        value35 = #{record.value35,jdbcType=VARCHAR},
      </if>
      <if test="record.value36 != null">
        value36 = #{record.value36,jdbcType=VARCHAR},
      </if>
      <if test="record.value37 != null">
        value37 = #{record.value37,jdbcType=VARCHAR},
      </if>
      <if test="record.value38 != null">
        value38 = #{record.value38,jdbcType=VARCHAR},
      </if>
      <if test="record.value39 != null">
        value39 = #{record.value39,jdbcType=VARCHAR},
      </if>
      <if test="record.value40 != null">
        value40 = #{record.value40,jdbcType=VARCHAR},
      </if>
      <if test="record.value41 != null">
        value41 = #{record.value41,jdbcType=VARCHAR},
      </if>
      <if test="record.value42 != null">
        value42 = #{record.value42,jdbcType=VARCHAR},
      </if>
      <if test="record.value43 != null">
        value43 = #{record.value43,jdbcType=VARCHAR},
      </if>
      <if test="record.value44 != null">
        value44 = #{record.value44,jdbcType=VARCHAR},
      </if>
      <if test="record.value45 != null">
        value45 = #{record.value45,jdbcType=VARCHAR},
      </if>
      <if test="record.value46 != null">
        value46 = #{record.value46,jdbcType=VARCHAR},
      </if>
      <if test="record.value47 != null">
        value47 = #{record.value47,jdbcType=VARCHAR},
      </if>
      <if test="record.value48 != null">
        value48 = #{record.value48,jdbcType=VARCHAR},
      </if>
      <if test="record.value49 != null">
        value49 = #{record.value49,jdbcType=VARCHAR},
      </if>
      <if test="record.value50 != null">
        value50 = #{record.value50,jdbcType=VARCHAR},
      </if>
      <if test="record.enabledFlag != null">
        enabled_flag = #{record.enabledFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.dateFrom != null">
        date_from = #{record.dateFrom,jdbcType=DATE},
      </if>
      <if test="record.dateTo != null">
        date_to = #{record.dateTo,jdbcType=DATE},
      </if>
      <if test="record.lang != null">
        lang = #{record.lang,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.lastUpdateDate != null">
        last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdatedBy != null">
        last_updated_by = #{record.lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.lastUpdateLogin != null">
        last_update_login = #{record.lastUpdateLogin,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.creationDate != null">
        creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cux_two_tab_values
    set id = #{record.id,jdbcType=BIGINT},
      header_id = #{record.headerId,jdbcType=BIGINT},
      value1 = #{record.value1,jdbcType=VARCHAR},
      value2 = #{record.value2,jdbcType=VARCHAR},
      value3 = #{record.value3,jdbcType=VARCHAR},
      value4 = #{record.value4,jdbcType=VARCHAR},
      value5 = #{record.value5,jdbcType=VARCHAR},
      value6 = #{record.value6,jdbcType=VARCHAR},
      value7 = #{record.value7,jdbcType=VARCHAR},
      value8 = #{record.value8,jdbcType=VARCHAR},
      value9 = #{record.value9,jdbcType=VARCHAR},
      value10 = #{record.value10,jdbcType=VARCHAR},
      value11 = #{record.value11,jdbcType=VARCHAR},
      value12 = #{record.value12,jdbcType=VARCHAR},
      value13 = #{record.value13,jdbcType=VARCHAR},
      value14 = #{record.value14,jdbcType=VARCHAR},
      value15 = #{record.value15,jdbcType=VARCHAR},
      value16 = #{record.value16,jdbcType=VARCHAR},
      value17 = #{record.value17,jdbcType=VARCHAR},
      value18 = #{record.value18,jdbcType=VARCHAR},
      value19 = #{record.value19,jdbcType=VARCHAR},
      value20 = #{record.value20,jdbcType=VARCHAR},
      value21 = #{record.value21,jdbcType=VARCHAR},
      value22 = #{record.value22,jdbcType=VARCHAR},
      value23 = #{record.value23,jdbcType=VARCHAR},
      value24 = #{record.value24,jdbcType=VARCHAR},
      value25 = #{record.value25,jdbcType=VARCHAR},
      value26 = #{record.value26,jdbcType=VARCHAR},
      value27 = #{record.value27,jdbcType=VARCHAR},
      value28 = #{record.value28,jdbcType=VARCHAR},
      value29 = #{record.value29,jdbcType=VARCHAR},
      value30 = #{record.value30,jdbcType=VARCHAR},
      value31 = #{record.value31,jdbcType=VARCHAR},
      value32 = #{record.value32,jdbcType=VARCHAR},
      value33 = #{record.value33,jdbcType=VARCHAR},
      value34 = #{record.value34,jdbcType=VARCHAR},
      value35 = #{record.value35,jdbcType=VARCHAR},
      value36 = #{record.value36,jdbcType=VARCHAR},
      value37 = #{record.value37,jdbcType=VARCHAR},
      value38 = #{record.value38,jdbcType=VARCHAR},
      value39 = #{record.value39,jdbcType=VARCHAR},
      value40 = #{record.value40,jdbcType=VARCHAR},
      value41 = #{record.value41,jdbcType=VARCHAR},
      value42 = #{record.value42,jdbcType=VARCHAR},
      value43 = #{record.value43,jdbcType=VARCHAR},
      value44 = #{record.value44,jdbcType=VARCHAR},
      value45 = #{record.value45,jdbcType=VARCHAR},
      value46 = #{record.value46,jdbcType=VARCHAR},
      value47 = #{record.value47,jdbcType=VARCHAR},
      value48 = #{record.value48,jdbcType=VARCHAR},
      value49 = #{record.value49,jdbcType=VARCHAR},
      value50 = #{record.value50,jdbcType=VARCHAR},
      enabled_flag = #{record.enabledFlag,jdbcType=VARCHAR},
      date_from = #{record.dateFrom,jdbcType=DATE},
      date_to = #{record.dateTo,jdbcType=DATE},
      lang = #{record.lang,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      last_updated_by = #{record.lastUpdatedBy,jdbcType=VARCHAR},
      last_update_login = #{record.lastUpdateLogin,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      creation_date = #{record.creationDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePO">
    update cux_two_tab_values
    <set>
      <if test="headerId != null">
        header_id = #{headerId,jdbcType=BIGINT},
      </if>
      <if test="value1 != null">
        value1 = #{value1,jdbcType=VARCHAR},
      </if>
      <if test="value2 != null">
        value2 = #{value2,jdbcType=VARCHAR},
      </if>
      <if test="value3 != null">
        value3 = #{value3,jdbcType=VARCHAR},
      </if>
      <if test="value4 != null">
        value4 = #{value4,jdbcType=VARCHAR},
      </if>
      <if test="value5 != null">
        value5 = #{value5,jdbcType=VARCHAR},
      </if>
      <if test="value6 != null">
        value6 = #{value6,jdbcType=VARCHAR},
      </if>
      <if test="value7 != null">
        value7 = #{value7,jdbcType=VARCHAR},
      </if>
      <if test="value8 != null">
        value8 = #{value8,jdbcType=VARCHAR},
      </if>
      <if test="value9 != null">
        value9 = #{value9,jdbcType=VARCHAR},
      </if>
      <if test="value10 != null">
        value10 = #{value10,jdbcType=VARCHAR},
      </if>
      <if test="value11 != null">
        value11 = #{value11,jdbcType=VARCHAR},
      </if>
      <if test="value12 != null">
        value12 = #{value12,jdbcType=VARCHAR},
      </if>
      <if test="value13 != null">
        value13 = #{value13,jdbcType=VARCHAR},
      </if>
      <if test="value14 != null">
        value14 = #{value14,jdbcType=VARCHAR},
      </if>
      <if test="value15 != null">
        value15 = #{value15,jdbcType=VARCHAR},
      </if>
      <if test="value16 != null">
        value16 = #{value16,jdbcType=VARCHAR},
      </if>
      <if test="value17 != null">
        value17 = #{value17,jdbcType=VARCHAR},
      </if>
      <if test="value18 != null">
        value18 = #{value18,jdbcType=VARCHAR},
      </if>
      <if test="value19 != null">
        value19 = #{value19,jdbcType=VARCHAR},
      </if>
      <if test="value20 != null">
        value20 = #{value20,jdbcType=VARCHAR},
      </if>
      <if test="value21 != null">
        value21 = #{value21,jdbcType=VARCHAR},
      </if>
      <if test="value22 != null">
        value22 = #{value22,jdbcType=VARCHAR},
      </if>
      <if test="value23 != null">
        value23 = #{value23,jdbcType=VARCHAR},
      </if>
      <if test="value24 != null">
        value24 = #{value24,jdbcType=VARCHAR},
      </if>
      <if test="value25 != null">
        value25 = #{value25,jdbcType=VARCHAR},
      </if>
      <if test="value26 != null">
        value26 = #{value26,jdbcType=VARCHAR},
      </if>
      <if test="value27 != null">
        value27 = #{value27,jdbcType=VARCHAR},
      </if>
      <if test="value28 != null">
        value28 = #{value28,jdbcType=VARCHAR},
      </if>
      <if test="value29 != null">
        value29 = #{value29,jdbcType=VARCHAR},
      </if>
      <if test="value30 != null">
        value30 = #{value30,jdbcType=VARCHAR},
      </if>
      <if test="value31 != null">
        value31 = #{value31,jdbcType=VARCHAR},
      </if>
      <if test="value32 != null">
        value32 = #{value32,jdbcType=VARCHAR},
      </if>
      <if test="value33 != null">
        value33 = #{value33,jdbcType=VARCHAR},
      </if>
      <if test="value34 != null">
        value34 = #{value34,jdbcType=VARCHAR},
      </if>
      <if test="value35 != null">
        value35 = #{value35,jdbcType=VARCHAR},
      </if>
      <if test="value36 != null">
        value36 = #{value36,jdbcType=VARCHAR},
      </if>
      <if test="value37 != null">
        value37 = #{value37,jdbcType=VARCHAR},
      </if>
      <if test="value38 != null">
        value38 = #{value38,jdbcType=VARCHAR},
      </if>
      <if test="value39 != null">
        value39 = #{value39,jdbcType=VARCHAR},
      </if>
      <if test="value40 != null">
        value40 = #{value40,jdbcType=VARCHAR},
      </if>
      <if test="value41 != null">
        value41 = #{value41,jdbcType=VARCHAR},
      </if>
      <if test="value42 != null">
        value42 = #{value42,jdbcType=VARCHAR},
      </if>
      <if test="value43 != null">
        value43 = #{value43,jdbcType=VARCHAR},
      </if>
      <if test="value44 != null">
        value44 = #{value44,jdbcType=VARCHAR},
      </if>
      <if test="value45 != null">
        value45 = #{value45,jdbcType=VARCHAR},
      </if>
      <if test="value46 != null">
        value46 = #{value46,jdbcType=VARCHAR},
      </if>
      <if test="value47 != null">
        value47 = #{value47,jdbcType=VARCHAR},
      </if>
      <if test="value48 != null">
        value48 = #{value48,jdbcType=VARCHAR},
      </if>
      <if test="value49 != null">
        value49 = #{value49,jdbcType=VARCHAR},
      </if>
      <if test="value50 != null">
        value50 = #{value50,jdbcType=VARCHAR},
      </if>
      <if test="enabledFlag != null">
        enabled_flag = #{enabledFlag,jdbcType=VARCHAR},
      </if>
      <if test="dateFrom != null">
        date_from = #{dateFrom,jdbcType=DATE},
      </if>
      <if test="dateTo != null">
        date_to = #{dateTo,jdbcType=DATE},
      </if>
      <if test="lang != null">
        lang = #{lang,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="lastUpdateDate != null">
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null">
        last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateLogin != null">
        last_update_login = #{lastUpdateLogin,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="creationDate != null">
        creation_date = #{creationDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePO">
    update cux_two_tab_values
    set header_id = #{headerId,jdbcType=BIGINT},
      value1 = #{value1,jdbcType=VARCHAR},
      value2 = #{value2,jdbcType=VARCHAR},
      value3 = #{value3,jdbcType=VARCHAR},
      value4 = #{value4,jdbcType=VARCHAR},
      value5 = #{value5,jdbcType=VARCHAR},
      value6 = #{value6,jdbcType=VARCHAR},
      value7 = #{value7,jdbcType=VARCHAR},
      value8 = #{value8,jdbcType=VARCHAR},
      value9 = #{value9,jdbcType=VARCHAR},
      value10 = #{value10,jdbcType=VARCHAR},
      value11 = #{value11,jdbcType=VARCHAR},
      value12 = #{value12,jdbcType=VARCHAR},
      value13 = #{value13,jdbcType=VARCHAR},
      value14 = #{value14,jdbcType=VARCHAR},
      value15 = #{value15,jdbcType=VARCHAR},
      value16 = #{value16,jdbcType=VARCHAR},
      value17 = #{value17,jdbcType=VARCHAR},
      value18 = #{value18,jdbcType=VARCHAR},
      value19 = #{value19,jdbcType=VARCHAR},
      value20 = #{value20,jdbcType=VARCHAR},
      value21 = #{value21,jdbcType=VARCHAR},
      value22 = #{value22,jdbcType=VARCHAR},
      value23 = #{value23,jdbcType=VARCHAR},
      value24 = #{value24,jdbcType=VARCHAR},
      value25 = #{value25,jdbcType=VARCHAR},
      value26 = #{value26,jdbcType=VARCHAR},
      value27 = #{value27,jdbcType=VARCHAR},
      value28 = #{value28,jdbcType=VARCHAR},
      value29 = #{value29,jdbcType=VARCHAR},
      value30 = #{value30,jdbcType=VARCHAR},
      value31 = #{value31,jdbcType=VARCHAR},
      value32 = #{value32,jdbcType=VARCHAR},
      value33 = #{value33,jdbcType=VARCHAR},
      value34 = #{value34,jdbcType=VARCHAR},
      value35 = #{value35,jdbcType=VARCHAR},
      value36 = #{value36,jdbcType=VARCHAR},
      value37 = #{value37,jdbcType=VARCHAR},
      value38 = #{value38,jdbcType=VARCHAR},
      value39 = #{value39,jdbcType=VARCHAR},
      value40 = #{value40,jdbcType=VARCHAR},
      value41 = #{value41,jdbcType=VARCHAR},
      value42 = #{value42,jdbcType=VARCHAR},
      value43 = #{value43,jdbcType=VARCHAR},
      value44 = #{value44,jdbcType=VARCHAR},
      value45 = #{value45,jdbcType=VARCHAR},
      value46 = #{value46,jdbcType=VARCHAR},
      value47 = #{value47,jdbcType=VARCHAR},
      value48 = #{value48,jdbcType=VARCHAR},
      value49 = #{value49,jdbcType=VARCHAR},
      value50 = #{value50,jdbcType=VARCHAR},
      enabled_flag = #{enabledFlag,jdbcType=VARCHAR},
      date_from = #{dateFrom,jdbcType=DATE},
      date_to = #{dateTo,jdbcType=DATE},
      lang = #{lang,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
      last_update_login = #{lastUpdateLogin,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      creation_date = #{creationDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabValuePOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from cux_two_tab_values
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>