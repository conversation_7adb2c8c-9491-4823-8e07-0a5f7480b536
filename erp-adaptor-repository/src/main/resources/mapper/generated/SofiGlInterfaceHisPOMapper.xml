<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfaceHisPOMapper">
    <resultMap id="BaseResultMap"
               type="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHisPO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="file_id" jdbcType="BIGINT" property="fileId"/>
        <result column="process_day" jdbcType="VARCHAR" property="processDay"/>
        <result column="file_name" jdbcType="VARCHAR" property="fileName"/>
        <result column="batch_id" jdbcType="VARCHAR" property="batchId"/>
        <result column="detalle_poliza_id" jdbcType="BIGINT" property="detallePolizaId"/>
        <result column="source_sys" jdbcType="VARCHAR" property="sourceSys"/>
        <result column="empresa_id" jdbcType="BIGINT" property="empresaId"/>
        <result column="poliza_id" jdbcType="BIGINT" property="polizaId"/>
        <result column="fecha" jdbcType="VARCHAR" property="fecha"/>
        <result column="centro_costo_id" jdbcType="BIGINT" property="centroCostoId"/>
        <result column="cuenta_completa" jdbcType="VARCHAR" property="cuentaCompleta"/>
        <result column="instrumento" jdbcType="BIGINT" property="instrumento"/>
        <result column="moneda_id" jdbcType="BIGINT" property="monedaId"/>
        <result column="cargos" jdbcType="DECIMAL" property="cargos"/>
        <result column="abonos" jdbcType="DECIMAL" property="abonos"/>
        <result column="descripcion" jdbcType="VARCHAR" property="descripcion"/>
        <result column="referencia" jdbcType="VARCHAR" property="referencia"/>
        <result column="procedimiento_cont" jdbcType="VARCHAR" property="procedimientoCont"/>
        <result column="tipo_instrumento_id" jdbcType="VARCHAR" property="tipoInstrumentoId"/>
        <result column="rfc" jdbcType="VARCHAR" property="rfc"/>
        <result column="total_factura" jdbcType="DECIMAL" property="totalFactura"/>
        <result column="folio_uuid" jdbcType="VARCHAR" property="folioUuid"/>
        <result column="usuario" jdbcType="BIGINT" property="usuario"/>
        <result column="fecha_actual" jdbcType="VARCHAR" property="fechaActual"/>
        <result column="direccion_ip" jdbcType="VARCHAR" property="direccionIp"/>
        <result column="programa_id" jdbcType="VARCHAR" property="programaId"/>
        <result column="sucursal" jdbcType="BIGINT" property="sucursal"/>
        <result column="num_transaccion" jdbcType="BIGINT" property="numTransaccion"/>
        <result column="ledger_id" jdbcType="BIGINT" property="ledgerId"/>
        <result column="currency_code" jdbcType="VARCHAR" property="currencyCode"/>
        <result column="journal_category" jdbcType="VARCHAR" property="journalCategory"/>
        <result column="journal_source" jdbcType="VARCHAR" property="journalSource"/>
        <result column="segment1" jdbcType="VARCHAR" property="segment1"/>
        <result column="segment2" jdbcType="VARCHAR" property="segment2"/>
        <result column="segment3" jdbcType="VARCHAR" property="segment3"/>
        <result column="segment4" jdbcType="VARCHAR" property="segment4"/>
        <result column="segment5" jdbcType="VARCHAR" property="segment5"/>
        <result column="segment6" jdbcType="VARCHAR" property="segment6"/>
        <result column="segment7" jdbcType="VARCHAR" property="segment7"/>
        <result column="segment8" jdbcType="VARCHAR" property="segment8"/>
        <result column="segment9" jdbcType="VARCHAR" property="segment9"/>
        <result column="segment10" jdbcType="VARCHAR" property="segment10"/>
        <result column="group_id" jdbcType="BIGINT" property="groupId"/>
        <result column="currency_conversion_date" jdbcType="TIMESTAMP" property="currencyConversionDate"/>
        <result column="currency_conversion_rate" jdbcType="DECIMAL" property="currencyConversionRate"/>
        <result column="user_currency_conversion_type" jdbcType="VARCHAR" property="userCurrencyConversionType"/>
        <result column="process_status" jdbcType="VARCHAR" property="processStatus"/>
        <result column="process_message" jdbcType="VARCHAR" property="processMessage"/>
        <result column="je_header_id" jdbcType="BIGINT" property="jeHeaderId"/>
        <result column="journal_name" jdbcType="VARCHAR" property="journalName"/>
        <result column="je_line_num" jdbcType="BIGINT" property="jeLineNum"/>
        <result column="document_id" jdbcType="BIGINT" property="documentId"/>
        <result column="load_request_id" jdbcType="BIGINT" property="loadRequestId"/>
        <result column="import_request_id" jdbcType="BIGINT" property="importRequestId"/>
        <result column="object_version_number" jdbcType="BIGINT" property="objectVersionNumber"/>
        <result column="creation_date" jdbcType="TIMESTAMP" property="creationDate"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="last_modify_date" jdbcType="TIMESTAMP" property="lastModifyDate"/>
        <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id
        , file_id, process_day, file_name, batch_id, detalle_poliza_id, source_sys, empresa_id,
    poliza_id, fecha, centro_costo_id, cuenta_completa, instrumento, moneda_id, cargos, 
    abonos, descripcion, referencia, procedimiento_cont, tipo_instrumento_id, rfc, total_factura, 
    folio_uuid, usuario, fecha_actual, direccion_ip, programa_id, sucursal, num_transaccion, 
    ledger_id, currency_code, journal_category, journal_source, segment1, segment2, segment3, 
    segment4, segment5, segment6, segment7, segment8, segment9, segment10, group_id, 
    currency_conversion_date, currency_conversion_rate, user_currency_conversion_type, 
    process_status, process_message, je_header_id, journal_name, je_line_num, document_id, 
    load_request_id, import_request_id, object_version_number, creation_date, created_by, 
    last_modify_date, last_modified_by
    </sql>
    <select id="selectByExample"
            parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHisPOExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from sofi_gl_interface_his
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sofi_gl_interface_his
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from sofi_gl_interface_his
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByExample"
            parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHisPOExample">
        delete from sofi_gl_interface_his
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert"
            parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHisPO">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into sofi_gl_interface_his (file_id, process_day, file_name,
        batch_id, detalle_poliza_id, source_sys,
        empresa_id, poliza_id, fecha,
        centro_costo_id, cuenta_completa, instrumento,
        moneda_id, cargos, abonos,
        descripcion, referencia, procedimiento_cont,
        tipo_instrumento_id, rfc, total_factura,
        folio_uuid, usuario, fecha_actual,
        direccion_ip, programa_id, sucursal,
        num_transaccion, ledger_id, currency_code,
        journal_category, journal_source, segment1,
        segment2, segment3, segment4,
        segment5, segment6, segment7,
        segment8, segment9, segment10,
        group_id, currency_conversion_date, currency_conversion_rate,
        user_currency_conversion_type, process_status,
        process_message, je_header_id, journal_name,
        je_line_num, document_id, load_request_id,
        import_request_id, object_version_number, creation_date,
        created_by, last_modify_date, last_modified_by
        )
        values (#{fileId,jdbcType=BIGINT}, #{processDay,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR},
        #{batchId,jdbcType=VARCHAR}, #{detallePolizaId,jdbcType=BIGINT}, #{sourceSys,jdbcType=VARCHAR},
        #{empresaId,jdbcType=BIGINT}, #{polizaId,jdbcType=BIGINT}, #{fecha,jdbcType=VARCHAR},
        #{centroCostoId,jdbcType=BIGINT}, #{cuentaCompleta,jdbcType=VARCHAR}, #{instrumento,jdbcType=BIGINT},
        #{monedaId,jdbcType=BIGINT}, #{cargos,jdbcType=DECIMAL}, #{abonos,jdbcType=DECIMAL},
        #{descripcion,jdbcType=VARCHAR}, #{referencia,jdbcType=VARCHAR}, #{procedimientoCont,jdbcType=VARCHAR},
        #{tipoInstrumentoId,jdbcType=VARCHAR}, #{rfc,jdbcType=VARCHAR}, #{totalFactura,jdbcType=DECIMAL},
        #{folioUuid,jdbcType=VARCHAR}, #{usuario,jdbcType=BIGINT}, #{fechaActual,jdbcType=VARCHAR},
        #{direccionIp,jdbcType=VARCHAR}, #{programaId,jdbcType=VARCHAR}, #{sucursal,jdbcType=BIGINT},
        #{numTransaccion,jdbcType=BIGINT}, #{ledgerId,jdbcType=BIGINT}, #{currencyCode,jdbcType=VARCHAR},
        #{journalCategory,jdbcType=VARCHAR}, #{journalSource,jdbcType=VARCHAR}, #{segment1,jdbcType=VARCHAR},
        #{segment2,jdbcType=VARCHAR}, #{segment3,jdbcType=VARCHAR}, #{segment4,jdbcType=VARCHAR},
        #{segment5,jdbcType=VARCHAR}, #{segment6,jdbcType=VARCHAR}, #{segment7,jdbcType=VARCHAR},
        #{segment8,jdbcType=VARCHAR}, #{segment9,jdbcType=VARCHAR}, #{segment10,jdbcType=VARCHAR},
        #{groupId,jdbcType=BIGINT}, #{currencyConversionDate,jdbcType=TIMESTAMP},
        #{currencyConversionRate,jdbcType=DECIMAL},
        #{userCurrencyConversionType,jdbcType=VARCHAR}, #{processStatus,jdbcType=VARCHAR},
        #{processMessage,jdbcType=VARCHAR}, #{jeHeaderId,jdbcType=BIGINT}, #{journalName,jdbcType=VARCHAR},
        #{jeLineNum,jdbcType=BIGINT}, #{documentId,jdbcType=BIGINT}, #{loadRequestId,jdbcType=BIGINT},
        #{importRequestId,jdbcType=BIGINT}, #{objectVersionNumber,jdbcType=BIGINT}, #{creationDate,jdbcType=TIMESTAMP},
        #{createdBy,jdbcType=VARCHAR}, #{lastModifyDate,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective"
            parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHisPO">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into sofi_gl_interface_his
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileId != null">
                file_id,
            </if>
            <if test="processDay != null">
                process_day,
            </if>
            <if test="fileName != null">
                file_name,
            </if>
            <if test="batchId != null">
                batch_id,
            </if>
            <if test="detallePolizaId != null">
                detalle_poliza_id,
            </if>
            <if test="sourceSys != null">
                source_sys,
            </if>
            <if test="empresaId != null">
                empresa_id,
            </if>
            <if test="polizaId != null">
                poliza_id,
            </if>
            <if test="fecha != null">
                fecha,
            </if>
            <if test="centroCostoId != null">
                centro_costo_id,
            </if>
            <if test="cuentaCompleta != null">
                cuenta_completa,
            </if>
            <if test="instrumento != null">
                instrumento,
            </if>
            <if test="monedaId != null">
                moneda_id,
            </if>
            <if test="cargos != null">
                cargos,
            </if>
            <if test="abonos != null">
                abonos,
            </if>
            <if test="descripcion != null">
                descripcion,
            </if>
            <if test="referencia != null">
                referencia,
            </if>
            <if test="procedimientoCont != null">
                procedimiento_cont,
            </if>
            <if test="tipoInstrumentoId != null">
                tipo_instrumento_id,
            </if>
            <if test="rfc != null">
                rfc,
            </if>
            <if test="totalFactura != null">
                total_factura,
            </if>
            <if test="folioUuid != null">
                folio_uuid,
            </if>
            <if test="usuario != null">
                usuario,
            </if>
            <if test="fechaActual != null">
                fecha_actual,
            </if>
            <if test="direccionIp != null">
                direccion_ip,
            </if>
            <if test="programaId != null">
                programa_id,
            </if>
            <if test="sucursal != null">
                sucursal,
            </if>
            <if test="numTransaccion != null">
                num_transaccion,
            </if>
            <if test="ledgerId != null">
                ledger_id,
            </if>
            <if test="currencyCode != null">
                currency_code,
            </if>
            <if test="journalCategory != null">
                journal_category,
            </if>
            <if test="journalSource != null">
                journal_source,
            </if>
            <if test="segment1 != null">
                segment1,
            </if>
            <if test="segment2 != null">
                segment2,
            </if>
            <if test="segment3 != null">
                segment3,
            </if>
            <if test="segment4 != null">
                segment4,
            </if>
            <if test="segment5 != null">
                segment5,
            </if>
            <if test="segment6 != null">
                segment6,
            </if>
            <if test="segment7 != null">
                segment7,
            </if>
            <if test="segment8 != null">
                segment8,
            </if>
            <if test="segment9 != null">
                segment9,
            </if>
            <if test="segment10 != null">
                segment10,
            </if>
            <if test="groupId != null">
                group_id,
            </if>
            <if test="currencyConversionDate != null">
                currency_conversion_date,
            </if>
            <if test="currencyConversionRate != null">
                currency_conversion_rate,
            </if>
            <if test="userCurrencyConversionType != null">
                user_currency_conversion_type,
            </if>
            <if test="processStatus != null">
                process_status,
            </if>
            <if test="processMessage != null">
                process_message,
            </if>
            <if test="jeHeaderId != null">
                je_header_id,
            </if>
            <if test="journalName != null">
                journal_name,
            </if>
            <if test="jeLineNum != null">
                je_line_num,
            </if>
            <if test="documentId != null">
                document_id,
            </if>
            <if test="loadRequestId != null">
                load_request_id,
            </if>
            <if test="importRequestId != null">
                import_request_id,
            </if>
            <if test="objectVersionNumber != null">
                object_version_number,
            </if>
            <if test="creationDate != null">
                creation_date,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="lastModifyDate != null">
                last_modify_date,
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileId != null">
                #{fileId,jdbcType=BIGINT},
            </if>
            <if test="processDay != null">
                #{processDay,jdbcType=VARCHAR},
            </if>
            <if test="fileName != null">
                #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="batchId != null">
                #{batchId,jdbcType=VARCHAR},
            </if>
            <if test="detallePolizaId != null">
                #{detallePolizaId,jdbcType=BIGINT},
            </if>
            <if test="sourceSys != null">
                #{sourceSys,jdbcType=VARCHAR},
            </if>
            <if test="empresaId != null">
                #{empresaId,jdbcType=BIGINT},
            </if>
            <if test="polizaId != null">
                #{polizaId,jdbcType=BIGINT},
            </if>
            <if test="fecha != null">
                #{fecha,jdbcType=VARCHAR},
            </if>
            <if test="centroCostoId != null">
                #{centroCostoId,jdbcType=BIGINT},
            </if>
            <if test="cuentaCompleta != null">
                #{cuentaCompleta,jdbcType=VARCHAR},
            </if>
            <if test="instrumento != null">
                #{instrumento,jdbcType=BIGINT},
            </if>
            <if test="monedaId != null">
                #{monedaId,jdbcType=BIGINT},
            </if>
            <if test="cargos != null">
                #{cargos,jdbcType=DECIMAL},
            </if>
            <if test="abonos != null">
                #{abonos,jdbcType=DECIMAL},
            </if>
            <if test="descripcion != null">
                #{descripcion,jdbcType=VARCHAR},
            </if>
            <if test="referencia != null">
                #{referencia,jdbcType=VARCHAR},
            </if>
            <if test="procedimientoCont != null">
                #{procedimientoCont,jdbcType=VARCHAR},
            </if>
            <if test="tipoInstrumentoId != null">
                #{tipoInstrumentoId,jdbcType=VARCHAR},
            </if>
            <if test="rfc != null">
                #{rfc,jdbcType=VARCHAR},
            </if>
            <if test="totalFactura != null">
                #{totalFactura,jdbcType=DECIMAL},
            </if>
            <if test="folioUuid != null">
                #{folioUuid,jdbcType=VARCHAR},
            </if>
            <if test="usuario != null">
                #{usuario,jdbcType=BIGINT},
            </if>
            <if test="fechaActual != null">
                #{fechaActual,jdbcType=VARCHAR},
            </if>
            <if test="direccionIp != null">
                #{direccionIp,jdbcType=VARCHAR},
            </if>
            <if test="programaId != null">
                #{programaId,jdbcType=VARCHAR},
            </if>
            <if test="sucursal != null">
                #{sucursal,jdbcType=BIGINT},
            </if>
            <if test="numTransaccion != null">
                #{numTransaccion,jdbcType=BIGINT},
            </if>
            <if test="ledgerId != null">
                #{ledgerId,jdbcType=BIGINT},
            </if>
            <if test="currencyCode != null">
                #{currencyCode,jdbcType=VARCHAR},
            </if>
            <if test="journalCategory != null">
                #{journalCategory,jdbcType=VARCHAR},
            </if>
            <if test="journalSource != null">
                #{journalSource,jdbcType=VARCHAR},
            </if>
            <if test="segment1 != null">
                #{segment1,jdbcType=VARCHAR},
            </if>
            <if test="segment2 != null">
                #{segment2,jdbcType=VARCHAR},
            </if>
            <if test="segment3 != null">
                #{segment3,jdbcType=VARCHAR},
            </if>
            <if test="segment4 != null">
                #{segment4,jdbcType=VARCHAR},
            </if>
            <if test="segment5 != null">
                #{segment5,jdbcType=VARCHAR},
            </if>
            <if test="segment6 != null">
                #{segment6,jdbcType=VARCHAR},
            </if>
            <if test="segment7 != null">
                #{segment7,jdbcType=VARCHAR},
            </if>
            <if test="segment8 != null">
                #{segment8,jdbcType=VARCHAR},
            </if>
            <if test="segment9 != null">
                #{segment9,jdbcType=VARCHAR},
            </if>
            <if test="segment10 != null">
                #{segment10,jdbcType=VARCHAR},
            </if>
            <if test="groupId != null">
                #{groupId,jdbcType=BIGINT},
            </if>
            <if test="currencyConversionDate != null">
                #{currencyConversionDate,jdbcType=TIMESTAMP},
            </if>
            <if test="currencyConversionRate != null">
                #{currencyConversionRate,jdbcType=DECIMAL},
            </if>
            <if test="userCurrencyConversionType != null">
                #{userCurrencyConversionType,jdbcType=VARCHAR},
            </if>
            <if test="processStatus != null">
                #{processStatus,jdbcType=VARCHAR},
            </if>
            <if test="processMessage != null">
                #{processMessage,jdbcType=VARCHAR},
            </if>
            <if test="jeHeaderId != null">
                #{jeHeaderId,jdbcType=BIGINT},
            </if>
            <if test="journalName != null">
                #{journalName,jdbcType=VARCHAR},
            </if>
            <if test="jeLineNum != null">
                #{jeLineNum,jdbcType=BIGINT},
            </if>
            <if test="documentId != null">
                #{documentId,jdbcType=BIGINT},
            </if>
            <if test="loadRequestId != null">
                #{loadRequestId,jdbcType=BIGINT},
            </if>
            <if test="importRequestId != null">
                #{importRequestId,jdbcType=BIGINT},
            </if>
            <if test="objectVersionNumber != null">
                #{objectVersionNumber,jdbcType=BIGINT},
            </if>
            <if test="creationDate != null">
                #{creationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifyDate != null">
                #{lastModifyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByExampleSelective" parameterType="map">
        update sofi_gl_interface_his
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.fileId != null">
                file_id = #{record.fileId,jdbcType=BIGINT},
            </if>
            <if test="record.processDay != null">
                process_day = #{record.processDay,jdbcType=VARCHAR},
            </if>
            <if test="record.fileName != null">
                file_name = #{record.fileName,jdbcType=VARCHAR},
            </if>
            <if test="record.batchId != null">
                batch_id = #{record.batchId,jdbcType=VARCHAR},
            </if>
            <if test="record.detallePolizaId != null">
                detalle_poliza_id = #{record.detallePolizaId,jdbcType=BIGINT},
            </if>
            <if test="record.sourceSys != null">
                source_sys = #{record.sourceSys,jdbcType=VARCHAR},
            </if>
            <if test="record.empresaId != null">
                empresa_id = #{record.empresaId,jdbcType=BIGINT},
            </if>
            <if test="record.polizaId != null">
                poliza_id = #{record.polizaId,jdbcType=BIGINT},
            </if>
            <if test="record.fecha != null">
                fecha = #{record.fecha,jdbcType=VARCHAR},
            </if>
            <if test="record.centroCostoId != null">
                centro_costo_id = #{record.centroCostoId,jdbcType=BIGINT},
            </if>
            <if test="record.cuentaCompleta != null">
                cuenta_completa = #{record.cuentaCompleta,jdbcType=VARCHAR},
            </if>
            <if test="record.instrumento != null">
                instrumento = #{record.instrumento,jdbcType=BIGINT},
            </if>
            <if test="record.monedaId != null">
                moneda_id = #{record.monedaId,jdbcType=BIGINT},
            </if>
            <if test="record.cargos != null">
                cargos = #{record.cargos,jdbcType=DECIMAL},
            </if>
            <if test="record.abonos != null">
                abonos = #{record.abonos,jdbcType=DECIMAL},
            </if>
            <if test="record.descripcion != null">
                descripcion = #{record.descripcion,jdbcType=VARCHAR},
            </if>
            <if test="record.referencia != null">
                referencia = #{record.referencia,jdbcType=VARCHAR},
            </if>
            <if test="record.procedimientoCont != null">
                procedimiento_cont = #{record.procedimientoCont,jdbcType=VARCHAR},
            </if>
            <if test="record.tipoInstrumentoId != null">
                tipo_instrumento_id = #{record.tipoInstrumentoId,jdbcType=VARCHAR},
            </if>
            <if test="record.rfc != null">
                rfc = #{record.rfc,jdbcType=VARCHAR},
            </if>
            <if test="record.totalFactura != null">
                total_factura = #{record.totalFactura,jdbcType=DECIMAL},
            </if>
            <if test="record.folioUuid != null">
                folio_uuid = #{record.folioUuid,jdbcType=VARCHAR},
            </if>
            <if test="record.usuario != null">
                usuario = #{record.usuario,jdbcType=BIGINT},
            </if>
            <if test="record.fechaActual != null">
                fecha_actual = #{record.fechaActual,jdbcType=VARCHAR},
            </if>
            <if test="record.direccionIp != null">
                direccion_ip = #{record.direccionIp,jdbcType=VARCHAR},
            </if>
            <if test="record.programaId != null">
                programa_id = #{record.programaId,jdbcType=VARCHAR},
            </if>
            <if test="record.sucursal != null">
                sucursal = #{record.sucursal,jdbcType=BIGINT},
            </if>
            <if test="record.numTransaccion != null">
                num_transaccion = #{record.numTransaccion,jdbcType=BIGINT},
            </if>
            <if test="record.ledgerId != null">
                ledger_id = #{record.ledgerId,jdbcType=BIGINT},
            </if>
            <if test="record.currencyCode != null">
                currency_code = #{record.currencyCode,jdbcType=VARCHAR},
            </if>
            <if test="record.journalCategory != null">
                journal_category = #{record.journalCategory,jdbcType=VARCHAR},
            </if>
            <if test="record.journalSource != null">
                journal_source = #{record.journalSource,jdbcType=VARCHAR},
            </if>
            <if test="record.segment1 != null">
                segment1 = #{record.segment1,jdbcType=VARCHAR},
            </if>
            <if test="record.segment2 != null">
                segment2 = #{record.segment2,jdbcType=VARCHAR},
            </if>
            <if test="record.segment3 != null">
                segment3 = #{record.segment3,jdbcType=VARCHAR},
            </if>
            <if test="record.segment4 != null">
                segment4 = #{record.segment4,jdbcType=VARCHAR},
            </if>
            <if test="record.segment5 != null">
                segment5 = #{record.segment5,jdbcType=VARCHAR},
            </if>
            <if test="record.segment6 != null">
                segment6 = #{record.segment6,jdbcType=VARCHAR},
            </if>
            <if test="record.segment7 != null">
                segment7 = #{record.segment7,jdbcType=VARCHAR},
            </if>
            <if test="record.segment8 != null">
                segment8 = #{record.segment8,jdbcType=VARCHAR},
            </if>
            <if test="record.segment9 != null">
                segment9 = #{record.segment9,jdbcType=VARCHAR},
            </if>
            <if test="record.segment10 != null">
                segment10 = #{record.segment10,jdbcType=VARCHAR},
            </if>
            <if test="record.groupId != null">
                group_id = #{record.groupId,jdbcType=BIGINT},
            </if>
            <if test="record.currencyConversionDate != null">
                currency_conversion_date = #{record.currencyConversionDate,jdbcType=TIMESTAMP},
            </if>
            <if test="record.currencyConversionRate != null">
                currency_conversion_rate = #{record.currencyConversionRate,jdbcType=DECIMAL},
            </if>
            <if test="record.userCurrencyConversionType != null">
                user_currency_conversion_type = #{record.userCurrencyConversionType,jdbcType=VARCHAR},
            </if>
            <if test="record.processStatus != null">
                process_status = #{record.processStatus,jdbcType=VARCHAR},
            </if>
            <if test="record.processMessage != null">
                process_message = #{record.processMessage,jdbcType=VARCHAR},
            </if>
            <if test="record.jeHeaderId != null">
                je_header_id = #{record.jeHeaderId,jdbcType=BIGINT},
            </if>
            <if test="record.journalName != null">
                journal_name = #{record.journalName,jdbcType=VARCHAR},
            </if>
            <if test="record.jeLineNum != null">
                je_line_num = #{record.jeLineNum,jdbcType=BIGINT},
            </if>
            <if test="record.documentId != null">
                document_id = #{record.documentId,jdbcType=BIGINT},
            </if>
            <if test="record.loadRequestId != null">
                load_request_id = #{record.loadRequestId,jdbcType=BIGINT},
            </if>
            <if test="record.importRequestId != null">
                import_request_id = #{record.importRequestId,jdbcType=BIGINT},
            </if>
            <if test="record.objectVersionNumber != null">
                object_version_number = #{record.objectVersionNumber,jdbcType=BIGINT},
            </if>
            <if test="record.creationDate != null">
                creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="record.createdBy != null">
                created_by = #{record.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="record.lastModifyDate != null">
                last_modify_date = #{record.lastModifyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="record.lastModifiedBy != null">
                last_modified_by = #{record.lastModifiedBy,jdbcType=VARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update sofi_gl_interface_his
        set id = #{record.id,jdbcType=BIGINT},
        file_id = #{record.fileId,jdbcType=BIGINT},
        process_day = #{record.processDay,jdbcType=VARCHAR},
        file_name = #{record.fileName,jdbcType=VARCHAR},
        batch_id = #{record.batchId,jdbcType=VARCHAR},
        detalle_poliza_id = #{record.detallePolizaId,jdbcType=BIGINT},
        source_sys = #{record.sourceSys,jdbcType=VARCHAR},
        empresa_id = #{record.empresaId,jdbcType=BIGINT},
        poliza_id = #{record.polizaId,jdbcType=BIGINT},
        fecha = #{record.fecha,jdbcType=VARCHAR},
        centro_costo_id = #{record.centroCostoId,jdbcType=BIGINT},
        cuenta_completa = #{record.cuentaCompleta,jdbcType=VARCHAR},
        instrumento = #{record.instrumento,jdbcType=BIGINT},
        moneda_id = #{record.monedaId,jdbcType=BIGINT},
        cargos = #{record.cargos,jdbcType=DECIMAL},
        abonos = #{record.abonos,jdbcType=DECIMAL},
        descripcion = #{record.descripcion,jdbcType=VARCHAR},
        referencia = #{record.referencia,jdbcType=VARCHAR},
        procedimiento_cont = #{record.procedimientoCont,jdbcType=VARCHAR},
        tipo_instrumento_id = #{record.tipoInstrumentoId,jdbcType=VARCHAR},
        rfc = #{record.rfc,jdbcType=VARCHAR},
        total_factura = #{record.totalFactura,jdbcType=DECIMAL},
        folio_uuid = #{record.folioUuid,jdbcType=VARCHAR},
        usuario = #{record.usuario,jdbcType=BIGINT},
        fecha_actual = #{record.fechaActual,jdbcType=VARCHAR},
        direccion_ip = #{record.direccionIp,jdbcType=VARCHAR},
        programa_id = #{record.programaId,jdbcType=VARCHAR},
        sucursal = #{record.sucursal,jdbcType=BIGINT},
        num_transaccion = #{record.numTransaccion,jdbcType=BIGINT},
        ledger_id = #{record.ledgerId,jdbcType=BIGINT},
        currency_code = #{record.currencyCode,jdbcType=VARCHAR},
        journal_category = #{record.journalCategory,jdbcType=VARCHAR},
        journal_source = #{record.journalSource,jdbcType=VARCHAR},
        segment1 = #{record.segment1,jdbcType=VARCHAR},
        segment2 = #{record.segment2,jdbcType=VARCHAR},
        segment3 = #{record.segment3,jdbcType=VARCHAR},
        segment4 = #{record.segment4,jdbcType=VARCHAR},
        segment5 = #{record.segment5,jdbcType=VARCHAR},
        segment6 = #{record.segment6,jdbcType=VARCHAR},
        segment7 = #{record.segment7,jdbcType=VARCHAR},
        segment8 = #{record.segment8,jdbcType=VARCHAR},
        segment9 = #{record.segment9,jdbcType=VARCHAR},
        segment10 = #{record.segment10,jdbcType=VARCHAR},
        group_id = #{record.groupId,jdbcType=BIGINT},
        currency_conversion_date = #{record.currencyConversionDate,jdbcType=TIMESTAMP},
        currency_conversion_rate = #{record.currencyConversionRate,jdbcType=DECIMAL},
        user_currency_conversion_type = #{record.userCurrencyConversionType,jdbcType=VARCHAR},
        process_status = #{record.processStatus,jdbcType=VARCHAR},
        process_message = #{record.processMessage,jdbcType=VARCHAR},
        je_header_id = #{record.jeHeaderId,jdbcType=BIGINT},
        journal_name = #{record.journalName,jdbcType=VARCHAR},
        je_line_num = #{record.jeLineNum,jdbcType=BIGINT},
        document_id = #{record.documentId,jdbcType=BIGINT},
        load_request_id = #{record.loadRequestId,jdbcType=BIGINT},
        import_request_id = #{record.importRequestId,jdbcType=BIGINT},
        object_version_number = #{record.objectVersionNumber,jdbcType=BIGINT},
        creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
        created_by = #{record.createdBy,jdbcType=VARCHAR},
        last_modify_date = #{record.lastModifyDate,jdbcType=TIMESTAMP},
        last_modified_by = #{record.lastModifiedBy,jdbcType=VARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHisPO">
        update sofi_gl_interface_his
        <set>
            <if test="fileId != null">
                file_id = #{fileId,jdbcType=BIGINT},
            </if>
            <if test="processDay != null">
                process_day = #{processDay,jdbcType=VARCHAR},
            </if>
            <if test="fileName != null">
                file_name = #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="batchId != null">
                batch_id = #{batchId,jdbcType=VARCHAR},
            </if>
            <if test="detallePolizaId != null">
                detalle_poliza_id = #{detallePolizaId,jdbcType=BIGINT},
            </if>
            <if test="sourceSys != null">
                source_sys = #{sourceSys,jdbcType=VARCHAR},
            </if>
            <if test="empresaId != null">
                empresa_id = #{empresaId,jdbcType=BIGINT},
            </if>
            <if test="polizaId != null">
                poliza_id = #{polizaId,jdbcType=BIGINT},
            </if>
            <if test="fecha != null">
                fecha = #{fecha,jdbcType=VARCHAR},
            </if>
            <if test="centroCostoId != null">
                centro_costo_id = #{centroCostoId,jdbcType=BIGINT},
            </if>
            <if test="cuentaCompleta != null">
                cuenta_completa = #{cuentaCompleta,jdbcType=VARCHAR},
            </if>
            <if test="instrumento != null">
                instrumento = #{instrumento,jdbcType=BIGINT},
            </if>
            <if test="monedaId != null">
                moneda_id = #{monedaId,jdbcType=BIGINT},
            </if>
            <if test="cargos != null">
                cargos = #{cargos,jdbcType=DECIMAL},
            </if>
            <if test="abonos != null">
                abonos = #{abonos,jdbcType=DECIMAL},
            </if>
            <if test="descripcion != null">
                descripcion = #{descripcion,jdbcType=VARCHAR},
            </if>
            <if test="referencia != null">
                referencia = #{referencia,jdbcType=VARCHAR},
            </if>
            <if test="procedimientoCont != null">
                procedimiento_cont = #{procedimientoCont,jdbcType=VARCHAR},
            </if>
            <if test="tipoInstrumentoId != null">
                tipo_instrumento_id = #{tipoInstrumentoId,jdbcType=VARCHAR},
            </if>
            <if test="rfc != null">
                rfc = #{rfc,jdbcType=VARCHAR},
            </if>
            <if test="totalFactura != null">
                total_factura = #{totalFactura,jdbcType=DECIMAL},
            </if>
            <if test="folioUuid != null">
                folio_uuid = #{folioUuid,jdbcType=VARCHAR},
            </if>
            <if test="usuario != null">
                usuario = #{usuario,jdbcType=BIGINT},
            </if>
            <if test="fechaActual != null">
                fecha_actual = #{fechaActual,jdbcType=VARCHAR},
            </if>
            <if test="direccionIp != null">
                direccion_ip = #{direccionIp,jdbcType=VARCHAR},
            </if>
            <if test="programaId != null">
                programa_id = #{programaId,jdbcType=VARCHAR},
            </if>
            <if test="sucursal != null">
                sucursal = #{sucursal,jdbcType=BIGINT},
            </if>
            <if test="numTransaccion != null">
                num_transaccion = #{numTransaccion,jdbcType=BIGINT},
            </if>
            <if test="ledgerId != null">
                ledger_id = #{ledgerId,jdbcType=BIGINT},
            </if>
            <if test="currencyCode != null">
                currency_code = #{currencyCode,jdbcType=VARCHAR},
            </if>
            <if test="journalCategory != null">
                journal_category = #{journalCategory,jdbcType=VARCHAR},
            </if>
            <if test="journalSource != null">
                journal_source = #{journalSource,jdbcType=VARCHAR},
            </if>
            <if test="segment1 != null">
                segment1 = #{segment1,jdbcType=VARCHAR},
            </if>
            <if test="segment2 != null">
                segment2 = #{segment2,jdbcType=VARCHAR},
            </if>
            <if test="segment3 != null">
                segment3 = #{segment3,jdbcType=VARCHAR},
            </if>
            <if test="segment4 != null">
                segment4 = #{segment4,jdbcType=VARCHAR},
            </if>
            <if test="segment5 != null">
                segment5 = #{segment5,jdbcType=VARCHAR},
            </if>
            <if test="segment6 != null">
                segment6 = #{segment6,jdbcType=VARCHAR},
            </if>
            <if test="segment7 != null">
                segment7 = #{segment7,jdbcType=VARCHAR},
            </if>
            <if test="segment8 != null">
                segment8 = #{segment8,jdbcType=VARCHAR},
            </if>
            <if test="segment9 != null">
                segment9 = #{segment9,jdbcType=VARCHAR},
            </if>
            <if test="segment10 != null">
                segment10 = #{segment10,jdbcType=VARCHAR},
            </if>
            <if test="groupId != null">
                group_id = #{groupId,jdbcType=BIGINT},
            </if>
            <if test="currencyConversionDate != null">
                currency_conversion_date = #{currencyConversionDate,jdbcType=TIMESTAMP},
            </if>
            <if test="currencyConversionRate != null">
                currency_conversion_rate = #{currencyConversionRate,jdbcType=DECIMAL},
            </if>
            <if test="userCurrencyConversionType != null">
                user_currency_conversion_type = #{userCurrencyConversionType,jdbcType=VARCHAR},
            </if>
            <if test="processStatus != null">
                process_status = #{processStatus,jdbcType=VARCHAR},
            </if>
            <if test="processMessage != null">
                process_message = #{processMessage,jdbcType=VARCHAR},
            </if>
            <if test="jeHeaderId != null">
                je_header_id = #{jeHeaderId,jdbcType=BIGINT},
            </if>
            <if test="journalName != null">
                journal_name = #{journalName,jdbcType=VARCHAR},
            </if>
            <if test="jeLineNum != null">
                je_line_num = #{jeLineNum,jdbcType=BIGINT},
            </if>
            <if test="documentId != null">
                document_id = #{documentId,jdbcType=BIGINT},
            </if>
            <if test="loadRequestId != null">
                load_request_id = #{loadRequestId,jdbcType=BIGINT},
            </if>
            <if test="importRequestId != null">
                import_request_id = #{importRequestId,jdbcType=BIGINT},
            </if>
            <if test="objectVersionNumber != null">
                object_version_number = #{objectVersionNumber,jdbcType=BIGINT},
            </if>
            <if test="creationDate != null">
                creation_date = #{creationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifyDate != null">
                last_modify_date = #{lastModifyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHisPO">
        update sofi_gl_interface_his
        set file_id                       = #{fileId,jdbcType=BIGINT},
            process_day                   = #{processDay,jdbcType=VARCHAR},
            file_name                     = #{fileName,jdbcType=VARCHAR},
            batch_id                      = #{batchId,jdbcType=VARCHAR},
            detalle_poliza_id             = #{detallePolizaId,jdbcType=BIGINT},
            source_sys                    = #{sourceSys,jdbcType=VARCHAR},
            empresa_id                    = #{empresaId,jdbcType=BIGINT},
            poliza_id                     = #{polizaId,jdbcType=BIGINT},
            fecha                         = #{fecha,jdbcType=VARCHAR},
            centro_costo_id               = #{centroCostoId,jdbcType=BIGINT},
            cuenta_completa               = #{cuentaCompleta,jdbcType=VARCHAR},
            instrumento                   = #{instrumento,jdbcType=BIGINT},
            moneda_id                     = #{monedaId,jdbcType=BIGINT},
            cargos                        = #{cargos,jdbcType=DECIMAL},
            abonos                        = #{abonos,jdbcType=DECIMAL},
            descripcion                   = #{descripcion,jdbcType=VARCHAR},
            referencia                    = #{referencia,jdbcType=VARCHAR},
            procedimiento_cont            = #{procedimientoCont,jdbcType=VARCHAR},
            tipo_instrumento_id           = #{tipoInstrumentoId,jdbcType=VARCHAR},
            rfc                           = #{rfc,jdbcType=VARCHAR},
            total_factura                 = #{totalFactura,jdbcType=DECIMAL},
            folio_uuid                    = #{folioUuid,jdbcType=VARCHAR},
            usuario                       = #{usuario,jdbcType=BIGINT},
            fecha_actual                  = #{fechaActual,jdbcType=VARCHAR},
            direccion_ip                  = #{direccionIp,jdbcType=VARCHAR},
            programa_id                   = #{programaId,jdbcType=VARCHAR},
            sucursal                      = #{sucursal,jdbcType=BIGINT},
            num_transaccion               = #{numTransaccion,jdbcType=BIGINT},
            ledger_id                     = #{ledgerId,jdbcType=BIGINT},
            currency_code                 = #{currencyCode,jdbcType=VARCHAR},
            journal_category              = #{journalCategory,jdbcType=VARCHAR},
            journal_source                = #{journalSource,jdbcType=VARCHAR},
            segment1                      = #{segment1,jdbcType=VARCHAR},
            segment2                      = #{segment2,jdbcType=VARCHAR},
            segment3                      = #{segment3,jdbcType=VARCHAR},
            segment4                      = #{segment4,jdbcType=VARCHAR},
            segment5                      = #{segment5,jdbcType=VARCHAR},
            segment6                      = #{segment6,jdbcType=VARCHAR},
            segment7                      = #{segment7,jdbcType=VARCHAR},
            segment8                      = #{segment8,jdbcType=VARCHAR},
            segment9                      = #{segment9,jdbcType=VARCHAR},
            segment10                     = #{segment10,jdbcType=VARCHAR},
            group_id                      = #{groupId,jdbcType=BIGINT},
            currency_conversion_date      = #{currencyConversionDate,jdbcType=TIMESTAMP},
            currency_conversion_rate      = #{currencyConversionRate,jdbcType=DECIMAL},
            user_currency_conversion_type = #{userCurrencyConversionType,jdbcType=VARCHAR},
            process_status                = #{processStatus,jdbcType=VARCHAR},
            process_message               = #{processMessage,jdbcType=VARCHAR},
            je_header_id                  = #{jeHeaderId,jdbcType=BIGINT},
            journal_name                  = #{journalName,jdbcType=VARCHAR},
            je_line_num                   = #{jeLineNum,jdbcType=BIGINT},
            document_id                   = #{documentId,jdbcType=BIGINT},
            load_request_id               = #{loadRequestId,jdbcType=BIGINT},
            import_request_id             = #{importRequestId,jdbcType=BIGINT},
            object_version_number         = #{objectVersionNumber,jdbcType=BIGINT},
            creation_date                 = #{creationDate,jdbcType=TIMESTAMP},
            created_by                    = #{createdBy,jdbcType=VARCHAR},
            last_modify_date              = #{lastModifyDate,jdbcType=TIMESTAMP},
            last_modified_by              = #{lastModifiedBy,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="selectByExampleWithRowbounds"
            parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHisPOExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from sofi_gl_interface_his
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <insert id="batchInsert"
            parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHisPO">
        insert into sofi_gl_interface_his (file_id, process_day, file_name,
        batch_id, detalle_poliza_id, source_sys,
        empresa_id, poliza_id, fecha,
        centro_costo_id, cuenta_completa, instrumento,
        moneda_id, cargos, abonos,
        descripcion, referencia, procedimiento_cont,
        tipo_instrumento_id, rfc, total_factura,
        folio_uuid, usuario, fecha_actual,
        direccion_ip, programa_id, sucursal,
        num_transaccion, ledger_id, currency_code,
        journal_category, journal_source, segment1,
        segment2, segment3, segment4,
        segment5, segment6, segment7,
        segment8, segment9, segment10,
        group_id, currency_conversion_date, currency_conversion_rate,
        user_currency_conversion_type, process_status,
        process_message, je_header_id, journal_name,
        je_line_num, document_id, load_request_id,
        import_request_id, object_version_number, creation_date,
        created_by, last_modify_date, last_modified_by)
        values
        <foreach item="item" collection="list" separator="," open="(" close=")" index="index">
            (#{item.fileId,jdbcType=BIGINT}, #{item.processDay,jdbcType=VARCHAR}, #{item.fileName,jdbcType=VARCHAR},
            #{item.batchId,jdbcType=VARCHAR}, #{item.detallePolizaId,jdbcType=BIGINT}, #{item.sourceSys,jdbcType=VARCHAR},
            #{item.empresaId,jdbcType=BIGINT}, #{item.polizaId,jdbcType=BIGINT}, #{item.fecha,jdbcType=VARCHAR},
            #{item.centroCostoId,jdbcType=BIGINT}, #{item.cuentaCompleta,jdbcType=VARCHAR}, #{item.instrumento,jdbcType=BIGINT},
            #{item.monedaId,jdbcType=BIGINT}, #{item.cargos,jdbcType=DECIMAL}, #{item.abonos,jdbcType=DECIMAL},
            #{item.descripcion,jdbcType=VARCHAR}, #{item.referencia,jdbcType=VARCHAR}, #{item.procedimientoCont,jdbcType=VARCHAR},
            #{item.tipoInstrumentoId,jdbcType=VARCHAR}, #{item.rfc,jdbcType=VARCHAR}, #{item.totalFactura,jdbcType=DECIMAL},
            #{item.folioUuid,jdbcType=VARCHAR}, #{item.usuario,jdbcType=BIGINT}, #{item.fechaActual,jdbcType=VARCHAR},
            #{item.direccionIp,jdbcType=VARCHAR}, #{item.programaId,jdbcType=VARCHAR}, #{item.sucursal,jdbcType=BIGINT},
            #{item.numTransaccion,jdbcType=BIGINT}, #{item.ledgerId,jdbcType=BIGINT}, #{item.currencyCode,jdbcType=VARCHAR},
            #{item.journalCategory,jdbcType=VARCHAR}, #{item.journalSource,jdbcType=VARCHAR}, #{item.segment1,jdbcType=VARCHAR},
            #{item.segment2,jdbcType=VARCHAR}, #{item.segment3,jdbcType=VARCHAR}, #{item.segment4,jdbcType=VARCHAR},
            #{item.segment5,jdbcType=VARCHAR}, #{item.segment6,jdbcType=VARCHAR}, #{item.segment7,jdbcType=VARCHAR},
            #{item.segment8,jdbcType=VARCHAR}, #{item.segment9,jdbcType=VARCHAR}, #{item.segment10,jdbcType=VARCHAR},
            #{item.groupId,jdbcType=BIGINT}, #{item.currencyConversionDate,jdbcType=TIMESTAMP},
            #{item.currencyConversionRate,jdbcType=DECIMAL},
            #{item.userCurrencyConversionType,jdbcType=VARCHAR}, #{item.processStatus,jdbcType=VARCHAR},
            #{item.processMessage,jdbcType=VARCHAR}, #{item.jeHeaderId,jdbcType=BIGINT}, #{item.journalName,jdbcType=VARCHAR},
            #{item.jeLineNum,jdbcType=BIGINT}, #{item.documentId,jdbcType=BIGINT}, #{item.oadRequestId,jdbcType=BIGINT},
            #{item.importRequestId,jdbcType=BIGINT}, #{item.objectVersionNumber,jdbcType=BIGINT}, #{item.creationDate,jdbcType=TIMESTAMP},
            #{item.createdBy,jdbcType=VARCHAR}, #{item.lastModifyDate,jdbcType=TIMESTAMP}, #{item.lastModifiedBy,jdbcType=VARCHAR})
        </foreach>
    </insert>
</mapper>