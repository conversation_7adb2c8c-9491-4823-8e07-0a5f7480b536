<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlShenmaPOMapper">
  <resultMap id="BaseResultMap" type="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlShenmaPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="process_day" jdbcType="VARCHAR" property="processDay" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="link_reference" jdbcType="VARCHAR" property="linkReference" />
    <result column="source_branch" jdbcType="VARCHAR" property="sourceBranch" />
    <result column="ccy" jdbcType="VARCHAR" property="ccy" />
    <result column="gl_code" jdbcType="VARCHAR" property="glCode" />
    <result column="entered_debit_amount" jdbcType="DECIMAL" property="enteredDebitAmount" />
    <result column="entered_credit_amount" jdbcType="DECIMAL" property="enteredCreditAmount" />
    <result column="profit_center" jdbcType="VARCHAR" property="profitCenter" />
    <result column="source_module" jdbcType="VARCHAR" property="sourceModule" />
    <result column="client_type" jdbcType="VARCHAR" property="clientType" />
    <result column="amt_type" jdbcType="VARCHAR" property="amtType" />
    <result column="tran_type" jdbcType="VARCHAR" property="tranType" />
    <result column="event_type" jdbcType="VARCHAR" property="eventType" />
    <result column="prod_type" jdbcType="VARCHAR" property="prodType" />
    <result column="post_date" jdbcType="VARCHAR" property="postDate" />
    <result column="value_date" jdbcType="VARCHAR" property="valueDate" />
    <result column="narrative" jdbcType="VARCHAR" property="narrative" />
    <result column="channel_seq_no" jdbcType="VARCHAR" property="channelSeqNo" />
    <result column="intercompany" jdbcType="VARCHAR" property="intercompany" />
    <result column="flat_rate" jdbcType="DECIMAL" property="flatRate" />
    <result column="cust_rate" jdbcType="DECIMAL" property="custRate" />
    <result column="inland_offshore" jdbcType="VARCHAR" property="inlandOffshore" />
    <result column="client_no" jdbcType="VARCHAR" property="clientNo" />
    <result column="seq_no" jdbcType="VARCHAR" property="seqNo" />
    <result column="system_id" jdbcType="VARCHAR" property="systemId" />
    <result column="company" jdbcType="VARCHAR" property="company" />
    <result column="group_client" jdbcType="VARCHAR" property="groupClient" />
    <result column="voucher_group" jdbcType="VARCHAR" property="voucherGroup" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="process_status" jdbcType="VARCHAR" property="processStatus" />
    <result column="process_message" jdbcType="VARCHAR" property="processMessage" />
    <result column="object_version_number" jdbcType="BIGINT" property="objectVersionNumber" />
    <result column="creation_date" jdbcType="TIMESTAMP" property="creationDate" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="last_modify_date" jdbcType="TIMESTAMP" property="lastModifyDate" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="cr_dr_ind" jdbcType="VARCHAR" property="crDrInd" />
  </resultMap>
  <resultMap id="extendBaseResultMap" extends="BaseResultMap" type="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlShenmaPO">
    <result column="external_reference" jdbcType="VARCHAR" property="externalReference" />
  </resultMap>
  <sql id="Base_Column_List">
    id, process_day, file_name, link_reference, source_branch, ccy, gl_code, entered_debit_amount, 
    entered_credit_amount, profit_center, source_module, client_type, amt_type, tran_type, 
    event_type, prod_type, post_date, value_date, narrative, channel_seq_no, intercompany, 
    flat_rate, cust_rate, inland_offshore, client_no, seq_no, system_id, company, group_client, 
    voucher_group, group_id, process_status, process_message, object_version_number, 
    creation_date, created_by, last_modify_date, last_modified_by, cr_dr_ind
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sofi_gl_shenma
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sofi_gl_shenma
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlShenmaPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sofi_gl_shenma (process_day, file_name, link_reference, 
      source_branch, ccy, gl_code, 
      entered_debit_amount, entered_credit_amount, 
      profit_center, source_module, client_type, 
      amt_type, tran_type, event_type, 
      prod_type, post_date, value_date, 
      narrative, channel_seq_no, intercompany, 
      flat_rate, cust_rate, inland_offshore, 
      client_no, seq_no, system_id, 
      company, group_client, voucher_group, 
      group_id, process_status, process_message, 
      object_version_number, creation_date, created_by, 
      last_modify_date, last_modified_by, cr_dr_ind
      )
    values (#{processDay,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, #{linkReference,jdbcType=VARCHAR}, 
      #{sourceBranch,jdbcType=VARCHAR}, #{ccy,jdbcType=VARCHAR}, #{glCode,jdbcType=VARCHAR}, 
      #{enteredDebitAmount,jdbcType=DECIMAL}, #{enteredCreditAmount,jdbcType=DECIMAL}, 
      #{profitCenter,jdbcType=VARCHAR}, #{sourceModule,jdbcType=VARCHAR}, #{clientType,jdbcType=VARCHAR}, 
      #{amtType,jdbcType=VARCHAR}, #{tranType,jdbcType=VARCHAR}, #{eventType,jdbcType=VARCHAR}, 
      #{prodType,jdbcType=VARCHAR}, #{postDate,jdbcType=VARCHAR}, #{valueDate,jdbcType=VARCHAR}, 
      #{narrative,jdbcType=VARCHAR}, #{channelSeqNo,jdbcType=VARCHAR}, #{intercompany,jdbcType=VARCHAR}, 
      #{flatRate,jdbcType=DECIMAL}, #{custRate,jdbcType=DECIMAL}, #{inlandOffshore,jdbcType=VARCHAR}, 
      #{clientNo,jdbcType=VARCHAR}, #{seqNo,jdbcType=VARCHAR}, #{systemId,jdbcType=VARCHAR}, 
      #{company,jdbcType=VARCHAR}, #{groupClient,jdbcType=VARCHAR}, #{voucherGroup,jdbcType=VARCHAR}, 
      #{groupId,jdbcType=BIGINT}, #{processStatus,jdbcType=VARCHAR}, #{processMessage,jdbcType=VARCHAR}, 
      #{objectVersionNumber,jdbcType=BIGINT}, #{creationDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, 
      #{lastModifyDate,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{crDrInd,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlShenmaPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sofi_gl_shenma
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="processDay != null">
        process_day,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="linkReference != null">
        link_reference,
      </if>
      <if test="sourceBranch != null">
        source_branch,
      </if>
      <if test="ccy != null">
        ccy,
      </if>
      <if test="glCode != null">
        gl_code,
      </if>
      <if test="enteredDebitAmount != null">
        entered_debit_amount,
      </if>
      <if test="enteredCreditAmount != null">
        entered_credit_amount,
      </if>
      <if test="profitCenter != null">
        profit_center,
      </if>
      <if test="sourceModule != null">
        source_module,
      </if>
      <if test="clientType != null">
        client_type,
      </if>
      <if test="amtType != null">
        amt_type,
      </if>
      <if test="tranType != null">
        tran_type,
      </if>
      <if test="eventType != null">
        event_type,
      </if>
      <if test="prodType != null">
        prod_type,
      </if>
      <if test="postDate != null">
        post_date,
      </if>
      <if test="valueDate != null">
        value_date,
      </if>
      <if test="narrative != null">
        narrative,
      </if>
      <if test="channelSeqNo != null">
        channel_seq_no,
      </if>
      <if test="intercompany != null">
        intercompany,
      </if>
      <if test="flatRate != null">
        flat_rate,
      </if>
      <if test="custRate != null">
        cust_rate,
      </if>
      <if test="inlandOffshore != null">
        inland_offshore,
      </if>
      <if test="clientNo != null">
        client_no,
      </if>
      <if test="seqNo != null">
        seq_no,
      </if>
      <if test="systemId != null">
        system_id,
      </if>
      <if test="company != null">
        company,
      </if>
      <if test="groupClient != null">
        group_client,
      </if>
      <if test="voucherGroup != null">
        voucher_group,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="processStatus != null">
        process_status,
      </if>
      <if test="processMessage != null">
        process_message,
      </if>
      <if test="objectVersionNumber != null">
        object_version_number,
      </if>
      <if test="creationDate != null">
        creation_date,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="lastModifyDate != null">
        last_modify_date,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="crDrInd != null">
        cr_dr_ind,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="processDay != null">
        #{processDay,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="linkReference != null">
        #{linkReference,jdbcType=VARCHAR},
      </if>
      <if test="sourceBranch != null">
        #{sourceBranch,jdbcType=VARCHAR},
      </if>
      <if test="ccy != null">
        #{ccy,jdbcType=VARCHAR},
      </if>
      <if test="glCode != null">
        #{glCode,jdbcType=VARCHAR},
      </if>
      <if test="enteredDebitAmount != null">
        #{enteredDebitAmount,jdbcType=DECIMAL},
      </if>
      <if test="enteredCreditAmount != null">
        #{enteredCreditAmount,jdbcType=DECIMAL},
      </if>
      <if test="profitCenter != null">
        #{profitCenter,jdbcType=VARCHAR},
      </if>
      <if test="sourceModule != null">
        #{sourceModule,jdbcType=VARCHAR},
      </if>
      <if test="clientType != null">
        #{clientType,jdbcType=VARCHAR},
      </if>
      <if test="amtType != null">
        #{amtType,jdbcType=VARCHAR},
      </if>
      <if test="tranType != null">
        #{tranType,jdbcType=VARCHAR},
      </if>
      <if test="eventType != null">
        #{eventType,jdbcType=VARCHAR},
      </if>
      <if test="prodType != null">
        #{prodType,jdbcType=VARCHAR},
      </if>
      <if test="postDate != null">
        #{postDate,jdbcType=VARCHAR},
      </if>
      <if test="valueDate != null">
        #{valueDate,jdbcType=VARCHAR},
      </if>
      <if test="narrative != null">
        #{narrative,jdbcType=VARCHAR},
      </if>
      <if test="channelSeqNo != null">
        #{channelSeqNo,jdbcType=VARCHAR},
      </if>
      <if test="intercompany != null">
        #{intercompany,jdbcType=VARCHAR},
      </if>
      <if test="flatRate != null">
        #{flatRate,jdbcType=DECIMAL},
      </if>
      <if test="custRate != null">
        #{custRate,jdbcType=DECIMAL},
      </if>
      <if test="inlandOffshore != null">
        #{inlandOffshore,jdbcType=VARCHAR},
      </if>
      <if test="clientNo != null">
        #{clientNo,jdbcType=VARCHAR},
      </if>
      <if test="seqNo != null">
        #{seqNo,jdbcType=VARCHAR},
      </if>
      <if test="systemId != null">
        #{systemId,jdbcType=VARCHAR},
      </if>
      <if test="company != null">
        #{company,jdbcType=VARCHAR},
      </if>
      <if test="groupClient != null">
        #{groupClient,jdbcType=VARCHAR},
      </if>
      <if test="voucherGroup != null">
        #{voucherGroup,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="processStatus != null">
        #{processStatus,jdbcType=VARCHAR},
      </if>
      <if test="processMessage != null">
        #{processMessage,jdbcType=VARCHAR},
      </if>
      <if test="objectVersionNumber != null">
        #{objectVersionNumber,jdbcType=BIGINT},
      </if>
      <if test="creationDate != null">
        #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifyDate != null">
        #{lastModifyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="crDrInd != null">
        #{crDrInd,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlShenmaPO">
    update sofi_gl_shenma
    <set>
      <if test="processDay != null">
        process_day = #{processDay,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="linkReference != null">
        link_reference = #{linkReference,jdbcType=VARCHAR},
      </if>
      <if test="sourceBranch != null">
        source_branch = #{sourceBranch,jdbcType=VARCHAR},
      </if>
      <if test="ccy != null">
        ccy = #{ccy,jdbcType=VARCHAR},
      </if>
      <if test="glCode != null">
        gl_code = #{glCode,jdbcType=VARCHAR},
      </if>
      <if test="enteredDebitAmount != null">
        entered_debit_amount = #{enteredDebitAmount,jdbcType=DECIMAL},
      </if>
      <if test="enteredCreditAmount != null">
        entered_credit_amount = #{enteredCreditAmount,jdbcType=DECIMAL},
      </if>
      <if test="profitCenter != null">
        profit_center = #{profitCenter,jdbcType=VARCHAR},
      </if>
      <if test="sourceModule != null">
        source_module = #{sourceModule,jdbcType=VARCHAR},
      </if>
      <if test="clientType != null">
        client_type = #{clientType,jdbcType=VARCHAR},
      </if>
      <if test="amtType != null">
        amt_type = #{amtType,jdbcType=VARCHAR},
      </if>
      <if test="tranType != null">
        tran_type = #{tranType,jdbcType=VARCHAR},
      </if>
      <if test="eventType != null">
        event_type = #{eventType,jdbcType=VARCHAR},
      </if>
      <if test="prodType != null">
        prod_type = #{prodType,jdbcType=VARCHAR},
      </if>
      <if test="postDate != null">
        post_date = #{postDate,jdbcType=VARCHAR},
      </if>
      <if test="valueDate != null">
        value_date = #{valueDate,jdbcType=VARCHAR},
      </if>
      <if test="narrative != null">
        narrative = #{narrative,jdbcType=VARCHAR},
      </if>
      <if test="channelSeqNo != null">
        channel_seq_no = #{channelSeqNo,jdbcType=VARCHAR},
      </if>
      <if test="intercompany != null">
        intercompany = #{intercompany,jdbcType=VARCHAR},
      </if>
      <if test="flatRate != null">
        flat_rate = #{flatRate,jdbcType=DECIMAL},
      </if>
      <if test="custRate != null">
        cust_rate = #{custRate,jdbcType=DECIMAL},
      </if>
      <if test="inlandOffshore != null">
        inland_offshore = #{inlandOffshore,jdbcType=VARCHAR},
      </if>
      <if test="clientNo != null">
        client_no = #{clientNo,jdbcType=VARCHAR},
      </if>
      <if test="seqNo != null">
        seq_no = #{seqNo,jdbcType=VARCHAR},
      </if>
      <if test="systemId != null">
        system_id = #{systemId,jdbcType=VARCHAR},
      </if>
      <if test="company != null">
        company = #{company,jdbcType=VARCHAR},
      </if>
      <if test="groupClient != null">
        group_client = #{groupClient,jdbcType=VARCHAR},
      </if>
      <if test="voucherGroup != null">
        voucher_group = #{voucherGroup,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="processStatus != null">
        process_status = #{processStatus,jdbcType=VARCHAR},
      </if>
      <if test="processMessage != null">
        process_message = #{processMessage,jdbcType=VARCHAR},
      </if>
      <if test="objectVersionNumber != null">
        object_version_number = #{objectVersionNumber,jdbcType=BIGINT},
      </if>
      <if test="creationDate != null">
        creation_date = #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifyDate != null">
        last_modify_date = #{lastModifyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="crDrInd != null">
        cr_dr_ind = #{crDrInd,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlShenmaPO">
    update sofi_gl_shenma
    set process_day = #{processDay,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      link_reference = #{linkReference,jdbcType=VARCHAR},
      source_branch = #{sourceBranch,jdbcType=VARCHAR},
      ccy = #{ccy,jdbcType=VARCHAR},
      gl_code = #{glCode,jdbcType=VARCHAR},
      entered_debit_amount = #{enteredDebitAmount,jdbcType=DECIMAL},
      entered_credit_amount = #{enteredCreditAmount,jdbcType=DECIMAL},
      profit_center = #{profitCenter,jdbcType=VARCHAR},
      source_module = #{sourceModule,jdbcType=VARCHAR},
      client_type = #{clientType,jdbcType=VARCHAR},
      amt_type = #{amtType,jdbcType=VARCHAR},
      tran_type = #{tranType,jdbcType=VARCHAR},
      event_type = #{eventType,jdbcType=VARCHAR},
      prod_type = #{prodType,jdbcType=VARCHAR},
      post_date = #{postDate,jdbcType=VARCHAR},
      value_date = #{valueDate,jdbcType=VARCHAR},
      narrative = #{narrative,jdbcType=VARCHAR},
      channel_seq_no = #{channelSeqNo,jdbcType=VARCHAR},
      intercompany = #{intercompany,jdbcType=VARCHAR},
      flat_rate = #{flatRate,jdbcType=DECIMAL},
      cust_rate = #{custRate,jdbcType=DECIMAL},
      inland_offshore = #{inlandOffshore,jdbcType=VARCHAR},
      client_no = #{clientNo,jdbcType=VARCHAR},
      seq_no = #{seqNo,jdbcType=VARCHAR},
      system_id = #{systemId,jdbcType=VARCHAR},
      company = #{company,jdbcType=VARCHAR},
      group_client = #{groupClient,jdbcType=VARCHAR},
      voucher_group = #{voucherGroup,jdbcType=VARCHAR},
      group_id = #{groupId,jdbcType=BIGINT},
      process_status = #{processStatus,jdbcType=VARCHAR},
      process_message = #{processMessage,jdbcType=VARCHAR},
      object_version_number = #{objectVersionNumber,jdbcType=BIGINT},
      creation_date = #{creationDate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      last_modify_date = #{lastModifyDate,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      cr_dr_ind = #{crDrInd,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  </mapper>