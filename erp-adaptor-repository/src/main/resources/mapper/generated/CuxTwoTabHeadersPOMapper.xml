<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.CuxTwoTabHeadersPOMapper">
  <resultMap id="BaseResultMap" type="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabHeadersPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="form_code" jdbcType="VARCHAR" property="formCode" />
    <result column="form_name" jdbcType="VARCHAR" property="formName" />
    <result column="enable_flag" jdbcType="VARCHAR" property="enableFlag" />
    <result column="locked_flag" jdbcType="VARCHAR" property="lockedFlag" />
    <result column="unique_fields" jdbcType="VARCHAR" property="uniqueFields" />
    <result column="date_from" jdbcType="DATE" property="dateFrom" />
    <result column="date_to" jdbcType="DATE" property="dateTo" />
    <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate" />
    <result column="last_updated_by" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="last_update_login" jdbcType="INTEGER" property="lastUpdateLogin" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="creation_date" jdbcType="TIMESTAMP" property="creationDate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, form_code, form_name, enable_flag, locked_flag, unique_fields, date_from, date_to, 
    last_update_date, last_updated_by, last_update_login, created_by, creation_date
  </sql>
  <select id="selectByExample" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabHeadersPOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from cux_two_tab_headers
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cux_two_tab_headers
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cux_two_tab_headers
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabHeadersPOExample">
    delete from cux_two_tab_headers
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabHeadersPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cux_two_tab_headers (form_code, form_name, enable_flag, 
      locked_flag, unique_fields, date_from, 
      date_to, last_update_date, last_updated_by, 
      last_update_login, created_by, creation_date
      )
    values (#{formCode,jdbcType=VARCHAR}, #{formName,jdbcType=VARCHAR}, #{enableFlag,jdbcType=VARCHAR}, 
      #{lockedFlag,jdbcType=VARCHAR}, #{uniqueFields,jdbcType=VARCHAR}, #{dateFrom,jdbcType=DATE}, 
      #{dateTo,jdbcType=DATE}, #{lastUpdateDate,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=VARCHAR}, 
      #{lastUpdateLogin,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, #{creationDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabHeadersPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cux_two_tab_headers
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="formCode != null">
        form_code,
      </if>
      <if test="formName != null">
        form_name,
      </if>
      <if test="enableFlag != null">
        enable_flag,
      </if>
      <if test="lockedFlag != null">
        locked_flag,
      </if>
      <if test="uniqueFields != null">
        unique_fields,
      </if>
      <if test="dateFrom != null">
        date_from,
      </if>
      <if test="dateTo != null">
        date_to,
      </if>
      <if test="lastUpdateDate != null">
        last_update_date,
      </if>
      <if test="lastUpdatedBy != null">
        last_updated_by,
      </if>
      <if test="lastUpdateLogin != null">
        last_update_login,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="creationDate != null">
        creation_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="formCode != null">
        #{formCode,jdbcType=VARCHAR},
      </if>
      <if test="formName != null">
        #{formName,jdbcType=VARCHAR},
      </if>
      <if test="enableFlag != null">
        #{enableFlag,jdbcType=VARCHAR},
      </if>
      <if test="lockedFlag != null">
        #{lockedFlag,jdbcType=VARCHAR},
      </if>
      <if test="uniqueFields != null">
        #{uniqueFields,jdbcType=VARCHAR},
      </if>
      <if test="dateFrom != null">
        #{dateFrom,jdbcType=DATE},
      </if>
      <if test="dateTo != null">
        #{dateTo,jdbcType=DATE},
      </if>
      <if test="lastUpdateDate != null">
        #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null">
        #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateLogin != null">
        #{lastUpdateLogin,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="creationDate != null">
        #{creationDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map">
    update cux_two_tab_headers
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.formCode != null">
        form_code = #{record.formCode,jdbcType=VARCHAR},
      </if>
      <if test="record.formName != null">
        form_name = #{record.formName,jdbcType=VARCHAR},
      </if>
      <if test="record.enableFlag != null">
        enable_flag = #{record.enableFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.lockedFlag != null">
        locked_flag = #{record.lockedFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.uniqueFields != null">
        unique_fields = #{record.uniqueFields,jdbcType=VARCHAR},
      </if>
      <if test="record.dateFrom != null">
        date_from = #{record.dateFrom,jdbcType=DATE},
      </if>
      <if test="record.dateTo != null">
        date_to = #{record.dateTo,jdbcType=DATE},
      </if>
      <if test="record.lastUpdateDate != null">
        last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdatedBy != null">
        last_updated_by = #{record.lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.lastUpdateLogin != null">
        last_update_login = #{record.lastUpdateLogin,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.creationDate != null">
        creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cux_two_tab_headers
    set id = #{record.id,jdbcType=BIGINT},
      form_code = #{record.formCode,jdbcType=VARCHAR},
      form_name = #{record.formName,jdbcType=VARCHAR},
      enable_flag = #{record.enableFlag,jdbcType=VARCHAR},
      locked_flag = #{record.lockedFlag,jdbcType=VARCHAR},
      unique_fields = #{record.uniqueFields,jdbcType=VARCHAR},
      date_from = #{record.dateFrom,jdbcType=DATE},
      date_to = #{record.dateTo,jdbcType=DATE},
      last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      last_updated_by = #{record.lastUpdatedBy,jdbcType=VARCHAR},
      last_update_login = #{record.lastUpdateLogin,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      creation_date = #{record.creationDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabHeadersPO">
    update cux_two_tab_headers
    <set>
      <if test="formCode != null">
        form_code = #{formCode,jdbcType=VARCHAR},
      </if>
      <if test="formName != null">
        form_name = #{formName,jdbcType=VARCHAR},
      </if>
      <if test="enableFlag != null">
        enable_flag = #{enableFlag,jdbcType=VARCHAR},
      </if>
      <if test="lockedFlag != null">
        locked_flag = #{lockedFlag,jdbcType=VARCHAR},
      </if>
      <if test="uniqueFields != null">
        unique_fields = #{uniqueFields,jdbcType=VARCHAR},
      </if>
      <if test="dateFrom != null">
        date_from = #{dateFrom,jdbcType=DATE},
      </if>
      <if test="dateTo != null">
        date_to = #{dateTo,jdbcType=DATE},
      </if>
      <if test="lastUpdateDate != null">
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null">
        last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateLogin != null">
        last_update_login = #{lastUpdateLogin,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="creationDate != null">
        creation_date = #{creationDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabHeadersPO">
    update cux_two_tab_headers
    set form_code = #{formCode,jdbcType=VARCHAR},
      form_name = #{formName,jdbcType=VARCHAR},
      enable_flag = #{enableFlag,jdbcType=VARCHAR},
      locked_flag = #{lockedFlag,jdbcType=VARCHAR},
      unique_fields = #{uniqueFields,jdbcType=VARCHAR},
      date_from = #{dateFrom,jdbcType=DATE},
      date_to = #{dateTo,jdbcType=DATE},
      last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
      last_update_login = #{lastUpdateLogin,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      creation_date = #{creationDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabHeadersPOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from cux_two_tab_headers
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>