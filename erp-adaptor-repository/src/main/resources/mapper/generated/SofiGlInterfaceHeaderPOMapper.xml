<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfaceHeaderPOMapper">
  <sql id="Base_Column_List">
    id, system_code, process_day, batch_id, external_reference, journal_count, process_status, 
    process_message, object_version_number, creation_date, created_by, last_modify_date, 
    last_modified_by, group_id
  </sql>
  <select id="selectByExample" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHeaderPOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from sofi_gl_interface_header
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sofi_gl_interface_header
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sofi_gl_interface_header
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHeaderPOExample">
    delete from sofi_gl_interface_header
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHeaderPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sofi_gl_interface_header (system_code, process_day, batch_id, 
      external_reference, journal_count, process_status, 
      process_message, object_version_number, creation_date, 
      created_by, last_modify_date, last_modified_by, 
      group_id)
    values (#{systemCode,jdbcType=VARCHAR}, #{processDay,jdbcType=VARCHAR}, #{batchId,jdbcType=VARCHAR}, 
      #{externalReference,jdbcType=VARCHAR}, #{journalCount,jdbcType=BIGINT}, #{processStatus,jdbcType=VARCHAR}, 
      #{processMessage,jdbcType=VARCHAR}, #{objectVersionNumber,jdbcType=INTEGER}, #{creationDate,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=VARCHAR}, #{lastModifyDate,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{groupId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHeaderPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sofi_gl_interface_header
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="systemCode != null">
        system_code,
      </if>
      <if test="processDay != null">
        process_day,
      </if>
      <if test="batchId != null">
        batch_id,
      </if>
      <if test="externalReference != null">
        external_reference,
      </if>
      <if test="journalCount != null">
        journal_count,
      </if>
      <if test="processStatus != null">
        process_status,
      </if>
      <if test="processMessage != null">
        process_message,
      </if>
      <if test="objectVersionNumber != null">
        object_version_number,
      </if>
      <if test="creationDate != null">
        creation_date,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="lastModifyDate != null">
        last_modify_date,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="systemCode != null">
        #{systemCode,jdbcType=VARCHAR},
      </if>
      <if test="processDay != null">
        #{processDay,jdbcType=VARCHAR},
      </if>
      <if test="batchId != null">
        #{batchId,jdbcType=VARCHAR},
      </if>
      <if test="externalReference != null">
        #{externalReference,jdbcType=VARCHAR},
      </if>
      <if test="journalCount != null">
        #{journalCount,jdbcType=BIGINT},
      </if>
      <if test="processStatus != null">
        #{processStatus,jdbcType=VARCHAR},
      </if>
      <if test="processMessage != null">
        #{processMessage,jdbcType=VARCHAR},
      </if>
      <if test="objectVersionNumber != null">
        #{objectVersionNumber,jdbcType=INTEGER},
      </if>
      <if test="creationDate != null">
        #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifyDate != null">
        #{lastModifyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map">
    update sofi_gl_interface_header
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.systemCode != null">
        system_code = #{record.systemCode,jdbcType=VARCHAR},
      </if>
      <if test="record.processDay != null">
        process_day = #{record.processDay,jdbcType=VARCHAR},
      </if>
      <if test="record.batchId != null">
        batch_id = #{record.batchId,jdbcType=VARCHAR},
      </if>
      <if test="record.externalReference != null">
        external_reference = #{record.externalReference,jdbcType=VARCHAR},
      </if>
      <if test="record.journalCount != null">
        journal_count = #{record.journalCount,jdbcType=BIGINT},
      </if>
      <if test="record.processStatus != null">
        process_status = #{record.processStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.processMessage != null">
        process_message = #{record.processMessage,jdbcType=VARCHAR},
      </if>
      <if test="record.objectVersionNumber != null">
        object_version_number = #{record.objectVersionNumber,jdbcType=INTEGER},
      </if>
      <if test="record.creationDate != null">
        creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.lastModifyDate != null">
        last_modify_date = #{record.lastModifyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastModifiedBy != null">
        last_modified_by = #{record.lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sofi_gl_interface_header
    set id = #{record.id,jdbcType=BIGINT},
      system_code = #{record.systemCode,jdbcType=VARCHAR},
      process_day = #{record.processDay,jdbcType=VARCHAR},
      batch_id = #{record.batchId,jdbcType=VARCHAR},
      external_reference = #{record.externalReference,jdbcType=VARCHAR},
      journal_count = #{record.journalCount,jdbcType=BIGINT},
      process_status = #{record.processStatus,jdbcType=VARCHAR},
      process_message = #{record.processMessage,jdbcType=VARCHAR},
      object_version_number = #{record.objectVersionNumber,jdbcType=INTEGER},
      creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      last_modify_date = #{record.lastModifyDate,jdbcType=TIMESTAMP},
      last_modified_by = #{record.lastModifiedBy,jdbcType=VARCHAR},
      group_id = #{record.groupId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHeaderPO">
    update sofi_gl_interface_header
    <set>
      <if test="systemCode != null">
        system_code = #{systemCode,jdbcType=VARCHAR},
      </if>
      <if test="processDay != null">
        process_day = #{processDay,jdbcType=VARCHAR},
      </if>
      <if test="batchId != null">
        batch_id = #{batchId,jdbcType=VARCHAR},
      </if>
      <if test="externalReference != null">
        external_reference = #{externalReference,jdbcType=VARCHAR},
      </if>
      <if test="journalCount != null">
        journal_count = #{journalCount,jdbcType=BIGINT},
      </if>
      <if test="processStatus != null">
        process_status = #{processStatus,jdbcType=VARCHAR},
      </if>
      <if test="processMessage != null">
        process_message = #{processMessage,jdbcType=VARCHAR},
      </if>
      <if test="objectVersionNumber != null">
        object_version_number = #{objectVersionNumber,jdbcType=INTEGER},
      </if>
      <if test="creationDate != null">
        creation_date = #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifyDate != null">
        last_modify_date = #{lastModifyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHeaderPO">
    update sofi_gl_interface_header
    set system_code = #{systemCode,jdbcType=VARCHAR},
      process_day = #{processDay,jdbcType=VARCHAR},
      batch_id = #{batchId,jdbcType=VARCHAR},
      external_reference = #{externalReference,jdbcType=VARCHAR},
      journal_count = #{journalCount,jdbcType=BIGINT},
      process_status = #{processStatus,jdbcType=VARCHAR},
      process_message = #{processMessage,jdbcType=VARCHAR},
      object_version_number = #{objectVersionNumber,jdbcType=INTEGER},
      creation_date = #{creationDate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      last_modify_date = #{lastModifyDate,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      group_id = #{groupId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHeaderPOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from sofi_gl_interface_header
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <resultMap id="BaseResultMap" type="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceHeaderPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="system_code" jdbcType="VARCHAR" property="systemCode" />
    <result column="process_day" jdbcType="VARCHAR" property="processDay" />
    <result column="batch_id" jdbcType="VARCHAR" property="batchId" />
    <result column="external_reference" jdbcType="VARCHAR" property="externalReference" />
    <result column="journal_count" jdbcType="BIGINT" property="journalCount" />
    <result column="process_status" jdbcType="VARCHAR" property="processStatus" />
    <result column="process_message" jdbcType="VARCHAR" property="processMessage" />
    <result column="object_version_number" jdbcType="INTEGER" property="objectVersionNumber" />
    <result column="creation_date" jdbcType="TIMESTAMP" property="creationDate" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="last_modify_date" jdbcType="TIMESTAMP" property="lastModifyDate" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  </mapper>