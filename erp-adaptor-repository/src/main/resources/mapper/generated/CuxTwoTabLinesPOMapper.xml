<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.CuxTwoTabLinesPOMapper">
  <resultMap id="BaseResultMap" type="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabLinesPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="header_id" jdbcType="BIGINT" property="headerId" />
    <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
    <result column="field_code" jdbcType="VARCHAR" property="fieldCode" />
    <result column="enabled_flag" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="width" jdbcType="INTEGER" property="width" />
    <result column="seq" jdbcType="INTEGER" property="seq" />
    <result column="data_type" jdbcType="VARCHAR" property="dataType" />
    <result column="required_flag" jdbcType="VARCHAR" property="requiredFlag" />
    <result column="lov_type" jdbcType="VARCHAR" property="lovType" />
    <result column="sql_str" jdbcType="VARCHAR" property="sqlStr" />
    <result column="desc_width" jdbcType="INTEGER" property="descWidth" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate" />
    <result column="last_updated_by" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="last_update_login" jdbcType="INTEGER" property="lastUpdateLogin" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="creation_date" jdbcType="TIMESTAMP" property="creationDate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, header_id, field_name, field_code, enabled_flag, title, width, seq, data_type, 
    required_flag, lov_type, sql_str, desc_width, version, last_update_date, last_updated_by, 
    last_update_login, created_by, creation_date
  </sql>
  <select id="selectByExample" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabLinesPOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from cux_two_tab_lines
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cux_two_tab_lines
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cux_two_tab_lines
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabLinesPOExample">
    delete from cux_two_tab_lines
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabLinesPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cux_two_tab_lines (header_id, field_name, field_code, 
      enabled_flag, title, width, 
      seq, data_type, required_flag, 
      lov_type, sql_str, desc_width, 
      version, last_update_date, last_updated_by, 
      last_update_login, created_by, creation_date
      )
    values (#{headerId,jdbcType=BIGINT}, #{fieldName,jdbcType=VARCHAR}, #{fieldCode,jdbcType=VARCHAR}, 
      #{enabledFlag,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{width,jdbcType=INTEGER}, 
      #{seq,jdbcType=INTEGER}, #{dataType,jdbcType=VARCHAR}, #{requiredFlag,jdbcType=VARCHAR}, 
      #{lovType,jdbcType=VARCHAR}, #{sqlStr,jdbcType=VARCHAR}, #{descWidth,jdbcType=INTEGER}, 
      #{version,jdbcType=INTEGER}, #{lastUpdateDate,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=VARCHAR}, 
      #{lastUpdateLogin,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, #{creationDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabLinesPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cux_two_tab_lines
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="headerId != null">
        header_id,
      </if>
      <if test="fieldName != null">
        field_name,
      </if>
      <if test="fieldCode != null">
        field_code,
      </if>
      <if test="enabledFlag != null">
        enabled_flag,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="width != null">
        width,
      </if>
      <if test="seq != null">
        seq,
      </if>
      <if test="dataType != null">
        data_type,
      </if>
      <if test="requiredFlag != null">
        required_flag,
      </if>
      <if test="lovType != null">
        lov_type,
      </if>
      <if test="sqlStr != null">
        sql_str,
      </if>
      <if test="descWidth != null">
        desc_width,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="lastUpdateDate != null">
        last_update_date,
      </if>
      <if test="lastUpdatedBy != null">
        last_updated_by,
      </if>
      <if test="lastUpdateLogin != null">
        last_update_login,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="creationDate != null">
        creation_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="headerId != null">
        #{headerId,jdbcType=BIGINT},
      </if>
      <if test="fieldName != null">
        #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="fieldCode != null">
        #{fieldCode,jdbcType=VARCHAR},
      </if>
      <if test="enabledFlag != null">
        #{enabledFlag,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="width != null">
        #{width,jdbcType=INTEGER},
      </if>
      <if test="seq != null">
        #{seq,jdbcType=INTEGER},
      </if>
      <if test="dataType != null">
        #{dataType,jdbcType=VARCHAR},
      </if>
      <if test="requiredFlag != null">
        #{requiredFlag,jdbcType=VARCHAR},
      </if>
      <if test="lovType != null">
        #{lovType,jdbcType=VARCHAR},
      </if>
      <if test="sqlStr != null">
        #{sqlStr,jdbcType=VARCHAR},
      </if>
      <if test="descWidth != null">
        #{descWidth,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="lastUpdateDate != null">
        #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null">
        #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateLogin != null">
        #{lastUpdateLogin,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="creationDate != null">
        #{creationDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map">
    update cux_two_tab_lines
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.headerId != null">
        header_id = #{record.headerId,jdbcType=BIGINT},
      </if>
      <if test="record.fieldName != null">
        field_name = #{record.fieldName,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldCode != null">
        field_code = #{record.fieldCode,jdbcType=VARCHAR},
      </if>
      <if test="record.enabledFlag != null">
        enabled_flag = #{record.enabledFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.width != null">
        width = #{record.width,jdbcType=INTEGER},
      </if>
      <if test="record.seq != null">
        seq = #{record.seq,jdbcType=INTEGER},
      </if>
      <if test="record.dataType != null">
        data_type = #{record.dataType,jdbcType=VARCHAR},
      </if>
      <if test="record.requiredFlag != null">
        required_flag = #{record.requiredFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.lovType != null">
        lov_type = #{record.lovType,jdbcType=VARCHAR},
      </if>
      <if test="record.sqlStr != null">
        sql_str = #{record.sqlStr,jdbcType=VARCHAR},
      </if>
      <if test="record.descWidth != null">
        desc_width = #{record.descWidth,jdbcType=INTEGER},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.lastUpdateDate != null">
        last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdatedBy != null">
        last_updated_by = #{record.lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.lastUpdateLogin != null">
        last_update_login = #{record.lastUpdateLogin,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.creationDate != null">
        creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cux_two_tab_lines
    set id = #{record.id,jdbcType=BIGINT},
      header_id = #{record.headerId,jdbcType=BIGINT},
      field_name = #{record.fieldName,jdbcType=VARCHAR},
      field_code = #{record.fieldCode,jdbcType=VARCHAR},
      enabled_flag = #{record.enabledFlag,jdbcType=VARCHAR},
      title = #{record.title,jdbcType=VARCHAR},
      width = #{record.width,jdbcType=INTEGER},
      seq = #{record.seq,jdbcType=INTEGER},
      data_type = #{record.dataType,jdbcType=VARCHAR},
      required_flag = #{record.requiredFlag,jdbcType=VARCHAR},
      lov_type = #{record.lovType,jdbcType=VARCHAR},
      sql_str = #{record.sqlStr,jdbcType=VARCHAR},
      desc_width = #{record.descWidth,jdbcType=INTEGER},
      version = #{record.version,jdbcType=INTEGER},
      last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      last_updated_by = #{record.lastUpdatedBy,jdbcType=VARCHAR},
      last_update_login = #{record.lastUpdateLogin,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      creation_date = #{record.creationDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabLinesPO">
    update cux_two_tab_lines
    <set>
      <if test="headerId != null">
        header_id = #{headerId,jdbcType=BIGINT},
      </if>
      <if test="fieldName != null">
        field_name = #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="fieldCode != null">
        field_code = #{fieldCode,jdbcType=VARCHAR},
      </if>
      <if test="enabledFlag != null">
        enabled_flag = #{enabledFlag,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="width != null">
        width = #{width,jdbcType=INTEGER},
      </if>
      <if test="seq != null">
        seq = #{seq,jdbcType=INTEGER},
      </if>
      <if test="dataType != null">
        data_type = #{dataType,jdbcType=VARCHAR},
      </if>
      <if test="requiredFlag != null">
        required_flag = #{requiredFlag,jdbcType=VARCHAR},
      </if>
      <if test="lovType != null">
        lov_type = #{lovType,jdbcType=VARCHAR},
      </if>
      <if test="sqlStr != null">
        sql_str = #{sqlStr,jdbcType=VARCHAR},
      </if>
      <if test="descWidth != null">
        desc_width = #{descWidth,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="lastUpdateDate != null">
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null">
        last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateLogin != null">
        last_update_login = #{lastUpdateLogin,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="creationDate != null">
        creation_date = #{creationDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabLinesPO">
    update cux_two_tab_lines
    set header_id = #{headerId,jdbcType=BIGINT},
      field_name = #{fieldName,jdbcType=VARCHAR},
      field_code = #{fieldCode,jdbcType=VARCHAR},
      enabled_flag = #{enabledFlag,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      width = #{width,jdbcType=INTEGER},
      seq = #{seq,jdbcType=INTEGER},
      data_type = #{dataType,jdbcType=VARCHAR},
      required_flag = #{requiredFlag,jdbcType=VARCHAR},
      lov_type = #{lovType,jdbcType=VARCHAR},
      sql_str = #{sqlStr,jdbcType=VARCHAR},
      desc_width = #{descWidth,jdbcType=INTEGER},
      version = #{version,jdbcType=INTEGER},
      last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
      last_update_login = #{lastUpdateLogin,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      creation_date = #{creationDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.CuxTwoTabLinesPOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from cux_two_tab_lines
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>