<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlAcctHistPOMapper">
  <resultMap id="BaseResultMap" type="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlAcctHistPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="process_day" jdbcType="VARCHAR" property="processDay" />
    <result column="internal_key" jdbcType="BIGINT" property="internalKey" />
    <result column="branch" jdbcType="VARCHAR" property="branch" />
    <result column="ccy" jdbcType="VARCHAR" property="ccy" />
    <result column="gl_code" jdbcType="VARCHAR" property="glCode" />
    <result column="client_no" jdbcType="VARCHAR" property="clientNo" />
    <result column="profit_center" jdbcType="VARCHAR" property="profitCenter" />
    <result column="seq_no" jdbcType="VARCHAR" property="seqNo" />
    <result column="acct_open_date" jdbcType="TIMESTAMP" property="acctOpenDate" />
    <result column="open_tran_date" jdbcType="TIMESTAMP" property="openTranDate" />
    <result column="acct_close_date" jdbcType="TIMESTAMP" property="acctCloseDate" />
    <result column="acct_close_reason" jdbcType="VARCHAR" property="acctCloseReason" />
    <result column="gl_acct_no" jdbcType="VARCHAR" property="glAcctNo" />
    <result column="acct_status" jdbcType="CHAR" property="acctStatus" />
    <result column="actual_bal" jdbcType="DECIMAL" property="actualBal" />
    <result column="dr_actual_bal" jdbcType="DECIMAL" property="drActualBal" />
    <result column="cr_actual_bal" jdbcType="DECIMAL" property="crActualBal" />
    <result column="ctd_days" jdbcType="INTEGER" property="ctdDays" />
    <result column="mtd_days" jdbcType="INTEGER" property="mtdDays" />
    <result column="ytd_days" jdbcType="INTEGER" property="ytdDays" />
    <result column="mtd_bal" jdbcType="DECIMAL" property="mtdBal" />
    <result column="ytd_bal" jdbcType="DECIMAL" property="ytdBal" />
    <result column="agg_bal_ctd" jdbcType="DECIMAL" property="aggBalCtd" />
    <result column="agg_bal_mtd" jdbcType="DECIMAL" property="aggBalMtd" />
    <result column="agg_bal_ytd" jdbcType="DECIMAL" property="aggBalYtd" />
    <result column="period_no" jdbcType="VARCHAR" property="periodNo" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="manual_account" jdbcType="CHAR" property="manualAccount" />
    <result column="od_facility" jdbcType="CHAR" property="odFacility" />
    <result column="last_change_date" jdbcType="TIMESTAMP" property="lastChangeDate" />
    <result column="acct_name" jdbcType="VARCHAR" property="acctName" />
    <result column="company" jdbcType="VARCHAR" property="company" />
    <result column="backup_date" jdbcType="TIMESTAMP" property="backupDate" />
    <result column="tran_timestamp" jdbcType="VARCHAR" property="tranTimestamp" />
    <result column="system_id" jdbcType="VARCHAR" property="systemId" />
    <result column="group_client" jdbcType="VARCHAR" property="groupClient" />
    <result column="dr_tran_amt" jdbcType="DECIMAL" property="drTranAmt" />
    <result column="cr_tran_amt" jdbcType="DECIMAL" property="crTranAmt" />
    <result column="last_actual_bal" jdbcType="DECIMAL" property="lastActualBal" />
    <result column="last_dr_actual_bal" jdbcType="DECIMAL" property="lastDrActualBal" />
    <result column="last_cr_actual_bal" jdbcType="DECIMAL" property="lastCrActualBal" />
    <result column="object_version_number" jdbcType="BIGINT" property="objectVersionNumber" />
    <result column="creation_date" jdbcType="TIMESTAMP" property="creationDate" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="last_modify_date" jdbcType="TIMESTAMP" property="lastModifyDate" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    id, process_day, internal_key, branch, ccy, gl_code, client_no, profit_center, seq_no, 
    acct_open_date, open_tran_date, acct_close_date, acct_close_reason, gl_acct_no, acct_status, 
    actual_bal, dr_actual_bal, cr_actual_bal, ctd_days, mtd_days, ytd_days, mtd_bal, 
    ytd_bal, agg_bal_ctd, agg_bal_mtd, agg_bal_ytd, period_no, user_id, manual_account, 
    od_facility, last_change_date, acct_name, company, backup_date, tran_timestamp, system_id, 
    group_client, dr_tran_amt, cr_tran_amt, last_actual_bal, last_dr_actual_bal, last_cr_actual_bal, 
    object_version_number, creation_date, created_by, last_modify_date, last_modified_by
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sofi_gl_acct_hist
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sofi_gl_acct_hist
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlAcctHistPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sofi_gl_acct_hist (process_day, internal_key, branch, 
      ccy, gl_code, client_no, 
      profit_center, seq_no, acct_open_date, 
      open_tran_date, acct_close_date, acct_close_reason, 
      gl_acct_no, acct_status, actual_bal, 
      dr_actual_bal, cr_actual_bal, ctd_days, 
      mtd_days, ytd_days, mtd_bal, 
      ytd_bal, agg_bal_ctd, agg_bal_mtd, 
      agg_bal_ytd, period_no, user_id, 
      manual_account, od_facility, last_change_date, 
      acct_name, company, backup_date, 
      tran_timestamp, system_id, group_client, 
      dr_tran_amt, cr_tran_amt, last_actual_bal, 
      last_dr_actual_bal, last_cr_actual_bal, object_version_number, 
      creation_date, created_by, last_modify_date, 
      last_modified_by)
    values (#{processDay,jdbcType=VARCHAR}, #{internalKey,jdbcType=BIGINT}, #{branch,jdbcType=VARCHAR}, 
      #{ccy,jdbcType=VARCHAR}, #{glCode,jdbcType=VARCHAR}, #{clientNo,jdbcType=VARCHAR}, 
      #{profitCenter,jdbcType=VARCHAR}, #{seqNo,jdbcType=VARCHAR}, #{acctOpenDate,jdbcType=TIMESTAMP}, 
      #{openTranDate,jdbcType=TIMESTAMP}, #{acctCloseDate,jdbcType=TIMESTAMP}, #{acctCloseReason,jdbcType=VARCHAR}, 
      #{glAcctNo,jdbcType=VARCHAR}, #{acctStatus,jdbcType=CHAR}, #{actualBal,jdbcType=DECIMAL}, 
      #{drActualBal,jdbcType=DECIMAL}, #{crActualBal,jdbcType=DECIMAL}, #{ctdDays,jdbcType=INTEGER}, 
      #{mtdDays,jdbcType=INTEGER}, #{ytdDays,jdbcType=INTEGER}, #{mtdBal,jdbcType=DECIMAL}, 
      #{ytdBal,jdbcType=DECIMAL}, #{aggBalCtd,jdbcType=DECIMAL}, #{aggBalMtd,jdbcType=DECIMAL}, 
      #{aggBalYtd,jdbcType=DECIMAL}, #{periodNo,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, 
      #{manualAccount,jdbcType=CHAR}, #{odFacility,jdbcType=CHAR}, #{lastChangeDate,jdbcType=TIMESTAMP}, 
      #{acctName,jdbcType=VARCHAR}, #{company,jdbcType=VARCHAR}, #{backupDate,jdbcType=TIMESTAMP}, 
      #{tranTimestamp,jdbcType=VARCHAR}, #{systemId,jdbcType=VARCHAR}, #{groupClient,jdbcType=VARCHAR}, 
      #{drTranAmt,jdbcType=DECIMAL}, #{crTranAmt,jdbcType=DECIMAL}, #{lastActualBal,jdbcType=DECIMAL}, 
      #{lastDrActualBal,jdbcType=DECIMAL}, #{lastCrActualBal,jdbcType=DECIMAL}, #{objectVersionNumber,jdbcType=BIGINT}, 
      #{creationDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{lastModifyDate,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlAcctHistPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sofi_gl_acct_hist
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="processDay != null">
        process_day,
      </if>
      <if test="internalKey != null">
        internal_key,
      </if>
      <if test="branch != null">
        branch,
      </if>
      <if test="ccy != null">
        ccy,
      </if>
      <if test="glCode != null">
        gl_code,
      </if>
      <if test="clientNo != null">
        client_no,
      </if>
      <if test="profitCenter != null">
        profit_center,
      </if>
      <if test="seqNo != null">
        seq_no,
      </if>
      <if test="acctOpenDate != null">
        acct_open_date,
      </if>
      <if test="openTranDate != null">
        open_tran_date,
      </if>
      <if test="acctCloseDate != null">
        acct_close_date,
      </if>
      <if test="acctCloseReason != null">
        acct_close_reason,
      </if>
      <if test="glAcctNo != null">
        gl_acct_no,
      </if>
      <if test="acctStatus != null">
        acct_status,
      </if>
      <if test="actualBal != null">
        actual_bal,
      </if>
      <if test="drActualBal != null">
        dr_actual_bal,
      </if>
      <if test="crActualBal != null">
        cr_actual_bal,
      </if>
      <if test="ctdDays != null">
        ctd_days,
      </if>
      <if test="mtdDays != null">
        mtd_days,
      </if>
      <if test="ytdDays != null">
        ytd_days,
      </if>
      <if test="mtdBal != null">
        mtd_bal,
      </if>
      <if test="ytdBal != null">
        ytd_bal,
      </if>
      <if test="aggBalCtd != null">
        agg_bal_ctd,
      </if>
      <if test="aggBalMtd != null">
        agg_bal_mtd,
      </if>
      <if test="aggBalYtd != null">
        agg_bal_ytd,
      </if>
      <if test="periodNo != null">
        period_no,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="manualAccount != null">
        manual_account,
      </if>
      <if test="odFacility != null">
        od_facility,
      </if>
      <if test="lastChangeDate != null">
        last_change_date,
      </if>
      <if test="acctName != null">
        acct_name,
      </if>
      <if test="company != null">
        company,
      </if>
      <if test="backupDate != null">
        backup_date,
      </if>
      <if test="tranTimestamp != null">
        tran_timestamp,
      </if>
      <if test="systemId != null">
        system_id,
      </if>
      <if test="groupClient != null">
        group_client,
      </if>
      <if test="drTranAmt != null">
        dr_tran_amt,
      </if>
      <if test="crTranAmt != null">
        cr_tran_amt,
      </if>
      <if test="lastActualBal != null">
        last_actual_bal,
      </if>
      <if test="lastDrActualBal != null">
        last_dr_actual_bal,
      </if>
      <if test="lastCrActualBal != null">
        last_cr_actual_bal,
      </if>
      <if test="objectVersionNumber != null">
        object_version_number,
      </if>
      <if test="creationDate != null">
        creation_date,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="lastModifyDate != null">
        last_modify_date,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="processDay != null">
        #{processDay,jdbcType=VARCHAR},
      </if>
      <if test="internalKey != null">
        #{internalKey,jdbcType=BIGINT},
      </if>
      <if test="branch != null">
        #{branch,jdbcType=VARCHAR},
      </if>
      <if test="ccy != null">
        #{ccy,jdbcType=VARCHAR},
      </if>
      <if test="glCode != null">
        #{glCode,jdbcType=VARCHAR},
      </if>
      <if test="clientNo != null">
        #{clientNo,jdbcType=VARCHAR},
      </if>
      <if test="profitCenter != null">
        #{profitCenter,jdbcType=VARCHAR},
      </if>
      <if test="seqNo != null">
        #{seqNo,jdbcType=VARCHAR},
      </if>
      <if test="acctOpenDate != null">
        #{acctOpenDate,jdbcType=TIMESTAMP},
      </if>
      <if test="openTranDate != null">
        #{openTranDate,jdbcType=TIMESTAMP},
      </if>
      <if test="acctCloseDate != null">
        #{acctCloseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="acctCloseReason != null">
        #{acctCloseReason,jdbcType=VARCHAR},
      </if>
      <if test="glAcctNo != null">
        #{glAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="acctStatus != null">
        #{acctStatus,jdbcType=CHAR},
      </if>
      <if test="actualBal != null">
        #{actualBal,jdbcType=DECIMAL},
      </if>
      <if test="drActualBal != null">
        #{drActualBal,jdbcType=DECIMAL},
      </if>
      <if test="crActualBal != null">
        #{crActualBal,jdbcType=DECIMAL},
      </if>
      <if test="ctdDays != null">
        #{ctdDays,jdbcType=INTEGER},
      </if>
      <if test="mtdDays != null">
        #{mtdDays,jdbcType=INTEGER},
      </if>
      <if test="ytdDays != null">
        #{ytdDays,jdbcType=INTEGER},
      </if>
      <if test="mtdBal != null">
        #{mtdBal,jdbcType=DECIMAL},
      </if>
      <if test="ytdBal != null">
        #{ytdBal,jdbcType=DECIMAL},
      </if>
      <if test="aggBalCtd != null">
        #{aggBalCtd,jdbcType=DECIMAL},
      </if>
      <if test="aggBalMtd != null">
        #{aggBalMtd,jdbcType=DECIMAL},
      </if>
      <if test="aggBalYtd != null">
        #{aggBalYtd,jdbcType=DECIMAL},
      </if>
      <if test="periodNo != null">
        #{periodNo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="manualAccount != null">
        #{manualAccount,jdbcType=CHAR},
      </if>
      <if test="odFacility != null">
        #{odFacility,jdbcType=CHAR},
      </if>
      <if test="lastChangeDate != null">
        #{lastChangeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="acctName != null">
        #{acctName,jdbcType=VARCHAR},
      </if>
      <if test="company != null">
        #{company,jdbcType=VARCHAR},
      </if>
      <if test="backupDate != null">
        #{backupDate,jdbcType=TIMESTAMP},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp,jdbcType=VARCHAR},
      </if>
      <if test="systemId != null">
        #{systemId,jdbcType=VARCHAR},
      </if>
      <if test="groupClient != null">
        #{groupClient,jdbcType=VARCHAR},
      </if>
      <if test="drTranAmt != null">
        #{drTranAmt,jdbcType=DECIMAL},
      </if>
      <if test="crTranAmt != null">
        #{crTranAmt,jdbcType=DECIMAL},
      </if>
      <if test="lastActualBal != null">
        #{lastActualBal,jdbcType=DECIMAL},
      </if>
      <if test="lastDrActualBal != null">
        #{lastDrActualBal,jdbcType=DECIMAL},
      </if>
      <if test="lastCrActualBal != null">
        #{lastCrActualBal,jdbcType=DECIMAL},
      </if>
      <if test="objectVersionNumber != null">
        #{objectVersionNumber,jdbcType=BIGINT},
      </if>
      <if test="creationDate != null">
        #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifyDate != null">
        #{lastModifyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlAcctHistPO">
    update sofi_gl_acct_hist
    <set>
      <if test="processDay != null">
        process_day = #{processDay,jdbcType=VARCHAR},
      </if>
      <if test="internalKey != null">
        internal_key = #{internalKey,jdbcType=BIGINT},
      </if>
      <if test="branch != null">
        branch = #{branch,jdbcType=VARCHAR},
      </if>
      <if test="ccy != null">
        ccy = #{ccy,jdbcType=VARCHAR},
      </if>
      <if test="glCode != null">
        gl_code = #{glCode,jdbcType=VARCHAR},
      </if>
      <if test="clientNo != null">
        client_no = #{clientNo,jdbcType=VARCHAR},
      </if>
      <if test="profitCenter != null">
        profit_center = #{profitCenter,jdbcType=VARCHAR},
      </if>
      <if test="seqNo != null">
        seq_no = #{seqNo,jdbcType=VARCHAR},
      </if>
      <if test="acctOpenDate != null">
        acct_open_date = #{acctOpenDate,jdbcType=TIMESTAMP},
      </if>
      <if test="openTranDate != null">
        open_tran_date = #{openTranDate,jdbcType=TIMESTAMP},
      </if>
      <if test="acctCloseDate != null">
        acct_close_date = #{acctCloseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="acctCloseReason != null">
        acct_close_reason = #{acctCloseReason,jdbcType=VARCHAR},
      </if>
      <if test="glAcctNo != null">
        gl_acct_no = #{glAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="acctStatus != null">
        acct_status = #{acctStatus,jdbcType=CHAR},
      </if>
      <if test="actualBal != null">
        actual_bal = #{actualBal,jdbcType=DECIMAL},
      </if>
      <if test="drActualBal != null">
        dr_actual_bal = #{drActualBal,jdbcType=DECIMAL},
      </if>
      <if test="crActualBal != null">
        cr_actual_bal = #{crActualBal,jdbcType=DECIMAL},
      </if>
      <if test="ctdDays != null">
        ctd_days = #{ctdDays,jdbcType=INTEGER},
      </if>
      <if test="mtdDays != null">
        mtd_days = #{mtdDays,jdbcType=INTEGER},
      </if>
      <if test="ytdDays != null">
        ytd_days = #{ytdDays,jdbcType=INTEGER},
      </if>
      <if test="mtdBal != null">
        mtd_bal = #{mtdBal,jdbcType=DECIMAL},
      </if>
      <if test="ytdBal != null">
        ytd_bal = #{ytdBal,jdbcType=DECIMAL},
      </if>
      <if test="aggBalCtd != null">
        agg_bal_ctd = #{aggBalCtd,jdbcType=DECIMAL},
      </if>
      <if test="aggBalMtd != null">
        agg_bal_mtd = #{aggBalMtd,jdbcType=DECIMAL},
      </if>
      <if test="aggBalYtd != null">
        agg_bal_ytd = #{aggBalYtd,jdbcType=DECIMAL},
      </if>
      <if test="periodNo != null">
        period_no = #{periodNo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="manualAccount != null">
        manual_account = #{manualAccount,jdbcType=CHAR},
      </if>
      <if test="odFacility != null">
        od_facility = #{odFacility,jdbcType=CHAR},
      </if>
      <if test="lastChangeDate != null">
        last_change_date = #{lastChangeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="acctName != null">
        acct_name = #{acctName,jdbcType=VARCHAR},
      </if>
      <if test="company != null">
        company = #{company,jdbcType=VARCHAR},
      </if>
      <if test="backupDate != null">
        backup_date = #{backupDate,jdbcType=TIMESTAMP},
      </if>
      <if test="tranTimestamp != null">
        tran_timestamp = #{tranTimestamp,jdbcType=VARCHAR},
      </if>
      <if test="systemId != null">
        system_id = #{systemId,jdbcType=VARCHAR},
      </if>
      <if test="groupClient != null">
        group_client = #{groupClient,jdbcType=VARCHAR},
      </if>
      <if test="drTranAmt != null">
        dr_tran_amt = #{drTranAmt,jdbcType=DECIMAL},
      </if>
      <if test="crTranAmt != null">
        cr_tran_amt = #{crTranAmt,jdbcType=DECIMAL},
      </if>
      <if test="lastActualBal != null">
        last_actual_bal = #{lastActualBal,jdbcType=DECIMAL},
      </if>
      <if test="lastDrActualBal != null">
        last_dr_actual_bal = #{lastDrActualBal,jdbcType=DECIMAL},
      </if>
      <if test="lastCrActualBal != null">
        last_cr_actual_bal = #{lastCrActualBal,jdbcType=DECIMAL},
      </if>
      <if test="objectVersionNumber != null">
        object_version_number = #{objectVersionNumber,jdbcType=BIGINT},
      </if>
      <if test="creationDate != null">
        creation_date = #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifyDate != null">
        last_modify_date = #{lastModifyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlAcctHistPO">
    update sofi_gl_acct_hist
    set process_day = #{processDay,jdbcType=VARCHAR},
      internal_key = #{internalKey,jdbcType=BIGINT},
      branch = #{branch,jdbcType=VARCHAR},
      ccy = #{ccy,jdbcType=VARCHAR},
      gl_code = #{glCode,jdbcType=VARCHAR},
      client_no = #{clientNo,jdbcType=VARCHAR},
      profit_center = #{profitCenter,jdbcType=VARCHAR},
      seq_no = #{seqNo,jdbcType=VARCHAR},
      acct_open_date = #{acctOpenDate,jdbcType=TIMESTAMP},
      open_tran_date = #{openTranDate,jdbcType=TIMESTAMP},
      acct_close_date = #{acctCloseDate,jdbcType=TIMESTAMP},
      acct_close_reason = #{acctCloseReason,jdbcType=VARCHAR},
      gl_acct_no = #{glAcctNo,jdbcType=VARCHAR},
      acct_status = #{acctStatus,jdbcType=CHAR},
      actual_bal = #{actualBal,jdbcType=DECIMAL},
      dr_actual_bal = #{drActualBal,jdbcType=DECIMAL},
      cr_actual_bal = #{crActualBal,jdbcType=DECIMAL},
      ctd_days = #{ctdDays,jdbcType=INTEGER},
      mtd_days = #{mtdDays,jdbcType=INTEGER},
      ytd_days = #{ytdDays,jdbcType=INTEGER},
      mtd_bal = #{mtdBal,jdbcType=DECIMAL},
      ytd_bal = #{ytdBal,jdbcType=DECIMAL},
      agg_bal_ctd = #{aggBalCtd,jdbcType=DECIMAL},
      agg_bal_mtd = #{aggBalMtd,jdbcType=DECIMAL},
      agg_bal_ytd = #{aggBalYtd,jdbcType=DECIMAL},
      period_no = #{periodNo,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      manual_account = #{manualAccount,jdbcType=CHAR},
      od_facility = #{odFacility,jdbcType=CHAR},
      last_change_date = #{lastChangeDate,jdbcType=TIMESTAMP},
      acct_name = #{acctName,jdbcType=VARCHAR},
      company = #{company,jdbcType=VARCHAR},
      backup_date = #{backupDate,jdbcType=TIMESTAMP},
      tran_timestamp = #{tranTimestamp,jdbcType=VARCHAR},
      system_id = #{systemId,jdbcType=VARCHAR},
      group_client = #{groupClient,jdbcType=VARCHAR},
      dr_tran_amt = #{drTranAmt,jdbcType=DECIMAL},
      cr_tran_amt = #{crTranAmt,jdbcType=DECIMAL},
      last_actual_bal = #{lastActualBal,jdbcType=DECIMAL},
      last_dr_actual_bal = #{lastDrActualBal,jdbcType=DECIMAL},
      last_cr_actual_bal = #{lastCrActualBal,jdbcType=DECIMAL},
      object_version_number = #{objectVersionNumber,jdbcType=BIGINT},
      creation_date = #{creationDate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      last_modify_date = #{lastModifyDate,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  </mapper>