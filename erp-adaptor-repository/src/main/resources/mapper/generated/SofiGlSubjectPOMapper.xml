<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlSubjectPOMapper">
  <resultMap id="BaseResultMap" type="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlSubjectPO">
    <id column="subject_code" jdbcType="VARCHAR" property="subjectCode" />
    <id column="subject_set_no" jdbcType="VARCHAR" property="subjectSetNo" />
    <result column="subject_desc" jdbcType="VARCHAR" property="subjectDesc" />
    <result column="subject_desc_en" jdbcType="VARCHAR" property="subjectDescEn" />
    <result column="control_subject" jdbcType="VARCHAR" property="controlSubject" />
    <result column="bspl_type" jdbcType="CHAR" property="bsplType" />
    <result column="gl_type" jdbcType="CHAR" property="glType" />
    <result column="subject_type" jdbcType="CHAR" property="subjectType" />
    <result column="balance_way" jdbcType="CHAR" property="balanceWay" />
    <result column="subject_status" jdbcType="CHAR" property="subjectStatus" />
    <result column="subject_level" jdbcType="CHAR" property="subjectLevel" />
    <result column="manual_account" jdbcType="CHAR" property="manualAccount" />
    <result column="special_bookkeeping" jdbcType="CHAR" property="specialBookkeeping" />
    <result column="od_facility" jdbcType="CHAR" property="odFacility" />
    <result column="range_no" jdbcType="VARCHAR" property="rangeNo" />
    <result column="revalue_rate_type" jdbcType="VARCHAR" property="revalueRateType" />
    <result column="system_id" jdbcType="VARCHAR" property="systemId" />
    <result column="measurement_attr" jdbcType="VARCHAR" property="measurementAttr" />
    <result column="item_segregation" jdbcType="VARCHAR" property="itemSegregation" />
    <result column="company" jdbcType="VARCHAR" property="company" />
    <result column="tran_timestamp" jdbcType="VARCHAR" property="tranTimestamp" />
    <result column="pay_rec" jdbcType="CHAR" property="payRec" />
    <result column="subject_remark" jdbcType="VARCHAR" property="subjectRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    subject_code, subject_set_no, subject_desc, subject_desc_en, control_subject, bspl_type, 
    gl_type, subject_type, balance_way, subject_status, subject_level, manual_account, 
    special_bookkeeping, od_facility, range_no, revalue_rate_type, system_id, measurement_attr, 
    item_segregation, company, tran_timestamp, pay_rec, subject_remark
  </sql>
  <select id="selectByPrimaryKey" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlSubjectPOKey" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sofi_gl_subject
    where subject_code = #{subjectCode,jdbcType=VARCHAR}
      and subject_set_no = #{subjectSetNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlSubjectPOKey">
    delete from sofi_gl_subject
    where subject_code = #{subjectCode,jdbcType=VARCHAR}
      and subject_set_no = #{subjectSetNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlSubjectPO">
    insert into sofi_gl_subject (subject_code, subject_set_no, subject_desc, 
      subject_desc_en, control_subject, bspl_type, 
      gl_type, subject_type, balance_way, 
      subject_status, subject_level, manual_account, 
      special_bookkeeping, od_facility, range_no, 
      revalue_rate_type, system_id, measurement_attr, 
      item_segregation, company, tran_timestamp, 
      pay_rec, subject_remark)
    values (#{subjectCode,jdbcType=VARCHAR}, #{subjectSetNo,jdbcType=VARCHAR}, #{subjectDesc,jdbcType=VARCHAR}, 
      #{subjectDescEn,jdbcType=VARCHAR}, #{controlSubject,jdbcType=VARCHAR}, #{bsplType,jdbcType=CHAR}, 
      #{glType,jdbcType=CHAR}, #{subjectType,jdbcType=CHAR}, #{balanceWay,jdbcType=CHAR}, 
      #{subjectStatus,jdbcType=CHAR}, #{subjectLevel,jdbcType=CHAR}, #{manualAccount,jdbcType=CHAR}, 
      #{specialBookkeeping,jdbcType=CHAR}, #{odFacility,jdbcType=CHAR}, #{rangeNo,jdbcType=VARCHAR}, 
      #{revalueRateType,jdbcType=VARCHAR}, #{systemId,jdbcType=VARCHAR}, #{measurementAttr,jdbcType=VARCHAR}, 
      #{itemSegregation,jdbcType=VARCHAR}, #{company,jdbcType=VARCHAR}, #{tranTimestamp,jdbcType=VARCHAR}, 
      #{payRec,jdbcType=CHAR}, #{subjectRemark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlSubjectPO">
    insert into sofi_gl_subject
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="subjectCode != null">
        subject_code,
      </if>
      <if test="subjectSetNo != null">
        subject_set_no,
      </if>
      <if test="subjectDesc != null">
        subject_desc,
      </if>
      <if test="subjectDescEn != null">
        subject_desc_en,
      </if>
      <if test="controlSubject != null">
        control_subject,
      </if>
      <if test="bsplType != null">
        bspl_type,
      </if>
      <if test="glType != null">
        gl_type,
      </if>
      <if test="subjectType != null">
        subject_type,
      </if>
      <if test="balanceWay != null">
        balance_way,
      </if>
      <if test="subjectStatus != null">
        subject_status,
      </if>
      <if test="subjectLevel != null">
        subject_level,
      </if>
      <if test="manualAccount != null">
        manual_account,
      </if>
      <if test="specialBookkeeping != null">
        special_bookkeeping,
      </if>
      <if test="odFacility != null">
        od_facility,
      </if>
      <if test="rangeNo != null">
        range_no,
      </if>
      <if test="revalueRateType != null">
        revalue_rate_type,
      </if>
      <if test="systemId != null">
        system_id,
      </if>
      <if test="measurementAttr != null">
        measurement_attr,
      </if>
      <if test="itemSegregation != null">
        item_segregation,
      </if>
      <if test="company != null">
        company,
      </if>
      <if test="tranTimestamp != null">
        tran_timestamp,
      </if>
      <if test="payRec != null">
        pay_rec,
      </if>
      <if test="subjectRemark != null">
        subject_remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="subjectCode != null">
        #{subjectCode,jdbcType=VARCHAR},
      </if>
      <if test="subjectSetNo != null">
        #{subjectSetNo,jdbcType=VARCHAR},
      </if>
      <if test="subjectDesc != null">
        #{subjectDesc,jdbcType=VARCHAR},
      </if>
      <if test="subjectDescEn != null">
        #{subjectDescEn,jdbcType=VARCHAR},
      </if>
      <if test="controlSubject != null">
        #{controlSubject,jdbcType=VARCHAR},
      </if>
      <if test="bsplType != null">
        #{bsplType,jdbcType=CHAR},
      </if>
      <if test="glType != null">
        #{glType,jdbcType=CHAR},
      </if>
      <if test="subjectType != null">
        #{subjectType,jdbcType=CHAR},
      </if>
      <if test="balanceWay != null">
        #{balanceWay,jdbcType=CHAR},
      </if>
      <if test="subjectStatus != null">
        #{subjectStatus,jdbcType=CHAR},
      </if>
      <if test="subjectLevel != null">
        #{subjectLevel,jdbcType=CHAR},
      </if>
      <if test="manualAccount != null">
        #{manualAccount,jdbcType=CHAR},
      </if>
      <if test="specialBookkeeping != null">
        #{specialBookkeeping,jdbcType=CHAR},
      </if>
      <if test="odFacility != null">
        #{odFacility,jdbcType=CHAR},
      </if>
      <if test="rangeNo != null">
        #{rangeNo,jdbcType=VARCHAR},
      </if>
      <if test="revalueRateType != null">
        #{revalueRateType,jdbcType=VARCHAR},
      </if>
      <if test="systemId != null">
        #{systemId,jdbcType=VARCHAR},
      </if>
      <if test="measurementAttr != null">
        #{measurementAttr,jdbcType=VARCHAR},
      </if>
      <if test="itemSegregation != null">
        #{itemSegregation,jdbcType=VARCHAR},
      </if>
      <if test="company != null">
        #{company,jdbcType=VARCHAR},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp,jdbcType=VARCHAR},
      </if>
      <if test="payRec != null">
        #{payRec,jdbcType=CHAR},
      </if>
      <if test="subjectRemark != null">
        #{subjectRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlSubjectPO">
    update sofi_gl_subject
    <set>
      <if test="subjectDesc != null">
        subject_desc = #{subjectDesc,jdbcType=VARCHAR},
      </if>
      <if test="subjectDescEn != null">
        subject_desc_en = #{subjectDescEn,jdbcType=VARCHAR},
      </if>
      <if test="controlSubject != null">
        control_subject = #{controlSubject,jdbcType=VARCHAR},
      </if>
      <if test="bsplType != null">
        bspl_type = #{bsplType,jdbcType=CHAR},
      </if>
      <if test="glType != null">
        gl_type = #{glType,jdbcType=CHAR},
      </if>
      <if test="subjectType != null">
        subject_type = #{subjectType,jdbcType=CHAR},
      </if>
      <if test="balanceWay != null">
        balance_way = #{balanceWay,jdbcType=CHAR},
      </if>
      <if test="subjectStatus != null">
        subject_status = #{subjectStatus,jdbcType=CHAR},
      </if>
      <if test="subjectLevel != null">
        subject_level = #{subjectLevel,jdbcType=CHAR},
      </if>
      <if test="manualAccount != null">
        manual_account = #{manualAccount,jdbcType=CHAR},
      </if>
      <if test="specialBookkeeping != null">
        special_bookkeeping = #{specialBookkeeping,jdbcType=CHAR},
      </if>
      <if test="odFacility != null">
        od_facility = #{odFacility,jdbcType=CHAR},
      </if>
      <if test="rangeNo != null">
        range_no = #{rangeNo,jdbcType=VARCHAR},
      </if>
      <if test="revalueRateType != null">
        revalue_rate_type = #{revalueRateType,jdbcType=VARCHAR},
      </if>
      <if test="systemId != null">
        system_id = #{systemId,jdbcType=VARCHAR},
      </if>
      <if test="measurementAttr != null">
        measurement_attr = #{measurementAttr,jdbcType=VARCHAR},
      </if>
      <if test="itemSegregation != null">
        item_segregation = #{itemSegregation,jdbcType=VARCHAR},
      </if>
      <if test="company != null">
        company = #{company,jdbcType=VARCHAR},
      </if>
      <if test="tranTimestamp != null">
        tran_timestamp = #{tranTimestamp,jdbcType=VARCHAR},
      </if>
      <if test="payRec != null">
        pay_rec = #{payRec,jdbcType=CHAR},
      </if>
      <if test="subjectRemark != null">
        subject_remark = #{subjectRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where subject_code = #{subjectCode,jdbcType=VARCHAR}
      and subject_set_no = #{subjectSetNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlSubjectPO">
    update sofi_gl_subject
    set subject_desc = #{subjectDesc,jdbcType=VARCHAR},
      subject_desc_en = #{subjectDescEn,jdbcType=VARCHAR},
      control_subject = #{controlSubject,jdbcType=VARCHAR},
      bspl_type = #{bsplType,jdbcType=CHAR},
      gl_type = #{glType,jdbcType=CHAR},
      subject_type = #{subjectType,jdbcType=CHAR},
      balance_way = #{balanceWay,jdbcType=CHAR},
      subject_status = #{subjectStatus,jdbcType=CHAR},
      subject_level = #{subjectLevel,jdbcType=CHAR},
      manual_account = #{manualAccount,jdbcType=CHAR},
      special_bookkeeping = #{specialBookkeeping,jdbcType=CHAR},
      od_facility = #{odFacility,jdbcType=CHAR},
      range_no = #{rangeNo,jdbcType=VARCHAR},
      revalue_rate_type = #{revalueRateType,jdbcType=VARCHAR},
      system_id = #{systemId,jdbcType=VARCHAR},
      measurement_attr = #{measurementAttr,jdbcType=VARCHAR},
      item_segregation = #{itemSegregation,jdbcType=VARCHAR},
      company = #{company,jdbcType=VARCHAR},
      tran_timestamp = #{tranTimestamp,jdbcType=VARCHAR},
      pay_rec = #{payRec,jdbcType=CHAR},
      subject_remark = #{subjectRemark,jdbcType=VARCHAR}
    where subject_code = #{subjectCode,jdbcType=VARCHAR}
      and subject_set_no = #{subjectSetNo,jdbcType=VARCHAR}
  </update>
</mapper>