<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.mapper.generated.SofiGlInterfaceCommonPOMapper">
  <resultMap id="BaseResultMap" type="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceCommonPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="system_code" jdbcType="VARCHAR" property="systemCode" />
    <result column="process_day" jdbcType="VARCHAR" property="processDay" />
    <result column="accounting_date" jdbcType="DATE" property="accountingDate" />
    <result column="period_name" jdbcType="VARCHAR" property="periodName" />
    <result column="ledger_id" jdbcType="BIGINT" property="ledgerId" />
    <result column="ledger_name" jdbcType="VARCHAR" property="ledgerName" />
    <result column="currency_code" jdbcType="VARCHAR" property="currencyCode" />
    <result column="journal_category" jdbcType="VARCHAR" property="journalCategory" />
    <result column="journal_source" jdbcType="VARCHAR" property="journalSource" />
    <result column="journal_source_name" jdbcType="VARCHAR" property="journalSourceName" />
    <result column="reference1" jdbcType="VARCHAR" property="reference1" />
    <result column="reference2" jdbcType="VARCHAR" property="reference2" />
    <result column="reference3" jdbcType="VARCHAR" property="reference3" />
    <result column="reference4" jdbcType="VARCHAR" property="reference4" />
    <result column="reference5" jdbcType="VARCHAR" property="reference5" />
    <result column="reference6" jdbcType="VARCHAR" property="reference6" />
    <result column="reference7" jdbcType="VARCHAR" property="reference7" />
    <result column="reference8" jdbcType="VARCHAR" property="reference8" />
    <result column="reference9" jdbcType="VARCHAR" property="reference9" />
    <result column="reference10" jdbcType="VARCHAR" property="reference10" />
    <result column="reference21" jdbcType="VARCHAR" property="reference21" />
    <result column="reference22" jdbcType="VARCHAR" property="reference22" />
    <result column="reference23" jdbcType="VARCHAR" property="reference23" />
    <result column="reference24" jdbcType="VARCHAR" property="reference24" />
    <result column="reference25" jdbcType="VARCHAR" property="reference25" />
    <result column="reference26" jdbcType="VARCHAR" property="reference26" />
    <result column="reference27" jdbcType="VARCHAR" property="reference27" />
    <result column="reference28" jdbcType="VARCHAR" property="reference28" />
    <result column="reference29" jdbcType="VARCHAR" property="reference29" />
    <result column="reference30" jdbcType="VARCHAR" property="reference30" />
    <result column="segment1" jdbcType="VARCHAR" property="segment1" />
    <result column="segment2" jdbcType="VARCHAR" property="segment2" />
    <result column="segment3" jdbcType="VARCHAR" property="segment3" />
    <result column="segment4" jdbcType="VARCHAR" property="segment4" />
    <result column="segment5" jdbcType="VARCHAR" property="segment5" />
    <result column="segment6" jdbcType="VARCHAR" property="segment6" />
    <result column="segment7" jdbcType="VARCHAR" property="segment7" />
    <result column="segment8" jdbcType="VARCHAR" property="segment8" />
    <result column="segment9" jdbcType="VARCHAR" property="segment9" />
    <result column="segment10" jdbcType="VARCHAR" property="segment10" />
    <result column="entered_dr" jdbcType="DECIMAL" property="enteredDr" />
    <result column="entered_cr" jdbcType="DECIMAL" property="enteredCr" />
    <result column="accounted_dr" jdbcType="DECIMAL" property="accountedDr" />
    <result column="accounted_cr" jdbcType="DECIMAL" property="accountedCr" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="currency_conversion_date" jdbcType="TIMESTAMP" property="currencyConversionDate" />
    <result column="currency_conversion_rate" jdbcType="DECIMAL" property="currencyConversionRate" />
    <result column="currency_conversion_type" jdbcType="VARCHAR" property="currencyConversionType" />
    <result column="attribute_category" jdbcType="VARCHAR" property="attributeCategory" />
    <result column="header_attribute1" jdbcType="VARCHAR" property="headerAttribute1" />
    <result column="header_attribute2" jdbcType="VARCHAR" property="headerAttribute2" />
    <result column="header_attribute3" jdbcType="VARCHAR" property="headerAttribute3" />
    <result column="header_attribute4" jdbcType="VARCHAR" property="headerAttribute4" />
    <result column="header_attribute5" jdbcType="VARCHAR" property="headerAttribute5" />
    <result column="header_attribute6" jdbcType="VARCHAR" property="headerAttribute6" />
    <result column="header_attribute7" jdbcType="VARCHAR" property="headerAttribute7" />
    <result column="header_attribute8" jdbcType="VARCHAR" property="headerAttribute8" />
    <result column="header_attribute9" jdbcType="VARCHAR" property="headerAttribute9" />
    <result column="header_attribute10" jdbcType="VARCHAR" property="headerAttribute10" />
    <result column="header_attribute11" jdbcType="VARCHAR" property="headerAttribute11" />
    <result column="header_attribute12" jdbcType="VARCHAR" property="headerAttribute12" />
    <result column="header_attribute13" jdbcType="VARCHAR" property="headerAttribute13" />
    <result column="header_attribute14" jdbcType="VARCHAR" property="headerAttribute14" />
    <result column="header_attribute15" jdbcType="VARCHAR" property="headerAttribute15" />
    <result column="attribute_category3" jdbcType="VARCHAR" property="attributeCategory3" />
    <result column="line_attribute1" jdbcType="VARCHAR" property="lineAttribute1" />
    <result column="line_attribute2" jdbcType="VARCHAR" property="lineAttribute2" />
    <result column="line_attribute3" jdbcType="VARCHAR" property="lineAttribute3" />
    <result column="line_attribute4" jdbcType="VARCHAR" property="lineAttribute4" />
    <result column="line_attribute5" jdbcType="VARCHAR" property="lineAttribute5" />
    <result column="line_attribute6" jdbcType="VARCHAR" property="lineAttribute6" />
    <result column="line_attribute7" jdbcType="VARCHAR" property="lineAttribute7" />
    <result column="line_attribute8" jdbcType="VARCHAR" property="lineAttribute8" />
    <result column="line_attribute9" jdbcType="VARCHAR" property="lineAttribute9" />
    <result column="line_attribute10" jdbcType="VARCHAR" property="lineAttribute10" />
    <result column="line_attribute11" jdbcType="VARCHAR" property="lineAttribute11" />
    <result column="line_attribute12" jdbcType="VARCHAR" property="lineAttribute12" />
    <result column="line_attribute13" jdbcType="VARCHAR" property="lineAttribute13" />
    <result column="line_attribute14" jdbcType="VARCHAR" property="lineAttribute14" />
    <result column="line_attribute15" jdbcType="VARCHAR" property="lineAttribute15" />
    <result column="line_attribute16" jdbcType="VARCHAR" property="lineAttribute16" />
    <result column="line_attribute17" jdbcType="VARCHAR" property="lineAttribute17" />
    <result column="line_attribute18" jdbcType="VARCHAR" property="lineAttribute18" />
    <result column="line_attribute19" jdbcType="VARCHAR" property="lineAttribute19" />
    <result column="line_attribute20" jdbcType="VARCHAR" property="lineAttribute20" />
    <result column="process_status" jdbcType="VARCHAR" property="processStatus" />
    <result column="process_message" jdbcType="VARCHAR" property="processMessage" />
    <result column="je_header_id" jdbcType="BIGINT" property="jeHeaderId" />
    <result column="journal_name" jdbcType="VARCHAR" property="journalName" />
    <result column="je_line_num" jdbcType="BIGINT" property="jeLineNum" />
    <result column="document_id" jdbcType="BIGINT" property="documentId" />
    <result column="load_request_id" jdbcType="BIGINT" property="loadRequestId" />
    <result column="import_request_id" jdbcType="BIGINT" property="importRequestId" />
    <result column="object_version_number" jdbcType="BIGINT" property="objectVersionNumber" />
    <result column="creation_date" jdbcType="TIMESTAMP" property="creationDate" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="last_modify_date" jdbcType="TIMESTAMP" property="lastModifyDate" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    id, system_code, process_day, accounting_date, period_name, ledger_id, ledger_name, 
    currency_code, journal_category, journal_source, journal_source_name, reference1, 
    reference2, reference3, reference4, reference5, reference6, reference7, reference8, 
    reference9, reference10, reference21, reference22, reference23, reference24, reference25, 
    reference26, reference27, reference28, reference29, reference30, segment1, segment2, 
    segment3, segment4, segment5, segment6, segment7, segment8, segment9, segment10, 
    entered_dr, entered_cr, accounted_dr, accounted_cr, group_id, currency_conversion_date, 
    currency_conversion_rate, currency_conversion_type, attribute_category, header_attribute1, 
    header_attribute2, header_attribute3, header_attribute4, header_attribute5, header_attribute6, 
    header_attribute7, header_attribute8, header_attribute9, header_attribute10, header_attribute11, 
    header_attribute12, header_attribute13, header_attribute14, header_attribute15, attribute_category3, 
    line_attribute1, line_attribute2, line_attribute3, line_attribute4, line_attribute5, 
    line_attribute6, line_attribute7, line_attribute8, line_attribute9, line_attribute10, 
    line_attribute11, line_attribute12, line_attribute13, line_attribute14, line_attribute15, 
    line_attribute16, line_attribute17, line_attribute18, line_attribute19, line_attribute20, 
    process_status, process_message, je_header_id, journal_name, je_line_num, document_id, 
    load_request_id, import_request_id, object_version_number, creation_date, created_by, 
    last_modify_date, last_modified_by
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sofi_gl_interface_common
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sofi_gl_interface_common
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceCommonPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sofi_gl_interface_common (system_code, process_day, accounting_date, 
      period_name, ledger_id, ledger_name, 
      currency_code, journal_category, journal_source, 
      journal_source_name, reference1, reference2, 
      reference3, reference4, reference5, 
      reference6, reference7, reference8, 
      reference9, reference10, reference21, 
      reference22, reference23, reference24, 
      reference25, reference26, reference27, 
      reference28, reference29, reference30, 
      segment1, segment2, segment3, 
      segment4, segment5, segment6, 
      segment7, segment8, segment9, 
      segment10, entered_dr, entered_cr, 
      accounted_dr, accounted_cr, group_id, 
      currency_conversion_date, currency_conversion_rate, 
      currency_conversion_type, attribute_category, 
      header_attribute1, header_attribute2, header_attribute3, 
      header_attribute4, header_attribute5, header_attribute6, 
      header_attribute7, header_attribute8, header_attribute9, 
      header_attribute10, header_attribute11, header_attribute12, 
      header_attribute13, header_attribute14, header_attribute15, 
      attribute_category3, line_attribute1, line_attribute2, 
      line_attribute3, line_attribute4, line_attribute5, 
      line_attribute6, line_attribute7, line_attribute8, 
      line_attribute9, line_attribute10, line_attribute11, 
      line_attribute12, line_attribute13, line_attribute14, 
      line_attribute15, line_attribute16, line_attribute17, 
      line_attribute18, line_attribute19, line_attribute20, 
      process_status, process_message, je_header_id, 
      journal_name, je_line_num, document_id, 
      load_request_id, import_request_id, object_version_number, 
      creation_date, created_by, last_modify_date, 
      last_modified_by)
    values (#{systemCode,jdbcType=VARCHAR}, #{processDay,jdbcType=VARCHAR}, #{accountingDate,jdbcType=DATE}, 
      #{periodName,jdbcType=VARCHAR}, #{ledgerId,jdbcType=BIGINT}, #{ledgerName,jdbcType=VARCHAR}, 
      #{currencyCode,jdbcType=VARCHAR}, #{journalCategory,jdbcType=VARCHAR}, #{journalSource,jdbcType=VARCHAR}, 
      #{journalSourceName,jdbcType=VARCHAR}, #{reference1,jdbcType=VARCHAR}, #{reference2,jdbcType=VARCHAR}, 
      #{reference3,jdbcType=VARCHAR}, #{reference4,jdbcType=VARCHAR}, #{reference5,jdbcType=VARCHAR}, 
      #{reference6,jdbcType=VARCHAR}, #{reference7,jdbcType=VARCHAR}, #{reference8,jdbcType=VARCHAR}, 
      #{reference9,jdbcType=VARCHAR}, #{reference10,jdbcType=VARCHAR}, #{reference21,jdbcType=VARCHAR}, 
      #{reference22,jdbcType=VARCHAR}, #{reference23,jdbcType=VARCHAR}, #{reference24,jdbcType=VARCHAR}, 
      #{reference25,jdbcType=VARCHAR}, #{reference26,jdbcType=VARCHAR}, #{reference27,jdbcType=VARCHAR}, 
      #{reference28,jdbcType=VARCHAR}, #{reference29,jdbcType=VARCHAR}, #{reference30,jdbcType=VARCHAR}, 
      #{segment1,jdbcType=VARCHAR}, #{segment2,jdbcType=VARCHAR}, #{segment3,jdbcType=VARCHAR}, 
      #{segment4,jdbcType=VARCHAR}, #{segment5,jdbcType=VARCHAR}, #{segment6,jdbcType=VARCHAR}, 
      #{segment7,jdbcType=VARCHAR}, #{segment8,jdbcType=VARCHAR}, #{segment9,jdbcType=VARCHAR}, 
      #{segment10,jdbcType=VARCHAR}, #{enteredDr,jdbcType=DECIMAL}, #{enteredCr,jdbcType=DECIMAL}, 
      #{accountedDr,jdbcType=DECIMAL}, #{accountedCr,jdbcType=DECIMAL}, #{groupId,jdbcType=BIGINT}, 
      #{currencyConversionDate,jdbcType=TIMESTAMP}, #{currencyConversionRate,jdbcType=DECIMAL}, 
      #{currencyConversionType,jdbcType=VARCHAR}, #{attributeCategory,jdbcType=VARCHAR}, 
      #{headerAttribute1,jdbcType=VARCHAR}, #{headerAttribute2,jdbcType=VARCHAR}, #{headerAttribute3,jdbcType=VARCHAR}, 
      #{headerAttribute4,jdbcType=VARCHAR}, #{headerAttribute5,jdbcType=VARCHAR}, #{headerAttribute6,jdbcType=VARCHAR}, 
      #{headerAttribute7,jdbcType=VARCHAR}, #{headerAttribute8,jdbcType=VARCHAR}, #{headerAttribute9,jdbcType=VARCHAR}, 
      #{headerAttribute10,jdbcType=VARCHAR}, #{headerAttribute11,jdbcType=VARCHAR}, #{headerAttribute12,jdbcType=VARCHAR}, 
      #{headerAttribute13,jdbcType=VARCHAR}, #{headerAttribute14,jdbcType=VARCHAR}, #{headerAttribute15,jdbcType=VARCHAR}, 
      #{attributeCategory3,jdbcType=VARCHAR}, #{lineAttribute1,jdbcType=VARCHAR}, #{lineAttribute2,jdbcType=VARCHAR}, 
      #{lineAttribute3,jdbcType=VARCHAR}, #{lineAttribute4,jdbcType=VARCHAR}, #{lineAttribute5,jdbcType=VARCHAR}, 
      #{lineAttribute6,jdbcType=VARCHAR}, #{lineAttribute7,jdbcType=VARCHAR}, #{lineAttribute8,jdbcType=VARCHAR}, 
      #{lineAttribute9,jdbcType=VARCHAR}, #{lineAttribute10,jdbcType=VARCHAR}, #{lineAttribute11,jdbcType=VARCHAR}, 
      #{lineAttribute12,jdbcType=VARCHAR}, #{lineAttribute13,jdbcType=VARCHAR}, #{lineAttribute14,jdbcType=VARCHAR}, 
      #{lineAttribute15,jdbcType=VARCHAR}, #{lineAttribute16,jdbcType=VARCHAR}, #{lineAttribute17,jdbcType=VARCHAR}, 
      #{lineAttribute18,jdbcType=VARCHAR}, #{lineAttribute19,jdbcType=VARCHAR}, #{lineAttribute20,jdbcType=VARCHAR}, 
      #{processStatus,jdbcType=VARCHAR}, #{processMessage,jdbcType=VARCHAR}, #{jeHeaderId,jdbcType=BIGINT}, 
      #{journalName,jdbcType=VARCHAR}, #{jeLineNum,jdbcType=BIGINT}, #{documentId,jdbcType=BIGINT}, 
      #{loadRequestId,jdbcType=BIGINT}, #{importRequestId,jdbcType=BIGINT}, #{objectVersionNumber,jdbcType=BIGINT}, 
      #{creationDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{lastModifyDate,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceCommonPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sofi_gl_interface_common
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="systemCode != null">
        system_code,
      </if>
      <if test="processDay != null">
        process_day,
      </if>
      <if test="accountingDate != null">
        accounting_date,
      </if>
      <if test="periodName != null">
        period_name,
      </if>
      <if test="ledgerId != null">
        ledger_id,
      </if>
      <if test="ledgerName != null">
        ledger_name,
      </if>
      <if test="currencyCode != null">
        currency_code,
      </if>
      <if test="journalCategory != null">
        journal_category,
      </if>
      <if test="journalSource != null">
        journal_source,
      </if>
      <if test="journalSourceName != null">
        journal_source_name,
      </if>
      <if test="reference1 != null">
        reference1,
      </if>
      <if test="reference2 != null">
        reference2,
      </if>
      <if test="reference3 != null">
        reference3,
      </if>
      <if test="reference4 != null">
        reference4,
      </if>
      <if test="reference5 != null">
        reference5,
      </if>
      <if test="reference6 != null">
        reference6,
      </if>
      <if test="reference7 != null">
        reference7,
      </if>
      <if test="reference8 != null">
        reference8,
      </if>
      <if test="reference9 != null">
        reference9,
      </if>
      <if test="reference10 != null">
        reference10,
      </if>
      <if test="reference21 != null">
        reference21,
      </if>
      <if test="reference22 != null">
        reference22,
      </if>
      <if test="reference23 != null">
        reference23,
      </if>
      <if test="reference24 != null">
        reference24,
      </if>
      <if test="reference25 != null">
        reference25,
      </if>
      <if test="reference26 != null">
        reference26,
      </if>
      <if test="reference27 != null">
        reference27,
      </if>
      <if test="reference28 != null">
        reference28,
      </if>
      <if test="reference29 != null">
        reference29,
      </if>
      <if test="reference30 != null">
        reference30,
      </if>
      <if test="segment1 != null">
        segment1,
      </if>
      <if test="segment2 != null">
        segment2,
      </if>
      <if test="segment3 != null">
        segment3,
      </if>
      <if test="segment4 != null">
        segment4,
      </if>
      <if test="segment5 != null">
        segment5,
      </if>
      <if test="segment6 != null">
        segment6,
      </if>
      <if test="segment7 != null">
        segment7,
      </if>
      <if test="segment8 != null">
        segment8,
      </if>
      <if test="segment9 != null">
        segment9,
      </if>
      <if test="segment10 != null">
        segment10,
      </if>
      <if test="enteredDr != null">
        entered_dr,
      </if>
      <if test="enteredCr != null">
        entered_cr,
      </if>
      <if test="accountedDr != null">
        accounted_dr,
      </if>
      <if test="accountedCr != null">
        accounted_cr,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="currencyConversionDate != null">
        currency_conversion_date,
      </if>
      <if test="currencyConversionRate != null">
        currency_conversion_rate,
      </if>
      <if test="currencyConversionType != null">
        currency_conversion_type,
      </if>
      <if test="attributeCategory != null">
        attribute_category,
      </if>
      <if test="headerAttribute1 != null">
        header_attribute1,
      </if>
      <if test="headerAttribute2 != null">
        header_attribute2,
      </if>
      <if test="headerAttribute3 != null">
        header_attribute3,
      </if>
      <if test="headerAttribute4 != null">
        header_attribute4,
      </if>
      <if test="headerAttribute5 != null">
        header_attribute5,
      </if>
      <if test="headerAttribute6 != null">
        header_attribute6,
      </if>
      <if test="headerAttribute7 != null">
        header_attribute7,
      </if>
      <if test="headerAttribute8 != null">
        header_attribute8,
      </if>
      <if test="headerAttribute9 != null">
        header_attribute9,
      </if>
      <if test="headerAttribute10 != null">
        header_attribute10,
      </if>
      <if test="headerAttribute11 != null">
        header_attribute11,
      </if>
      <if test="headerAttribute12 != null">
        header_attribute12,
      </if>
      <if test="headerAttribute13 != null">
        header_attribute13,
      </if>
      <if test="headerAttribute14 != null">
        header_attribute14,
      </if>
      <if test="headerAttribute15 != null">
        header_attribute15,
      </if>
      <if test="attributeCategory3 != null">
        attribute_category3,
      </if>
      <if test="lineAttribute1 != null">
        line_attribute1,
      </if>
      <if test="lineAttribute2 != null">
        line_attribute2,
      </if>
      <if test="lineAttribute3 != null">
        line_attribute3,
      </if>
      <if test="lineAttribute4 != null">
        line_attribute4,
      </if>
      <if test="lineAttribute5 != null">
        line_attribute5,
      </if>
      <if test="lineAttribute6 != null">
        line_attribute6,
      </if>
      <if test="lineAttribute7 != null">
        line_attribute7,
      </if>
      <if test="lineAttribute8 != null">
        line_attribute8,
      </if>
      <if test="lineAttribute9 != null">
        line_attribute9,
      </if>
      <if test="lineAttribute10 != null">
        line_attribute10,
      </if>
      <if test="lineAttribute11 != null">
        line_attribute11,
      </if>
      <if test="lineAttribute12 != null">
        line_attribute12,
      </if>
      <if test="lineAttribute13 != null">
        line_attribute13,
      </if>
      <if test="lineAttribute14 != null">
        line_attribute14,
      </if>
      <if test="lineAttribute15 != null">
        line_attribute15,
      </if>
      <if test="lineAttribute16 != null">
        line_attribute16,
      </if>
      <if test="lineAttribute17 != null">
        line_attribute17,
      </if>
      <if test="lineAttribute18 != null">
        line_attribute18,
      </if>
      <if test="lineAttribute19 != null">
        line_attribute19,
      </if>
      <if test="lineAttribute20 != null">
        line_attribute20,
      </if>
      <if test="processStatus != null">
        process_status,
      </if>
      <if test="processMessage != null">
        process_message,
      </if>
      <if test="jeHeaderId != null">
        je_header_id,
      </if>
      <if test="journalName != null">
        journal_name,
      </if>
      <if test="jeLineNum != null">
        je_line_num,
      </if>
      <if test="documentId != null">
        document_id,
      </if>
      <if test="loadRequestId != null">
        load_request_id,
      </if>
      <if test="importRequestId != null">
        import_request_id,
      </if>
      <if test="objectVersionNumber != null">
        object_version_number,
      </if>
      <if test="creationDate != null">
        creation_date,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="lastModifyDate != null">
        last_modify_date,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="systemCode != null">
        #{systemCode,jdbcType=VARCHAR},
      </if>
      <if test="processDay != null">
        #{processDay,jdbcType=VARCHAR},
      </if>
      <if test="accountingDate != null">
        #{accountingDate,jdbcType=DATE},
      </if>
      <if test="periodName != null">
        #{periodName,jdbcType=VARCHAR},
      </if>
      <if test="ledgerId != null">
        #{ledgerId,jdbcType=BIGINT},
      </if>
      <if test="ledgerName != null">
        #{ledgerName,jdbcType=VARCHAR},
      </if>
      <if test="currencyCode != null">
        #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="journalCategory != null">
        #{journalCategory,jdbcType=VARCHAR},
      </if>
      <if test="journalSource != null">
        #{journalSource,jdbcType=VARCHAR},
      </if>
      <if test="journalSourceName != null">
        #{journalSourceName,jdbcType=VARCHAR},
      </if>
      <if test="reference1 != null">
        #{reference1,jdbcType=VARCHAR},
      </if>
      <if test="reference2 != null">
        #{reference2,jdbcType=VARCHAR},
      </if>
      <if test="reference3 != null">
        #{reference3,jdbcType=VARCHAR},
      </if>
      <if test="reference4 != null">
        #{reference4,jdbcType=VARCHAR},
      </if>
      <if test="reference5 != null">
        #{reference5,jdbcType=VARCHAR},
      </if>
      <if test="reference6 != null">
        #{reference6,jdbcType=VARCHAR},
      </if>
      <if test="reference7 != null">
        #{reference7,jdbcType=VARCHAR},
      </if>
      <if test="reference8 != null">
        #{reference8,jdbcType=VARCHAR},
      </if>
      <if test="reference9 != null">
        #{reference9,jdbcType=VARCHAR},
      </if>
      <if test="reference10 != null">
        #{reference10,jdbcType=VARCHAR},
      </if>
      <if test="reference21 != null">
        #{reference21,jdbcType=VARCHAR},
      </if>
      <if test="reference22 != null">
        #{reference22,jdbcType=VARCHAR},
      </if>
      <if test="reference23 != null">
        #{reference23,jdbcType=VARCHAR},
      </if>
      <if test="reference24 != null">
        #{reference24,jdbcType=VARCHAR},
      </if>
      <if test="reference25 != null">
        #{reference25,jdbcType=VARCHAR},
      </if>
      <if test="reference26 != null">
        #{reference26,jdbcType=VARCHAR},
      </if>
      <if test="reference27 != null">
        #{reference27,jdbcType=VARCHAR},
      </if>
      <if test="reference28 != null">
        #{reference28,jdbcType=VARCHAR},
      </if>
      <if test="reference29 != null">
        #{reference29,jdbcType=VARCHAR},
      </if>
      <if test="reference30 != null">
        #{reference30,jdbcType=VARCHAR},
      </if>
      <if test="segment1 != null">
        #{segment1,jdbcType=VARCHAR},
      </if>
      <if test="segment2 != null">
        #{segment2,jdbcType=VARCHAR},
      </if>
      <if test="segment3 != null">
        #{segment3,jdbcType=VARCHAR},
      </if>
      <if test="segment4 != null">
        #{segment4,jdbcType=VARCHAR},
      </if>
      <if test="segment5 != null">
        #{segment5,jdbcType=VARCHAR},
      </if>
      <if test="segment6 != null">
        #{segment6,jdbcType=VARCHAR},
      </if>
      <if test="segment7 != null">
        #{segment7,jdbcType=VARCHAR},
      </if>
      <if test="segment8 != null">
        #{segment8,jdbcType=VARCHAR},
      </if>
      <if test="segment9 != null">
        #{segment9,jdbcType=VARCHAR},
      </if>
      <if test="segment10 != null">
        #{segment10,jdbcType=VARCHAR},
      </if>
      <if test="enteredDr != null">
        #{enteredDr,jdbcType=DECIMAL},
      </if>
      <if test="enteredCr != null">
        #{enteredCr,jdbcType=DECIMAL},
      </if>
      <if test="accountedDr != null">
        #{accountedDr,jdbcType=DECIMAL},
      </if>
      <if test="accountedCr != null">
        #{accountedCr,jdbcType=DECIMAL},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="currencyConversionDate != null">
        #{currencyConversionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="currencyConversionRate != null">
        #{currencyConversionRate,jdbcType=DECIMAL},
      </if>
      <if test="currencyConversionType != null">
        #{currencyConversionType,jdbcType=VARCHAR},
      </if>
      <if test="attributeCategory != null">
        #{attributeCategory,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute1 != null">
        #{headerAttribute1,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute2 != null">
        #{headerAttribute2,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute3 != null">
        #{headerAttribute3,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute4 != null">
        #{headerAttribute4,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute5 != null">
        #{headerAttribute5,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute6 != null">
        #{headerAttribute6,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute7 != null">
        #{headerAttribute7,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute8 != null">
        #{headerAttribute8,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute9 != null">
        #{headerAttribute9,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute10 != null">
        #{headerAttribute10,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute11 != null">
        #{headerAttribute11,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute12 != null">
        #{headerAttribute12,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute13 != null">
        #{headerAttribute13,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute14 != null">
        #{headerAttribute14,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute15 != null">
        #{headerAttribute15,jdbcType=VARCHAR},
      </if>
      <if test="attributeCategory3 != null">
        #{attributeCategory3,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute1 != null">
        #{lineAttribute1,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute2 != null">
        #{lineAttribute2,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute3 != null">
        #{lineAttribute3,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute4 != null">
        #{lineAttribute4,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute5 != null">
        #{lineAttribute5,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute6 != null">
        #{lineAttribute6,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute7 != null">
        #{lineAttribute7,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute8 != null">
        #{lineAttribute8,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute9 != null">
        #{lineAttribute9,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute10 != null">
        #{lineAttribute10,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute11 != null">
        #{lineAttribute11,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute12 != null">
        #{lineAttribute12,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute13 != null">
        #{lineAttribute13,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute14 != null">
        #{lineAttribute14,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute15 != null">
        #{lineAttribute15,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute16 != null">
        #{lineAttribute16,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute17 != null">
        #{lineAttribute17,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute18 != null">
        #{lineAttribute18,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute19 != null">
        #{lineAttribute19,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute20 != null">
        #{lineAttribute20,jdbcType=VARCHAR},
      </if>
      <if test="processStatus != null">
        #{processStatus,jdbcType=VARCHAR},
      </if>
      <if test="processMessage != null">
        #{processMessage,jdbcType=VARCHAR},
      </if>
      <if test="jeHeaderId != null">
        #{jeHeaderId,jdbcType=BIGINT},
      </if>
      <if test="journalName != null">
        #{journalName,jdbcType=VARCHAR},
      </if>
      <if test="jeLineNum != null">
        #{jeLineNum,jdbcType=BIGINT},
      </if>
      <if test="documentId != null">
        #{documentId,jdbcType=BIGINT},
      </if>
      <if test="loadRequestId != null">
        #{loadRequestId,jdbcType=BIGINT},
      </if>
      <if test="importRequestId != null">
        #{importRequestId,jdbcType=BIGINT},
      </if>
      <if test="objectVersionNumber != null">
        #{objectVersionNumber,jdbcType=BIGINT},
      </if>
      <if test="creationDate != null">
        #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifyDate != null">
        #{lastModifyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceCommonPO">
    update sofi_gl_interface_common
    <set>
      <if test="systemCode != null">
        system_code = #{systemCode,jdbcType=VARCHAR},
      </if>
      <if test="processDay != null">
        process_day = #{processDay,jdbcType=VARCHAR},
      </if>
      <if test="accountingDate != null">
        accounting_date = #{accountingDate,jdbcType=DATE},
      </if>
      <if test="periodName != null">
        period_name = #{periodName,jdbcType=VARCHAR},
      </if>
      <if test="ledgerId != null">
        ledger_id = #{ledgerId,jdbcType=BIGINT},
      </if>
      <if test="ledgerName != null">
        ledger_name = #{ledgerName,jdbcType=VARCHAR},
      </if>
      <if test="currencyCode != null">
        currency_code = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="journalCategory != null">
        journal_category = #{journalCategory,jdbcType=VARCHAR},
      </if>
      <if test="journalSource != null">
        journal_source = #{journalSource,jdbcType=VARCHAR},
      </if>
      <if test="journalSourceName != null">
        journal_source_name = #{journalSourceName,jdbcType=VARCHAR},
      </if>
      <if test="reference1 != null">
        reference1 = #{reference1,jdbcType=VARCHAR},
      </if>
      <if test="reference2 != null">
        reference2 = #{reference2,jdbcType=VARCHAR},
      </if>
      <if test="reference3 != null">
        reference3 = #{reference3,jdbcType=VARCHAR},
      </if>
      <if test="reference4 != null">
        reference4 = #{reference4,jdbcType=VARCHAR},
      </if>
      <if test="reference5 != null">
        reference5 = #{reference5,jdbcType=VARCHAR},
      </if>
      <if test="reference6 != null">
        reference6 = #{reference6,jdbcType=VARCHAR},
      </if>
      <if test="reference7 != null">
        reference7 = #{reference7,jdbcType=VARCHAR},
      </if>
      <if test="reference8 != null">
        reference8 = #{reference8,jdbcType=VARCHAR},
      </if>
      <if test="reference9 != null">
        reference9 = #{reference9,jdbcType=VARCHAR},
      </if>
      <if test="reference10 != null">
        reference10 = #{reference10,jdbcType=VARCHAR},
      </if>
      <if test="reference21 != null">
        reference21 = #{reference21,jdbcType=VARCHAR},
      </if>
      <if test="reference22 != null">
        reference22 = #{reference22,jdbcType=VARCHAR},
      </if>
      <if test="reference23 != null">
        reference23 = #{reference23,jdbcType=VARCHAR},
      </if>
      <if test="reference24 != null">
        reference24 = #{reference24,jdbcType=VARCHAR},
      </if>
      <if test="reference25 != null">
        reference25 = #{reference25,jdbcType=VARCHAR},
      </if>
      <if test="reference26 != null">
        reference26 = #{reference26,jdbcType=VARCHAR},
      </if>
      <if test="reference27 != null">
        reference27 = #{reference27,jdbcType=VARCHAR},
      </if>
      <if test="reference28 != null">
        reference28 = #{reference28,jdbcType=VARCHAR},
      </if>
      <if test="reference29 != null">
        reference29 = #{reference29,jdbcType=VARCHAR},
      </if>
      <if test="reference30 != null">
        reference30 = #{reference30,jdbcType=VARCHAR},
      </if>
      <if test="segment1 != null">
        segment1 = #{segment1,jdbcType=VARCHAR},
      </if>
      <if test="segment2 != null">
        segment2 = #{segment2,jdbcType=VARCHAR},
      </if>
      <if test="segment3 != null">
        segment3 = #{segment3,jdbcType=VARCHAR},
      </if>
      <if test="segment4 != null">
        segment4 = #{segment4,jdbcType=VARCHAR},
      </if>
      <if test="segment5 != null">
        segment5 = #{segment5,jdbcType=VARCHAR},
      </if>
      <if test="segment6 != null">
        segment6 = #{segment6,jdbcType=VARCHAR},
      </if>
      <if test="segment7 != null">
        segment7 = #{segment7,jdbcType=VARCHAR},
      </if>
      <if test="segment8 != null">
        segment8 = #{segment8,jdbcType=VARCHAR},
      </if>
      <if test="segment9 != null">
        segment9 = #{segment9,jdbcType=VARCHAR},
      </if>
      <if test="segment10 != null">
        segment10 = #{segment10,jdbcType=VARCHAR},
      </if>
      <if test="enteredDr != null">
        entered_dr = #{enteredDr,jdbcType=DECIMAL},
      </if>
      <if test="enteredCr != null">
        entered_cr = #{enteredCr,jdbcType=DECIMAL},
      </if>
      <if test="accountedDr != null">
        accounted_dr = #{accountedDr,jdbcType=DECIMAL},
      </if>
      <if test="accountedCr != null">
        accounted_cr = #{accountedCr,jdbcType=DECIMAL},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="currencyConversionDate != null">
        currency_conversion_date = #{currencyConversionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="currencyConversionRate != null">
        currency_conversion_rate = #{currencyConversionRate,jdbcType=DECIMAL},
      </if>
      <if test="currencyConversionType != null">
        currency_conversion_type = #{currencyConversionType,jdbcType=VARCHAR},
      </if>
      <if test="attributeCategory != null">
        attribute_category = #{attributeCategory,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute1 != null">
        header_attribute1 = #{headerAttribute1,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute2 != null">
        header_attribute2 = #{headerAttribute2,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute3 != null">
        header_attribute3 = #{headerAttribute3,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute4 != null">
        header_attribute4 = #{headerAttribute4,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute5 != null">
        header_attribute5 = #{headerAttribute5,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute6 != null">
        header_attribute6 = #{headerAttribute6,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute7 != null">
        header_attribute7 = #{headerAttribute7,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute8 != null">
        header_attribute8 = #{headerAttribute8,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute9 != null">
        header_attribute9 = #{headerAttribute9,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute10 != null">
        header_attribute10 = #{headerAttribute10,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute11 != null">
        header_attribute11 = #{headerAttribute11,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute12 != null">
        header_attribute12 = #{headerAttribute12,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute13 != null">
        header_attribute13 = #{headerAttribute13,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute14 != null">
        header_attribute14 = #{headerAttribute14,jdbcType=VARCHAR},
      </if>
      <if test="headerAttribute15 != null">
        header_attribute15 = #{headerAttribute15,jdbcType=VARCHAR},
      </if>
      <if test="attributeCategory3 != null">
        attribute_category3 = #{attributeCategory3,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute1 != null">
        line_attribute1 = #{lineAttribute1,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute2 != null">
        line_attribute2 = #{lineAttribute2,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute3 != null">
        line_attribute3 = #{lineAttribute3,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute4 != null">
        line_attribute4 = #{lineAttribute4,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute5 != null">
        line_attribute5 = #{lineAttribute5,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute6 != null">
        line_attribute6 = #{lineAttribute6,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute7 != null">
        line_attribute7 = #{lineAttribute7,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute8 != null">
        line_attribute8 = #{lineAttribute8,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute9 != null">
        line_attribute9 = #{lineAttribute9,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute10 != null">
        line_attribute10 = #{lineAttribute10,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute11 != null">
        line_attribute11 = #{lineAttribute11,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute12 != null">
        line_attribute12 = #{lineAttribute12,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute13 != null">
        line_attribute13 = #{lineAttribute13,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute14 != null">
        line_attribute14 = #{lineAttribute14,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute15 != null">
        line_attribute15 = #{lineAttribute15,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute16 != null">
        line_attribute16 = #{lineAttribute16,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute17 != null">
        line_attribute17 = #{lineAttribute17,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute18 != null">
        line_attribute18 = #{lineAttribute18,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute19 != null">
        line_attribute19 = #{lineAttribute19,jdbcType=VARCHAR},
      </if>
      <if test="lineAttribute20 != null">
        line_attribute20 = #{lineAttribute20,jdbcType=VARCHAR},
      </if>
      <if test="processStatus != null">
        process_status = #{processStatus,jdbcType=VARCHAR},
      </if>
      <if test="processMessage != null">
        process_message = #{processMessage,jdbcType=VARCHAR},
      </if>
      <if test="jeHeaderId != null">
        je_header_id = #{jeHeaderId,jdbcType=BIGINT},
      </if>
      <if test="journalName != null">
        journal_name = #{journalName,jdbcType=VARCHAR},
      </if>
      <if test="jeLineNum != null">
        je_line_num = #{jeLineNum,jdbcType=BIGINT},
      </if>
      <if test="documentId != null">
        document_id = #{documentId,jdbcType=BIGINT},
      </if>
      <if test="loadRequestId != null">
        load_request_id = #{loadRequestId,jdbcType=BIGINT},
      </if>
      <if test="importRequestId != null">
        import_request_id = #{importRequestId,jdbcType=BIGINT},
      </if>
      <if test="objectVersionNumber != null">
        object_version_number = #{objectVersionNumber,jdbcType=BIGINT},
      </if>
      <if test="creationDate != null">
        creation_date = #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifyDate != null">
        last_modify_date = #{lastModifyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xiaoju.corebanking.erp.adaptor.repository.mybatis.domain.SofiGlInterfaceCommonPO">
    update sofi_gl_interface_common
    set system_code = #{systemCode,jdbcType=VARCHAR},
      process_day = #{processDay,jdbcType=VARCHAR},
      accounting_date = #{accountingDate,jdbcType=DATE},
      period_name = #{periodName,jdbcType=VARCHAR},
      ledger_id = #{ledgerId,jdbcType=BIGINT},
      ledger_name = #{ledgerName,jdbcType=VARCHAR},
      currency_code = #{currencyCode,jdbcType=VARCHAR},
      journal_category = #{journalCategory,jdbcType=VARCHAR},
      journal_source = #{journalSource,jdbcType=VARCHAR},
      journal_source_name = #{journalSourceName,jdbcType=VARCHAR},
      reference1 = #{reference1,jdbcType=VARCHAR},
      reference2 = #{reference2,jdbcType=VARCHAR},
      reference3 = #{reference3,jdbcType=VARCHAR},
      reference4 = #{reference4,jdbcType=VARCHAR},
      reference5 = #{reference5,jdbcType=VARCHAR},
      reference6 = #{reference6,jdbcType=VARCHAR},
      reference7 = #{reference7,jdbcType=VARCHAR},
      reference8 = #{reference8,jdbcType=VARCHAR},
      reference9 = #{reference9,jdbcType=VARCHAR},
      reference10 = #{reference10,jdbcType=VARCHAR},
      reference21 = #{reference21,jdbcType=VARCHAR},
      reference22 = #{reference22,jdbcType=VARCHAR},
      reference23 = #{reference23,jdbcType=VARCHAR},
      reference24 = #{reference24,jdbcType=VARCHAR},
      reference25 = #{reference25,jdbcType=VARCHAR},
      reference26 = #{reference26,jdbcType=VARCHAR},
      reference27 = #{reference27,jdbcType=VARCHAR},
      reference28 = #{reference28,jdbcType=VARCHAR},
      reference29 = #{reference29,jdbcType=VARCHAR},
      reference30 = #{reference30,jdbcType=VARCHAR},
      segment1 = #{segment1,jdbcType=VARCHAR},
      segment2 = #{segment2,jdbcType=VARCHAR},
      segment3 = #{segment3,jdbcType=VARCHAR},
      segment4 = #{segment4,jdbcType=VARCHAR},
      segment5 = #{segment5,jdbcType=VARCHAR},
      segment6 = #{segment6,jdbcType=VARCHAR},
      segment7 = #{segment7,jdbcType=VARCHAR},
      segment8 = #{segment8,jdbcType=VARCHAR},
      segment9 = #{segment9,jdbcType=VARCHAR},
      segment10 = #{segment10,jdbcType=VARCHAR},
      entered_dr = #{enteredDr,jdbcType=DECIMAL},
      entered_cr = #{enteredCr,jdbcType=DECIMAL},
      accounted_dr = #{accountedDr,jdbcType=DECIMAL},
      accounted_cr = #{accountedCr,jdbcType=DECIMAL},
      group_id = #{groupId,jdbcType=BIGINT},
      currency_conversion_date = #{currencyConversionDate,jdbcType=TIMESTAMP},
      currency_conversion_rate = #{currencyConversionRate,jdbcType=DECIMAL},
      currency_conversion_type = #{currencyConversionType,jdbcType=VARCHAR},
      attribute_category = #{attributeCategory,jdbcType=VARCHAR},
      header_attribute1 = #{headerAttribute1,jdbcType=VARCHAR},
      header_attribute2 = #{headerAttribute2,jdbcType=VARCHAR},
      header_attribute3 = #{headerAttribute3,jdbcType=VARCHAR},
      header_attribute4 = #{headerAttribute4,jdbcType=VARCHAR},
      header_attribute5 = #{headerAttribute5,jdbcType=VARCHAR},
      header_attribute6 = #{headerAttribute6,jdbcType=VARCHAR},
      header_attribute7 = #{headerAttribute7,jdbcType=VARCHAR},
      header_attribute8 = #{headerAttribute8,jdbcType=VARCHAR},
      header_attribute9 = #{headerAttribute9,jdbcType=VARCHAR},
      header_attribute10 = #{headerAttribute10,jdbcType=VARCHAR},
      header_attribute11 = #{headerAttribute11,jdbcType=VARCHAR},
      header_attribute12 = #{headerAttribute12,jdbcType=VARCHAR},
      header_attribute13 = #{headerAttribute13,jdbcType=VARCHAR},
      header_attribute14 = #{headerAttribute14,jdbcType=VARCHAR},
      header_attribute15 = #{headerAttribute15,jdbcType=VARCHAR},
      attribute_category3 = #{attributeCategory3,jdbcType=VARCHAR},
      line_attribute1 = #{lineAttribute1,jdbcType=VARCHAR},
      line_attribute2 = #{lineAttribute2,jdbcType=VARCHAR},
      line_attribute3 = #{lineAttribute3,jdbcType=VARCHAR},
      line_attribute4 = #{lineAttribute4,jdbcType=VARCHAR},
      line_attribute5 = #{lineAttribute5,jdbcType=VARCHAR},
      line_attribute6 = #{lineAttribute6,jdbcType=VARCHAR},
      line_attribute7 = #{lineAttribute7,jdbcType=VARCHAR},
      line_attribute8 = #{lineAttribute8,jdbcType=VARCHAR},
      line_attribute9 = #{lineAttribute9,jdbcType=VARCHAR},
      line_attribute10 = #{lineAttribute10,jdbcType=VARCHAR},
      line_attribute11 = #{lineAttribute11,jdbcType=VARCHAR},
      line_attribute12 = #{lineAttribute12,jdbcType=VARCHAR},
      line_attribute13 = #{lineAttribute13,jdbcType=VARCHAR},
      line_attribute14 = #{lineAttribute14,jdbcType=VARCHAR},
      line_attribute15 = #{lineAttribute15,jdbcType=VARCHAR},
      line_attribute16 = #{lineAttribute16,jdbcType=VARCHAR},
      line_attribute17 = #{lineAttribute17,jdbcType=VARCHAR},
      line_attribute18 = #{lineAttribute18,jdbcType=VARCHAR},
      line_attribute19 = #{lineAttribute19,jdbcType=VARCHAR},
      line_attribute20 = #{lineAttribute20,jdbcType=VARCHAR},
      process_status = #{processStatus,jdbcType=VARCHAR},
      process_message = #{processMessage,jdbcType=VARCHAR},
      je_header_id = #{jeHeaderId,jdbcType=BIGINT},
      journal_name = #{journalName,jdbcType=VARCHAR},
      je_line_num = #{jeLineNum,jdbcType=BIGINT},
      document_id = #{documentId,jdbcType=BIGINT},
      load_request_id = #{loadRequestId,jdbcType=BIGINT},
      import_request_id = #{importRequestId,jdbcType=BIGINT},
      object_version_number = #{objectVersionNumber,jdbcType=BIGINT},
      creation_date = #{creationDate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      last_modify_date = #{lastModifyDate,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>