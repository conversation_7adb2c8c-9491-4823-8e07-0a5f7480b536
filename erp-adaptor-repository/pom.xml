<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>erp-adaptor-repository</artifactId>
    <parent>
        <groupId>com.xiaoju.corebanking</groupId>
        <artifactId>erp-adaptor</artifactId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <dependencies>
        <dependency>
            <groupId>com.xiaoju.corebanking</groupId>
            <artifactId>erp-adaptor-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xiaoju.godson</groupId>
            <artifactId>godson-mybatis-interceptor</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xiaoju.godson</groupId>
            <artifactId>godson-mybatis-datasource</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <!-- mybatis依赖 -->
    </dependencies>
    <!--    <build>-->
    <!--        <plugins>-->
    <!--        <plugin>-->
    <!--            <groupId>org.mybatis.generator</groupId>-->
    <!--            <artifactId>mybatis-generator-maven-plugin</artifactId>-->
    <!--            <version>1.3.7</version>-->
    <!--            <configuration>-->
    <!--                <configurationFile>-->
    <!--                    src/main/resources/generatorConfig.xml-->
    <!--                </configurationFile>-->
    <!--                <verbose>true</verbose>-->
    <!--                <overwrite>true</overwrite>-->
    <!--            </configuration>-->
    <!--            <executions>-->
    <!--                <execution>-->
    <!--                    <id>Generate MyBatis Artifacts</id>-->
    <!--                    <goals>-->
    <!--                        <goal>generate</goal>-->
    <!--                    </goals>-->
    <!--                    <phase>generate-sources</phase>-->
    <!--                </execution>-->
    <!--            </executions>-->
    <!--            <dependencies>-->
    <!--                <dependency>-->
    <!--                    <groupId>mysql</groupId>-->
    <!--                    <artifactId>mysql-connector-java</artifactId>-->
    <!--                    <version>8.0.23</version>-->
    <!--                </dependency>-->
    <!--                <dependency>-->
    <!--                    <groupId>org.mybatis.generator</groupId>-->
    <!--                    <artifactId>mybatis-generator-core</artifactId>-->
    <!--                    <version>1.3.7</version>-->
    <!--                </dependency>-->
    <!--            </dependencies>-->
    <!--        </plugin>-->
    <!--        </plugins>-->
    <!--    </build>-->
</project>