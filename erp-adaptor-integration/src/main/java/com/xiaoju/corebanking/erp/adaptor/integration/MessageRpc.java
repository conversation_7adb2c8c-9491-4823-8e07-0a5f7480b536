package com.xiaoju.corebanking.erp.adaptor.integration;

import com.fasterxml.jackson.core.type.TypeReference;
import com.xiaoju.corebanking.erp.adaptor.common.apollo.MessageApolloConfig;
import com.xiaoju.corebanking.erp.adaptor.common.enums.RpcEnum;
import com.xiaoju.corebanking.erp.adaptor.common.utils.ErpRpcInvokeUtils;
import com.xiaoju.digitalbank.entity.ReturnMsg;

import com.xiaoju.digitalbank.message.GeneralRetParams;
import com.xiaoju.digitalbank.message.platform.req.PlatformSingleMessageRequest;
import com.xiaoju.digitalbank.message.platform.res.PlatformBatchMessageResponse;
import com.xiaoju.digitalbank.message.platform.res.PlatformSingleMessageResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MessageRpc {
    @Autowired
    private MessageApolloConfig messageApolloConfig;

    /**
     * 单笔消息发送
     */
    public PlatformSingleMessageResponse sendMessagePush(PlatformSingleMessageRequest request) {
        request.setAppId(messageApolloConfig.getAccessConfig().getAppId());
        return ErpRpcInvokeUtils.invoke(RpcEnum.MESSAGE_RPC_PLATFORM_MESSAGE_PUSH, messageApolloConfig,
                request, null, new TypeReference<ReturnMsg<PlatformSingleMessageResponse>>() {
                });
    }

    public boolean checkMessageReceived(GeneralRetParams generalRetParams) {
        return generalRetParams != null && StringUtils.isNotBlank(generalRetParams.getMessageId());
    }

}
